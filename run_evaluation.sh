#!/bin/bash

# Zero-Shot Evaluation Runner Script
# This script sets up the environment and runs the zero-shot evaluation

set -e  # Exit on any error

echo "🚀 Zero-Shot Evaluation Pipeline"
echo "================================"

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo "❌ Python is not installed or not in PATH"
    exit 1
fi

echo "✅ Python found: $(python --version)"

# Check if we're in the right directory
if [ ! -f "zero_shot_evaluation.py" ]; then
    echo "❌ zero_shot_evaluation.py not found. Please run this script from the correct directory."
    exit 1
fi

# Install dependencies if requirements file exists
if [ -f "requirements_evaluation.txt" ]; then
    echo "📦 Installing dependencies..."
    pip install -r requirements_evaluation.txt
    echo "✅ Dependencies installed"
else
    echo "⚠️  requirements_evaluation.txt not found, skipping dependency installation"
fi

# Run tests first
echo "🧪 Running tests..."
if python test_evaluation.py; then
    echo "✅ Tests passed"
else
    echo "❌ Tests failed. Please check the output above."
    exit 1
fi

# Ask user what to run
echo ""
echo "What would you like to do?"
echo "1) Run full evaluation (all models, all datasets)"
echo "2) Run test evaluation (single model, limited samples)"
echo "3) Show evaluation plan (dry run)"
echo "4) Custom evaluation"

read -p "Enter your choice (1-4): " choice

case $choice in
    1)
        echo "🏃 Running full evaluation..."
        python zero_shot_evaluation.py
        ;;
    2)
        echo "🧪 Running test evaluation..."
        python zero_shot_evaluation.py --models opt350 --datasets dialogsum --dry_run
        read -p "Proceed with test evaluation? (y/n): " proceed
        if [ "$proceed" = "y" ]; then
            python zero_shot_evaluation.py --models opt350 --datasets dialogsum
        fi
        ;;
    3)
        echo "📋 Showing evaluation plan..."
        python zero_shot_evaluation.py --dry_run
        ;;
    4)
        echo "Available models: meta-llama/Llama-3.2-1B meta-llama/Llama-3.2-3B pythia410 opt350 bloomz560"
        echo "Available datasets: dialogsum qed"
        echo ""
        read -p "Enter models (space-separated): " models
        read -p "Enter datasets (space-separated): " datasets
        
        if [ -n "$models" ] && [ -n "$datasets" ]; then
            echo "🏃 Running custom evaluation..."
            python zero_shot_evaluation.py --models $models --datasets $datasets
        else
            echo "❌ Invalid input"
            exit 1
        fi
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

echo ""
echo "🎉 Evaluation script completed!"
echo "Check the output directory for results."
