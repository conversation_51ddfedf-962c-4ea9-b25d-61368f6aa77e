2025-07-09 02:45:16,938 INFO    MainThread:1393542 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-09 02:45:16,942 INFO    MainThread:1393542 [wandb_setup.py:_flush():80] Configure stats pid to 1393542
2025-07-09 02:45:16,942 INFO    MainThread:1393542 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-09 02:45:16,942 INFO    MainThread:1393542 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/KD-SLM/Multi-Level-OT/wandb/settings
2025-07-09 02:45:16,942 INFO    MainThread:1393542 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-09 02:45:16,942 INFO    MainThread:1393542 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250709_024516-xgsh09dm/logs/debug.log
2025-07-09 02:45:16,942 INFO    MainThread:1393542 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250709_024516-xgsh09dm/logs/debug-internal.log
2025-07-09 02:45:16,942 INFO    MainThread:1393542 [wandb_init.py:init():830] calling init triggers
2025-07-09 02:45:16,942 INFO    MainThread:1393542 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'evaluation_type': 'zero_shot', 'datasets': ['/storage/nammt/KD-SLM/Multi-Level-OT/qed/qed.py', 'knkarthick/dialogsum'], 'models': ['bigscience/bloomz-560m'], 'total_evaluations': 2, '_wandb': {}}
2025-07-09 02:45:16,942 INFO    MainThread:1393542 [wandb_init.py:init():871] starting backend
2025-07-09 02:45:17,217 INFO    MainThread:1393542 [wandb_init.py:init():874] sending inform_init request
2025-07-09 02:45:17,231 INFO    MainThread:1393542 [wandb_init.py:init():882] backend started and connected
2025-07-09 02:45:17,250 INFO    MainThread:1393542 [wandb_init.py:init():953] updated telemetry
2025-07-09 02:45:17,285 INFO    MainThread:1393542 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-09 02:45:18,153 INFO    MainThread:1393542 [wandb_init.py:init():1029] starting run threads in backend
2025-07-09 02:45:18,593 INFO    MainThread:1393542 [wandb_run.py:_console_start():2458] atexit reg
2025-07-09 02:45:18,594 INFO    MainThread:1393542 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-09 02:45:18,594 INFO    MainThread:1393542 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-09 02:45:18,594 INFO    MainThread:1393542 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-09 02:45:18,601 INFO    MainThread:1393542 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-09 02:45:18,916 INFO    MsgRouterThr:1393542 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
