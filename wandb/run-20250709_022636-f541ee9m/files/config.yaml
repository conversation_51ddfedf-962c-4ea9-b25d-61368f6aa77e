_wandb:
    value:
        cli_version: 0.21.0
        e:
            rwfvd2m986exjncnnq5cyrf3o02nrsp0:
                args:
                    - --model_id
                    - bigscience/bloomz-560m
                    - --dataset_id
                    - knkarthick/dialogsum
                    - --split_name
                    - test
                    - --task
                    - summary_dialogue
                    - --mapping
                    - /storage/nammt/KD-SLM/Multi-Level-OT/llm_distillation/benchmark/mapping/dialogsum.json
                    - --number_few_shot
                    - "0"
                    - --batch_size
                    - "1"
                    - --num_workers
                    - "0"
                    - --bfloat
                    - --output_path
                    - evaluation_results_20250708_182517/individual_results/bloomz560_dialogsum
                    - --save_predictions
                    - --max_samples
                    - "500"
                    - --seed
                    - "42"
                    - --wandb_api_key
                    - ****************************************
                codePath: llm_distillation/benchmark/benchmarkdialogsum.py
                codePathLocal: llm_distillation/benchmark/benchmarkdialogsum.py
                cpu_count: 6
                cpu_count_logical: 6
                cudaVersion: "12.8"
                disk:
                    /:
                        total: "982820896768"
                        used: "745196437504"
                email: <EMAIL>
                executable: /storage/nammt/KD-SLM/venv/bin/python
                git:
                    commit: d8d298f244395ee8e9550fb446e6b73a9e9a5193
                    remote: https://github.com/MothMalone/KD-SLM.git
                gpu: NVIDIA RTX A4000
                gpu_count: 1
                gpu_nvidia:
                    - architecture: Ampere
                      cudaCores: 6144
                      memoryTotal: "17171480576"
                      name: NVIDIA RTX A4000
                      uuid: GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a
                host: csews-Precision-7920-Tower
                memory:
                    total: "33291026432"
                os: Linux-6.8.0-60-generic-x86_64-with-glibc2.35
                program: /storage/nammt/KD-SLM/Multi-Level-OT/llm_distillation/benchmark/benchmarkdialogsum.py
                python: CPython 3.10.12
                root: /storage/nammt/KD-SLM/Multi-Level-OT
                startedAt: "2025-07-08T19:26:36.298701Z"
                writerId: rwfvd2m986exjncnnq5cyrf3o02nrsp0
        m: []
        python_version: 3.10.12
        t:
            "1":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 71
                - 100
            "2":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 71
                - 100
            "3":
                - 2
                - 13
                - 16
            "4": 3.10.12
            "5": 0.21.0
            "6": 4.53.1
            "12": 0.21.0
            "13": linux-x86_64
batch_size:
    value: 1
bert_score:
    value: false
bfloat:
    value: true
context:
    value: false
context_length:
    value: null
dataset_id:
    value: knkarthick/dialogsum
from_disk:
    value: false
mapping:
    value: /storage/nammt/KD-SLM/Multi-Level-OT/llm_distillation/benchmark/mapping/dialogsum.json
mapping_dict:
    value: text
max_samples:
    value: 500
model_id:
    value: bigscience/bloomz-560m
model_tokenizer:
    value: null
num_workers:
    value: 0
number_few_shot:
    value: 0
output_path:
    value: evaluation_results_20250708_182517/individual_results/bloomz560_dialogsum
save_predictions:
    value: true
seed:
    value: 42
seq2seq:
    value: false
split_name:
    value: test
task:
    value: summary_dialogue
title:
    value: false
wandb_api_key:
    value: ****************************************
