2025-07-09 02:26:36,300 INFO    MainThread:2463754 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-09 02:26:36,308 INFO    MainThread:2463754 [wandb_setup.py:_flush():80] Configure stats pid to 2463754
2025-07-09 02:26:36,309 INFO    MainThread:2463754 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-09 02:26:36,309 INFO    MainThread:2463754 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/KD-SLM/Multi-Level-OT/wandb/settings
2025-07-09 02:26:36,309 INFO    MainThread:2463754 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-09 02:26:36,309 INFO    MainThread:2463754 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250709_022636-f541ee9m/logs/debug.log
2025-07-09 02:26:36,309 INFO    MainThread:2463754 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250709_022636-f541ee9m/logs/debug-internal.log
2025-07-09 02:26:36,309 INFO    MainThread:2463754 [wandb_init.py:init():830] calling init triggers
2025-07-09 02:26:36,309 INFO    MainThread:2463754 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'model_id': 'bigscience/bloomz-560m', 'model_tokenizer': None, 'dataset_id': 'knkarthick/dialogsum', 'split_name': 'test', 'context': False, 'title': False, 'number_few_shot': 0, 'batch_size': 1, 'num_workers': 0, 'bfloat': True, 'save_predictions': True, 'from_disk': False, 'task': 'summary_dialogue', 'mapping': '/storage/nammt/KD-SLM/Multi-Level-OT/llm_distillation/benchmark/mapping/dialogsum.json', 'mapping_dict': 'text', 'bert_score': False, 'output_path': 'evaluation_results_20250708_182517/individual_results/bloomz560_dialogsum', 'context_length': None, 'seq2seq': False, 'max_samples': 500, 'wandb_api_key': '****************************************', 'seed': 42, '_wandb': {}}
2025-07-09 02:26:36,309 INFO    MainThread:2463754 [wandb_init.py:init():871] starting backend
2025-07-09 02:26:36,564 INFO    MainThread:2463754 [wandb_init.py:init():874] sending inform_init request
2025-07-09 02:26:36,576 INFO    MainThread:2463754 [wandb_init.py:init():882] backend started and connected
2025-07-09 02:26:36,583 INFO    MainThread:2463754 [wandb_init.py:init():953] updated telemetry
2025-07-09 02:26:36,611 INFO    MainThread:2463754 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-09 02:26:37,639 INFO    MainThread:2463754 [wandb_init.py:init():1029] starting run threads in backend
2025-07-09 02:26:38,186 INFO    MainThread:2463754 [wandb_run.py:_console_start():2458] atexit reg
2025-07-09 02:26:38,186 INFO    MainThread:2463754 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-09 02:26:38,186 INFO    MainThread:2463754 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-09 02:26:38,186 INFO    MainThread:2463754 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-09 02:26:38,188 INFO    MainThread:2463754 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-09 02:39:34,279 INFO    MainThread:2463754 [wandb_run.py:_finish():2224] finishing run joshuafoshua-university-of-engineering-and-technology-hanoi/dialogsum-benchmark/f541ee9m
2025-07-09 02:39:34,280 INFO    MainThread:2463754 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-09 02:39:34,281 INFO    MainThread:2463754 [wandb_run.py:_restore():2405] restore
2025-07-09 02:39:34,281 INFO    MainThread:2463754 [wandb_run.py:_restore():2411] restore done
2025-07-09 02:39:36,111 INFO    MainThread:2463754 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-09 02:39:36,112 INFO    MainThread:2463754 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-09 02:39:36,112 INFO    MainThread:2463754 [wandb_run.py:_footer_sync_info():3864] logging synced files
