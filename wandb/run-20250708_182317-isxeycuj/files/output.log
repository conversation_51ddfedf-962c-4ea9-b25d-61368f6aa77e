  0%|▏                                                                                      | 1/500 [00:21<2:59:16, 21.56s/it]
Traceback (most recent call last):
  File "/storage/nammt/KD-SLM/Multi-Level-OT/llm_distillation/benchmark/benchmarkqed.py", line 215, in <module>
    output = model.generate(
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
    return func(*args, **kwargs)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/transformers/generation/utils.py", line 2625, in generate
    result = self._sample(
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/transformers/generation/utils.py", line 3609, in _sample
    outputs = model_forward(**model_inputs, return_dict=True)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
    output = func(self, *args, **kwargs)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/transformers/models/opt/modeling_opt.py", line 861, in forward
    outputs = self.model.decoder(
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
    output = func(self, *args, **kwargs)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/transformers/models/opt/modeling_opt.py", line 672, in forward
    layer_outputs = decoder_layer(
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/transformers/modeling_layers.py", line 83, in __call__
    return super().__call__(*args, **kwargs)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/transformers/models/opt/modeling_opt.py", line 269, in forward
    hidden_states = nn.functional.dropout(hidden_states, p=self.dropout, training=self.training)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/torch/nn/functional.py", line 1425, in dropout
    _VF.dropout_(input, p, training) if inplace else _VF.dropout(input, p, training)
  File "/storage/nammt/KD-SLM/venv/lib/python3.10/site-packages/torch/_VF.py", line 28, in __getattr__
    return getattr(self.vf, name)
KeyboardInterrupt
