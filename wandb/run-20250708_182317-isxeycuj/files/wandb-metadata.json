{"os": "Linux-6.8.0-60-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.12", "startedAt": "2025-07-08T11:23:17.418045Z", "args": ["--model_id", "facebook/opt-350m", "--dataset_id", "/storage/nammt/KD-SLM/Multi-Level-OT/qed/qed.py", "--split_name", "validation", "--task", "qa", "--mapping", "/storage/nammt/KD-SLM/Multi-Level-OT/llm_distillation/benchmark/mapping/qed.json", "--number_few_shot", "0", "--batch_size", "1", "--num_workers", "0", "--bfloat", "--output_path", "evaluation_results_20250708_182212/individual_results/opt350_qed", "--save_predictions", "--max_samples", "500", "--seed", "42", "--wandb_api_key", "****************************************", "--from_disk"], "program": "/storage/nammt/KD-SLM/Multi-Level-OT/llm_distillation/benchmark/benchmarkqed.py", "codePath": "llm_distillation/benchmark/benchmarkqed.py", "codePathLocal": "llm_distillation/benchmark/benchmarkqed.py", "git": {"remote": "https://github.com/2018cx/Multi-Level-OT.git", "commit": "d8d298f244395ee8e9550fb446e6b73a9e9a5193"}, "email": "<EMAIL>", "root": "/storage/nammt/KD-SLM/Multi-Level-OT", "host": "csews-Precision-7920-Tower", "executable": "/storage/nammt/KD-SLM/venv/bin/python", "cpu_count": 6, "cpu_count_logical": 6, "gpu": "NVIDIA RTX A4000", "gpu_count": 1, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "33291026432"}, "gpu_nvidia": [{"name": "NVIDIA RTX A4000", "memoryTotal": "17171480576", "cudaCores": 6144, "architecture": "Ampere", "uuid": "GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a"}], "cudaVersion": "12.8", "writerId": "0sy6vt69vsz13no9mw8o5kab1ktia1mu"}