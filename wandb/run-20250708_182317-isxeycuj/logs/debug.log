2025-07-08 18:23:17,425 INFO    MainThread:1386574 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-08 18:23:17,426 INFO    MainThread:1386574 [wandb_setup.py:_flush():80] Configure stats pid to 1386574
2025-07-08 18:23:17,426 INFO    MainThread:1386574 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-08 18:23:17,426 INFO    MainThread:1386574 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/KD-SLM/Multi-Level-OT/wandb/settings
2025-07-08 18:23:17,426 INFO    MainThread:1386574 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-08 18:23:17,426 INFO    MainThread:1386574 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250708_182317-isxeycuj/logs/debug.log
2025-07-08 18:23:17,426 INFO    MainThread:1386574 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250708_182317-isxeycuj/logs/debug-internal.log
2025-07-08 18:23:17,426 INFO    MainThread:1386574 [wandb_init.py:init():830] calling init triggers
2025-07-08 18:23:17,426 INFO    MainThread:1386574 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'model_id': 'facebook/opt-350m', 'model_tokenizer': None, 'dataset_id': '/storage/nammt/KD-SLM/Multi-Level-OT/qed/qed.py', 'split_name': 'validation', 'context': False, 'title': False, 'number_few_shot': 0, 'batch_size': 1, 'num_workers': 0, 'bfloat': True, 'save_predictions': True, 'from_disk': True, 'task': 'qa', 'mapping': '/storage/nammt/KD-SLM/Multi-Level-OT/llm_distillation/benchmark/mapping/qed.json', 'mapping_dict': 'text', 'bert_score': False, 'output_path': 'evaluation_results_20250708_182212/individual_results/opt350_qed', 'context_length': None, 'seq2seq': False, 'max_samples': 500, 'wandb_api_key': '****************************************', 'seed': 42, '_wandb': {}}
2025-07-08 18:23:17,426 INFO    MainThread:1386574 [wandb_init.py:init():871] starting backend
2025-07-08 18:23:17,663 INFO    MainThread:1386574 [wandb_init.py:init():874] sending inform_init request
2025-07-08 18:23:17,672 INFO    MainThread:1386574 [wandb_init.py:init():882] backend started and connected
2025-07-08 18:23:17,681 INFO    MainThread:1386574 [wandb_init.py:init():953] updated telemetry
2025-07-08 18:23:17,706 INFO    MainThread:1386574 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-08 18:23:18,572 INFO    MainThread:1386574 [wandb_init.py:init():1029] starting run threads in backend
2025-07-08 18:23:19,144 INFO    MainThread:1386574 [wandb_run.py:_console_start():2458] atexit reg
2025-07-08 18:23:19,145 INFO    MainThread:1386574 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-08 18:23:19,145 INFO    MainThread:1386574 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-08 18:23:19,146 INFO    MainThread:1386574 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-08 18:23:19,153 INFO    MainThread:1386574 [wandb_init.py:init():1075] run started, returning control to user process
