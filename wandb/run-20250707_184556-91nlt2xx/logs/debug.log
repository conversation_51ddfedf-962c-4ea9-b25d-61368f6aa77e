2025-07-07 18:45:56,876 INFO    MainThread:1402931 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-07 18:45:56,879 INFO    MainThread:1402931 [wandb_setup.py:_flush():80] Configure stats pid to 1402931
2025-07-07 18:45:56,879 INFO    MainThread:1402931 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-07 18:45:56,879 INFO    MainThread:1402931 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/KD-SLM/Multi-Level-OT/wandb/settings
2025-07-07 18:45:56,879 INFO    MainThread:1402931 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-07 18:45:56,880 INFO    MainThread:1402931 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250707_184556-91nlt2xx/logs/debug.log
2025-07-07 18:45:56,880 INFO    MainThread:1402931 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250707_184556-91nlt2xx/logs/debug-internal.log
2025-07-07 18:45:56,880 INFO    MainThread:1402931 [wandb_init.py:init():830] calling init triggers
2025-07-07 18:45:56,880 INFO    MainThread:1402931 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'evaluation_type': 'zero_shot', 'datasets': ['knkarthick/dialogsum', '/storage/nammt/KD-SLM/Multi-Level-OT/qed/qed.py'], 'models': ['facebook/opt-350m', 'meta-llama/Llama-3.2-3B', 'EleutherAI/pythia-410m', 'bigscience/bloomz-560m', 'meta-llama/Llama-3.2-1B'], 'total_evaluations': 10, '_wandb': {}}
2025-07-07 18:45:56,880 INFO    MainThread:1402931 [wandb_init.py:init():871] starting backend
2025-07-07 18:45:58,133 INFO    MainThread:1402931 [wandb_init.py:init():874] sending inform_init request
2025-07-07 18:45:58,161 INFO    MainThread:1402931 [wandb_init.py:init():882] backend started and connected
2025-07-07 18:45:58,163 INFO    MainThread:1402931 [wandb_init.py:init():953] updated telemetry
2025-07-07 18:45:58,178 INFO    MainThread:1402931 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-07 18:45:59,176 INFO    MainThread:1402931 [wandb_init.py:init():1029] starting run threads in backend
2025-07-07 18:45:59,965 INFO    MainThread:1402931 [wandb_run.py:_console_start():2458] atexit reg
2025-07-07 18:45:59,965 INFO    MainThread:1402931 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-07 18:45:59,965 INFO    MainThread:1402931 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-07 18:45:59,966 INFO    MainThread:1402931 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-07 18:45:59,986 INFO    MainThread:1402931 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-07 18:46:00,930 INFO    MainThread:1402931 [wandb_run.py:_finish():2224] finishing run joshuafoshua-university-of-engineering-and-technology-hanoi/zero-shot-evaluation/91nlt2xx
2025-07-07 18:46:00,930 INFO    MainThread:1402931 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-07 18:46:00,931 INFO    MainThread:1402931 [wandb_run.py:_restore():2405] restore
2025-07-07 18:46:00,931 INFO    MainThread:1402931 [wandb_run.py:_restore():2411] restore done
2025-07-07 18:46:03,413 INFO    MainThread:1402931 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-07 18:46:03,416 INFO    MainThread:1402931 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-07 18:46:03,422 INFO    MainThread:1402931 [wandb_run.py:_footer_sync_info():3864] logging synced files
