2025-07-08 18:26:17,879 INFO    MainThread:1394094 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-08 18:26:17,879 INFO    MainThread:1394094 [wandb_setup.py:_flush():80] Configure stats pid to 1394094
2025-07-08 18:26:17,885 INFO    MainThread:1394094 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-08 18:26:17,886 INFO    MainThread:1394094 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/KD-SLM/Multi-Level-OT/wandb/settings
2025-07-08 18:26:17,886 INFO    MainThread:1394094 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-08 18:26:17,886 INFO    MainThread:1394094 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250708_182617-0pus1svy/logs/debug.log
2025-07-08 18:26:17,886 INFO    MainThread:1394094 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250708_182617-0pus1svy/logs/debug-internal.log
2025-07-08 18:26:17,886 INFO    MainThread:1394094 [wandb_init.py:init():830] calling init triggers
2025-07-08 18:26:17,886 INFO    MainThread:1394094 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'model_id': 'meta-llama/Llama-3.2-1B', 'model_tokenizer': None, 'dataset_id': 'knkarthick/dialogsum', 'split_name': 'test', 'context': False, 'title': False, 'number_few_shot': 0, 'batch_size': 1, 'num_workers': 0, 'bfloat': True, 'save_predictions': True, 'from_disk': False, 'task': 'summary_dialogue', 'mapping': '/storage/nammt/KD-SLM/Multi-Level-OT/llm_distillation/benchmark/mapping/dialogsum.json', 'mapping_dict': 'text', 'bert_score': False, 'output_path': 'evaluation_results_20250708_182517/individual_results/meta-llama_Llama-3.2-1B_dialogsum', 'context_length': None, 'seq2seq': False, 'max_samples': 500, 'wandb_api_key': '****************************************', 'seed': 42, '_wandb': {}}
2025-07-08 18:26:17,886 INFO    MainThread:1394094 [wandb_init.py:init():871] starting backend
2025-07-08 18:26:18,120 INFO    MainThread:1394094 [wandb_init.py:init():874] sending inform_init request
2025-07-08 18:26:18,137 INFO    MainThread:1394094 [wandb_init.py:init():882] backend started and connected
2025-07-08 18:26:18,147 INFO    MainThread:1394094 [wandb_init.py:init():953] updated telemetry
2025-07-08 18:26:18,178 INFO    MainThread:1394094 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-08 18:26:19,064 INFO    MainThread:1394094 [wandb_init.py:init():1029] starting run threads in backend
2025-07-08 18:26:19,784 INFO    MainThread:1394094 [wandb_run.py:_console_start():2458] atexit reg
2025-07-08 18:26:19,784 INFO    MainThread:1394094 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-08 18:26:19,784 INFO    MainThread:1394094 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-08 18:26:19,784 INFO    MainThread:1394094 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-08 18:26:19,786 INFO    MainThread:1394094 [wandb_init.py:init():1075] run started, returning control to user process
