{"os": "Linux-6.8.0-60-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.12", "startedAt": "2025-07-06T17:30:57.838695Z", "program": "/storage/nammt/KD-SLM/Multi-Level-OT/zero_shot_evaluation.py", "codePath": "zero_shot_evaluation.py", "codePathLocal": "zero_shot_evaluation.py", "git": {"remote": "https://github.com/2018cx/Multi-Level-OT.git", "commit": "d8d298f244395ee8e9550fb446e6b73a9e9a5193"}, "email": "<EMAIL>", "root": "/storage/nammt/KD-SLM/Multi-Level-OT", "host": "csews-Precision-7920-Tower", "executable": "/storage/nammt/KD-SLM/venv/bin/python3", "cpu_count": 6, "cpu_count_logical": 6, "gpu": "NVIDIA RTX A4000", "gpu_count": 1, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "33291026432"}, "gpu_nvidia": [{"name": "NVIDIA RTX A4000", "memoryTotal": "17171480576", "cudaCores": 6144, "architecture": "Ampere", "uuid": "GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a"}], "cudaVersion": "12.8", "writerId": "n3nhobfy7p4ncp0alz045vxfvshit7jn"}