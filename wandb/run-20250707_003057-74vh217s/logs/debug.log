2025-07-07 00:30:57,841 INFO    MainThread:3570229 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-07 00:30:57,841 INFO    MainThread:3570229 [wandb_setup.py:_flush():80] Configure stats pid to 3570229
2025-07-07 00:30:57,841 INFO    MainThread:3570229 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-07 00:30:57,841 INFO    MainThread:3570229 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/KD-SLM/Multi-Level-OT/wandb/settings
2025-07-07 00:30:57,841 INFO    MainThread:3570229 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-07 00:30:57,841 INFO    MainThread:3570229 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250707_003057-74vh217s/logs/debug.log
2025-07-07 00:30:57,841 INFO    MainThread:3570229 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250707_003057-74vh217s/logs/debug-internal.log
2025-07-07 00:30:57,841 INFO    MainThread:3570229 [wandb_init.py:init():830] calling init triggers
2025-07-07 00:30:57,842 INFO    MainThread:3570229 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'evaluation_type': 'zero_shot', 'datasets': ['knkarthick/dialogsum', '/storage/nammt/KD-SLM/Multi-Level-OT/qed/qed.py'], 'models': ['meta-llama/Llama-3.2-1B', 'facebook/opt-350m', 'EleutherAI/pythia-410m-deduped', 'bigscience/bloomz-560m', 'meta-llama/Llama-3.2-3B'], 'total_evaluations': 10, '_wandb': {}}
2025-07-07 00:30:57,842 INFO    MainThread:3570229 [wandb_init.py:init():871] starting backend
2025-07-07 00:30:59,087 INFO    MainThread:3570229 [wandb_init.py:init():874] sending inform_init request
2025-07-07 00:30:59,115 INFO    MainThread:3570229 [wandb_init.py:init():882] backend started and connected
2025-07-07 00:30:59,118 INFO    MainThread:3570229 [wandb_init.py:init():953] updated telemetry
2025-07-07 00:30:59,199 INFO    MainThread:3570229 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-07 00:31:00,110 INFO    MainThread:3570229 [wandb_init.py:init():1029] starting run threads in backend
2025-07-07 00:31:00,431 INFO    MainThread:3570229 [wandb_run.py:_console_start():2458] atexit reg
2025-07-07 00:31:00,432 INFO    MainThread:3570229 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-07 00:31:00,432 INFO    MainThread:3570229 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-07 00:31:00,432 INFO    MainThread:3570229 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-07 00:31:00,449 INFO    MainThread:3570229 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-07 00:31:01,421 INFO    MainThread:3570229 [wandb_run.py:_finish():2224] finishing run joshuafoshua-university-of-engineering-and-technology-hanoi/zero-shot-evaluation/74vh217s
2025-07-07 00:31:01,422 INFO    MainThread:3570229 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-07 00:31:01,422 INFO    MainThread:3570229 [wandb_run.py:_restore():2405] restore
2025-07-07 00:31:01,422 INFO    MainThread:3570229 [wandb_run.py:_restore():2411] restore done
2025-07-07 00:31:04,196 INFO    MainThread:3570229 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-07 00:31:04,199 INFO    MainThread:3570229 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-07 00:31:04,203 INFO    MainThread:3570229 [wandb_run.py:_footer_sync_info():3864] logging synced files
