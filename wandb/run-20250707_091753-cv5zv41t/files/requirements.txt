transformers==4.53.1
pyarrow==20.0.0
safetensors==0.5.3
nvidia-cufile-cu12==********
cycler==0.12.1
sympy==1.14.0
MarkupSafe==3.0.2
nvidia-cuda-runtime-cu12==12.6.77
scikit-learn==1.7.0
annotated-types==0.7.0
grpcio-tools==1.73.1
grpcio==1.73.1
triton==3.3.1
nvidia-cusparse-cu12==********
wandb==0.21.0
charset-normalizer==3.4.2
prompt==0.4.1
Jinja2==3.1.6
attrs==25.3.0
protobuf==6.31.1
aiohappyeyeballs==2.6.1
pillow==11.3.0
nvidia-cuda-nvrtc-cu12==12.6.77
pip==22.0.2
platformdirs==4.3.8
hf-xet==1.1.5
six==1.17.0
joblib==1.5.1
PyYAML==6.0.2
evaluate==0.4.4
nvidia-nvtx-cu12==12.6.77
rouge_score==0.1.2
contourpy==1.3.2
filelock==3.18.0
absl-py==2.3.1
typing_extensions==4.14.1
yarl==1.20.1
huggingface-hub==0.33.2
pydantic_core==2.33.2
GitPython==3.1.44
mpmath==1.3.0
pydantic==2.11.7
pytz==2025.2
googleapis-common-protos==1.70.0
tzdata==2025.2
gitdb==4.0.12
bert-score==0.3.13
typing-inspection==0.4.1
nvidia-cudnn-cu12==********
urllib3==2.5.0
tokenizers==0.21.2
scipy==1.15.3
python-dateutil==2.9.0.post0
aiohttp==3.12.13
matplotlib==3.10.3
kiwisolver==1.4.8
regex==2024.11.6
sentry-sdk==2.32.0
idna==3.10
fonttools==4.58.5
async-timeout==5.0.1
propcache==0.3.2
smmap==5.0.2
xxhash==3.5.0
nvidia-cublas-cu12==********
nvidia-cusparselt-cu12==0.6.3
nvidia-nvjitlink-cu12==12.6.85
fsspec==2025.3.0
nvidia-cufft-cu12==********
tqdm==4.67.1
frozenlist==1.7.0
pyparsing==3.2.3
accelerate==1.8.1
datasets==3.6.0
nvidia-curand-cu12==*********
dill==0.3.8
nvidia-nccl-cu12==2.26.2
multidict==6.6.3
multiprocess==0.70.16
torch==2.7.1
packaging==25.0
pandas==2.3.0
threadpoolctl==3.6.0
nltk==3.9.1
networkx==3.4.2
certifi==2025.6.15
numpy==2.2.6
nvidia-cusolver-cu12==********
requests==2.32.4
nvidia-cuda-cupti-cu12==12.6.80
aiosignal==1.4.0
click==8.2.1
setuptools==59.6.0
psutil==7.0.0
