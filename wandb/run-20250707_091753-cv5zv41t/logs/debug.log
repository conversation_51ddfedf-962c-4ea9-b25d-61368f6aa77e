2025-07-07 09:17:53,718 INFO    MainThread:796193 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-07 09:17:53,718 INFO    MainThread:796193 [wandb_setup.py:_flush():80] Configure stats pid to 796193
2025-07-07 09:17:53,719 INFO    MainThread:796193 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-07 09:17:53,719 INFO    MainThread:796193 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/KD-SLM/Multi-Level-OT/wandb/settings
2025-07-07 09:17:53,719 INFO    MainThread:796193 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-07 09:17:53,719 INFO    MainThread:796193 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250707_091753-cv5zv41t/logs/debug.log
2025-07-07 09:17:53,719 INFO    MainThread:796193 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250707_091753-cv5zv41t/logs/debug-internal.log
2025-07-07 09:17:53,719 INFO    MainThread:796193 [wandb_init.py:init():830] calling init triggers
2025-07-07 09:17:53,719 INFO    MainThread:796193 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'evaluation_type': 'zero_shot', 'datasets': ['/storage/nammt/KD-SLM/Multi-Level-OT/qed/qed.py', 'knkarthick/dialogsum'], 'models': ['EleutherAI/pythia-410m'], 'total_evaluations': 2, '_wandb': {}}
2025-07-07 09:17:53,719 INFO    MainThread:796193 [wandb_init.py:init():871] starting backend
2025-07-07 09:17:53,936 INFO    MainThread:796193 [wandb_init.py:init():874] sending inform_init request
2025-07-07 09:17:53,948 INFO    MainThread:796193 [wandb_init.py:init():882] backend started and connected
2025-07-07 09:17:53,949 INFO    MainThread:796193 [wandb_init.py:init():953] updated telemetry
2025-07-07 09:17:53,965 INFO    MainThread:796193 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-07 09:17:54,778 INFO    MainThread:796193 [wandb_init.py:init():1029] starting run threads in backend
2025-07-07 09:17:55,482 INFO    MainThread:796193 [wandb_run.py:_console_start():2458] atexit reg
2025-07-07 09:17:55,482 INFO    MainThread:796193 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-07 09:17:55,483 INFO    MainThread:796193 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-07 09:17:55,483 INFO    MainThread:796193 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-07 09:17:55,487 INFO    MainThread:796193 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-07 09:17:56,406 INFO    MainThread:796193 [wandb_run.py:_finish():2224] finishing run joshuafoshua-university-of-engineering-and-technology-hanoi/zero-shot-evaluation/cv5zv41t
2025-07-07 09:17:56,407 INFO    MainThread:796193 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-07 09:17:56,408 INFO    MainThread:796193 [wandb_run.py:_restore():2405] restore
2025-07-07 09:17:56,408 INFO    MainThread:796193 [wandb_run.py:_restore():2411] restore done
2025-07-07 09:17:59,238 INFO    MainThread:796193 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-07 09:17:59,239 INFO    MainThread:796193 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-07 09:17:59,242 INFO    MainThread:796193 [wandb_run.py:_footer_sync_info():3864] logging synced files
