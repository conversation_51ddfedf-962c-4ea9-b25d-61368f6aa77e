_wandb:
    value:
        cli_version: 0.21.0
        e:
            mxv54c320gfkxz33budp4rtx6zp6cl6u:
                codePath: zero_shot_evaluation.py
                codePathLocal: zero_shot_evaluation.py
                cpu_count: 6
                cpu_count_logical: 6
                cudaVersion: "12.8"
                disk:
                    /:
                        total: "982820896768"
                        used: "917493309440"
                email: <EMAIL>
                executable: /storage/nammt/KD-SLM/venv/bin/python3
                git:
                    commit: d8d298f244395ee8e9550fb446e6b73a9e9a5193
                    remote: https://github.com/2018cx/Multi-Level-OT.git
                gpu: NVIDIA RTX A4000
                gpu_count: 1
                gpu_nvidia:
                    - architecture: Ampere
                      cudaCores: 6144
                      memoryTotal: "17171480576"
                      name: NVIDIA RTX A4000
                      uuid: GPU-e9925f08-06b7-78e4-b50b-7f59948fa22a
                host: csews-Precision-7920-Tower
                memory:
                    total: "33291026432"
                os: Linux-6.8.0-60-generic-x86_64-with-glibc2.35
                program: /storage/nammt/KD-SLM/Multi-Level-OT/zero_shot_evaluation.py
                python: CPython 3.10.12
                root: /storage/nammt/KD-SLM/Multi-Level-OT
                startedAt: "2025-07-07T04:08:17.358161Z"
                writerId: mxv54c320gfkxz33budp4rtx6zp6cl6u
        m: []
        python_version: 3.10.12
        t:
            "3":
                - 2
                - 13
                - 16
            "4": 3.10.12
            "5": 0.21.0
            "12": 0.21.0
            "13": linux-x86_64
datasets:
    value:
        - knkarthick/dialogsum
evaluation_type:
    value: zero_shot
models:
    value:
        - EleutherAI/pythia-410m
        - facebook/opt-350m
total_evaluations:
    value: 2
