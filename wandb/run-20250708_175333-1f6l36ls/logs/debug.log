2025-07-08 17:53:33,255 INFO    MainThread:1319925 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-08 17:53:33,255 INFO    MainThread:1319925 [wandb_setup.py:_flush():80] Configure stats pid to 1319925
2025-07-08 17:53:33,255 INFO    MainThread:1319925 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-08 17:53:33,255 INFO    MainThread:1319925 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/KD-SLM/Multi-Level-OT/wandb/settings
2025-07-08 17:53:33,255 INFO    MainThread:1319925 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-08 17:53:33,256 INFO    MainThread:1319925 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250708_175333-1f6l36ls/logs/debug.log
2025-07-08 17:53:33,256 INFO    MainThread:1319925 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250708_175333-1f6l36ls/logs/debug-internal.log
2025-07-08 17:53:33,256 INFO    MainThread:1319925 [wandb_init.py:init():830] calling init triggers
2025-07-08 17:53:33,256 INFO    MainThread:1319925 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'model_id': 'facebook/opt-350m', 'model_tokenizer': None, 'dataset_id': '/storage/nammt/KD-SLM/Multi-Level-OT/qed/qed.py', 'split_name': 'validation', 'context': False, 'title': False, 'number_few_shot': 0, 'batch_size': 1, 'num_workers': 2, 'bfloat': False, 'save_predictions': False, 'from_disk': True, 'task': 'qa', 'mapping': 'llm_distillation/benchmark/mapping/qed.json', 'mapping_dict': 'text', 'bert_score': False, 'output_path': '', 'context_length': None, 'seq2seq': False, 'max_samples': 50, 'wandb_api_key': '****************************************', 'seed': 42, '_wandb': {}}
2025-07-08 17:53:33,256 INFO    MainThread:1319925 [wandb_init.py:init():871] starting backend
2025-07-08 17:53:33,550 INFO    MainThread:1319925 [wandb_init.py:init():874] sending inform_init request
2025-07-08 17:53:33,574 INFO    MainThread:1319925 [wandb_init.py:init():882] backend started and connected
2025-07-08 17:53:33,580 INFO    MainThread:1319925 [wandb_init.py:init():953] updated telemetry
2025-07-08 17:53:33,609 INFO    MainThread:1319925 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-08 17:53:34,592 INFO    MainThread:1319925 [wandb_init.py:init():1029] starting run threads in backend
2025-07-08 17:53:35,744 INFO    MainThread:1319925 [wandb_run.py:_console_start():2458] atexit reg
2025-07-08 17:53:35,744 INFO    MainThread:1319925 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-08 17:53:35,745 INFO    MainThread:1319925 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-08 17:53:35,745 INFO    MainThread:1319925 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-08 17:53:35,760 INFO    MainThread:1319925 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-08 18:02:07,181 INFO    MainThread:1319925 [wandb_run.py:_finish():2224] finishing run joshuafoshua-university-of-engineering-and-technology-hanoi/qed-benchmark/1f6l36ls
2025-07-08 18:02:07,184 INFO    MainThread:1319925 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-08 18:02:07,184 INFO    MainThread:1319925 [wandb_run.py:_restore():2405] restore
2025-07-08 18:02:07,185 INFO    MainThread:1319925 [wandb_run.py:_restore():2411] restore done
2025-07-08 18:02:08,964 INFO    MainThread:1319925 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-08 18:02:08,964 INFO    MainThread:1319925 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-08 18:02:08,971 INFO    MainThread:1319925 [wandb_run.py:_footer_sync_info():3864] logging synced files
