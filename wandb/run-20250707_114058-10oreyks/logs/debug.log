2025-07-07 11:40:58,618 INFO    MainThread:1195352 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-07 11:40:58,618 INFO    MainThread:1195352 [wandb_setup.py:_flush():80] Configure stats pid to 1195352
2025-07-07 11:40:58,619 INFO    MainThread:1195352 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-07-07 11:40:58,619 INFO    MainThread:1195352 [wandb_setup.py:_flush():80] Loading settings from /storage/nammt/KD-SLM/Multi-Level-OT/wandb/settings
2025-07-07 11:40:58,619 INFO    MainThread:1195352 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-07 11:40:58,619 INFO    MainThread:1195352 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250707_114058-10oreyks/logs/debug.log
2025-07-07 11:40:58,619 INFO    MainThread:1195352 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /storage/nammt/KD-SLM/Multi-Level-OT/wandb/run-20250707_114058-10oreyks/logs/debug-internal.log
2025-07-07 11:40:58,619 INFO    MainThread:1195352 [wandb_init.py:init():830] calling init triggers
2025-07-07 11:40:58,619 INFO    MainThread:1195352 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'evaluation_type': 'zero_shot', 'datasets': ['/storage/nammt/KD-SLM/Multi-Level-OT/qed/qed.py'], 'models': ['facebook/opt-350m'], 'total_evaluations': 1, '_wandb': {}}
2025-07-07 11:40:58,619 INFO    MainThread:1195352 [wandb_init.py:init():871] starting backend
2025-07-07 11:40:58,833 INFO    MainThread:1195352 [wandb_init.py:init():874] sending inform_init request
2025-07-07 11:40:58,842 INFO    MainThread:1195352 [wandb_init.py:init():882] backend started and connected
2025-07-07 11:40:58,844 INFO    MainThread:1195352 [wandb_init.py:init():953] updated telemetry
2025-07-07 11:40:58,858 INFO    MainThread:1195352 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-07 11:40:59,670 INFO    MainThread:1195352 [wandb_init.py:init():1029] starting run threads in backend
2025-07-07 11:40:59,918 INFO    MainThread:1195352 [wandb_run.py:_console_start():2458] atexit reg
2025-07-07 11:40:59,919 INFO    MainThread:1195352 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-07 11:40:59,919 INFO    MainThread:1195352 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-07 11:40:59,919 INFO    MainThread:1195352 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-07 11:40:59,921 INFO    MainThread:1195352 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-07 11:41:00,886 INFO    MainThread:1195352 [wandb_run.py:_finish():2224] finishing run joshuafoshua-university-of-engineering-and-technology-hanoi/zero-shot-evaluation/10oreyks
2025-07-07 11:41:00,886 INFO    MainThread:1195352 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-07-07 11:41:00,888 INFO    MainThread:1195352 [wandb_run.py:_restore():2405] restore
2025-07-07 11:41:00,888 INFO    MainThread:1195352 [wandb_run.py:_restore():2411] restore done
2025-07-07 11:41:03,190 INFO    MainThread:1195352 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-07-07 11:41:03,191 INFO    MainThread:1195352 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-07-07 11:41:03,191 INFO    MainThread:1195352 [wandb_run.py:_footer_sync_info():3864] logging synced files
