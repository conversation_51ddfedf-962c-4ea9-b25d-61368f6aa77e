#!/usr/bin/env python3
"""
Test script to validate the evaluation pipeline with a small subset.
"""

import os
import sys
import subprocess
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_single_evaluation():
    """Test a single evaluation with a small model and limited samples."""
    logger.info("Testing single evaluation...")

    # Test with the smallest model and very limited samples on DialogSum
    cmd_dialogsum = [
        "python", "llm_distillation/benchmark/benchmarkdialogsum.py",
        "--model_id", "facebook/opt-350m",
        "--dataset_id", "knkarthick/dialogsum",
        "--split_name", "test",
        "--task", "summary_dialogue",
        "--mapping", "llm_distillation/benchmark/mapping/dialogsum.json",
        "--number_few_shot", "0",
        "--batch_size", "1",
        "--num_workers", "0",
        "--max_samples", "20",  # Very small for testing
        "--output_path", "test_output_dialogsum",
        "--save_predictions"
    ]

    # Test QED with local Python script
    cmd_qed = [
        "python", "llm_distillation/benchmark/benchmarkqed.py",
        "--model_id", "facebook/opt-350m",
        "--dataset_id", f"{os.getcwd()}/qed/qed.py",
        "--split_name", "validation",
        "--task", "qa",
        "--mapping", "llm_distillation/benchmark/mapping/qed.json",
        "--number_few_shot", "0",
        "--batch_size", "1",
        "--num_workers", "0",
        "--max_samples", "5",  # Very small for testing
        "--output_path", "test_output_qed",
        "--save_predictions",
        "--from_disk"
    ]

    # Test DialogSum
    try:
        logger.info(f"Testing DialogSum: {' '.join(cmd_dialogsum)}")
        result = subprocess.run(cmd_dialogsum, capture_output=True, text=True, timeout=300)  # 5 min timeout

        if result.returncode == 0:
            logger.info("✅ DialogSum test completed successfully!")
        else:
            logger.error("❌ DialogSum test failed!")
            logger.error("Error output:")
            logger.error(result.stderr)
            return False

    except subprocess.TimeoutExpired:
        logger.error("❌ DialogSum test timed out!")
        return False
    except Exception as e:
        logger.error(f"❌ DialogSum test error: {e}")
        return False

    # Test QED
    try:
        logger.info(f"Testing QED: {' '.join(cmd_qed)}")
        result = subprocess.run(cmd_qed, capture_output=True, text=True, timeout=300)  # 5 min timeout

        if result.returncode == 0:
            logger.info("✅ QED test completed successfully!")
            logger.info("Both tests passed!")
            return True
        else:
            logger.error("❌ QED test failed!")
            logger.error("Error output:")
            logger.error(result.stderr)
            return False

    except subprocess.TimeoutExpired:
        logger.error("❌ QED test timed out!")
        return False
    except Exception as e:
        logger.error(f"❌ QED test error: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are available."""
    logger.info("Checking dependencies...")
    
    required_packages = [
        'torch', 'transformers', 'datasets', 'pandas', 
        'tqdm', 'evaluate', 'rouge_score', 'bert_score'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {package} is available")
        except ImportError:
            logger.error(f"❌ {package} is missing")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing packages: {missing_packages}")
        logger.info("Install with: pip install -r requirements_evaluation.txt")
        return False
    
    return True

def check_wandb():
    """Check if wandb is available and can be initialized."""
    logger.info("Checking wandb...")
    
    try:
        import wandb
        # Test with the provided API key
        os.environ["WANDB_API_KEY"] = "****************************************"
        wandb.login(key="****************************************")
        logger.info("✅ Wandb is available and API key works")
        return True
    except ImportError:
        logger.error("❌ Wandb not installed")
        return False
    except Exception as e:
        logger.error(f"❌ Wandb error: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting evaluation pipeline tests...")
    
    # Check dependencies
    if not check_dependencies():
        logger.error("Dependency check failed. Please install missing packages.")
        return False
    
    # Check wandb
    wandb_ok = check_wandb()
    if not wandb_ok:
        logger.warning("Wandb check failed, but evaluation can continue without it")
    
    # Test single evaluation
    if not test_single_evaluation():
        logger.error("Single evaluation test failed.")
        return False
    
    logger.info("🎉 All tests passed! The evaluation pipeline is ready.")
    logger.info("You can now run the full evaluation with:")
    logger.info("python zero_shot_evaluation.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
