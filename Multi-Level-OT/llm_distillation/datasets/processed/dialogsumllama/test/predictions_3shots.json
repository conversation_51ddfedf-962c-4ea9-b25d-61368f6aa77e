{"answers": ["<PERSON><PERSON> <PERSON> helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks <PERSON><PERSON> to send out a memo to all employees by the afternoon.", "Ms<PERSON> takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists."], "prediction_text": "Mr<PERSON> <PERSON> tells <PERSON><PERSON> to take a dictation for an intra-office memorandum that restricts the use of Instant Messaging programs by employees during working hours. Ms<PERSON> asks if it applies to external communications as well, and Mr<PERSON> replies that it does."}
{"answers": ["#Person2# arrives late because of traffic jam. #Person1# persuades #Person2# to use public transportations to keep healthy and to protect the environment.", "#Person2# decides to follow #Person1#'s suggestions on quitting driving to work and will try to use public transportations.", "#Person2# complains to #Person1# about the traffic jam, #Person1# suggests quitting driving and taking public transportation instead."], "prediction_text": "#Person1# is surprised that #Person2# is late and suggests taking public transportation to work. #Person2# agrees and mentions the environmental impact of driving. #Person1# suggests biking as an alternative."}
{"answers": ["#Person1# tells Kate that <PERSON>sha and <PERSON> get divorced. <PERSON> is surprised because she thought they are perfect couple.", "#Person1# tells <PERSON> that <PERSON>sha and <PERSON> are getting a peaceful divorce. <PERSON> feels surprised and asks about their kids.", "#Person1# and <PERSON> talk about the divorce between <PERSON>sha and <PERSON>. <PERSON> feels surprised because she thought they are well matched"], "prediction_text": "<PERSON> tells #<PERSON>2# that <PERSON>sha and <PERSON> are getting divorced, and #<PERSON>2# is surprised since they seemed well-matched. <PERSON> explains that <PERSON>sha seems to be handling the divorce well and that the divorce will be final early in the New Year."}
{"answers": ["#Person1# and Brian are at the birthday party of Brian. Brian thinks #Person1# looks great and is popular.", "#Person1# attends Brian's birthday party. Brian thinks #Person1# looks great and charming.", "#Person1# has a dance with Brian at Brian's birthday party. Brian thinks #Person1# looks great and is popular."], "prediction_text": "#Person1# gives Brian a present on his birthday and invites him to enjoy the party. #Person2# is happy and thanks #Person1# for coming. #Person1# and #Person2# dance together and #Person1# feels happy and confident."}
{"answers": ["#Person1# is surprised at the Olympic Stadium'volume, capacity and interior setting to #Person1#.", "#Person2# shows #Person1# around the constructing Olympic stadium and introduces the stadium.", "#Person2# introduces the Olympic Stadium's finishing time, capacity and interior setting to #Person1#."], "prediction_text": "#Person1# is amazed at the size of the Olympic park and asks about the stadium's completion date. #Person2# tells #Person1# that the stadium will be finished in June and there are 5000 seats in total. #Person1# is impressed and sees the tracks and jumping pit. #Person2# mentions that many signs with English translations are placed for foreign visitors."}
{"answers": ["#Person1# wants to create a company and is going to write a business plan. #Person2# gives #Person1# suggestions on how to summarise business ideas, describe the service, differ from competitors and attract investment in a good business plan. #Person1# decides to stick to the old job.", "#Person1# abandons the idea of creating a company after #Person2# explains what a business plan includes. #Person2# specifies that a well-written business plan includes an executive summary, goods and service description, strategy and implementation, and financial analysis.", "#Person1# wants to start #Person1#'s own business, but #Person2# warns #Person1# of the hassle. #Person2# tells #Person1# what is needed in a business plan and #Person1# decides to stick to the old job for now."], "prediction_text": "#Person1# wants to start their own company, but #Person2# explains the difficulty of writing a business plan and the importance of including various information such as legal structure, goods/services, market analysis, strategy/implementation, and financial analysis. #Person1# decides it's too hard and will stick with their old job."}
{"answers": ["#Person2# feels itchy. #Person1# doubts it is chicken pox and asks #Person2# to get away. #Person2# doesn't believe it.", "#Person1# suspects that #Person2# has chicken pox and wants to keep away from #Person2#. #Person2# thinks #Person1# is exaggerating.", "#Person1# thinks #Person2# has chicken pox and warns #Person2# about the possible hazards but #Person2# thinks it will be fine."], "prediction_text": "#Person1# is worried that #Person2# might have chicken pox and tells #Person2# to stay away from him. #Person2# thinks it might just be a rash or allergy and plans to see a doctor."}
{"answers": ["#Person2# is checking out and asks #Person1# for the bill. #Person1# gives #Person2# a wrong bill at first then corrects it.", "#Person1# helps #Person2# correct a mischarged bill on laundry service and helps #Person2# check out.", "#Person2# finds #Person2# being mischarged. #Person1# corrects the bill and #Person2# pays for it."], "prediction_text": "#Person1# is at the front desk and #Person2# is checking out. #Person2# questions a $30 charge on their bill and #Person1# checks with the department to resolve the issue."}
{"answers": ["#Person1# is begging Steven to persuade his wife not to divorce him. Steven agrees.", "Steve will try to persuade #Person1#'s wife not to divorce #Person1# as #Person1# swears to remain faithful forever.", "#Person1# begs Steven's to persuade #Person1#'s wife not to divorce him, and Steven agrees."], "prediction_text": "Steven's wife is going to divorce him because he had an affair with his secretary, and Steven admits that he was wrong and swears to be faithful to his wife forever."}
{"answers": ["#Person1# and #Person2# are talking about Abraham Lincoln. They think he was a noble man.", "#Person2# admires Abraham Lincoln for his perseverance, courage and humility.", "#Person1# and #Person2# talk about Abraham Lincoln and his glorious history. They both admire him."], "prediction_text": "#Person1# and #Person2# talk about famous people with good character, with #Person2# mentioning Abraham Lincoln as an example. #Person2# admires Lincoln's perseverance and commitment to equality."}
{"answers": ["#Person2# plans to have a trip in Hebei but #Person1# says there are sandstorms in there.", "#Person1# tells #Person2# Hebei was experiencing severe sandstorms and sandstorms threaten people's health.", "#Person2# wants to travel to Hebei but #Person1# informs #Person2# of terrible sandstorms there."], "prediction_text": "#Person1# and #Person2# are discussing a trip, and #Person1# mentions that the north of China is experiencing severe sandstorms, which affects the people who live there."}
{"answers": ["#Person2# comes to the birthday party of Francis and sends him a remote car model.", "#Person2# gives Francis a car model that both of them love as a birthday gift.", "#Person2# gives Francis a nice car model as a birthday gift and Francis appreciates it."], "prediction_text": "#Person1# is happy to see #Person2# at their birthday party and opens the gift, a remote car model, which #Person1# likes. #Person2# struggles to give the gift and #Person1# appreciates the gift more."}
{"answers": ["Tony got caught cheating and feels ashamed. Steven tells him to learn from it and study hard.", "Tony tells Steven that he failed a class because of cheating and he feels awful.", "Tony tells Steven that he cheated on the exam and realized his mistake."], "prediction_text": "Tony asks Steven why he looks unhappy, and Steven tells Tony he made a big mistake, got caught cheating, and feels ashamed. Tony tells Steven to study hard and learn from his mistakes."}
{"answers": ["#Person1# is catching a train. Tom asks #Person1# not to hurry.", "#Person1# is in a hurry to catch a train. Tom tells #Person1# there is plenty of time.", "#Person1# is rushing to catch a train but Tom thinks it isn't necessary."], "prediction_text": "Tom tells #Person1# that it is ten to nine, but #Person1# has to catch the nine-thirty train and has plenty of time to get to the railway station."}
{"answers": ["#Person1# wants to adjust #Person1#'s life and #Person2# suggests #Person1# be positive and stay healthy.", "#Person1# has a bad lifestyle. #Person2# kindly advises #Person1# to be positive.", "#Person2# hopes #Person1# will become healthy and positive."], "prediction_text": "#Person1# is having trouble adjusting their life and seeks advice from #Person2#. #Person2# suggests getting plenty of sleep, not drinking too much wine, and exercising every morning. #Person1# is worried about their future and #Person2# encourages them to not worry and make a contribution to the world."}
{"answers": ["#Person1# tells #Person2# that Ruojia is married and will have a party tonight. #Person2#'s surprised to know that. They will bring their gifts to bless her.", "#Person2# is surprised to know from #Person1# that Ruojia is married. Then #Person2# finds Ruojia has sent an email about it. They will go to Ruojia's party and give their presents to her.", "#Person2# is surprised that Ruojia's married. #Person1# and #Person2# will go to her party and give their presents to wish her a happy marriage."], "prediction_text": "#Person1# invites #Person2# to Ruojia's party tonight, and #Person2# expresses happiness for Ruojia's marriage and plans to bring a tea set as a gift."}
{"answers": ["#Person2# at first thinks #Person1#'s behaviour cruel but finally joins #Person1#.", "#Person1# plans on playing a trick to others. #Person2# thinks it's cruel at first but then joins.", "#Person1# is about to make a prank. #Person2# thinks it's cruel at first but then joins."], "prediction_text": "#Person1# and #Person2# are pulling off a prank on two people, making them think they are taking a strip off a shirt. #Person1# and #Person2# think it's fun, but #Person2# is hesitant at first."}
{"answers": ["Mike is describing his sister to #Person1#.", "Mike describes to #Person1# his sister's characters and personality.", "#Person1# asks Mike about his sister."], "prediction_text": "#Person1# asks Mike about his sister, and Mike describes her as tall and pretty, but not as clever as him. #Person1# teases Mike about being proud of his sister's looks."}
{"answers": ["#Person1# feels sick and #Person2# gives #Person1# a check-up.", "#Person2# finds that #Person1# has a fever and says #Person1# should've called in sick earlier.", "#Person2# checks #Person1#'s physical condition and finds #Person1# has a fever."], "prediction_text": "#Person1# has a headache and goes to see #Person2#, who takes their temperature and tells them they have a small fever. #Person1# felt dizzy that morning and is told to call in sick next time."}
{"answers": ["#Person1# helps #Person2# to choose a new phone.", "#Person2# wants to buy a new mobile phone from #Person1#.", "#Person2# wants to buy a new mobile phone from #Person1#."], "prediction_text": "#Person1# helps #Person2# find a new mobile phone with camera, MP3 player, and video call capabilities."}
{"answers": ["Frank got a new job and is telling Judy not only the heavy schedule but also the benefits of this job.", "Frank explains to Judy the reason why he took a job at the Post office is that the government offers excellent health insurance benefits for its employees.", "Frank describes his new job's schedule and insurance at the Post Office to Judy."], "prediction_text": "Frank got a new job at the Post Office and will work from 5am every day. He mentioned that the government offers excellent health insurance benefits to its employees and their families."}
{"answers": ["#Person2# is describing the educational background and previous working experience to #Person1# in a job interview.", "#Person2# is being interviewed by #Person1#. #Person2# is equipped with a bunch of valuable office skills.", "#Person1# asks about #Person2#'s special skills, training, previous working experience and other qualification for the job."], "prediction_text": "#Person1# asks #Person2# about their skills and qualifications, and #Person2# replies with their experience in typing, shorthand, operating a fax machine, and other office equipment."}
{"answers": ["#Person1# is not satisfied with the steak and #Person2# will change it.", "#Person2# will change a steak for #Person1# as that one doesn't satisfy #Person1#.", "#Person1# is not satisfied with #Person1#'s steak and asks to change one, and #Person2# agrees."], "prediction_text": "#Person1# is unhappy with the steak he received and asks #Person2# to change it. #Person2# apologizes and offers to bring a new steak, but #Person1# will have to wait for a few minutes."}
{"answers": ["#Person1# congratulates Tom for achieving the Nobel Prize.", "#Person1# tells Tom that his novel has won the Nobel Prize.", "#Person2# tells Tom he won the Nobel Prize."], "prediction_text": "Tom has won the Nobel Prize for his novel, and #Person1# is proud of him and wants to celebrate."}
{"answers": ["#Person1# is interviewing #Person2# about #Person2#'s ability and previous experience.", "#Person1# asks #Person2# about #Person2#'s capacities and past working experience during an interview.", "#Person1# asks #Person2# about #Person2#'s qualification for the job."], "prediction_text": "#Person1# asks #Person2# about their qualifications for the job, and #Person2# explains their major, degree, and past work experience in automobile designing."}
{"answers": ["#Person1# and #Person2# are talking about some personal facts of drinking. #Person2# drinks a lot, while #Person1# cannot drink much for health reasons. They decide to have a drink together tomorrow night.", "#Person2#, a heavy drinker, invites #Person1#, a light drinker to go out the next day. #Person2# chooses a place that has a special on pitchers.", "#Person1# and #Person2# talk about their drinking capacity and their drinking habits. They decide to have a drink together tomorrow night."], "prediction_text": "#Person1# and #Person2# are talking about their drinking habits. #Person1# is a lightweight and only drinks occasionally, while #Person2# drinks frequently and can drink 10 drinks in one night. #Person2# suggests going out tomorrow night to a bar with a special on pitchers."}
{"answers": ["May is helping her mother to do some preparation for the picnic.", "May's mother asks May for help in preparing for a picnic. May gives her a hand.", "Mom asks May to help to prepare for the picnic and May agrees."], "prediction_text": "#Person1# asks #Person2# to help prepare for the picnic, #Person2# reminds #Person1# of the weather forecast and the food, and #Person1# asks #Person2# to take the items to the living room."}
{"answers": ["Muriel Douglas and James meet each other and talk about what they have done during the holiday.", "James and Muriel are talking while waiting for Susan, Muriel's associate. They talk about how they spent the holiday with their families.", "Muriel Douglas and James send greetings and talk about their holiday before the meeting. Douglas stayed in L.A., while James went to Tahoe."], "prediction_text": "James meets Muriel Douglas for a meeting about a new account. They chat about their holidays and James mentions that his wife doesn't like skiing, while Muriel enjoyed skiing in Tahoe and would like to go back soon."}
{"answers": ["#Person1# wants to withdraw money from an ATM, but the ATM automatically transfers 10000 USD to the World Wildlife Foundation after confirming the withdrawal option. #Person1# gets mad and somehow locked in.", "#Person1# run out of money because of a girl, and is withdrawing money from an ATM. But the ATM seems to go wrong and transfers #Person1#'s money to the World Wildlife Foundation, driving #Person1# crazy.", "#Person1# is withdrawing money from an ATM. But the ATM wrongly transfers #Person1#'s money to the World Wildlife Foundation. It drives #Person1# crazy."], "prediction_text": "#Person1# is upset about spending money and goes to an ATM. #Person2# works at the ATM and helps #Person1# withdraw money, but #Person1# accidentally selects a transfer option and #Person2# informs #Person1# of the situation."}
{"answers": ["#Person2# tells #Person1# #Person2#'s communication strategy.", "Since #Person2# is very social, #Person1# asks for the communication strategy.", "#Person2# shares #Person2#'s communication strategy with #Person1#."], "prediction_text": "#Person1# asks #Person2# if they are a social person, and #Person2# replies that they are. #Person1# then asks about #Person2#'s strategy in communicating with colleagues, and #Person2# responds that sincerity is the most important thing."}
{"answers": ["Mr. Polly is tired and wants a break from work. #Person1# cannot buy a bottle of soft drink for him.", "Mr. Polly wants to get a break from work and he asks #Person1# to buy a drink for him, but #Person1# refuses.", "Mr. Polly asks #Person1#'s help to buy a drink, but #Person1# refuses."], "prediction_text": "Mr. Polly wants a break from his job, but #Person1# tells him that his boss is in the shop where he wants to buy a bottle of soft drink. Mr. Polly goes to the shop anyway, despite the problem."}
{"answers": ["Francis and Monica are discussing when to work on the financial report.", "Francis and Monica manage to find time to work on a report together.", "Francis and Monica negotiate on the time to work on the report."], "prediction_text": "Monica wants to work on a financial report with Francis, but Francis is busy all day and can only meet on Friday afternoon from 1 PM to 4 PM."}
{"answers": ["#Person1# joins #Person2#'s interview workshop. They discuss the tips to improve their interview performance.", "#Person1# takes an interview workshop. #Person2# offer #Person1# some useful tips on getting ready for an interview.", "In the workshop, #Person2# offer #Person1# some suggestions on how to perform well in interviews."], "prediction_text": "#Person1# is in an interview class and #Person2# gives #Person1# some tips on how to prepare for an interview, including dressing appropriately, being friendly, and being honest in answers."}
{"answers": ["#Person1# and Mike are discussing what kind of emotion should be expressed by Mike in this play. They have different understandings.", "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "#Person1# wants Mike to act more angry, but Mike thinks he should act both angry and sad."], "prediction_text": "#Person1# and #Person2# are rehearsing a scene where #Person2#'s character is breaking up with #Person1#'s character. #Person1# wants #Person2# to act more angry and sad, but #Person2# is unsure."}
{"answers": ["#Person1# greets Mrs. Todd and then they say goodbye to each other.", "#Person1# comes over to say hello to Mrs. Todd, and will go shopping afterwards.", "#Person1# visits Mrs. Todd, and Mrs. Todd appreciates that."], "prediction_text": "#Person1# is leaving after saying hello to #Person2#, who mentions that there is a lot to do on weekends with small children and offers to call on #Person1#. #Person1# offers to take #Person2# shopping but #Person2# declines."}
{"answers": ["Bill is tired. Bill and #Person1# talk about Bill's brother.", "#Person1# has a chat with Bill, and learns updates about Bill and his brother. #Person1# asks for time because #Person1# will go meet a friend.", "#Person1# suggests Bill take it easy and asks him about his brother. #Person1# also synchronizes the time with Bill."], "prediction_text": "Bill is tired because he has been working late every night this week, and he plans to go home and rest. Bill's brother is on a business trip in the States and won't be back for several more weeks."}
{"answers": ["Simon and Cleo have different opinions towards the demonstration to help stop the spread of nuclear weapons. Cleo thinks it is useless, while Simon considers that Cleo should go to the demonstration.", "Cleo has no intention to attend the demonstration to help stop the spread of nuclear weapons, because Cleo hates police standing by with tear gas. Simon tries to change Cleo's mind but it doesn't work.", "Simon and Cleo argue the effectiveness of demonstrations. Simon thinks they are helpful, but Cleo disagrees and refuses to go to the demonstration of helping stop the spread of nuclear weapons with Simon."], "prediction_text": "Simon wants Cleo to go to a demonstration to help stop the spread of nuclear weapons, but Cleo is not interested and tells Simon that demonstrations don't seem to accomplish anything and she has no intention of getting hurt."}
{"answers": ["#Person2# let in someone and #Person1# thinks it was #Person2#'s fault.", "#Person1# blames #Person2# for letting someone in without much discretion.", "#Person1# advises #Person2# not to let anyone in casually."], "prediction_text": "#Person1# blames #Person2# for letting someone into the house without proper checking, while #Person2# thinks it is understandable to trust someone showing a card."}
{"answers": ["Mark wants to borrow Maggie's class notes. Maggie suggests Mark copy them in the library and invites him to be study partners.", "Mark asks Maggie for her history notes because Mark has been too tired in class. They become study partners at the end.", "Mark asks Maggie for her notes because Mark's notes aren't good. They decide to be study partners and study together."], "prediction_text": "Mark wants to borrow Maggie's history notes, but Maggie tells him she usually goes to the cafeteria to review them. Mark explains that he works in a supermarket from 7 to 10 pm every evening and needs help staying awake in class. Maggie agrees to be his study partner and push him when he falls asleep."}
{"answers": ["#Person2# wants to take a course of Professor Turner and Professor agrees if #Person2# achieves the allowance of Professor Burman.", "#Person2#, a junior student, wants to enroll in Professor Turner's course for seniors very much. Professor Turner will ask for another professor's opinion.", "#Person2# wants to enroll in Professor Turner's course, and Professor Turner agrees to consider his application."], "prediction_text": "#Person1# is a professor who is asked by #Person2# if he will allow #Person2# to enroll in his Advanced Geology course, despite being a junior. #Person1# agrees to speak to Professor Burman about it."}
{"answers": ["#Person1# wants to change the broken pendant in #Person2#'s shop.", "#Person1# goes back to #Person2#'s shop to replace a broken pendant.", "#Person1# wants a product changed from #Person2#, and #Person2# agrees."], "prediction_text": "#Person1# bought a pendant from #Person2#'s shop before and now the pendant is broken. #Person2# agrees to replace it with a new one and asks #Person1# to come to the shop with the receipt by 10 am."}
{"answers": ["#Person1# is showing the new clothes to #Person2#, but #Person2# is busy watching the games. They quarrel and get angry.", "#Person1# and #Person2# have a serious quarrel over whether shopping for clothes or watching a sports game is more important.", "#Person1# asks #Person2#'s opinion on new clothes, but #Person2# only focuses on the game and feels annoyed. They are both angry and argue."], "prediction_text": "#Person1# and #Person2# are shopping for clothes. #Person1# shows #Person2# some clothes and asks for their opinion. #Person2# is not interested in the conversation and wants to watch the game. #Person1# insults the Sals-bury Seals and #Person2# tells #Person1# to leave."}
{"answers": ["#Person1# gives suggestions on job choices to #Person2#. #Person2# likes interactive media.", "#Person1# and #Person2# are looking at jobs. #Person2# is interested in the media industry.", "#Person1# offers some job choices to #Person2#, but #Person2# doesn't like them."], "prediction_text": "#Person1# and #Person2# are discussing jobs. #Person2# expresses interest in working in the media, but #Person1# suggests various jobs, including TV news director, writing for a magazine, and interactive media. #Person2# expresses reservations about directing the news and prefers working with computers."}
{"answers": ["#Person2# feels bored with the meeting. #Person1# gives #Person2# some suggestions on how to entertain but finally asks #Person2# to keep quiet.", "#Person1#, a reporter, takes #Person2# to a forum. #Person2# is bored and tries to find a pastime.", "#Person2# tells #Person1# that #Person2# is bored and explains the reasons, so #Person1# suggests #Person2# read some old newspapers and be quiet."], "prediction_text": "#Person1# is bored at the conference and suggests #Person2# find something to do, but #Person2# wants to listen to music. #Person1# gives #Person2# old newspapers to read and suggests they play crossword puzzles. #Person1# has to pay attention to the next speaker, the head of a charity organization."}
{"answers": ["Sarah is considering moving. #Person1# gives her advice on buying a house.", "Sarah wants to move. #Person1# suggests buying a cheaper house and offers information on possible options.", "Sarah tells #Person1# that she can't afford a new house. #Person1# says #Person1#'s sister-in-law bought a cheap house and suggests Sarah should buy a house far from the city center."], "prediction_text": "#Person1# suggests to #Person2# that they consider buying a house far away from the city center to save money, and shares an example of his sister-in-law's experience."}
{"answers": ["#Person1# introduces Mark Leach to this week's People You Meet. Mark Leach describes his job responsibilities and gives advice to people coming to Britain.", "#Person1# hosts the week's People You Meet, featuring Mark Leach. Mark introduces his work as an information officer in London.", "Mark Leach introduces his information service and his work to the audience. He also offers suggestions for people coming to Britain."], "prediction_text": "Mark Leach is an information officer at the Britain Business Center in London. He and #Person1# discuss how he and his team of 10 officers provide tourist information to 500,000 visitors each year, and how they plan trips for visitors. #Person2# asks for suggestions for people coming to Britain, and Mark suggests seeing as much of the country as possible and returning multiple times to experience different parts."}
{"answers": ["Lin Fang and Lucy are talking about their favourite subjects.", "Lin Fang and Lucy are talking about how they like different subjects.", "Lucy likes English and P.E. best, but Lin Fang's favorite is Chinese and Science."], "prediction_text": "Lucy and Lin Fang are talking about their favorite subjects in school. Lucy finds English difficult but likes it, while Lin Fang finds it easy. Nancy likes English and math but doesn't like science."}
{"answers": ["James comes to help Mrs. Thomas to do some housework on the weekend. James is saving up to buy a bike.", "James helped Mrs. Thomas with housework since she has broken her legs, and he wanted to save money for a bike.", "James helps Mrs. Thomas to do chores because James wanted to save money and Mrs. Thomas is incapable."], "prediction_text": "James finished cleaning up the yard and took out the garbage. Mrs. Thomas asks James to take her dog Oscar for a walk. James mentions he is saving up to buy a bike and will still work for Mrs. Thomas after he gets it, but reminds her he will be away visiting his grandfather the following weekend."}
{"answers": ["#Person1# and #Person2# are talking about the low temperature at night, although spring has come.", "#Person1# and #Person2# agree that it still felt very cold in spring.", "#Person1# and #Person2# talk about the weather and how to keep warm."], "prediction_text": "#Person1# and #Person2# are discussing the weather. #Person1# mentions that it is still cold at night despite spring being here, and #Person2# agrees. #Person1# decides to follow their cats' example and sit in the sun to warm up."}
{"answers": ["#Person1# and Mike are discussing what kind of emotion should be expressed by Mike in this play. They have different understandings.", "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "#Person1# wants Mike to act more angry, but Mike thinks he should act both angry and sad."], "prediction_text": "#Person1# and #Person2# are rehearsing a scene where #Person2#'s character is breaking up with #Person1#'s character. #Person1# wants #Person2# to act more angry and sad, but #Person2# is unsure."}
{"answers": ["#Person1# takes a taxi to the Friendship Hotel for something important.", "#Person2# drives #Person1# to the Friendship Hotel as fast as #Person2# can be.", "The taxi driver takes #Person1# to the Friendship Hotel quickly."], "prediction_text": "#Person1# gets into a taxi and tells the driver to go to the Friendship Hotel. The driver tries to go as fast as possible and arrives quickly. #Person1# pays the driver 20 yuan and thanks him."}
{"answers": ["#Person1# doesn't have any change for the bus because #Person1# bought some chips. #Person2# suggests #Person1# get a transfer from the bus driver.", "#Person1#'s broke, so #Person2# suggests #Person1# get a free bus transfer.", "#Person1#'s broke, so #Person2# suggests #Person1# get a transfer from the bus driver to go home."], "prediction_text": "#Person1# is broke and needs money for another bus. #Person2# suggests getting a transfer from the bus driver, but #Person1# doesn't know how much it costs. #Person2# tells #Person1# that it doesn't cost anything. #Person1# thanks #Person2# for the help."}
{"answers": ["#Person2# tells #Person1# information about their company and its surroundings.", "#Person1# asks #Person2# about the company's surroundings, location, and staff restaurant.", "#Person2# introduces the surroundings and information about their company to #Person1#."], "prediction_text": "#Person1# asks about the distance to downtown, the companies in the neighborhood, and if the 7th floor belongs to their company. #Person2# confirms the distance, the companies, and that their company has two floors in the building and a staff restaurant."}
{"answers": ["#Person1# and #Person2# are gossiping about Lulu and Vic's break-up.", "#Person1# and #Person2# are talking about Lulu and Vic's breakup.", "#Person1# and #Person2# talk about Lulu and Vic's breakup."], "prediction_text": "Lulu and Vic broke up, and Lulu has asked for a transfer. #Person1# tells #Person2# about it."}
{"answers": ["Dave Thomson calls Jim but Jim went out. Sally will tell Jim Dave called.", "Dave calls Jim but Jim is out. Sally tells Dave Jim's whereabouts.", "Dave Thomson calls to speak to Jim but Jim is out."], "prediction_text": "Dave calls Jim but Jim is not available. Dave asks Sally to tell Jim he has called and will try again later."}
{"answers": ["#Person2# shows #Person1# the way to City Hall patiently.", "#Person1# asks #Person2# the way to City Hall and #Person2# answers with patience.", "#Person2# patiently shows #Person1# the way to City Hall."], "prediction_text": "#Person1# asks #Person2# for directions to City Hall. #Person2# gives detailed instructions on how to get there. #Person1# confirms that they understand the directions."}
{"answers": ["#Person1# lost #Person1#'s passport. #Person2# suggests #Person1# fill in the lost property report and inform the embassy of the situation so they can issue a new passport in case it cannot be found.", "#Person1# asks #Person2# for help to find #Person1#'s passport. #Person2# checks the shoe department but finds nothing. Thus, #Person2# lets #Person1# fill in a report and contact the embassy.", "#Person1# lost the passport. #Person2# suggests #Person1# fill in the lost property report and contact the embassy."], "prediction_text": "#Person1# loses their passport and goes to the department store where they last used it to report it. #Person2# helps #Person1# fill out a lost property report and suggests that #Person1# contact the embassy for a new passport."}
{"answers": ["Leah informs Nathaniel that Ms. Collins will not be able to call him back until next week, so he needs to book an appointment with someone else from the International Student Office. Nathaniel decides to speak with Ms. Fonda.", "Leah informs Nathaniel that Ms. Collins won't be available for their appointment and explains the reason. Then Nathaniel decides to speak with someone else.", "The International Student Office calls Nathaniel to inform him that he has to book another appointment with someone else as Ms. Collins is not available. Nathaniel decides to speak with Ms. Fonda."], "prediction_text": "Leah calls Nathaniel to inform him that Ms. Collins won't be able to call him back until next week and offers him an appointment with Mr. Liu at 2:15 pm or Ms. Fonda at 11:30 am."}
{"answers": ["#Person1# and #Person2# feel scared when heard Sarah insists on marrying Dick.", "#Person1# and #Person2# are shocked about Sarah's love for Dick.", "#Person1# and #Person2# are surprised to know Sarah wants to marry Dick."], "prediction_text": "#Person1# and #Person2# are talking about Sarah who wants to marry Dick despite his reputation of playing the field with many girls."}
{"answers": ["#Person1# and #Person2# are at a nice party. #Person2# has a crush on Janice but is afraid of talking to her. #Person1# wishes #Person2# good luck.", "#Person1# and #Person2# talk about girls at a party and #Person2# tells #Person1# that #Person2# likes Janice but he feels nervous talking to girls. #Person1# encourages #Person2#.", "#Person1# and #Person2# are at an exciting party. #Person2# has a crush on Janice and #Person1# encourages him to have a try to talk to her."], "prediction_text": "#Person1# and #Person2# are at a party and #Person1# encourages #Person2# to talk to a girl named Janice, but #Person2# is nervous and hesitant."}
{"answers": ["Jack tells #Person1# that business communication is his favorite last year and #Person1# will check it.", "#Person1# and Jack talk about Jack's classes last year.", "#Person1# and Jack are talking about Jack's favorite classes."], "prediction_text": "Jack and #Person1# talk about their classes. Jack liked his poli-sci class, but doesn't consider it his favorite. Jack's favorite class was a business communication class he took last year."}
{"answers": ["#Person1# and #Person2# are talking about the weather in different seasons in Beijing. #Person2# reminds #Person1# to put on more clothes in the evening and not to catch a cold.", "#Person1# and #Person2# are talking about the weather in Beijing. #Person2# tells #Person1# it's cold in the evening around this time.", "#Person1# and #Person2# are talking about the heavy rain last night and the weather in Beijing."], "prediction_text": "#Person1# and #Person2# are talking about the weather. #Person1# mentions that it was not fine yesterday, and #Person2# explains that it rained last night and might rain again later in the day. #Person2# also talks about the trees they have been planting to reduce dust, and warns #Person1# about the cool evening temperatures."}
{"answers": ["#Person1# invites #Person2# to watch a movie together that night. #Person2# wants to see nothing but a war movie.", "#Person1# and #Person2# talk about what kind of movies to watch and they decided to watch a war movie.", "#Person1# invites #Person2# to movie and #Person2# wants to see a war movie."], "prediction_text": "#Person1# suggests going to the movies, but #Person2# has already seen a horror movie and doesn't want to see another scary movie. #Person1# suggests a comedy, but #Person2# doesn't think they are natural or cheerful. #Person1# suggests a love story, but #Person2# finds romance boring. #Person2# suggests a war movie, which #Person1# finds surprising but doesn't object to."}
{"answers": ["Adam is showing #Person1# around his school. #Person1# envies Adam and hoped #Person1# could enter this school one day.", "Adam shows #Person1# around his school and introduces the library, the new classroom building, and the swimming pool.", "Adam shows #Person1# around the school and introduces buildings and facilities in the school to #Person1#."], "prediction_text": "Adam shows #Person1# around the school and tells #Person1# about the tallest building (the library), the new classroom building, and the swimming pool. #Person1# expresses envy and hopes to enter the school one day."}
{"answers": ["#Person1# and #Person2# feel happy because #Person1# is pregnant.", "#Person1# and #Person2# are happy about #Person1#'s pregnancy.", "#Person1# is pregnant. She and #Person2# feel happy."], "prediction_text": "#Person1# tells #Person2# that they are going to be parents. #Person2# is happy."}
{"answers": ["#Person1# and #Person2# think John's fallen for a girl.", "#Person1# and #Person2# talk about John's dating life.", "#Person1# and #Person2# are talking about a couple."], "prediction_text": "#Person1# and #Person2# are talking about John and his girlfriend, with #Person1# suggesting that John might be falling in love with her."}
{"answers": ["#Person1# is giving #Person2# some advice for upgrading #Person2#'s system, such as adding a painting program and a faster processor.", "#Person1# tells #Person2# how to upgrade #Person2#'s system for better software and hardware.", "#Person1# teaches #Person2# how to upgrade software and hardware in #Person2#'s system."], "prediction_text": "#Person1# suggests upgrading #Person2#'s system by adding a painting program, hardware, and a CD-ROM drive. #Person2# is interested in the suggestions."}
{"answers": ["#Person1# is driving #Person2# to an inn. They talk about their careers, ages, and where they was born.", "#Person1# drives #Person2# to an inn and they have a talk. #Person2# is 26 and had a business trip to China. #Person1# is 40 years old American.", "#Person1# drives #Person2# from the airport to an inn and they have a casual talk about themselves."], "prediction_text": "#Person1# picks up #Person2# from the airport and they talk about their backgrounds. #Person1# is 40 years old and from the US, while #Person2# is 26 years old and from Mexico. #Person2# speaks some Spanish, which they both use in their conversation."}
{"answers": ["#Person1# wants to lose weight. #Person2# suggests #Person1# take an exercise class to exercise more.", "#Person2# offers #Person1# some suggestions to lose weight.", "#Person2# gives #Person1# some suggestions on how to lose weight."], "prediction_text": "#Person1# is concerned about their weight gain and #Person2# suggests dieting and exercise. #Person1# has tried diets before but they haven't worked and #Person2# suggests going to an exercise class."}
{"answers": ["James reserves a dining room for eight at a restaurant. #Person1# will ask the waitress to show him the way.", "#Person1# confirms Jame's reservation at the restaurant and asks a waitress to show him the way.", "James has reserved a dining room and #Person1#'ll asks the waitress to show him the way."], "prediction_text": "James wants to make a reservation for a table for eight, but the restaurant is fully booked. #Person1# offers to find a table in the dining hall instead."}
{"answers": ["#Person1# is visiting a large plant and #Person2# introduces its basic information.", "#Person2# introduces the area of the plant, its history, size, and function to #Person1#.", "#Person1# and #Person2# visit a plant. #Person2# introduces its occupy, history, and employee number."], "prediction_text": "#Person1# asks #Person2# about the plant and #Person2# answers the questions in detail. #Person1# expresses congratulations and #Person2# tells #Person1# about the number of employees and the process of the plant."}
{"answers": ["#Person1# is asking Rebecca about her working experience. She worked for York Herald as an intern and then as a junior local news reporter. After that, she began to work for London Weekly.", "Rebecca tells #Person1# about her working experiences. Rebecca first worked for the York Herald after her graduation, and then for the London Weekly.", "#Person1# interviews Rebecca and asks her about her previous working experiences."], "prediction_text": "Rebecca started her career at the York Herald as an intern, then became a junior local news reporter and stayed for two years. She left in 2006 to move to London with her husband and found a position with the London Weekly."}
{"answers": ["#Person1# and #Person2# will do a group report, so they plan to buy something to make posters. They make a shopping list before they go and find items according to signs on shelves.", "#Person1# and #Person2# make a shopping list and #Person1# helps #Person2# to find goods to make a poster for their group report tomorrow.", "#Person1# and #Person2# are preparing for the group report tomorrow and making a shopping list of things used to make the posters."], "prediction_text": "#Person1# needs to buy materials for posters for a group report, and #Person2# helps #Person1# make a shopping list and find the items at the stationery store."}
{"answers": ["Mary is tired because of finding jobs in the market. #Person1# suggests Mary apply for jobs on the Internet and says she could place her information on the Job Wanted channel on a recruitment web. Mary will try at once.", "#Person1# advises Mary to try applying for a job online and teaches her how to do it. Mary will try it.", "Mary goes to the personnel market every day and is tired. #Person1# suggests she go job hunting online. Mary will try it."], "prediction_text": "Mary is tired from going to the personnel market every day, so #Person1# suggests applying for a job on the internet. #Person1# explains how to apply for a job on the internet, including using a recruitment website and placing job hunting information on the Job Wanted channel. Mary is interested in trying it out."}
{"answers": ["#Person2# is making a shopping budget to save the rest of the money. #Person1# thinks it's a good idea.", "#Person2# tells #Person1# #Person2# plan to save money and #Person1# thinks it is good.", "#Person2# introduces the shopping budget which is used to save money to #Person1#. #Person1# thinks it's good."], "prediction_text": "#Person1# asks #Person2# what they are working on, and #Person2# replies that they are making a shopping budget to avoid spending too much money. #Person1# thinks it's a good idea, and #Person2# explains that they need to save the rest of the money to pay their bills."}
{"answers": ["Jane is going to the hospital to see Susan. Henry tells her to catch the number 13.", "Jane will go to the hospital to visit Susan. Henry tells her she can take the number 13.", "Jane is going to visit Suman in the hospital, Henry suggests she catch a number 13 to get there."], "prediction_text": "#Person1# tells #Person2# that #Person2# needs to take a number 13 bus to get to the hospital, but #Person1# warns #Person2# that the buses run frequently and #Person2# decides to walk two miles to catch one."}
{"answers": ["#Person1# wants to talk to #Person2# about the sales projections for next year. They decide to meet on Tuesday at 2:30 the next week.", "#Person1# and #Person2# negotiate on a time to talk about the sales projections for next year. They decide to meet next Tuesday.", "#Person1# and #Person2# are scheduling to talk about the sales projections for next year and they decide to meet next Tuesday."], "prediction_text": "#Person1# wants to talk to #Person2# about the sales projections for next year, but #Person2# is busy tomorrow morning. #Person1# suggests Friday afternoon, but #Person2# suggests Tuesday at 2:30. #Person1# agrees."}
{"answers": ["#Person1# is going to New York for the first time. #Person2# suggests #Person1# use a personal tour guide service to make #Person1#'s trip plan.", "#Person2# recommends a personal tour guide service to #Person1#.", "#Person2# introduces #Person1# a website for personal tour guide service."], "prediction_text": "#Person1# is going to New York for the first time and doesn't have a tour guide. #Person2# suggests using 'A friend in New York', a personal tour guide service that creates a perfect trip according to the user's budget. #Person1# is interested and asks where to find the questionnaire."}
{"answers": ["#Person1# is interviewing #Person2#. They discuss department #Person2# wants to work in, salary, and fringe benefits.", "#Person1# asks #Person2# how well #Person2# knows about the company, #Person2#'s ideal department, and current salary. #Person2# also asks #Person1# about the company's benefits and insurance.", "#Person1# interviews #Person2# about the understanding of this company, personal preference, and salary expectation. #Person2# asks about the fringe benefits."], "prediction_text": "#Person1# asks #Person2# about their knowledge of the company and #Person2# tells #Person1# about the company's history, size, and departments. #Person2# expresses interest in working in sales and #Person1# provides details about salary, benefits, and fringe benefits."}
{"answers": ["#Person1# comes to sign an agreement but it isn't ready until evening. So #Person1# is going through the draft.", "#Person1# is checking the draft of an agreement and #Person2# will finish the agreement this evening.", "#Person2# gives #Person1# an agreement draft and #Person1# thinks it good. #Person2# will get the agreement ready this evening."], "prediction_text": "#Person1# wants to sign the agreement but #Person2# tells him that it hasn't been fully prepared yet and will be ready by tomorrow. #Person1# agrees to go over the draft and #Person2# will type and duplicate the agreement for signatures."}
{"answers": ["#Person1# rent a car from ABC Rent-a-car Company this morning and met an accident. #Person2# will call an ambulance and police for #Person1#.", "#Person1# tells ABC Rent-a-car Company that #Person1# had a car accident. #Person2# will call an ambulance and the police.", "#Person1# calls ABC Rent-a-car Company to report #Person1#'s car accident. #Person2# will call an ambulance and the police."], "prediction_text": "#Person1# calls ABC Rent-a-car Company after a car accident on the way to Niagara Falls. #Person2# asks for details and offers to call an ambulance and the police."}
{"answers": ["#Person1# is lost on the way to the school clinic. #Person2# shows #Person1# the correct direction.", "#Person1# lost #Person1#'s way to the school clinic, #Person2# guides #Person1#.", "#Person1# is lost, #Person2# shows the direction to the school clinic."], "prediction_text": "#Person1# lost their way to the school clinic and #Person2# gives directions to get there."}
{"answers": ["#Person2# wants to change her room because the baggage elevator is too noisy. #Person1# is sorry and tells her #Person1# will manage it when there's a spare room.", "#Person2# wants to change a room because it's too noisy, but #Person1# tells #Person2# there are no rooms left so #Person2# has to wait till tomorrow.", "#Person2# cannot stand the noise near her room and #Person1# promises to change her room tomorrow as there is no room available today."], "prediction_text": "#Person1# is at the front desk of a hotel. #Person2# wants to change her room because it is too noisy. #Person1# apologizes and tells #Person2# that there are no spare rooms available today but suggests she wait until tomorrow when a tour company will be leaving and there will be some rooms available for her to choose from."}
{"answers": ["#Person1# is driving #Person2# to the Beijing hotel. #Person2# will attend a banquet at six o'clock in the International Hotel.", "#Person1# warmly welcomes #Person2# and drives #Person2# to the Beijing Hotel. Mr. Wu has arranged a banquet for #Person2# in the evening.", "#Person1# has arranged everything for #Person2# after #Person2# arrives in Beijing and invites #Person2# to a banquet."], "prediction_text": "#Person1# greets #Person2# and helps them with their luggage before taking them to the hotel. #Person1# tells #Person2# about the hotel's service and invites #Person2# to a banquet in the evening."}
{"answers": ["#Person1# gets lost on the way to Liu Ichang. #Person2# tells #Person1# to take Bus No. 713 because it's far.", "#Person2# helps #Person1# to find out the location of Liu Ichang and tells #Person1# to take Bus No. 173.", "#Person1# gets lost and #Person2# shows the way to Liu Ichang to #Person1#."], "prediction_text": "#Person1# is lost and asks #Person2# for help. #Person2# shows #Person1# where they are on the map and tells #Person1# how to get to Liu Ichang."}
{"answers": ["#Person2# finds that the computer has stopped running. #Person1# suggests #Person2# ring up the repairmen.", "#Person1# advises #Person2# to call the repairmen to fix #Person2#'s computer.", "#Person2#'s computer breaks down and #Person1# suggests ringing up the repairman."], "prediction_text": "#Person1# suggests that #Person2# ring the repairmen to fix the computer problem. #Person2# agrees and asks for help."}
{"answers": ["#Person2# wants to buy a gift for his mother's birthday. After knowing his mother's preference, #Person1# recommends a gold watch.", "#Person1# helps #Person2# pick a gift for #Person2#'s mother, and #Person2# decides to buy a watch.", "#Person2# is looking for a gift for his mother in a shop and #Person1# gives him some helpful suggestions."], "prediction_text": "#Person1# helps #Person2# find a gift for his mother's birthday. #Person2# wants a watch with big, clear numbers, and #Person1# suggests a gold watch that sells well and is liked by old ladies. #Person2# decides to buy it."}
{"answers": ["Ms. Ross and Mr. Fisher are having a coffee chat face to face. They talk about their career, child, life, and then get down to business. Ms. Ross will provide service for a presentation on Saturday 24 October in the morning held by Mr. Fisher. They are discussing the preparations as time, food, furniture, guest numbers. A problem is Mr. Fisher sent out 100 invitations but the room's capacity is only about 50.", "Ms. Ross helps Mr. Fisher make arrangements for a presentation. Mr. Fisher decides to serve a pre-lunch snack buffet and designs the room setting. Mr. Fisher isn't sure about how many people to come and Ms. Ross is worried that there might be too many people because the room is small.", "Ms. Ross and Mr. Fisher start the conversation with greetings. Then they discuss the preparation for a presentation. Ms. Ross confirms the details of the presentation, including schedule, food service. Ms. Ross is concerned that Mr. Fisher's sent too many invitations than the room capacity."], "prediction_text": "Mr. Fisher and Ms. Ross discuss the details of a presentation on October 24th. Ms. Ross confirms the time and date, and asks about the equipment and furniture needed. Mr. Fisher tells her that they have sent out 100 invitations, but expect only about 30 people to come."}
{"answers": ["#Person2# tells #Person1#, in Russia, things happen very fast except for bank business, while in Canada people are calmer.", "#Person1# and #Person2# are talking about the differences between Russia and Canada. #Person2# tells #Person1# that almost everything in Russia happens fast while it's the opposite in Canada.", "#Person1# and #Person2# are sharing their opinions towards how different is Russia from Canada in lifestyle, especially the speed in life."], "prediction_text": "#Person1# asks #Person2# about the differences between Russia and Canada, and #Person2# explains that in Russia, everything happens quickly, while in Canada, people are more relaxed and the government uses technology to improve businesses."}
{"answers": ["#Person2# thought to travel to the Caribbean Sea. #Person1# went there last year and thinks the weather can sometimes be really bad.", "#Person2# wanted to travel to the Caribbean Sea and but is worried about the storms.", "#Person2# hasn't decided to go to the Caribbean Sea on vacation because #Person2# is concerned about the storms there."], "prediction_text": "#Person1# and #Person2# are discussing their vacation plans, specifically their interest in going on a voyage to the Caribbean Sea. #Person1# shares their experience of visiting the area last year and mentions the potential for bad weather."}
{"answers": ["#Person1# and #Person1#'s mother are preparing the fruits they are going to take to the picnic.", "#Person1# and #Person2# prepare the fruits for the picnic.", "#Person1# and #Person2# are preparing fruits for the picnic."], "prediction_text": "#Person1# asks #Person2# if there are any fruits to take on a picnic, and #Person2# suggests taking bananas, but they are all gone. #Person1# agrees to take grapes instead."}
{"answers": ["#Person1# wants to rent an economy car from #Person2# for $19 a day or $129 a week.", "#Person1# rents a car from #Person2#", "#Person1# rents an economy car in #Person2#'s shop."], "prediction_text": "#Person1# wants to rent an economy car for tomorrow morning, #Person2# tells him the price and asks for his driver's license. #Person1# completes the form."}
{"answers": ["#Person1# hates those who keep smiling at #Person1# and #Person2# is fed up with those who keep a straight face in front of #Person2#.", "#Person2# is fed up with people with a straight face, and #Person1# hates people keeping smiling to #Person1#.", "#Person1# and #Person2# hold different opinions towards people with different facial expressions."], "prediction_text": "#Person1# and #Person2# have different opinions on smiling in different situations. #Person1# finds it uncomfortable when people smile in certain situations, while #Person2# finds it annoying when people don't smile at them."}
{"answers": ["#Person1# and #Person2# are talking about the good performance of their business for last year. #Person2# thinks the success is partly because of Wallace's contribution, and partly because of their new marketing strategy.", "#Person1# and #Person2# talk about the success of their business and attribute the success to Wallace's contribution and the new marketing strategy. They hope good luck can last.", "#Person1# and #Person2# are pleasant to see their business performance has improved last year. They think the credits should be given to Wallace and the new marketing strategy."], "prediction_text": "#Person1# and #Person2# discuss the company's sales figures and how they have improved under the new president, Wallace. They attribute the success to a new marketing strategy and the president's leadership."}
{"answers": ["#Person2# left #Person2#'s backpack and wallet in a taxi and failed to contact the driver because #Person2# didn't have the number. #Person1# lends 50 dollars to #Person1# and will drive #Person2# home.", "#Person2# borrows some money from #Person1# because #Person2#'s backpack and wallet were lost and cannot take it back. #Person1# will drive #Person2# home.", "#Person2# lost #Person2#'s backpack, wallet, and money in a taxi. #Person1# lends #Person2# some money and will take #Person2# home."], "prediction_text": "#Person1# and #Person2# are having a conversation. #Person2# lost their backpack and #Person1# lends #Person2# $50 to help them. #Person2# plans to take a bus home and ask their mom for money."}
{"answers": ["Steven and Lin just had a great meal. Then they talk about the different tipping cultures between America and China.", "Steven buys Lin a magnificent dinner in America and they then talk about the tipping culture in China and America.", "Steven treats Lin to a nice meal. Then they talk about the tipping cultures in their countries."], "prediction_text": "#Person1# and #Person2# are discussing the meal they had and #Person1# thanks #Person2# for the meal. #Person2# asks about tipping in China and #Person1# explains the customs."}
{"answers": ["Bill is happy because he made a move to know his roommate today.", "#Person1# and Bill talk about Bill's roommate Brain Locker.", "Bill tells #Person1# that he has made a move to read of his roommate."], "prediction_text": "#Person1# asks #Person2# how they are feeling today, and #Person2# reveals that they have moved into a new apartment. #Person1# does not recognize Brain Locker, but #Person2# describes him in detail."}
{"answers": ["#Person2# checks Tom Wilson's information and Tom pays his hotel and meal bill.", "Tom Wilson pays for his bill for hotel and food by credit card.", "Tom Wilson pays the bill for his room and meal cost by credit card."], "prediction_text": "Tom wants to pay his bill, and the hotel staff asks for his name and room number. Tom confirms he hasn't used any hotel services that morning, and the staff provides him with a total of $660 for his four-night stay and meals. Tom asks if he can pay by credit card and signs his name."}
{"answers": ["Susan calls Carol to ask about the party time. But Carol is taking a shower so #Person1# answers for her.", "Carol is taking a shower when Carol calls her, so #Person1# answers the telephone and tells her the party time.", "Susan calls to ask Carol about the party time. #Person1# answers the phone and tells her."], "prediction_text": "Susan calls and asks about the party, Carol tells #Person2# to tell Susan that the party is tomorrow night at 8:30 at Jills House and to bring a dessert if she wants."}
{"answers": ["#Person1# thinks that she knows #Person2# somewhere, but #Person2# denies it.", "#Person1# thinks she has met #Person2# somewhere, but #Person2# thinks it's a mistake.", "#Person1# keeps asking where #Person2#'s from because she thinks she knows #Person2# but #Person2# denies it."], "prediction_text": "#Person1# thinks she knows #Person2#, but #Person2# tells her she is mistaken. #Person1# is confused and frustrated."}
{"answers": ["#Person1# is crazy for Trump and voted for him. #Person2# doesn't agree with #Person1# on Trump and will vote for Biden.", "#Person1# is a crazy fan of Trump and wants him to be re-elected. #Person2# will vote for Biden.", "#Person1# is crazy for Trump and voted for him but #Person2# will vote for Biden."], "prediction_text": "#Person1# does not think Trump will make America great again, while #Person2# supports Trump and believes he will take good care of the country."}
{"answers": ["#Person1# doesn't know how to use the ATM. #Person2# teaches #Person1# step by step.", "#Person1# doesn't know how to use an ATM. #Person2# teaches #Person1#.", "#Person2# is teaching #Person1# how to use an ATM step by step because #Person1# doesn't know how to use."], "prediction_text": "#Person1# has never used an ATM before and #Person2# helps him figure it out."}
{"answers": ["Susan wants to find the memo. John has no idea where it is.", "Susan Miller is asking John if he knows where the memo is. John has no idea.", "Susan asks John where a memo is but John doesn't know."], "prediction_text": "Susan Miller calls John to ask where a memo about office procedures is, but John doesn't know and can't help."}
{"answers": ["#Person1# invites Lily to picnic this weekend. Lily accepts.", "#Person1# is inviting Lily to take part in their picnic this weekend, and she accepts.", "#Person1# invites Lily to take part in their weekend picnic and Lily accepts."], "prediction_text": "Lily is invited to a picnic by #Person1#. #Person1# tells #Person2# that they will go to the river, have a walk, and have supper there. #Person2# asks what to bring and #Person1# replies that they will bring everything."}
{"answers": ["#Person1# asks #Person2# about the table manners in China. #Person2# says there are many hazy rules that are different from Western. And #Person2# tells #Person1# stabbing chopsticks into a bowl resembles sacrifices for the death and is very inauspicious.", "#Person1# and #Person2# are discussing the differences between China and Western feasts. There are so many rules on the Chinese table, and they both feel hazy about its etiquette.", "#Person1# and #Person2# talk about the difference in table etiquette in China. They both feel hazy about Chinese table etiquette and wrong use of chopsticks can lead to people's enrage."], "prediction_text": "#Person1# asks #Person2# if they have ever been to a Chinese feast, and #Person2# replies that they have. #Person1# mentions that using chopsticks is different from their home country, and #Person2# agrees. #Person1# also mentions that there are many rules for table etiquette in China that foreigners might not know, such as not stabbing chopsticks into rice bowls."}
{"answers": ["Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies.", "Frank and Mary both like watching movies in their spare time. Mary usually rents movies at Movie Salon, and Frank is interested in signing up for its membership.", "Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies"], "prediction_text": "#Person1# and #Person2# are talking about their hobbies. #Person1# likes to watch movies, while #Person2# prefers art films. #Person1# asks #Person2# about movie rental stores and #Person2# recommends Movie Salon, a store in their neighborhood with a membership service."}
{"answers": ["#Person2# wanted to join a small political party and thinks the smaller group can influence the larger one. #Person1# and #Person2# agree that most people don't fully understand politics.", "#Person2# believes smaller political and pressure groups can influence large parties. #Person1# and #Person2# both agree that most people often don't understand politics fully.", "#Person2# thought about joining a small party and thinks smaller political and pressure groups can influence larger ones. #Person1# and #Person2# agree most people don't understand political issues fully."], "prediction_text": "#Person1# asks #Person2# if they have ever belonged to a political party, and #Person2# replies that they have thought about joining the Green Party. #Person1# mentions that the Green Party has no chance of winning an election, but smaller political and pressure groups can influence large political parties."}
{"answers": ["#Person1# apologizes for mistakes in goods. #Person1# will be responsible for Mr. Wilson's loss, and take measures to avoid such mistakes.", "#Person1# feels sorry for Mr. Wilson's loss caused by #Person1# and assures that such mistakes will not happen again.", "#Person1# apologizes for the loss caused by them to Mr. Wilson and assures that it will never happen again."], "prediction_text": "#Person1# apologizes for the mistakes in goods and promises to improve the package of their exported goods. #Person2# is satisfied with the apology."}
{"answers": ["#Person1# asks #Person2# who saw the robbery some questions. #Person2# agrees to come to the station for more questioning.", "#Person1# asks #Person2# who saw a robbery some questions and #Person2# is willing to go to the station for more questioning.", "#Person2# tells #Person1# #Person2# witnessed the robbery and agrees to take more questions in the station."], "prediction_text": "#Person1# asks #Person2# about a robbery and #Person2# tells #Person1# what they saw, including the robber wearing a mask and coming alone. #Person1# asks #Person2# to come to the station for more questioning."}
{"answers": ["#Person1# and #Person2#'s parents are out on a date and will go out for dinner regularly.", "The parents of #Person1# and #Person2# are out on a date.", "#Person1# and #Person2#'s parent are out on a date to revive their old tradition."], "prediction_text": "#Person1# is hungry and asks #Person2# if there is dinner, #Person2# tells #Person1# that their parents are going out on a date and will be doing it once a week, #Person1# suggests ordering pizza."}
{"answers": ["#Person1# has decided to go on a diet in the new year. Carol reasonably doubts it.", "#Person1#'s decided to go on a diet for New Year's resolution. Carol doesn't believe #Person1# will stick to it.", "#Person1# has decided to go on a diet in the new year. Carol doesn't believe #Person1# will stick to it"], "prediction_text": "#Person1# has decided to go on a diet and cut out junk food. #Person2# is skeptical but supports #Person1#'s decision."}
{"answers": ["Karen Huang tried to register for Comp Lit 287 but failed because it's full. So Karen comes to the department office. #Person1# finds Karen is a Comp Lit major and the university has saved extra places for them. #Person1# gives Karen a special code and tells Karan how to use it to get into the class.", "Karen Huang couldn't register for Comp Lit 287 so Karen comes to talk to #Person1# for a solution. #Person1# says it is full and will put Karen on the waiting list. But after #Person1# knows Karen majors in comparative literature, #Person1# gives her a special code to register for the class, as they've saved extra places for them.", "Karen Huang wants to register for a class. #Person1# says it's full and will put Karen on the waiting list. But then #Person1# gives Karen a special code to register the class after #Person1# knows Karen is a Comparative Literature major."], "prediction_text": "Karen Huang wants to register for Comp Lit 287 but the class is already full. Mr. #Person1# tells Karen that he can put her on a waiting list but doesn't guarantee she will get into the class. Mr. #Person1# gives Karen a special code to use when registering by phone."}
{"answers": ["#Person2# voluntarily shares an umbrella with #Person1# who doesn't bring the umbrella when it's rainy.", "#Person1# forgot to take an umbrella while it's raining. #Person2# shares an umbrella with #Person1#.", "#Person2# shares an umbrella with #Person1# when it's rainy."], "prediction_text": "#Person1# and #Person2# are walking together and #Person1# forgot her umbrella. #Person2# offers to share his umbrella and they walk together to the Garden Hotel."}
{"answers": ["Jack gives Daisy a ride in his new car. Daisy praises it.", "Jack bought a new car and invites Daisy to hop in and take a ride. Daisy thinks it's a good feeling.", "Jack takes Daisy for a ride to experience his new car. Daisy thinks he makes a perfect choice."], "prediction_text": "Jack got a new car and Daisy is impressed with its speed and handling. Jack tells Daisy to find the shade and they are on the highway."}
{"answers": ["#Person1# asks #Person2# the exact time when the fire broke out. #Person2# says it was about 10 o'clock.", "#Person1# asks the exact time when the big fire broke out. #Person2# remembers that it was about 10 o'clock.", "#Person1# asks #Person2# the exact time when the fire broke out. #Person2# says it was about 10 o'clock."], "prediction_text": "#Person1# asks #Person2# for the exact time when the big fire broke out, but #Person2# can only remember that it was around 10 o'clock at night."}
{"answers": ["#Person2# doesn't have enough cash to pay 905 yuan and asks to cut the service charge. #Person1# refuses. So #Person2# pays by her husband's credit card because it can be accepted.", "#Person1# requires 905 yuan in total, but #Person2# only has 900 in cash and asks for a release. #Person1# refuses, so #Person2# pays by her husband's credit card because it can be accepted.", "#Person2# is asked to pay 905 yuan but she just has 900 in cash and asks for a release from #Person1#. #Person1# refuses. So #Person2# pays by her husband's credit card because it can be accepted."], "prediction_text": "#Person1# and #Person2# are at a store. #Person2# wants to pay for something but only has $900 in cash. #Person1# tells her it's a store rule to pay the full amount of $905. #Person2# offers to use a credit card instead."}
{"answers": ["#Person1# describes the contents of the regular car wash package. #Person2# will take that.", "#Person1# introduces the content of regular car wash package and #Person2# accepts.", "#Person1# introduces the services included in regular car wash package and #Person2# will take it."], "prediction_text": "#Person1# offers a regular car wash package to #Person2#, explaining the services included. #Person2# agrees to the package."}
{"answers": ["Harry and his wife may go abroad during the holiday, but Harry's wife worries too much, so they may not go anywhere.", "Harry and his wife may go abroad during the holidays. However, they might not go anywhere because his wife always worries too much.", "Harry is not sure about the holiday plan of going abroad to spend the holiday, because his wife worries about things after they leave."], "prediction_text": "Harry and #Person1# are discussing their holiday plans. Harry and his wife are undecided about where to go and how to travel, and Harry's wife worries about who will take care of their dog, house, and garden while they are away."}
{"answers": ["#Person1# is a new member who wants to use the machines. Johnson teaches #Person1# to get a card to keep track of the progress and record the suitable weight for each machine. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "Johnson is teaching #Person1# how to use the training machines. He introduces a training card that allows #Person1# to keep track of the weight used on each machine. #Person1# is exhausted when there are still 7 machines left. Johnson suggests #Person1# stop to train and go through the rest of the machines next time.", "Johnson shows #Person1# the correct way to use weights machine and tells #Person1# using a card to keep track of exercise intensity. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time."], "prediction_text": "#Person1# asks Johnson how to use the machines and Johnson explains the training card and how to use it. Johnson goes through the machines with #Person1# and determines a good starting weight. #Person1# is exhausted and decides to stop for now."}
{"answers": ["#Person1# and #Person2# are both unemployed. #Person2# suggests applying for the electrician program and #Person1# agrees.", "Both #Person1# and #Person2# lost their jobs. They would like to apply for the electrician program.", "#Person1# and #Person2# plan to apply for the electrician program after they were laid off."], "prediction_text": "#Person1# lost their job and is looking for a new opportunity. #Person2# suggests the electrician program and offers to help #Person1# apply."}
{"answers": ["#Person1# asks something about #Person2#'s care with puppies and reminds #Person2# of the vet appointment.", "#Person1# and #Person2# are talking about taking care of their puppies and the time of vet appointment.", "#Person1# inquires about #Person2#'s care with the puppies and reminds #Person2# of the vet appointment."], "prediction_text": "#Person1# reminds #Person2# to give the puppies a bath and to take them to the vet appointment on Saturday."}
{"answers": ["#Person1# wants to borrow $200 from Ayden to pay the rent. Ayden is short on cash but still lends $20 to #Person1#. They decide to have dinner at #Person1#'s place.", "#Person1# borrows $ 20 from Ayden because #Person1# owes the landlady $ 200. Then #Person1# invites Ayden to have dinner at #Person1#'s place.", "#Person1# wants to borrow $200 from Ayden to pay for the house rent but Ayden lends $20 because of the lack of money. They decide to have dinner at #Person1#'s place."], "prediction_text": "#Person1# needs money to pay their landlady by Friday but doesn't get paid until Monday. #Person2# can only lend $20 but promises to pay it back on Monday. #Person1# invites #Person2# over for dinner that night."}
{"answers": ["#Person2# had been in the charity organization for 2 months. Social activities had a direct impact on #Person2#'s way of thinking.", "#Person2# had been in the charity organization for 2 months and the social activities influenced #Person2#'s way of thinking.", "#Person2# had been in the charity organization for 2 months. Social activities influenced #Person2#'s way of thinking."], "prediction_text": "#Person1# asks #Person2# if they worked in a charity organization and #Person2# explains how it impacted their perspective on life."}
{"answers": ["#Person1#'s organization will make a decision in about one week and contact #Person2# via phone or e-mail.", "#Person1# can inform #Person2# by phone or e-mail when the organization makes a decision in about one week.", "#Person1# can tell #Person2# their organization's decision by phone or e-mail in about one week."], "prediction_text": "#Person1# and #Person2# discuss how to contact #Person1#'s organization and when a decision will be made."}
{"answers": ["#Person2# recommends Moutan and Roast Beijing Duck. #Person1# will take that in the hotel.", "#Person2# recommends #Person1# to have Roast Beijing Duck along with Moutan in the hotel. #Person1# will have a try.", "#Person2# recommends Moutan and Roast Beijing Duck. #Person1# will have a try in the hotel."], "prediction_text": "#Person1# wants to try some Chinese wine, and #Person2# recommends Moutan and orders some typical Chinese dishes, including Roast Beijing Duck."}
{"answers": ["Angela calls Dan to make sure he's coming to the wedding with him next week and also asks him to invite Megan. They'll drive to the wedding together.", "Angela calls to remind Dan that they are going to the wedding of her friend next week and asks him to invite Megan. The three of them will go together.", "Angela reminds Dan to come to the wedding next week and asks him to invite Megan as well. They agree to go there together."], "prediction_text": "Dan calls Angela to remind her about his friend's wedding next week and Angela tells Dan that she already bought a present and will tell Megan about the wedding when she sees her next."}
{"answers": ["#Person2# has traditional Greek yogurt, which #Person1# thinks rather plain. #Person1# has an Italian tiramisu, which #Person2# thinks delicious. #Person1# goes and gets both of them a fried banana.", "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. They both decide to have fried bananas and #Person1# will bring bananas back.", "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. Then they want to have fried bananas and #Person1# goes out to buy some."], "prediction_text": "#Person1# and #Person2# are at a restaurant. #Person1# wants to try traditional Greek yogurt and #Person2# wants to try it. #Person1# tries Italian tiramisu and #Person2# tries it and likes it. #Person1# offers #Person2# some of their tiramisu and #Person2# finishes it. #Person1# goes to get two fried bananas for #Person2#."}
{"answers": ["Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week.", "Mr. Smith tells #Person1# that he graduated from Yale University with a B. A. in Economics, and he has worked in a bank for the last five years with $500 a week.", "Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week."], "prediction_text": "#Person1# asks Mr. Smith about his education, age, and work experience. Mr. Smith replies that he went to Yale University, studied Economics, is 27 years old, and has worked in a bank for the last five years, earning $ 500 a week."}
{"answers": ["#Person2# recommends Digital Barbie for #Person1#'s niece. #Person1# pays $32.42 for it by cash.", "#Person2# recommends #Person1# to buy a $32. 42 Digital Barbie for #Person1#'s niece. #Person1# pays it by cash.", "#Person1# paid $32.42 by cash to buy a Digital Barbie as a niece's gift after listening to #Person2#'s suggestion."], "prediction_text": "#Person1# wants to buy a gift for their niece and decides on a Barbie doll. #Person2# suggests it and provides details, and #Person1# purchases it for $29.95."}
{"answers": ["#Person2# wants a pair of Jordan shoes in size 41.", "#Person2# wants to buy a pair of Jordan shoes in size 41.", "#Person2# wants to buy a pair of Jordan shoes in size 41."], "prediction_text": "#Person1# helps #Person2# find a pair of sport shoes, Jordan, in size 41. #Person2# asks if it can be made cheaper, but #Person1# says it's already on sale."}
{"answers": ["#Person1# wants to go to the science museum but loses the way. #Person2# helps #Person1# buy the ticket and gives #Person1# directions.", "#Person1# is lost on the way to the science museum. #Person2# helps #Person1# to buy the tickets for the train and gives #Person1# directions.", "#Person2# helps #Person1# operate the ticket machine to buy a train ticket and tells #Person1# who loses the way how to go to the science museum."], "prediction_text": "#Person1# is lost and needs help finding the science museum. #Person2# helps #Person1# find the museum and gives directions on how to get there."}
{"answers": ["#Person1# asks Simon about his retirement and finds out Simon is on a trial scheme called phased retirement. He can work with his former company but manage his own time. #Person1# thinks it's good for Simon.", "Simon tells #Person1# that he is on a scheme called phased retirement during which he had a six-month break from work, and after that he could apply for projects of the company he used to work for but manage his own time. #Person1# thinks it's good for Simon.", "Simon tells #Person1# that he joined in a phased retirement scheme which offers retired people work positions from the former company, and he can manage his own time. #Person1# thinks it's good for Simon."], "prediction_text": "Simon is retired and spends his time with his family and traveling. He is on a phased retirement scheme where he can work on projects with his old company. The company benefits from flexibility and Simon still has his old contacts to get things done."}
{"answers": ["Rocky doesn't want to dance because no one at the party fits his type, but #Person1# likes different kinds of women from him. Finally, Rocky decides to go home to a TV dinner and his dog, Rusty", "Rocky wants a woman who's affectionate and fulfills his every need. #Person1# likes different kinds of women from him. Rocky doesn't mind and plans to go home and spend the night with a TV dinner and his dog, Rusty.", "Rocky has particular requirements and cannot find a suitable woman in the party, so he doesn't want to dance. #Person1# likes different kinds of women from him. He sticks with his views and decides to go home to a TV dinner and his dog, Rusty."], "prediction_text": "#Person1# and #Person2# are at a party. #Person1# tells #Person2# that he wants a woman who is outgoing, caring, and non-judgmental, while #Person2# wants a woman who will stay home, cook, clean, and watch the kids. #Person1# thinks #Person2# is not going to get married because of his views."}
{"answers": ["#Person1# and #Person2# is talking about the heavy storm last night. #Person2# thinks the weather is terrible. #Person1# is positive towards that.", "#Person1# and #Person2# are talking about the heavy storm last night, and #Person1#'s positive. #Person2# thinks the weather is terrible. #Person1# thinks they should not complain.", "#Person2# complains the terrible storm last night and wishes for nice weather. #Person1# thinks they should not complain."], "prediction_text": "#Person1# and #Person2# are talking about the heavy storm the night before. #Person1# mentions that a big branch from a tree in front of their house came down, but it didn't damage their home. #Person2# expresses their dislike for storms and wishes for nicer spring weather."}
{"answers": ["#Person1# wants to watch TV together with #Person2#, but #Person2# finds it boring. #Person2# suggests doing music together.", "#Person1# asks #Person2# to watch TV with #Person1# at home. However, #Person2# finds it boring and suggests doing music together.", "#Person1# wants #Person2# to watch TV with #Person1#. #Person2# thinks it is boring and suggests doing their music together."], "prediction_text": "#Person1# doesn't understand why #Person2# doesn't want to watch TV, and #Person2# explains that they find it boring and would rather do other things. #Person1# suggests sharing music instead."}
{"answers": ["Ben feels a bit nervous about his new school life. #Person1# cheers him up and tells him about the schedule of the new school.", "Ben is nervous about his new school life. #Person1# is giving him some brief information about the school.", "Ben is nervous about the upcoming new school life. #Person1# comforts him and tells him the school daily routine."], "prediction_text": "Ben is nervous about starting school the next day and asks about the schedule. #Person1# tells him the schedule, including the time for each class, the break, and the lunchtime."}
{"answers": ["Adam's knee is getting better but #Person1# still suggests that he should not do any running today. Adam decides to watch the films of Michigan after practice and will be back tomorrow for full practice.", "Adam's knee is getting better but #Person1# suggests that he should not do any running today. Adam decides to see films of Michigan after practice and says he'll be back tomorrow for full practice.", "Adam's knee is getting better but #Person1# suggests that he should not do any running today. #Person1# says he can watch some films of Michigan after practice. Adam decides to go back tomorrow for full practice."], "prediction_text": "Adam's knee is feeling better after seeing a doctor, but Mr. #Person1# suggests he take it easy today and only practice throwing. Mr. #Person1# has game films of Michigan that he wants Adam to see after practice."}
{"answers": ["#Person1# wants #Person2# to help to print up a copy. #Person2# accepts and edits it for #Person1#.", "#Person1# asks for #Person2#'s help to print up a copy. #Person2# accepts and edits it for #Person1#.", "#Person1# asks for #Person2#'s help to print up a copy. #Person2# accepts and edits it for #Person1#."], "prediction_text": "#Person1# asks #Person2# for help with printing a document, and #Person2# agrees to do it."}
{"answers": ["#Person2# lets #Person1# hold the ladder and #Person2# will put the curtain up.", "#Person2# suggests that #Person1# hold the ladder and #Person2# will put the curtain up.", "#Person2# lets #Person1# hold the ladder and #Person2# will put the curtain up."], "prediction_text": "#Person1# needs help putting up a curtain and asks #Person2# for help. #Person2# agrees to hold the ladder while #Person1# puts it up."}
{"answers": ["Jack is available for a camping trip the weekend after next.", "Jack will be available for a weekend camping trip the weekend after next.", "The weekend after next is suitable for Jack to have a camping trip."], "prediction_text": "Jack and #Person1# are planning a weekend camping trip and want to know which weekend is best for everyone. Jack checks his calendar and finds the weekend after next is available."}
{"answers": ["#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells #Person2# she is pregnant. #Person2# feels happy. They go to the doctor. #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells him that she's pregnant. They go to see the doctor. However, #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "#Person1# apologises to #Person2# after the quarrel and tells #Person2# she's pregnant. #Person2# feels so happy and they decide to see the doctor. After the pregnancy test about the date, #Person2# finds that it is not his baby, because he was away the first week of February at a training seminar."], "prediction_text": "#Person1# and #Person2# are happy to see each other after being apart for a while. #Person1# shares the news that she might be pregnant, and #Person2# is excited to be a father. However, #Person2# questions the accuracy of the test results and reveals that he was away during the week the baby was conceived."}
{"answers": ["#Person2# is worried about #Person2#'s daughter. #Person1# suggests #Person2# should encourage #Person2#'s daughter to make her own decision.", "#Person1# advises #Person2# to let #Person2#'s daughter make her own decisions instead of worrying about her.", "#Person1# suggests #Person2# letting the child make own decision instead of worrying about her."], "prediction_text": "#Person1# tells #Person2# not to worry about their daughter's college decision, as it is a normal part of being a teenager, and encourages #Person2# to let their daughter make her own decision."}
{"answers": ["#Person1# may lose #Person1#'s job because of a mistake that may cause a huge loss and a dishonest supervisor. #Person2# suggests #Person1# live with #Person2# and stop buying all the junk to save money if #Person1# loses the job. #Person1# agrees.", "#Person1# makes a big mistake which may cause a huge loss and #Person1# may lose this job because of the loss and #Person1#'s dishonest supervisor. #Person2# suggests that #Person1# move in with #Person2# until #Person1# find another job and stop buying all the junk, if #Person1# loses the job.", "#Person1# might lose the job because of a working mistake and is worried about financial problems after losing a job. #Person2# suggests #Person1# save money until #Person1# find another job, if #Person1# loses the job."], "prediction_text": "#Person1# is worried about losing their job and how it will affect their finances. #Person2# tries to reassure #Person1# that they will be okay and offers to help them out. #Person1# is grateful for the offer but is also worried about losing their job and how it will change their lifestyle."}
{"answers": ["#Person2#'s friend is visiting #Person2#. #Person2# invites him to give a speech about multimodal text generation. #Person1# will attend it.", "#Person2#'s friend is a Ph.D. student at the University of Edinburgh. He is visiting #Person2# and will give a speech about multimodal text generation. #Person1# will attend it.", "#Person2#'s friend is visiting #Person2# and will give a speech about multimodal text generation. #Person1# thinks it interesting and will attend it."], "prediction_text": "#Person1# is happy and wants to know why. #Person2# tells #Person1# that a friend from the United Kingdom has arrived and will give a speech at #Person2#'s lab about multimodal text generation. #Person1# expresses interest in attending the speech."}
{"answers": ["#Person1# suggests going to John's house, but #Person2# feels sick and decides to go to sleep.", "#Person2# prefers to stay at home and rest rather than go over to John's house tonight because #Person2# gets sick.", "#Person2# doesn't want to go to John's house tonight because of getting sick. #Person2# decides to go to sleep."], "prediction_text": "#Person1# asks #Person2# if they want to go to John's house, but #Person2# declines due to feeling sick and wants to rest at home."}
{"answers": ["Mr. Faber calls #Person1# to book a double room for 3 nights at York Hotel.", "Mr. Faber books a double room for 3 nights from July 20th at York Hotel.", "Mr. Faber books a double room for three nights from July 20th at York Hotel."], "prediction_text": "#Person1# is working at the York Hotel and #Person2# is booking a room for three nights starting on July 20th. #Person1# asks for the name and #Person2# gives it to #Person1#."}
{"answers": ["#Person1# wants a cheap single room. #Person2# recommends calling John Godfrey and see him on Saturday.", "#Person1# is looking for a cheap single room. #Person2# recommends #Person1# to contact John Godfrey on Saturday.", "#Person1# is looking for a cheap single room. #Person2# gives #Person1# John Godfrey's phone number that could help to find a room and suggests that they meet on Saturday."], "prediction_text": "#Person1# wants to rent a flat in the area, but #Person2# tells him that there are mostly larger houses in the area and suggests West Derby, which has good schools. #Person1# is only interested in flats and #Person2# gives him John Godfrey's telephone number."}
{"answers": ["#Person1# and #Person2# blame each other for the series of bad experiences during the journey.", "#Person1# and #Person2# are blaming each other for lots of bad experience during the journey.", "#Person1# and #Person2# quarrel and blame each other because of bad experiences during the journey."], "prediction_text": "#Person1# and #Person2# are arguing about who is responsible for their current situation. #Person1# blames #Person2# for speeding and giving money to a policeman, while #Person2# blames #Person1# for suggesting a quicker route and not booking tickets."}
{"answers": ["Darlene calls to check the delayed order. Dan tells her the situation. Darlene will talk to Steve.", "Darlene calls Dan to check on the delay of the order. Dan explains to her the reason for the delay. Darlene decides to talk to Steven.", "Darlen calls Dan to check the delayed order of computers. Dan explains to her the reason for the delay. Darlene decides to talk to Steven."], "prediction_text": "Darlene calls Dan to check on an order of computers that was supposed to be delivered on the tenth of September but has been delayed by two days. Dan tells Darlene that the factory is short-handed and the order will be delivered by the eighteenth. Darlene asks for Steve's phone number to call him about the delay."}
{"answers": ["#Person2# tells #Person1# the founders and founding times of the Washington Post, the New York Times, and the Los Angeles Times.", "#Person1# and #Person2# are talking about the founding time and founder of the New York Times, the Washington Post, and the Los Angeles Times were founded.", "#Person2# tells #Person1# the founder and the founding time of the New York Times, the Washington Post, and the Los Angeles Times."], "prediction_text": "#Person1# asks #Person2# about the Washington Post and #Person2# tells him the newspaper was founded in 1877. #Person1# then asks which newspaper is older, the New York Times or the Washington Post, and #Person2# tells him the New York Times is older."}
{"answers": ["#Person1# finds that #Person2# e-mail exceeds capacity and suggests #Person2# compress the email.", "#Person2#'s attachment exceeds the e-mail capacity, #Person1# suggests compressing it.", "#Person2# can't send out an email. #Person1# suggests #Person2#'s attachment be compressed."], "prediction_text": "#Person1# helps #Person2# fix the problem with the email attachment. #Person1# suggests compressing the attachment to send it."}
{"answers": ["#Person2# tells #Person1# #Person2# enjoyed the weekend organized by #Person1#.", "#Person1# and #Person2# has enjoyed a nice weekend together.", "#Person1# invited #Person2# to spend the weekend and #Person2# has enjoyed it."], "prediction_text": "#Person1# and #Person2# have enjoyed their weekend together. #Person1# thanks #Person2# for coming and invites them to come back next time they are in Changsha."}
{"answers": ["#Person2# offers #Person1# suggestions about what to eat. #Person1# will go to Panda Express.", "#Person1# wants to eat something and #Person2# gives some advice. #Person1# finally goes to eat Chinese food.", "#Person1# is hungry. #Person2# says the Chinese food from Panda Express is good."], "prediction_text": "#Person1# wants to get something to eat, #Person2# suggests Chinese food and tells #Person1# where to go, #Person1# agrees to go to Panda Express."}
{"answers": ["Mary from Hans furniture tells Tom they decide not to employ him but Tom asks the store to reconsider him.", "Marry calls Tom to inform him that they decide to offer the sales position to someone else. Tom wants them to reconsider.", "Tom wants Marry to reconsider the decision of offering the sales position to someone else instead of him."], "prediction_text": "Marry calls Tom to inform him that they have decided to offer the sales position to another candidate. Tom expresses disappointment but Marry explains that they considered him carefully and decided to go with another candidate."}
{"answers": ["#Person1# is angry about the crank calls.", "#Person1# gets a crank call and is angry about it.", "#Person1# receives a phone call but no one speaks."], "prediction_text": "#Person1# receives a call from an unknown number, but the caller does not identify themselves. #Person1# threatens to call the police if the caller calls again."}
{"answers": ["#Person2# is being interviewed for a music teacher. #Person2# says #Person2# has degrees in music and specializes in classic music. Then #Person2# shares some research about classic music that can make people relaxed. #Person1# is very satisfied with #Person2#. #Person2# gives some suggestions on how to start listening to classical music.", "#Person1# interviews #Person2# for a music teacher position. #Person1# is very satisfied with #Person2#'s educational background in music and #Person2#'s understanding of classical music. After the interview, #Person2# suggests #Person1# can develop interests in classic music by listening to different classic music online.", "#Person2# is being interviewed for a music teacher. #Person1# is impressed by #Person2#'s background and knowledge of classical music. And #Person1# will try classical music."], "prediction_text": "#Person1# and #Person2# discuss why #Person2# wants to be a music teacher. #Person1# asks about #Person2#'s background and interests, and #Person2# talks about her love of music and her degree in music and art. #Person1# suggests that #Person2# listen to classical music to reduce stress, and #Person2# thanks #Person1# for the suggestion."}
{"answers": ["#Person2# likes his neibourhood girl who is popular. Although #Person1# analyses the disadvantages, #Person2# still decides to date with her.", "#Person2# tells #Person1# that he falls in love with his neighbour and #Person1# thinks he has some competition and wishes him good luck.", "#Person2# tells #Person1# he falls in love with the girl living under him and she's very popular. #Person2#'s going to call her now and invite her to dinner tonight."], "prediction_text": "#Person1# and #Person2# are having a conversation about the weather and #Person2# tells #Person1# that he met a girl living under him and she is nice and single. #Person1# expresses concern about the convenience of being in a relationship with a neighbor and #Person2# tells #Person1# that he is thinking of inviting her to dinner."}
{"answers": ["#Person1# and #Person2# talk about Mirella's dressing style in the office which is different from others. The management decided to give Mirella formal clothes directly to warn her.", "#Person1# thinks it's strange of Mirella wearing casually in the office. #Person2# thinks she was influenced by the casual atmosphere in California and tells #Person1# management decided to give her a new white dress to warn her.", "#Person1# and #Person2# talks about Mirella dressed casually after she came back from Silicon Valley. Managements put white shirts on her desk to warn her."], "prediction_text": "#Person1# and #Person2# are talking about Mirella's new dress code at work. #Person1# finds it strange, but #Person2# explains that it's not as bad as it seems and that management gave Mirella four new white dresses to dress more professionally."}
{"answers": ["#Person1# congratulates #Person2# on #Person2#'s new venture and expresses #Person1#'s willingness to help.", "#Person1# is willing to offer #Person2# help in setting up a law office.", "#Person2# is going to set up #Person2#'s own law office. #Person1# wishes #Person2# good luck."], "prediction_text": "#Person1# wishes #Person2# success in their new venture."}
{"answers": ["Peter tells Kate he played games and listened to music the whole last night.", "Peter tells Kate he stayed up and he's tired now.", "Peter tells Kate he stayed up. He's tired now and needs a rest."], "prediction_text": "Peter and Kate are talking. Peter tells Kate that he was almost online the whole night and Kate asks him what he is going to do now. Peter replies that he is tired and needs a rest."}
{"answers": ["#Person2# explains alternatives of sea transportation but #Person1# addresses the importance of boat in transoceanic cargo transportation.", "#Person1# and #Person2# are talking about the development of transportation and the importance of boats.", "#Person2# tells #Person1# that ships and boats have been giving places to other transportations because people's life is getting faster, but #Person1# thinks they are still important."], "prediction_text": "#Person1# and #Person2# discuss how ships and boats are no longer as important in transportation as they used to be due to the extension of railways, highways, and the improvement of airplanes. #Person1# mentions that passenger liners from Shanghai to Ningpo and Dalian have been out of business and liners up and down the Yangtze River have been reduced by 70%. #Person2# thinks that the pace of life of people is getting faster and faster, which is why people don't like to travel by boat. #Person1# points out that most of transoceanic cargo transportation is still carried by boat."}
{"answers": ["Fanny had a nightmare because of the pressure from school applications. Her mother and Andy both comfort her.", "Fanny had a nightmare last night and her mother consoled her. Andy listens to her experience and thinks she has a good mother.", "Fanny had a bad dream last night. She is worried about getting into the University of Michigan. Andy comforts her."], "prediction_text": "#Person1# asks #Person2# how they are feeling, and #Person2# tells #Person1# about a bad dream they had. #Person1# tries to comfort #Person2# and tells them that they have a great mother. #Person2# expresses their worry about getting into the University of Michigan."}
{"answers": ["#Person1# and Ernie plan to start a band and they decide to play hip hop music.", "#Person1# and Ernie are preparing to start the band at school.", "#Person1# and Ernie start their own band on campus. #Person1# suggests they play Vanilla Ice songs."], "prediction_text": "#Person1# and #Person2# are starting a band together and are excited about it. #Person1# suggests playing Vanilla Ice songs, but #Person2# is not familiar with his other songs."}
{"answers": ["#Person1# and #Person2# are discussing where to have fun, and they decide to go to the theater tonight.", "#Person1# and #Person2# are talking about what to do tonight and they finally decide to go to watch a show.", "#Person2# hasn't been to the theater for a long time, so #Person1# and #Person2# decide to make a reservation for a show at the Sanger Theater."], "prediction_text": "#Person1# asks #Person2# if they want to do something tonight in New Orleans, and #Person2# suggests going to the theater."}
{"answers": ["#Person1# buys some nice clothes by credit card with #Person2#'s assistance.", "#Person1# pays #Person2# by credit card for some clothes.", "#Person1# purchases some clothes by credit card with #Person2#'s assistance."], "prediction_text": "#Person1# tries on a dress and decides to buy it. #Person2# helps #Person1# sign the receipt."}
{"answers": ["Mr. Blake explains the training manuals cannot be sent today because they are still being copied.", "#Person1# is transferring the message between Mr. Blake and Mr. Foster about the training manuals.", "Mr. Foster wants the training manuals to be sent this afternoon but Mr. Blake explains that they haven't been printed out yet."], "prediction_text": "#Person1# tells #Person2# that Mr. Foster wants the training manuals, but #Person2# tells #Person1# that they are at the printer's and won't be available until the next day."}
{"answers": ["#Person2# tells David about #Person2#'s planned a long trip for #Person2#'s vacation. David thinks it's nice.", "David and #Person2# are talking about #Person2#'s plan for the vacation. David thinks it sounds good.", "#Person2# tells David the plan for a tour and #Person2# will celebrate #Person2#'s brother's fortieth birthday when at Salt Lake City."], "prediction_text": "#Person1# and #Person2# are planning a tour. #Person2# tells #Person1# that they will start from Long Island and drive to Salt Lake City to join #Person2#'s brother's fortieth birthday. #Person1# thinks it sounds good and mentions some places they can visit."}
{"answers": ["#Person1# is taking off to restart a business. #Person1# expresses appreciation to #Person2# and asks #Person2# to take care of Pamela.", "#Person1#'s flight is about to take off but #Person1# is waiting for Pamela. #Person1# thanks #Person2# for their friendship and asks #Person2# to take care of Pamela.", "#Person1#'s flight is going to take off. #Person1# thanks #Person2# and asks to take care of Pamela when #Person1# is away."], "prediction_text": "#Person1# is worried about missing his flight and asks #Person2# to take care of Pamela while he is away. #Person1# thanks #Person2# for being a true friend and #Person2# wishes #Person1# a successful start in business."}
{"answers": ["#Person2# tells #Person1# how to get to the seventy seventh.", "#Person2# shows #Person1# the way to the seventy seventh.", "#Person2# tells #Person1# #Person1# can take Bus No. 12 to the seventy seventh."], "prediction_text": "#Person1# asks #Person2# for directions to the busiest street in the city, and #Person2# tells him to take Bus No. 12."}
{"answers": ["#Person1# and #Person2# talk about a programme about Muslims' pilgrimage to Mecca and the accidents. They find such pilgrimage is also common in many faiths but #Person2# doesn't believe it.", "#Person2# introduces a programme about Islam's pilgrimage to #Person1#. They talk about the pilgrimage in other faiths and countries as well as. #Person2# doesn't think the place where people go to get healed is magical.", "#Person2# is watching a programme about Islam's pilgrimage to Mecca. #Person1# has heard that there are often accidents happen during this pilgrimage. They also talk about pilgrimage in other countries."], "prediction_text": "#Person1# asks #Person2# about a program about Islam and learns about the Haj pilgrimage, the sacred stone, and the Saudi government's efforts to limit the number of pilgrims. #Person1# also asks about other pilgrimages in different faiths and #Person2# explains about Lourdes and the beliefs about its healing powers. #Person1# expresses skepticism about the place being magical."}
{"answers": ["#Person1# gets lost and asks #Person2# where the central department stall and the national bank are. #Person2# directs #Person1#.", "#Person2# shows #Person1# the ways to the central department stall and the national bank.", "#Person1# asks #Person2# the way to the central department stall and the national bank."], "prediction_text": "#Person1# is lost and asks #Person2# for directions to the central department store and the national bank. #Person2# gives clear directions and #Person1# thanks him."}
{"answers": ["#Person1# will go to London and invites #Person2# along. #Person1# prefers the train as it's quicker and relaxing but #Person2# prefers the car as it's convenient.", "#Person1# will go to London and invites #Person2# to come along. But they have disagreements on how they get there.", "#Person1# and #Person2# are going to London next week. #Person2# prefers by car. #Person1# prefers by train. #Person2# suggests maybe they could go by express bus."], "prediction_text": "#Person1# wants to go to London next week and invites #Person2# to join them. #Person1# prefers to take the train, but #Person2# prefers to take the car. #Person1# mentions that the train is more expensive but quicker, while #Person2# suggests going by express bus."}
{"answers": ["Tom complains about his unhappy lunch experience in the fast-food restaurant while Catherine enjoyed her home-made meal. Catherin doesn't like fast food and says most Americans avoid it too.", "Tom complains about his terrible lunch at the food station to Catherine. Catherine brings lunch from home because she does not like fast food and thinks it is unhealthy.", "Tom tells Catherine he had an underdone sandwich for lunch. Catherine doesn't like fast food because she thinks it's not healthy. She explains that two-thirds of Americans may avoid places like McDonald's and KFC."], "prediction_text": "Tom had a terrible lunch at a food station and Catherine brought her lunch from home. Catherine doesn't like fast food because of their unhealthy ingredients, while Tom believes fast food is a part of their lifestyle."}
{"answers": ["#Person2# orders a glass of lemonade, barbeque wings, and baby-back ribs for meal with #Person1#'s assistance.", "#Person2# is ordering lemonade, barbeque wings, and baby-back ribs, and #Person1# serves #Person2# gently.", "#Person2# orders a glass of lemonade, barbeque wings, and baby-back ribs with the help of #Person1#."], "prediction_text": "#Person1# offers to get #Person2# a drink and asks if they want an appetizer. #Person2# orders barbeque wings and is ready to order their food."}
{"answers": ["#Person2# orders a $ 5. 48 meal including cheeseburger, fries, and Pepsi.", "#Person2# orders a double cheeseburger, fries, and drink.", "#Person2# orders a double cheeseburger, large curly fries, and a medium Pepsi."], "prediction_text": "#Person1# is working at a fast food restaurant and #Person2# orders a double cheeseburger with everything on it, large curly fries, and a medium Pepsi."}
{"answers": ["#Person1# and #Person2# are preparing breakfast. #Person1# hates overcooked boiled eggs while #Person2# hates scrambled eggs and prefers sunny-side-up ones.", "#Person1# and #Person2# are making their breakfast. #Person2# was fed up with scrambled eggs and wants a sunny side up egg. But #Person1# makes #Person2# fried eggs.", "#Person1# is making some eggs for #Person2# and #Person2# wants a sunny side up egg, but #Person1# gives #Person2# fried eggs."], "prediction_text": "#Person1# and #Person2# are having breakfast together. #Person1# makes coffee and eggs, but #Person2# wants sunny side up eggs. #Person1# is picky about eggs and makes fried eggs instead."}
{"answers": ["#Person1# drives #Person2# to the Grand Hotel.", "#Person1# drive #Person2# to the address of Grand Hotel provided by #Person2#.", "#Person2# tells #Person1# the address of the Grand Hotel and #Person1# drives her there."], "prediction_text": "#Person1# is a taxi driver and #Person2# is a passenger who asks to be taken to the Grand Hotel. #Person1# confirms that he knows where it is."}
{"answers": ["#Person1# requests to take a picture of #Person1# to show #Person1#'s children.", "#Person1# takes a picture of #Person2# who's a policeman to show #Person1#'s children.", "#Person1# wants to take a photo of #Person2# to show #Person1#'s children how an American policeman looks likes."], "prediction_text": "#Person1# wants to take a picture of #Person2# to show their children how an American policeman looks. #Person2# agrees but hopes it won't take too long."}
{"answers": ["The hotel is fully booked and #Person1# offers #Person2# the information about hotels nearby.", "#Person1#'s hotel is full and #Person1# recommends #Person2# another hotel.", "#Person1# recommends #Person2# going to the Sunset Hotel to find vacant rooms as #Person1#'s hotel is full."], "prediction_text": "#Person1# tells #Person2# that the hotel is full for tomorrow, but suggests the Sunset Hotel nearby."}
{"answers": ["#Person2# has applied for a student visa in the states and tells #Person1# about the rules and the required documents of applying for a USA visa and why they are so strict.", "#Person2# tells #Person1# #Person2#'s applied for the student visa to study in the united states. They talk about the procedures, materials, and points for attention of the application.", "#Person2# tells #Person1# that #Person2# has applied for a student visa to the United States. #Person2# says many people are rejected because they are not careful about it."], "prediction_text": "#Person1# asks #Person2# if they have applied for a visa to study in the United States, and #Person2# replies that they have and are going for an interview next week. #Person1# expresses their luck and #Person2# explains the difficulties of the application process, including the complexity of the form and the need for financial and educational documents. #Person2# attributes the strictness of the embassy to the actions of some Chinese people who break the rules regarding their visa conditions."}
{"answers": ["#Person2# thinks #Person2# and Anne had a good time and invites her out again, but Anne refuses it.", "#Person2# asks Anne out for a drink tomorrow but Anne didn't enjoy the date with #Person2# last time and refuses #Person2#'s invitation.", "Anne didn't enjoy the date with #Person2# last time, she rejects #Person2#'s invitation firmly."], "prediction_text": "#Person1# is tired and doesn't want to see #Person2# again, despite having a good time last time. #Person2# asks for a second chance but #Person1# declines."}
{"answers": ["#Person1# asks about online shopping. Mary favors it as it's time-saving and economical. Then she helps #Person2# shop online.", "#Person1# inquires Mary about the price of products online and their payments. Mary teaches #Person1# how to shop online.", "Mary tells #Person1# the advantages of shopping online and #Person1# should open an online bank account and try it."], "prediction_text": "Mary often shops online and finds it convenient and time-saving. She tells #Person1# about how to pay for online purchases and mentions that there are many things available online from all over the world."}
{"answers": ["#Person2# tells #Person1# about the fundamental concepts of the accounting process.", "#Person1# and #Person2# talk about the accounting process.", "#Person1# and #Person2# are talking about the most fundamental concepts underlying the accounting process."], "prediction_text": "#Person1# asks #Person2# about American-styled accounting and #Person2# explains the fundamental concepts underlying the accounting process."}
{"answers": ["Jane invites Peter to join her travel to Xi'an. Peter asks the duration and cost and is interested. Then they discuss their ideal places for a holiday if they had enough money.", "Jane invites Peter to go on a trip with their friends together to Xi'an in the summer. Peter asks about the trip and is interested. They also talk about their ideal place for a holiday.", "Peter is interested in joining Jane's trip to Xi'an in July for about one week. If they had enough money, Peter would go to Xinjiang or Tibet."], "prediction_text": "Jane invites Peter to go to Xi'an with her in July, but he declines as he doesn't have enough money. Peter expresses his interest in traveling to Xinjiang or Tibet, and Jane says she would like to go to the southwest."}
{"answers": ["#Person2# wants to borrow romance videos for #Person2#'s daughter and asks the detailed cost and tenancy term. #Person1# reminds of the overdue fee.", "#Person1# serves #Person2# to borrow movies and tells #Person2# the price and when to return them.", "#Person2# wants to borrow some Romance movies for #Person2#'s daughter. #Person1# asks #Person2# to register a member card. The movie rental comes to seven dollars and fifty cents in total and needs to be returned on time."], "prediction_text": "#Person1# helps #Person2# rent movies. #Person1# explains the membership card and the prices of the movies. #Person2# decides to rent 3 movies and #Person1# tells #Person2# the due date and the overdue fee."}
{"answers": ["Mr. Lee has something to express when #Person1# delivers a parcel for him.", "Mr. Lee signs his parcel and has something to express.", "#Person1# delivers a parcel for Mr. Lee. He happens to have something to express."], "prediction_text": "#Person1# brings a parcel to #Person2#'s desk, who signs for it and thanks #Person1# before expressing something."}
{"answers": ["Planes won't take off tonight and #Person2# tells #Person1# that they've arranged rooms for passengers.", "#Person2# apologizes to #Person1# for the flight delay and arranges rooms for every passenger.", "#Person2# tells #Person1# that #Person1# may have to stay in the airport hotel tonight because the planes are delayed."], "prediction_text": "#Person1# is concerned about the airport closure and asks about accommodations. #Person2# informs #Person1# that two passengers will share a room in the airport hotel. #Person1# expresses her concern about sleeping without lights on and #Person2# mentions that another passenger also has the same habit."}
{"answers": ["Both #Person1# and #Person2# want to pay for a meal. Finally, they go dutch and #Person1# finds the place.", "#Person1# wants to repay #Person2# while #Person2# wants to do the same. They decide to go dutch finally.", "#Person1# and #Person2# argue to repay for a meal but no one succeeds. They finally decide to go dutch."], "prediction_text": "#Person1# and #Person2# are arguing about who should treat the other to dinner. #Person1# insists that #Person2# helped #Person1# with their computer last week and therefore #Person1# should treat #Person2# this time. #Person2# disagrees and suggests going Dutch."}
{"answers": ["#Person2# has insomnia because of academic stress and #Person1# suggests #Person2# relax and listen to some light or classical music to release pressure. #Person2# will try now.", "#Person2# is too stressed to sleep well because of worrying about #Person2#'s grades. #Person1# gives #Person2# some suggestions that #Person2# should listen to some music and relax. #Person2#'ll try it now.", "#Person2# is having a hard time sleeping because #Person2# worries about #Person2#'s grades. #Person1# recommends listening to light or classic music to release #Person2#'s stress."], "prediction_text": "#Person1# notices that #Person2# is still awake and suggests that #Person2# is stressed due to school. #Person1# recommends yoga or relaxation techniques to help with stress, but #Person2# is too busy studying to take a break. #Person1# suggests listening to music to relax, and #Person2# agrees to try it."}
{"answers": ["#Person1# wants to refurbish the kitchen while #Person2# thinks it unnecessary.", "#Person1# wants to redecorate the kitchen but #Person2# thinks it unnecessary.", "#Person1# wants to renovate the kitchen to make it more modern but #Person2# doesn't."], "prediction_text": "#Person1# wants to renovate the kitchen because it is outdated and their friends have a modern kitchen. #Person2# does not see the need for renovation."}
{"answers": ["Walter and Sterling think their new professor is brilliant but absent-minded.", "Walter and Sterling think Professor Wood is brilliant but absent-minded.", "Walter thinks Professor Wood is brilliant, but Sterling thinks he's absent-minded."], "prediction_text": "Sterling and Walter are talking about their new teacher, Professor Wood. They both think he's a brilliant scientist but also a bit absent-minded."}
{"answers": ["#Person2# asks #Person1# to bring #Person1#'s husband to talk about #Person1#'s test results.", "#Person2# schedules an appointment with #Person1# and #Person1#'s husband. #Person1# is nervous to know the test results.", "#Person2# wants to schedule an appointment with #Person1# and #Person1#'s husband to talk about #Person1#'s test results."], "prediction_text": "#Person1# wants to get their test results from the other day, but #Person2# wants to schedule an appointment to talk about it further. #Person1# is worried something is wrong, but #Person2# wants to take a second look before giving any information. #Person1# is scheduled to come in this afternoon."}
{"answers": ["Miss Eliza asks about Martin's preparation for exams and economic problems. Martin appreciates Mister Lam and the students' welfare club for providing a part-time job.", "Miss Eliza cares about Martin's preparation for the exams and his problem with the pocket money. Martin thanks Mister Lam and thinks the students' welfare club is fantastic.", "Martin tells Miss Eliza about his exams and his part-time job. Martin appreciates Mister Lam and the students' welfare club."], "prediction_text": "#Person1# asks #Person2# about their preparations for exams and #Person2# replies that they have worked hard and are ready, and that Mister Lamb helped them with their problem of lacking pocket money."}
{"answers": ["Informed of the cost and duration by #Person2#, #Person1# chooses a cheaper way of mailing which takes more time.", "#Person2# offers #Person1# two ways to send the package and #Person1# chooses the cheaper one.", "#Person1# prefers sending the package by post to by first class after being told the price and the duration by #Person2#."], "prediction_text": "#Person1# wants to mail a package to Korea and #Person2# explains the options available, including the cost and delivery time for each. #Person1# chooses the package post way, which is cheaper but takes longer to arrive."}
{"answers": ["#Person1# tells Linda that #Person1# cannot find #Person1#'s cellphone anywhere and they need to go to the wedding. #Person1# remembers it was left in the barkery.", "Linda and #Person1# fail to find #Person1#'s phone because it was left in the bakery, but they are in a hurry to the wedding.", "#Person1# and Linda are going to the wedding in 15 minutes, but #Person1#'s cellphone was left in the bakery. #Person2# will call the bakery."], "prediction_text": "Linda can't find her cellphone in the hotel room and asks #Person2# to call it. #Person1# remembers where it is and #Person2# calls the bakery to see if they can pick it up before the wedding."}
{"answers": ["#Person1# is a tourist from America and has a chat with #Person2#.", "#Person1# joins #Person2# and they chat about their cities. #Person1# tells #Person2# that #Person1# comes here for tourism.", "#Person1# is an American and comes here for tourism. #Person2# treats #Person1# a drink."], "prediction_text": "#Person1# is American and joins #Person2# at a table. #Person1# is not English and is visiting the city for tourism. #Person2# offers #Person1# another drink."}
{"answers": ["#Person2# plans to take a business class in the summer vacation while #Person1# prefers to relax.", "#Person1# and #Person2# share their plan for summer vacation and their visions of the future.", "#Person2# is going to take a business class and work part-time during summer vacation. #Person1# wants to relax."], "prediction_text": "#Person1# is looking forward to summer vacation, while #Person2# plans to take a business class and work part-time at their uncle's company to gain experience. #Person1# is more interested in traveling after graduation, while #Person2# is focused on their future success."}
{"answers": ["#Person2# tells Mary about #Person2#'s terrible experience that a dog rushed at #Person2# and barked loudly when #Person2# was delivering milk.", "#Person2# shares with Mary the terrible experience this morning. #Person2# was run after by a dog and knocked into a tree.", "#Person2# tells Mary about #Person2#'s experience this morning. when #Person2# was delivering milk, a huge dog rushed at #Person2# and #Person2# was knocked into a tree. It reminds Mary of a movie."], "prediction_text": "#Person1# asks #Person2# if they had a car accident, and #Person2# explains that they were delivering milk and encountered a locked gate with a note, then a dog rushed at them and they knocked into a tree branch."}
{"answers": ["#Person2# tells #Person1# about #Person2#'s changes and improvements after working in the same company for six years. #Person2# didn't have much chance to travel.", "#Person1# has been working for a company for six years, and #Person2# thinks #Person2#'s ready for promotion.", "#Person2# has worked for a company for 6 years. With much experience and new skills, #Person2# tells #Person1# that #Person2# is ready for a promotion."], "prediction_text": "#Person1# asks #Person2# about their experience working for a company and #Person2# replies that they have been there for 6 years, but their enthusiasm has decreased, they work longer hours, earn more, and have done management training courses, but don't get much opportunity to travel due to looking after their mother."}
{"answers": ["#Person2# helps #Person1# to open a current account and explains the current account doesn't pay interest and #Person1# can draw money at a bank or an ATM.", "#Person1# wants to open a current account. After #Person1# providing information and filling in a form, #Person2# opens one for him and tells him how to draw money from it.", "#Person2# opens a current account for #Person1# and explains it doesn't pay interest and tells him how to draw money from it."], "prediction_text": "#Person1# wants to open a current account and provides his social security and ID. #Person2# checks the information and has the account ready. #Person1# asks about interest and #Person2# explains that current accounts don't pay interest and are primarily for business transactions. #Person1# asks how to draw money for personal use and #Person2# explains that it can be done at a bank or ATMs."}
{"answers": ["It's #Person1#'s first time to China and #Person1# wants some Chinese cuisine. #Person2# recommends some but it's too far and #Person1# is starving. Then #Person2# suggests a nearby Quanjude restaurant and its Beijing roast duck. #Person1# will go there.", "#Person1# is starving and wants to have authentic Chinese cuisine so #Person2# recommends some. #Person1# chooses the Quanjude restaurant as #Person2# wants to try the Beijing roast duck and it's nearby.", "#Person1# wants to try real Chinese cuisine. #Person2# recommends several ones. #Person1# decides to go to Quanjude restaurant."], "prediction_text": "#Person1# wants to try Chinese food and asks #Person2# for recommendations. #Person2# suggests Cantonese and Kiangsu cuisines, but #Person1# prefers something less spicy. #Person2# suggests a nearby Beijing dishes restaurant called Quanjude. #Person1# thanks #Person2# for the information."}
{"answers": ["#Person1# and #Person2# agree to sign an agreement since #Person1# could speed up the delivery as #PErson2# hopes.", "#Person1# will accelerate the slow delivery to address #Person2# concerns, and they reach an agreement right away.", "#Person2# agrees to the price but is worried about the slow delivery. #Person1# promises to fasten the delivery, and they will sign the agreement."], "prediction_text": "#Person1# and #Person2# discuss the delivery of an order. #Person1# reminds #Person2# that they agreed to the delivery time of three months, but #Person2# is worried about the slow delivery. #Person1# contacts Auckland and they agree to ship the order on the first vessel available next month."}
{"answers": ["#Person1# broke a reading lamp and #Person2# will replace a new one.", "#Person1# will pay for the broken lamp and #Person2# will replace it.", "#Person1# reports #Person1# broke a lamp."], "prediction_text": "#Person1# broke the reading lamp in their room and #Person2# asks for the room number to process the request."}
{"answers": ["#Person1# first introduces the 2006 fiscal year marketing plan then analyzes their performance and explains with the sale graph.", "#Person1# and #Person2# recall the goals they had in the marketing plan, and #Person1# compares last and this years' sales figure. Their performance has grown rapidly.", "#Person1# and #Person2# discuss the marketing goals they set this year to begin with and evaluate their sale performance which has grown greatly."], "prediction_text": "#Person1# talks about the goals set for the 2006 fiscal year marketing plan, which include appealing to a younger set of consumers, redoing the company's image, and doubling distribution in overseas markets. #Person1# describes the three things to consider when evaluating whether the goals were met and shows an overhead graph to illustrate the sales performance of the company in 2005 and 2006."}
{"answers": ["#Person2# is going to travel around Europe and will buy #Person1# a souvenir.", "#Person2# plans to travel around Europe during #Person2#'s holiday.", "#Person2# tells #Person1# #Person2# plans to travel around Europe to visit museums and attend the Edinburgh festival."], "prediction_text": "#Person1# asks #Person2# about their plans for their holiday, and #Person2# tells #Person1# that they will travel around Europe, starting in Paris and ending in the UK, where they will attend the festival in Edinburgh. #Person1# jokes about buying a souvenir."}
{"answers": ["#Person2# fails to hold Mr. Fox's reservation for a mid-size vehicle. Mr. Fox thinks it unreasonable. #Person2# offers Mr. Fox a compact instead and offer him a discount for his costs.", "Jimmy Fox made a reservation for a mid-size vehicle but is told the mid-size is not available. #Person2# apologizes and offers a compact with a discount for #Person1#.", "Mr. Fox is angry because he makes a reservation for a mid-size vehicle but #Person2# doesn't keep a car for him. Finally, he takes a compact with a discount."], "prediction_text": "Jimmy Fox made a reservation for a mid-size vehicle, but the car rental agency has run out of cars. They offer him a compact car instead and give him a discount on the insurance and rental fee."}
{"answers": ["#Person1# gets mom's permission to play card games quietly for a while.", "#Person1# asks for mom's permission to play card games and promises not to make noise.", "#Person2# allows #Person1# to play card games without making any noise."], "prediction_text": "#Person1# wants to play card games but #Person2# tells him that #Person1#'s dad is working in the study and #Person1# must be quiet. #Person1# promises to be careful and #Person2# allows #Person1# to play for five minutes."}
{"answers": ["Ann tells Mr. Jones about his weekly meeting schedule.", "Ann is telling Mr Jones his meeting arrangements this week.", "Ann informs Mr. Jones of his meeting schedule for this week and reminds him of Ms. Von."], "prediction_text": "#Person1# asks #Person2# for the schedule of meetings for the week and #Person2# informs #Person1# of the meetings and new project manager Ms. Von."}
{"answers": ["#Person2# is looking at a world map to prepare for a geography class. #Person1# and #Person2# talk about geographical features.", "#Person1# and #Person2# are talking about a map of the world. They talk about geographical features and find geographical interesting.", "#Person2# is looking at the map. #Person1# finds geography interesting and wants to know more about it."], "prediction_text": "#Person1# is looking at a map of the world with #Person2#. #Person1# finds it surprising that most of the world is covered with water and #Person2# finds it incredible how mountains were created through volcanic activity. #Person1# wants to find more information about it on the internet or attend #Person2#'s geography class."}
{"answers": ["#Person2# rings #Person1# to change to a non-smoking room. #Person1# apologizes and will arrange one.", "#Person2# is asking #Person1# to change #Person2#'s room to a non-smoking room.", "#Person2# wants to change to a non-smoking room. #Person1# will arrange it."], "prediction_text": "#Person1# is a receptionist at a hotel. #Person2# checks in but needs to change to a non-smoking room due to strong smoke smell. #Person1# arranges a new room for #Person2# and offers assistance with luggage."}
{"answers": ["#Person1# asks Bill to be careful and then sees the workmen walking across the wet cement.", "The workmen are walking across the wet cement to put up the notice.", "#Person1# reminds Bill of the wet cement and then sees the workmen walking across it."], "prediction_text": "#Person1# warns #Person2# not to touch the wet paint, #Person2# apologizes and promises to be more careful, and #Person1# expresses frustration that the workmen left the wet cement without a notice despite the headmaster's request."}
{"answers": ["Ella and Ben had a good time together. Ella gives phone number to Ben before Ben leaves.", "Ben and Ella had a good time. Ben will call Ella next week.", "Ben has to leave. Ella feels bad about it and gives Ben her phone number."], "prediction_text": "Ben wants to leave but Ella invites him to stay longer and gives him her phone number. Ben agrees to call her next week."}
{"answers": ["Bill tells Jim that Dick's been sick.", "Dick's been sick and will come back as soon as possible.", "Bill tells Jim about Dick's health condition."], "prediction_text": "Jim tells Jim that Dick has been sick since he came back from Italy, but is now better according to Doctor William."}
{"answers": ["#Person2# has been working overtime and only gets minimum wage. #Person1# suggests #Person2# either asking for more compensation or quitting, but #Person2# refuses in fear of not being able to find another job.", "#Person2# has worked overtime with minimum wage and little bonus. Although #Person2# are not very confident, #Person1# hopes #Person2# could quit that job and find another one.", "#Person2# works overtime every day and doesn't get enough paid. #Person1# thinks it's unfair and suggests #Person2# quitting the job, but #Person2#'s afraid of being unemployed."], "prediction_text": "#Person1# is concerned about #Person2# working overtime without a break and not being paid fairly. #Person2# explains that their company has complete control over their work schedules and they don't receive extra pay for overtime, but they do receive a bonus at the end of the year. #Person1# suggests that #Person2# should quit their job, but #Person2# is afraid of not being able to find another job."}
{"answers": ["Lisa found out Mark had been cheating on her and decided to divorce him if he wouldn't tell the truth. #Person1# agrees with Lisa.", "Lisa found that her husband was cheating on her. She asked him to tell her the truth or end the relationship that she would divorce him. #Person1# agrees with what she did.", "Lisa has found her husband cheating on her. Her husband first denied and then only admitted to a small indiscretion. #Person1# supports Lisa to seek the truth."], "prediction_text": "Lisa found out her husband Mark is cheating on her, and she confronted him about it. Mark admitted to a small indiscretion, and Lisa threatened to divorce him if he didn't tell her the truth or end the relationship with the other woman."}
{"answers": ["The doctor suggested #Person2# clean up the diet.", "#Person2# will be on a diet for #Person2#'s health.", "#Person1# agrees with #Person2#'s doctor that #Person2# should diet."], "prediction_text": "#Person1# asks #Person2# about the doctor's advice, and #Person2# tells #Person1# that the doctor told him to cut red meat from his diet."}
{"answers": ["#Person1# refuses to stay by #Person2#'s side and won't return.", "#Person1# refuses #Person2#'s plea for staying and will never come back.", "#Person2# asks #Person1# to stay but #Person1# insists on leaving."], "prediction_text": "#Person1# wants to leave, but #Person2# wants #Person1# to stay with him. #Person1# explains that they have spoken their mind and can go anywhere now. #Person2# asks #Person1# to be their second self and best earthly companion. #Person1# refuses and turns away."}
{"answers": ["#Person1# asks #Person2# how to use the library. #Person2# offers specific directions.", "#Person1# is asking #Person2# how to use the library and will check out two books.", "#Person1# wants to use the library. #Person2# teaches #Person1# to use the library and helps to check out two books."], "prediction_text": "#Person1# wants to know how to use the library and #Person2# explains the rules and procedures. #Person1# wants to check out two books on spoken English and #Person2# shows her where they are located."}
{"answers": ["It's a lovely day, so #Person1# and #Person2# have their lunch outside and plan to go to the beach this weekend.", "#Person1# and #Person2# are enjoying the weather and may go to the beach this weekend.", "Weather's nice. #Person1# suggests having lunch outside and hopes to go to the beach to get some sun."], "prediction_text": "#Person1# and #Person2# are enjoying a nice day and decide to have lunch outside. #Person1# misses the change of seasons in Colorado, but is pleased with the nice spring day."}
{"answers": ["#Person1# stops #Person2# from taking photos in the museum.", "#Person2# breaks the museum's rule of non-taking pictures.", "#Person1# asks #Person2# not to take pictures and takes his phone temporarily"], "prediction_text": "#Person1# tells #Person2# that taking pictures is not allowed in the museum, and #Person1# confiscates #Person2#'s camera and will return it later. #Person2# asks if they can buy slides and picture postcards somewhere."}
{"answers": ["#Person2# asks #Person1# for information for requesting a loan. #Person1# says the bank will assess people's credit scores then #Person2# just leaves.", "#Person2#'d like some information for requesting a loan. However, it's likely that his order cannot be approved because of his terrible credit score.", "#Person2# wants to know about requesting a loan. #Person1# introduces the policy of loan interest and credit assessment. #Person2# then leaves as he has a terrible credit score."], "prediction_text": "#Person1# explains the terms of the loan to #Person2#, who mentions that they have a terrible credit score and decides not to take the loan."}
{"answers": ["#Person1# thanks Monica for giving a successful project presentation at the meeting. Monica attributes the success to collaboration.", "Monica tells #Person1# her presentation was very successful because she got help from several colleagues and an economic expert.", "#Person1# praises Monica for her presentation. Monica admits her efforts and also gives the credit to her colleagues."], "prediction_text": "#Person1# congratulates #Person2# on their successful presentation and thanks them for their contribution to the company. #Person2# mentions that they had help from colleagues and an economic expert."}
{"answers": ["Tom invites #Person1# to run in the morning. #Person1# would try to get up and join him.", "Tom will stop by #Person1#'s house tomorrow, and #Person1# will try to get up to join his running.", "Tom likes to run in the morning. #Person1# tries but fails. Tom invites #Person1# to run together tomorrow. #Person1# accepts."], "prediction_text": "Tom wants to go for a run, but can't get up early enough. Tom suggests that #Person1# join him the next day, and #Person1# agrees to try."}
{"answers": ["#Person1# calls a Japanese restaurant to ask about food and opening time and will go there.", "#Person1# calls #Person2# to ask about their food and opening time.", "#Person1# calls #Person2#'s restaurant to ask about the food and opening hours."], "prediction_text": "#Person1# asks about the Japanese Restaurant and #Person2# tells him about the variety of food they serve and their hours of operation."}
{"answers": ["Mr. Simpson agrees to have lunch with #Person1# on Thursday.", "#Person1# and Mr. Simpson will have lunch together.", "Mr. Simpson accepts #Person1#'s invitation to lunch on Thursday."], "prediction_text": "#Person1# invites #Person2# to lunch on Thursday and #Person2# agrees to go."}
{"answers": ["#Person1# is taking a date to a restaurant. #Person2# suggests an expensive one and #Person1# asks #Person2# to reserve a table.", "#Person1# wants to take a date to the best restaurant, #Person2# suggests the Gramercy Tavern and is calling to reserve a table for him.", "#Person1# wants to take a date to a restaurant. #Person2# recommends the Gramercy Tavern."], "prediction_text": "#Person1# wants to take a date to a restaurant and asks #Person2# for help. #Person2# suggests the hotel restaurant, which #Person1# declines, and #Person2# suggests Gramercy Tavern, which #Person1# approves. #Person2# calls to reserve a table."}
{"answers": ["#Person1# pays the meal and tips #Person2# but #Person2# doesn't take it.", "#Person2# takes no tips despite #Person1#'s kindness.", "#Person1# pays and leaves a tip but #Person2# politely refuses."], "prediction_text": "#Person1# pays #Person2# $50 for a roasted duck, a plate of pork, and a bowl of beef. #Person2# refuses the tip."}
{"answers": ["#Person1# and #Person2# talk about popular sports in their country. Rugby is popular in both countries.", "#Person1# and #Person2# are talking about the popular sports in their countries, including football, basketball, tennis, swimming, golf, extreme sports and rugby.", "People in #Person2#'s country like football, basketball, tennis, and swimming. People in #Person1#'s country like golf. A few people like extreme sports. Both people like rugby."], "prediction_text": "#Person1# and #Person2# discuss sports popularity in their countries. #Person1# mentions football, basketball, tennis, swimming, and golf, while #Person2# mentions football, basketball, tennis, and rugby."}
{"answers": ["#Person2# wants to make a reservation for a round trip flight this month. #Person1# helps #Person2# book the cheapest flight and vegetarian meals.", "#Person2# is asking #Person1# to make a flight reservation. #Person2# chooses the cheapest flight and asks #Person1# for their departure and arrival times.", "#Person2# is calling #Person1# to make a round trip flight reservation to and back from Helsinki and asks for the cheapest flight available."], "prediction_text": "#Person1# works at Freedom Travel and helps #Person2# book a flight to Helsinki, Finland. #Person1# checks the flights available and tells #Person2# the price and departure times. #Person2# requests a vegetarian meal and gives their name."}
{"answers": ["#Person1# would go to Disney World with #Person1#'s grandmother.", "#Person1#'s grandmother will take #Person1# to Disney World.", "#Person1#'s grandmother will take #Person1# to Disney World."], "prediction_text": "#Person1# is going to visit their grandmother in Florida and go to Disney World with her."}
{"answers": ["#Person2# would love to go to Australia.", "#Person2#'d love to go to Australia.", "#Person2# would like to go to Australia."], "prediction_text": "#Person1# asks #Person2# if they have been to Australia and #Person2# expresses interest in visiting and seeing the Great Barrier Reef."}
{"answers": ["Laura comes across #Person1# on the way to the gym, and they stop to talk about exercise habits. Unlike Laura, #Person1# prefers other exercises over going to the gym.", "Laura does exercise for her health and good looking. #Person1# talks about regular exercise. They may do exercise together one day.", "Laura exercises both for health and to look good. #Person1# walks but never goes to the gym. Laura invites #Person1# to join her at the gym but #Person1# refuses."], "prediction_text": "#Person1# and #Person2# are having a conversation at the park. #Person1# mentions that she walks for exercise and enjoyment, but doesn't go to the gym. #Person2# tells #Person1# that she exercises regularly at the gym and invites #Person1# to join her sometime."}
{"answers": ["#Person1# asks #Person2# about past experiences of living in different cities around the world.", "#Person1# is asking #Person2# about the places that #Person2# has stayed or lived.", "#Person1# and #Person2# talk about #Person2#'s experience. #Person2# moves a lot. #Person1# finds it interesting."], "prediction_text": "#Person1# asks #Person2# where they were born and #Person2# replies they were born in Chicago but moved to Los Angeles when they were 14. #Person1# asks about high school and #Person2# replies they graduated from Lincoln High and then lived in Munich for almost two years."}
{"answers": ["#Person1# bought a new suit with $ 150 and #Person2# thinks it is too expensive.", "#Person2# thinks #Person1#'s new suit is not worthwhile.", "#Person1#'s new suit cost $150. #Person2# doesn't think it's a good bargain."], "prediction_text": "#Person1# shows #Person2# his new suit and #Person2# thinks it's not a good bargain."}
{"answers": ["Robin and Robin are broadcasting. Robin introduces the mountain lake cycle tour and how they organize this event.", "Ann interviews Robin at a university radio. Robin talks about the mountain lake cycle tour and their method to avoid roadblock problem.", "Ann interviews Robin about the mountain lake cycle tour. Robin introduces the event and explains its settings of the tour."], "prediction_text": "Robin, the president of the Martha Bicycle Club, talks to Ann on university radio about the mountain lake cycle tour. Robin explains the tour is not a race, but for fun and fitness, and that the tour will have 1200 registered cyclists."}
{"answers": ["#Person1# makes drinks for #Person2# at a bar.", "#Person2# starts with a Compari and then tries a Singer at a bar.", "#Person2# starts with an aperitif and asks about signature drinks. #Person1# recommends one and #Person2# likes that."], "prediction_text": "#Person1# offers #Person2# a Compari, which #Person2# accepts. #Person1# recommends a signature drink, the Singer, which #Person2# also enjoys."}
{"answers": ["Eric offers to treat Gregory a meal. Eric is telling Gregory about the bungee jumping experience. Eric's found that his wallet was lost.", "Eric invites Gregory for lunch and they talk about bungee jumping Eric tried. Eric thinks Gregory should try it but Gregory doesn't think so.", "Eric treats Gregory to lunch. Eric went bungee jumping and suggests Gregory should try. Gregory thinks Eric has lost himself. Eric finds he loses his wallet."], "prediction_text": "Eric and Gregory go for lunch together, but Gregory is short on cash and Eric offers to pay. They talk about bungee jumping and Gregory is scared, but Eric thinks he would like it. Eric then realizes he has lost his wallet."}
{"answers": ["#Person1# wants some online information about the Normandy Landing, #Person2# teaches #Person1# how to google it.", "#Person2# helps #Person1# find a website about Normandy Landing and suggests reading a book for details.", "#Person1# wants to know about the Normandy Landing. #Person2# teaches #Person1# to search and suggests reading a history book."], "prediction_text": "#Person1# wants to find information on World War Two, specifically the Normandy Landing. #Person2# helps #Person1# search for information online and provides a website for detailed information."}
{"answers": ["#Person2# has problems using training machines to build #Person2#'s body. #Person1# explains it and shows #Person2#.", "#Person1# teaches #Person2# how to use the training machine and suggests starting working small reps if #Person2# wants a good physique.", "#Person1# teaches #Person2#, a newbie, to use a machine. #Person2# doesn't want to be ripped and thus works with less weight than #Person1#."], "prediction_text": "#Person1# notices that #Person2# looks lost and offers to give them some pointers. #Person2# is starting their training and wants to develop muscle tone and definition without getting too ripped. #Person1# demonstrates how to use a machine and advises #Person2# to start with lighter weights and stretch before lifting."}
{"answers": ["#Person2# tells #Person1# #Person1# can park at a parking structure and show #Person1# the direction.", "#Person1# is asking #Person2# for parking information on campus.", "#Person2# tells #Person1# a parking structure for parking and where it is."], "prediction_text": "#Person1# wants to know where to park on campus and #Person2# tells him there is a parking structure for students on the western side of the campus. #Person1# asks if it is full and #Person2# is not sure."}
{"answers": ["Emily's never done paycheck in the States before, so she asks Susan questions about it. Susan explains what the number and terms on the paycheck mean. Emily thanks Susan for her help.", "Emily is asking Susan some questions about her paycheck. Susan tells her that there are two separate income taxes-federal deductions, helping people out after they retire or if they were unable to workand, and state deductions, paying into an unemployment and disability fund that the state has set up.", "Emily has her first paycheck in the States and there are a few things she doesn't understand. Susan explains the federal and state deductions to her. Emily thinks it might be the same in the UK but she just never paid much attention before."], "prediction_text": "Emily asks Susan about her paycheck and Susan explains the different deductions on Emily's paystub, including FICA, SUI/SDI, state income tax, and health insurance."}
{"answers": ["#Person2# shows #Person1# how to use the vending machine.", "#Person2# is telling #Person1# how to use the machine to buy some snacks.", "#Person2# helps #Person1# to buy things from the vending machine. #Person1# finds it unreliable."], "prediction_text": "#Person1# wants to buy something from a vending machine, but #Person2# explains that the new machines can be tricky and gives instructions on how to use it. #Person1# has trouble getting the money to go in, but #Person2# tells her to keep trying and that vending machines aren't always reliable."}
{"answers": ["#Person2# has known a girl from Thailand on the internet and will spend the winter vacation with her. #Person1# is surprised.", "#Person1# is surprised that #Person2# has a girlfriend in Thailand. They've known each other over the Internet for a few months.", "#Person2# has a girlfriend in Thailand. They know each other on the Internet but never meet. #Person1# is surprised but congratulates #Person2#."], "prediction_text": "#Person1# is surprised to hear that #Person2# has a girlfriend in Thailand that he has never heard of, and #Person2# explains that they have known each other over the internet for a few months and plan to be engaged next month."}
{"answers": ["James is visiting Kate's new suite and surprisingly finds she's redecorated it.", "James comes to visit Kate's living room and is surprised that she's redecorated it. Kate talks about her furniture proudly.", "Kate saved money to buy new furniture and do some decorating. James loves her suite and spots that her carpet is the old one."], "prediction_text": "James visits Kate and compliments her new furniture and curtains. Kate tells James that she bought the new furniture and carpet on her trip to China."}
{"answers": ["#Person2# gives Bill a rigorous daily exercise schedule, and Bill finds it torturing.", "#Person2# has made a harsh daily exercise schedule for Bill.", "#Person2# gives Bill his daily exercise schedule. Bill feels desperate."], "prediction_text": "#Person1# is given a daily exercise schedule by #Person2#, which includes jogging before breakfast, walking to work, using the stairs instead of the elevator, and participating in a dance class. #Person1# is hesitant and unsure about the schedule."}
{"answers": ["Marquet offers #Person1# adivce on what courses to take.", "#Person1# wants to enroll in a science course and asks Marquet's advice.", "Marquet suggests #Person1# take a good introductory course for non-science majors."], "prediction_text": "#Person1# is considering enrolling in a science course, and #Person2# thinks it's a good idea since #Person1# wants to graduate this year and is weak in maps. #Person2# suggests an introductory course for non-science majors."}
{"answers": ["Tim's project is about how to lead a greener life. #Person1# consults about his motivation and his findings at school. Tim finds there are many aspects that can be improved for his school.", "Tims is talking about his greener life. He now rides his bike to school and recommends teachers to send electrical paper rather than paper ones. And he thinks the school shouldn't have machines with bottled drinks.", "Tim chooses a green-life-related project because he wants to see what he could do in his own life. He's started being greener with himself and influences others."], "prediction_text": "Tim chose the subject of leading a greener life for his project and shares his reasons and experiences with #Person1#. Tim found it easy to get information and has made changes in his daily life, such as riding his bike to school and suggesting electronic submission of work to teachers. Tim also suggests reducing the school's use of bottled drinks and using local produce."}
{"answers": ["#Person1# complains to Tony that Christmas has made #Person1# busier.", "#Person1# works as a toy salesperson and feels so tired recently because Christmas is coming, and everyone's shopping for presents.", "#Person1# thinks selling gifts for kids is such an unpleasant job before Christmas."], "prediction_text": "#Person1# is feeling tired and stressed about Christmas due to their job at the shopping center. #Person2# listens and tries to comfort #Person1#."}
{"answers": ["Bill made a cake for Sue's birthday, but Sue is on a diet to avoid allergy. Bill offers to buy her something healthy to eat, but Sue is enjoying her party. So they will go afterwards.", "Sue doesn't eat cakes and sandwiches at her birthday party to avoid being allergic. Bill is willing to order her some hot soup to make her feel better. They decide to go to the restaurant later.", "Bill made a cake for Sue's birthday but Sue can't eat it because she's on a diet to avoid being allergic. Bill and Sue will go to the restaurant to have some soup after the party."], "prediction_text": "Bill offers to get Sue a salad since she is on a diet, but Sue explains that she is not on a diet to lose weight but to avoid being allergic to something. Bill offers to get her hot soup from the restaurant across the street."}
{"answers": ["#Person2# likes a T-shirt in #Person1#'s clothes shop. #Person1# recommends one section for #Person2#.", "#Person2# goes to shop for clothes and is told there is a sale. #Person2# buys clothes under #Person1#'s recommendation.", "#Person2# buys summer clothes with 20% off at #Person1#'s shop."], "prediction_text": "#Person1# helps #Person2# find summer clothes in a shop. #Person1# tells #Person2# that everything is 20% off and shows #Person2# a section of skirts and trousers that would look good with a T-shirt. #Person2# buys a skirt and pays for it."}
{"answers": ["As not reconfirming recently, #Person1# cannot take the reserved air ticket.", "#Person1# was too busy to reconfirm a fight reservation and needs to buy another ticket.", "#Person1# goes to pick up the ticket but is told by #Person2# that the ticket was cancelled due to the late reconfirmation."], "prediction_text": "#Person1# comes to pick up their tickets but is told that without reconfirmation, their reservation has been cancelled. #Person1# asks if there are any other tickets available."}
{"answers": ["#Person2# tells #Person1# everyone from #Person2#'s family gathers here to spend the Easter together.", "#Person2# introduces to #Person1# the family's tradition to celebrate Easter.", "#Person2# tells #Person1# about the family activities on Easter day."], "prediction_text": "#Person1# and #Person2# are at a family gathering, where everyone from young to old is present. #Person2# explains that the family goes to church together on Easter and has a potluck buffet afterward. #Person1# wonders where the children are, and #Person2# reveals that the children are hiding Easter eggs."}
{"answers": ["#Person1# and #Person2# are talking about their son's bad habits and #Person2# gives suggestions on how to guide and help the kid.", "#Person1# and #Person2# exchanges thoughts on how to deal with their son's bad behavior. #Person2# suggests treating the kid with patience instead of punishment.", "#Person1# asks #Person2# to help to address their son's problems, and #Person2# has come up with several motivating and gentle ideas."], "prediction_text": "#Person1# is worried about their son Tony's bad habits, such as saying painful words like \"kick mommy\" or \"beat mommy\". #Person2# suggests treating Tony with patience, affection, and respect, and rewarding good behavior to motivate him to cooperate. #Person1# agrees to try."}
{"answers": ["#Person1# is going to the bachelor party of Alex, and #Person2# will organise a bachelorette party for Wendy.", "#Person1# is excited about attending two friends' bachelor party. #Person2# doesn't trust #Person1# and will organize a bachelorette party for a friend too.", "#Person1# shares the Alex's bachelor party plan with #Person2# and promises nothing will be too much. #Person2# doubts that and will also organize a bachelorette party."], "prediction_text": "#Person1# is excited about the bachelor party for Alex, but #Person2# is hesitant about the idea. #Person1# tries to reassure #Person2# that it will be a fun and innocent event."}
{"answers": ["#Person1# helps #Person2# get a ballot card and guides #Person2# the next step.", "#Person1# helps #Person2# to get a ballot and go vote.", "#Person2# successfully gets the ballot card and votes with the help of #Person1#."], "prediction_text": "#Person1# helps #Person2# find their ballot and explains how to vote."}
{"answers": ["#Person1# and #Person2# are conjecturing who will get sacked based on other employees' performance and find that it might be themselves. #Person1# is so anxious as #Person1# needs to raise family but #Person2# will be happy to leave.", "#Person1# and #Person2# talk about the company's upcoming downsizing and discuss who will get sacked. #Person1# is worried but #Person2# doesn't care.", "#Person1# and #Person2# have rounds of conjectures of whom shall be fired due to the company's cutback and comes to the conclusion that they both have the biggest possibility. It drives #Person1# crazy but doesn't worry #Person2#."], "prediction_text": "#Person1# and #Person2# are discussing the possibility of cutbacks at their company. #Person1# is worried about who will get sacked, and #Person2# suggests various colleagues who might be at risk, including Lisa, who is rumored to be having an affair with the boss. #Person1# is concerned about the impact on their family."}
{"answers": ["#Person1# and #Person2# start from one Chinese TV series and talk about short-lived marriage nowadays and views of marriage of only-child generation in China.", "#Person1# and #Person2# have a conversation about recent trends of Chinese marriage. They agree that the younger generation are taking marriage less seriously and will make hasty decisions.", "#Person1# and #Person2# have a discussion about the divorce rate in China and reasons hidden behind."], "prediction_text": "#Person1# and #Person2# are having a casual conversation about their lives, including their views on marriage. #Person1# mentions that the divorce rate has been increasing and that many young people in China tend to get married quickly and divorce even quicker. #Person2# agrees and adds that the only-child generation in China doesn't take marriage seriously and views it as something that will end once the passion is gone."}
{"answers": ["#Person1# and Judy are sorting out their cost to France. Since #Person1# thinks it's too much, they decide to choose the Youth hotel to save some money.", "Judy explains to #Person1# about the budget for a trip and tries to lower the cost by choosing a economic hotel.", "#Person1# and Judy have a discussion on their financial budget and find out ways to save money."], "prediction_text": "Judy and John are discussing the budget for their trip. Judy shows John the itinerary and explains that the total amount is 13,000 RMB, with transportation and hotel costs taking up a significant portion of the budget. John expresses concern about the quality of the hotel, but Judy suggests choosing the Youth Hotel, which is clean and economic."}
{"answers": ["Mary got married. #Person2# is surprised when told by #Person1#.", "#Person2# is surprised to hear from #Person1# that Mary got married.", "#Person1# shares Mary's marriage news with #Person2#. #Person2# is surprised."], "prediction_text": "Mary got married to Jerrod and will live in Paris."}
{"answers": ["#Person2# lost the wallet and borrows some money from #Person1#.", "#Person2#'s wallet was lost and has to borrow some money from #Person1#.", "#Person1# lends money to #Person2# as #Person2# lost the wallet."], "prediction_text": "#Person1# lends #Person2# $50 after #Person2# lost their wallet. #Person1# is going to buy books and go to the gas station."}
{"answers": ["#Person1# and #Person2# are talking about the news that two competing companies are going to merge into one.", "#Person2# learns from #Person1# that two major suppliers of their company decided to end competition and merge into one.", "#Person1# and #Person2# have a discussion on the merging of their major suppliers, Murphy Music and U-Tunes and its effect."], "prediction_text": "#Person1# and #Person2# are discussing the news of two major suppliers, Murphy Music and U-Tunes, merging into one company. #Person1# read it on the financial page and #Person2# doubts the news is true."}
{"answers": ["Joe and Tom hold different impressions on Tom's new assistant.", "Joe doesn't think highly of Tom's new assistant while Tom does.", "Joe disagrees with Tom on Tom's new assistant and thinks her stuck up."], "prediction_text": "Joe met #Person1#'s new assistant but found her stuck up, but #Person1# thinks she's helpful to everyone."}
{"answers": ["Sarah is upset and complains to #Person1# about an inefficient meeting and Bob's interruption. #Person1# gives Sarah some suggestions on how to keep her speech short and sweet.", "Sarah is upset because Bob kept interrupting everyone else during a meeting, making it impossible to elaborate her ideas. #Person1# gives Sarah a useful tip to get her point across at the next meeting.", "Sarah is upset for being interrupted by Bob in an meeting and is not able to put forward the points, to which #Person1# offers some useful tips."], "prediction_text": "Sarah is upset after a meeting where no one listened to her suggestions and Bob kept interrupting. #Person1# advises Sarah to keep things short and sweet during meetings and to bring up main points directly after the meeting."}
{"answers": ["#Person2# introduces one program about Islam to #Person1#. They are talking about the pilgrimages of Muslims and people in other faiths.", "#Person1# and #Person2# talk about pilgrims around the world, including Muslims' pilgrimage to mecca and Christians' pilgrimage to Canterbury or Vatican. #Person2# thinks faith heals people instead of magical places.", "#Person2# watches a program about Muslim pilgrims and talks about how pilgrims are like in other religion with #Person1#"], "prediction_text": "#Person1# asks #Person2# about a program about Islam and learns about the Haj pilgrimage, the sacred stone, and the Saudi government's efforts to limit the number of pilgrims. #Person1# also asks about other pilgrimages in different faiths and #Person2# explains about Lourdes and the beliefs about its healing powers. #Person1# expresses skepticism about the idea of a magical place."}
{"answers": ["Ted likes Jenny but is afraid to ask her out. Mike encourages Ted to tell Jenny his feelings.", "Ted likesJenny but is too afraid to express his feeling. Mike encourages him and Ted will tell her.", "Ted likes Jenny but is afraid of telling her because he doesn't want to be turned down. Mike encourages Ted to tell Jenny."], "prediction_text": "Mike wants to know if Jenny is coming with them, and Ted teases Mike about having a crush on Jenny. Ted encourages Mike to tell Jenny how he feels about her, but Mike is afraid of being rejected."}
{"answers": ["#Person1# and #Person2# are talking about ways to relieve anger.", "#Person2# introduces ways to relieve anger to #Person1#, including listening to music and working out.", "#Person1# and #Person2# have a discussion about what they usually do to relieve anger."], "prediction_text": "#Person1# asks #Person2# how they relieve their anger, and #Person2# suggests listening to music, working out, or taking a walk."}
{"answers": ["#Person2# is introducing to #Person1# their family members in New Zealand and their relationships.", "#Person2# introduces to #Person1# their family members in New Zealand who would visit them next year.", "#Person2# tells #Person1# about the relationships between their family and the uncle Bill's, who will visit them next year."], "prediction_text": "#Person1# asks #Person2# about family in New Zealand, including uncle Bill, his wife, and two daughters. #Person2# explains that uncle Bill is #Person1#'s mother's brother, and that Sarah and Jane are #Person1#'s cousins, although they are step-sisters. #Person2# also mentions that the family wants to travel to Europe next year and visit #Person1# at the same time."}
{"answers": ["#Person1# congratulates Mr. Stuart on winning a marathon.", "#Person1# congratulates Mr. Stuart on winning the city marathon.", "#Person1# congratulates Mr. Stuart on winning the city marathon."], "prediction_text": "#Person1# congratulates #Person2# on winning the city marathon and expresses pride and excitement."}
{"answers": ["#Person2# is from America and is picking up presents for families in a duty-free shop. #Person1# recommends some for #Person2#.", "#Person2# is surprised at a low price of products in the duty-free shop. #Person1# offers #Person2# suggestions on what gifts to bring home.", "#Person2# buys gifts for his children and wife in a duty-free shop where #Person1# is working at. #Person1# recommends presents for #Person2#."], "prediction_text": "#Person1# helps #Person2# find presents for his children and wife. #Person2# buys 5 pairs of sneakers and a bottle of perfume. #Person1# tells #Person2# that the perfume is by DENY, which is expensive in America but cheaper in the duty-free shop."}
{"answers": ["Hong suggests #Person1# use a local SIM card to save money to call.", "Hong tells #Person1# to buy a local SIM card to make a cheap phone call to the UK.", "#Person1# can't afford to call back to UK and Hong offers a cheap way to address the problem."], "prediction_text": "#Person1# wants to call back to the UK but can't afford the roaming charges. #Person2# suggests using a local SIM card and explains how to do it. #Person1# is pleased with the price."}
{"answers": ["Mr. Brown is interviewing #Person2# and they are talking over #Person2#'s salary.", "Mr. Brown gives #Person2# a well-paying job offer.", "Mr. Brown decides to hire #Person2# with a higher salary and other benefits."], "prediction_text": "#Person1# asks #Person2# about their salary and #Person2# tells #Person1# they are paid on a salary basis of 1,800 yuan per month. #Person1# offers #Person2# a salary of 2,500 yuan per month if they are hired."}
{"answers": ["#Person1# and #Person2# have the same impression on Barry and Paul, but hold different views on #Person2#'s personality.", "#Person1# and #Person2# change ideas on Barry and Paul, and then talk about their own personalities.", "#Person1# and #Person2# come to an agreement towards the impression of Barry and Paul but a disagreement on whether #Person2# is shy."], "prediction_text": "#Person1# and #Person2# are discussing their opinions on people they know, including Barry and Paul. #Person1# and #Person2# are not fans of Barry due to his ambition and dishonesty, but they do like Paul. #Person1# considers themselves to be polite, careful, relaxed, and outgoing, while #Person2# thinks they are not as shy as they think. #Person1# confirms they will be at #Person2#'s birthday party on Friday."}
{"answers": ["#Person1# is buying a house and consulting #Person2# about the location, size and surroundings.", "#Person2# will get back to #Person1# after knowing #Person1#'s specific needs for a house.", "#Person1# intends to buy a house and needs some information from #Person2#. #Person2# will call #Person2# after #Person2# finds one."], "prediction_text": "#Person1# is interested in buying a house in Pasadena or Arcadia and provides details about the desired house, including the number of bedrooms and bathrooms, school district, and view. #Person2# agrees to start a search and will call #Person1# in a few days."}
{"answers": ["#Person2# leaves the samples to Mr. Grant and will talk to him next week.", "#Person2# leaves Mr. Grant with samples and will call Mr. Grant later to know the decision.", "#Person2# leaves the samples at Mr. Grant's so he can make a decision with more consideration."], "prediction_text": "#Person1# is not in a position to make a decision and asks #Person2# to leave the samples with him and call him next week."}
{"answers": ["#Person2# tells #Person1# the bus route to get to Sons.", "#Person2# offers bus information for #Person1# to go to Sons from PHS.", "#Person1# asks #Person2# the way to Sons from PHS."], "prediction_text": "#Person1# wants to know if there is a bus that will take them from PHS to Sons, and #Person2# explains the bus routes that #Person1# needs to take."}
{"answers": ["Mr. Lee gives Mrs. Word a lift home.", "Mr. Lee gives Mrs. Word a lift home on a rainy night.", "Mr. Lee offers to give Mrs. Word a lift home on a terrible night."], "prediction_text": "Mr. Lee offers Mrs. Word a lift home and holds her umbrella while she gets her keys."}
{"answers": ["#Person1# checks the right arm of #Person2# and suggests a shoulder X-ray tomorrow.", "#Person1# gives #Person2# a physical check and advises #Person2# to stay in hospital for a shoulder X-ray the next day.", "#Person1# examines #Person2#'s arm by testing which part is hurt and suggests a shoulder X-ray tomorrow."], "prediction_text": "#Person1# is checking #Person2#'s arm and shoulder for pain. #Person2# feels pain in the shoulder and arm but not in the legs. #Person1# suggests that #Person2# stay in the hospital overnight for further examination."}
{"answers": ["#Person2# lost the credit card in travelling and is asking the Client Services for help.", "#Person2# lost an international credit card while travelling in the city and therefore calls #Person1# for help.", "#Person2# calls #Person1#, the IBA Client Services Department to report his lost credit card and asks for help."], "prediction_text": "Shelley from IBA's Client Services Department helps a lost tourist find their International Credit Card."}
{"answers": ["#Person1# is going to have a nap and #Person2# will start dinner at 6.00.", "#Person1# is tired and will take a nap and #Person2# gets the dinner ready.", "#Person1# is going to take a nap and #Person2# will let #Person1# wake up naturally."], "prediction_text": "#Person1# is going to take a nap and #Person2# suggests unplugging the phone and starting dinner at 6:00. #Person1# is too tired to dream about dinner."}
{"answers": ["#Person2# orders Chinese food and asks for two value meals.", "#Person2# takes orders of Chinese food. #Person2# is pleased with #Person1#'s service.", "#Person2# orders some Chinese food with the help of #Person1#."], "prediction_text": "#Person1# is a waiter at a Chinese restaurant. #Person2# orders Chinese food and asks for bean milk to drink. #Person1# answers questions and takes the order."}
{"answers": ["#Person1# and #Person2# are waiting for food.", "#Person1# and #Person2# are standing in line to buy food.", "#Person1# and #Person2# are waiting in line for food."], "prediction_text": "#Person1# and #Person2# are in a bakery. #Person1# wants to buy many cream cakes, but #Person2# doesn't like them."}
{"answers": ["#Person1# asks Lucy to sing but Lucy is unwilling to do so.", "#Person1# tries to persuade Lucy to sing but Lucy would rather just listen.", "#Person1# encourages Lucy to sing but Lucy just wants to listen."], "prediction_text": "Lucy doesn't want to sing, but #Person1# encourages her to sing. #Person2# wants to hear Stanley sing instead."}
{"answers": ["#Person2# thinks the function of movie theatres should be updated.", "#Person2# thinks the movie industry should be revolutionized to survive the threat from DVD industry.", "#Person1# and #Person2# talk about the possible destiny of movie theatre."], "prediction_text": "#Person1# asks if home video players will replace movie theatres, and #Person2# responds that the movie theatre should not just be a place to watch a film, but a place to meet people."}
{"answers": ["#Person2# wants to buy some clothes but #Person1#'s recommendation doesn't interest #Person2#.", "#Person1# gives #Person2# a few suggestions on clothes but none is taken.", "#Person2# is not happy with #Person1#'s recommendeds."], "prediction_text": "#Person1# shows #Person2# some dresses and #Person2# likes the white one."}
{"answers": ["Joshua is asking his dad for money and explains his plan with the money.", "#Person2# is annoyed when Joshua asks for the allowance, but is pleased to hear how Joshua will use the money.", "Joshua asks for allowance from his dad. #Person2# gives the money to Joshua from his secret money."], "prediction_text": "#Person1# reminds #Person2# that it is allowance day and asks how much he owes. #Person2# forgets and #Person1# tells him that he owes $13. #Person2# says he doesn't have that much money and #Person1# suggests looking under his bed for a secret money jar. #Person2# agrees to give #Person1# the money."}
{"answers": ["#Person1# and #Person2# are going to Mike's birthday party tomorrow.", "Both #Person1# and #Person2# were invited to Mike's birthday party. #Person2# will give #Person1# a ride.", "#Person2# will drive #Person1# to Mike's birthday."], "prediction_text": "#Person1# received an invitation to Mike's birthday party and asks #Person2# if he received one too. #Person2# tells #Person1# that Mike didn't tell him the party time, and #Person1# offers to call Mike to ask. #Person2# offers to give #Person1# a ride to the party."}
{"answers": ["#Person1# is sending a package with the help of #Person2#.", "#Person2# instructs #Person1# on how to send a package by mail.", "#Person1# sends a package at #Person2#'s and buys some stamps at the other window."], "prediction_text": "#Person1# wants to send a package by first-class mail and asks for insurance for $50. #Person2# tells #Person1# to get stamps at the stamp window and money orders at the money order window."}
{"answers": ["#Person1# and #Person2# are talking about natural disasters and Wenchuan earthquake in China. They feel the importance of life and love.", "#Person1# and #Person2# discuss the destructive impact that earthquakes have in Chinese history, and the assistance for the Wenchuan Earthquake.", "#Person1# and #Person2# exchange opinions toward the effect of earthquake and how people feel about it."], "prediction_text": "#Person1# and #Person2# talk about Typhoon and earthquake. #Person1# is glad that Typhoon is not as bad as earthquake, and #Person2# explains why China experiences frequent earthquakes due to the earth's plates knocking against each other. #Person1# and #Person2# also discuss the importance of saving lives during natural disasters."}
{"answers": ["#Person1# cannot stand rainy days, but #Person2#'s been used to it.", "#Person1# complains to #Person2# about the miserable weather, but #Person2# is used to it.", "#Person1# and #Person2# have a chat about the current rainy season which #Person1# can hardly endure while #Person2# has already been used to."], "prediction_text": "#Person1# and #Person2# are discussing the weather. #Person1# is unhappy with the rain and cold, while #Person2# is used to it and looks forward to spring."}
{"answers": ["#Person1# and #Person2# are discussing where to go after the volleyball match and talking about the souvenirs of the Olympic Games, such as the Olympic Mascots.", "#Person1# and #Person2# are talking about what gifts to buy for their families from the Olympic souvenir store after the volleyball match.", "#Person1# and #Person2# discuss where should they go to get gifts for their family after the match."], "prediction_text": "#Person1# and #Person2# are planning to go to the Olympic souvenir store after the volleyball match. #Person1# wants to buy gifts for their family and is interested in the Olympic Mascots, especially the red one called 'Huan Huan'. #Person2# tells #Person1# that the souvenirs are very hot and there are many other things to choose from, such as postcards, key chains, posters, and arts and crafts."}
{"answers": ["#Person1# is buying a pan in #Person2#'s shop and #Person2# gives #Person1# some suggestions.", "#Person1# buys a big, light pan with a wooden handle and a lid from #Person2#.", "#Person1# takes a size 16, aluminum pan with a lightweight wooden handle at #Person2#'s shop as #Person1# cooks big meals often."], "prediction_text": "#Person1# wants to buy a pan, and #Person2# shows him different sizes and materials. #Person1# chooses a lightweight pan with a wooden handle and asks about a lid."}
{"answers": ["#Person1# and #Person2# will meet after work.", "#Person2# will give #Person1# a ride after work.", "#Person1# and #Person2# go back to work and will meet after work."], "prediction_text": "#Person1# and #Person2# are saying their goodbyes before returning to work. #Person1# is hoping to catch a ride with #Person2#."}
{"answers": ["#Person1# visits Mr. Becker's office and Mr. Becker introduces #Person1# some facts of people working for the federal government.", "Mr. Becker shows #Person1# around the Washington office and explains bureaucrats' situation.", "Mr. Becker shows #Person1# around the office of the Washington bureaucrats and #Person1# has changed the image about the bureaucrats."], "prediction_text": "#Person1# visits Mr. Becker's office and finds it modern and comfortable. #Person2# explains that government service can be a rewarding and secure career, and that many positions pay salaries comparable to those in private industries. #Person1# asks how much Mr. Becker makes, and Mr. Becker replies that he is a GS-15, which is high on the scale of workers but even a GS-1 is paid a living wage."}
{"answers": ["#Person1# introduces Henry to Pete. The three persons talk about their previous experiences and life and decide to go to a bar after dinner.", "#Person1# introduces Henry Johnson, an expat in China to Pete. Mr. Johnson shares with Pete how much he enjoys his time in China, until #Person1# interrupts and tries to finalize the evening plan. The three decide to go have dinner and then to a bar district.", "#Person1# introduces Henry Johnson to Pete for they come from the same city and the latter two exchange ideas about Beijing, Karaoke particularly. The three decide to go have dinner and then to a bar district."], "prediction_text": "#Person1# introduces Henry Johnson to #Person2#, who is from the same city. #Person2# expresses his dislike of singing, but #Person3# enjoys it and has learned Chinese songs. #Person1# suggests having dinner and then going to a bar district, which has thousands of bars."}
{"answers": ["Julie got food poisoning last Sunday and Mr. Jones cares about her health and study.", "Julie explains to Mr. Jones that her absence of a week is due to food poisoning. Julie will make up for the missed lessons by learning the materials.", "Mr. Jones asks Julie the reasons Julie got absent for class the whole week and finds out Julie got food poisoning."], "prediction_text": "Julie has been ill since last Sunday and thinks it was food poisoning from a street food stand. She is feeling better now and is able to keep up with her lessons with the help of her classmates."}
{"answers": ["#Person1# is preparing for her friends' coming this afternoon. Her son Mike helps her to buy drinks and make coffee.", "Mike's mom is preparing for her few friends, Mike will go out to buy some drinks and fruits.", "To better host mom's friends, Mike helps his mother to get some food and drinks."], "prediction_text": "#Person1# needs help preparing for friends coming over and asks #Person2# to buy drinks and make coffee. #Person2# agrees and buys apples."}
{"answers": ["#Person2# comes to Berlin to visit #Person1#. They talk about #Person2#'s unpleasant journey on their way back to #Person1#'s apartment.", "#Person2# is awaiting #Person1# at a bus station. #Person2# chose bus over airplanes with the intention to help the environment, but didn't expect it would be such a terrible ride.", "To protect the environment, #Person2# took a bus instead of plane to Berlin to visit #Person1#, but #Person2# feels exhausted."], "prediction_text": "#Person1# and #Person2# meet at the station and go to #Person1#'s apartment. #Person2# talks about their long bus ride and how they prefer to take the bus to help the environment, but #Person1# thinks they should be more careful with their health."}
{"answers": ["#Person1# is interviewing #Person2# and asking #Person2# some information relating the job.", "#Person2# has a conversation with #Person1# about a job position. #Person2# is willing to work weekends, travel around, and is able to speak some foreign languages.", "#Person1# interviews #Person2# to see whether #Person2# fits in the job requirements."], "prediction_text": "#Person1# asks #Person2# about their experience and qualifications for the job, and #Person2# answers positively."}
{"answers": ["Amy called Jimmy to return his book yesterday but Jimmy was out for a lovely picnic. They will meet soon at a cafe.", "Jimmy didn't pick up a phone call from Amy because Jimmy was having a fun picnic. Amy will meet him later to return his book.", "Amy called Jimmy to return his book but Jimmy went to Beihai Park for picnic so Amy didn't reach him. They will meet later."], "prediction_text": "Amy calls Jimmy to give him back his book and asks if he is free to meet at a cafe. Jimmy says he is free in 30 minutes."}
{"answers": ["#Person1# invites Peter to have tea, but Peter is watering the garden. Then it rains, Peter can get in and have tea with #Person1#.", "#Person1# invites Peter to have tea, but Peter is busy watering the garden. Then it rains, so Peter stops and has tea with #Person1#.", "Peter stops watering the garden and will have tea with #Person1#, since it's raining."], "prediction_text": "#Person1# invites Peter for tea, but Peter declines and tells #Person1# that he must water the garden first. #Person1# reminds Peter that it is raining and suggests that he can have tea instead."}
{"answers": ["#Person1# asks #Person2# to scedule an emergency meeting for an important order when Ken gets back. #Person2# doesn't think he would be back today.", "#Person1# asks #Person2# to call an emergency meeting whether Ken comes back or not.", "#Person1# and #Person2# will call an emergency meeting today even #Person2# doesn't think Ken can get back."], "prediction_text": "#Person1# wants to call an emergency meeting about an order, and #Person2# agrees to send a memo around scheduling it for that afternoon."}
{"answers": ["#Person2# wants to send a letter. #Person1# says it's five grams overweight plus overnight so seven dollars in all.", "#Person1# helps #Person2# to buy stamps for #Person2#'s letter.", "#Person1# is helping #Person2# confirm the price for sending the letter."], "prediction_text": "#Person1# helps #Person2# buy stamps for a registered letter. #Person1# weighs the letter and tells #Person2# the cost, which is $7. #Person2# pays with a $10 bill and receives change."}
{"answers": ["Mr. Smith has a nasty infection. The doctor prescribes some antibiotics and some cream. Mr. Smith can buy them from the pharmacy with discounts.", "The doctor prescribes some medicine for Mr. Smith and tells him where to get it.", "The doctor prescribes some medicine and asks Mr. Smith to buy them to treat his infection."], "prediction_text": "#Person1# is a doctor and diagnoses #Person2# with an infection. #Person1# prescribes antibiotics and cream to ease the itching and burning and tells #Person2# to go to the pharmacy on the fourth floor to pick it up."}
{"answers": ["#Person2# wants to buy Chinese arts and crafts, something distinctively Chinese. #Person1# suggests #Person2# buy paper-cuts, embroidery and batik.", "#Person2# wants to buy Chinese arts and crafts, so #Person1# offers some suggestions.", "#Person1# is suggesting some Chinese arts and crafts to #Person2#. #Person2# is interested in embroidery."], "prediction_text": "#Person1# suggests paper-cuts, embroidery, and batik to #Person2# as they are easy to carry and distinctively Chinese."}
{"answers": ["#Person1# and #Person2# are visiting the Eiffel Tower, which was named after its designer Gustave Eiffel.", "#Person1# introduces the Eiffel Tower's history and height to #Person2#.", "#Person1# is introducing the Eiffel Tower to #Person2#."], "prediction_text": "#Person1# tells #Person2# about the Eiffel Tower, including its height and history."}
{"answers": ["Brian tells #Person1# that he learned English in college and he went to Las Vegas on a business trip.", "#Person1# asks Brian about his English and his life in the U.S.", "#Person1# compliments Brian on his English and asks Brian about his life in the U.S."], "prediction_text": "#Person1# asks Brian about his English skills and where he learned them. Brian replies that he learned in college and speaks well. #Person1# also asks Brian about his time in the U.S. and his wife, and Brian replies that he has been in the U.S. for 3 weeks and his wife arrived yesterday. #Person1# also asks Brian if he has been to California or Las Vegas before, and Brian replies that he has been to Las Vegas once on a business trip."}
{"answers": ["#Person2# is worried about one man, and #Person1# thinks that that man might be on the way home now.", "#Person2# is worried about a man, but #Person1# thinks it would be fine.", "#Person2# is worried about a man but #Person1# is not."], "prediction_text": "#Person1# is worried about someone who is late, and #Person2# is trying to reassure him that everything will be okay."}
{"answers": ["#Person1# warns #Person2# that they might get into trouble, but #Person2# doesn't agree with #Person1#'s warning.", "#Person1# warns #Person2# of the possible trouble in the future, but #Person2# doesn't mind.", "#Person1# is warning #Person2# not to do that or #Person2# will get into trouble, but #Person2# doesn't care."], "prediction_text": "#Person1# warns #Person2# that trouble may come from their actions, despite everything going smoothly."}
{"answers": ["#Person1# and #Person2# are talking about a perfect match between John and a girl.", "#Person1# and #Person2# talk about John's love life.", "#Person1# and #Person2# think that John and the girl are a perfect match."], "prediction_text": "#Person1# and #Person2# are talking about John and his girlfriend, with #Person1# suggesting that John might be falling in love with her."}
{"answers": ["#Person1# and #Person2# are planning the places of interest they are going to visit in London, such as Nelson's Column, Westminster Abbey, Tower of London, Madame Tussaud's Waxworks Museum, and so on. They are both looking forward to it.", "#Person1# and #Person2# talk about historic figures and buildings in London, including Nelson's Column, Westminster Abbey, famous female figures, Madame Tussaud's the waxworks museum and so on.", "#Person1# and #Person2# are talking about London's places of interest, including a monument, Westminster Abbey, the statue of Boadicea, the famous Tower of London, the famous castle and prison, and Madame Tussaud's the waxworks museum."], "prediction_text": "#Person1# and #Person2# are discussing the history of London, including Nelson's Column, Westminster Abbey, Boadicea's statue, and the Tower of London. #Person1# wants to visit the Tower of London and see the Crown Jewels, while #Person2# suggests visiting Madame Tussaud's to see historical figures."}
{"answers": ["#Person2# thought Daniel's favorite course would be PE, but actually he likes science most.", "Daniel is interested in science because he can learn more about the world through it.", "#Person2# is surprised that Daniel's favorite course is science and he is interested in it."], "prediction_text": "#Person1# likes science class the most among new courses added to school this semester, and #Person2# is surprised. #Person1# explains why he likes science class and #Person2# teases #Person1# about being a \"naughty boy\"."}
{"answers": ["#Person2# is holding a baby shower for #Person1# and there are many presents for #Person1#. #Person1#'s water broke.", "#Person2# helps #Person1# to open the presents for #Person1#'s baby at the baby shower. #Person1#'s water broke.", "#Person2# organized a great baby shower for #Person1#. #Person1# receives many gifts. #Person1#'s water broke."], "prediction_text": "#Person1# is having a baby shower and is grateful for the gifts. #Person2# opens the gifts and there are a highchair, car seat, playpen, and crib. #Person1#'s water breaks and #Person2# takes her to the hospital."}
{"answers": ["#Person1# wants to travel to China, but #Person1#'s mother is not available.", "#Person1# invites #Person2# to China, but she is too busy.", "#Person1# will go to China while #Person2# won't."], "prediction_text": "#Person1# wants to go to China for sight-seeing and asks #Person2# if they will go with them. #Person2# says no because they are too busy."}
{"answers": ["Tim was chatting with Karren, but now he has to go because it's getting late.", "Karren and Tim say goodbye.", "Tim is saying goodbye to Karren."], "prediction_text": "Tim is leaving and says it was nice meeting Karren."}
{"answers": ["#Person1# asks whether #Person2# tried Michael's new motorcycle. #Person2# says #Person2# likes it, but still #Person2# will buy a bicycle for safety.", "#Person2# shares #Person2#'s experience of riding Michael's motorcycle with #Person1#.", "#Person2# liked Michael's motorcycle but won't buy one."], "prediction_text": "#Person1# asks #Person2# if they went to see Michael last night, and #Person2# says they did. #Person1# asks if #Person2# rode Michael's new motorcycle and #Person2# says they did and liked it. #Person1# asks if #Person2# would like to buy a motorcycle themselves and #Person2# says no because they prefer bicycles."}
{"answers": ["#Person2# can speak Chinese, English and French. #Person2#'s English skill is enough to do general desk work and sufficient to work in an American firm.", "#Person1# asks #Person2# about #Person2#'s mastery of English and French. #Person2# says #Person2#'s English competence is sufficient to work in an American firm.", "#Person2# can speak three languages and do general desk work in English."], "prediction_text": "#Person1# asks #Person2# how many languages they can speak, and #Person2# replies they can speak English and French. #Person1# asks if they can read and write both languages, and #Person2# replies they can speak the language better than they read and write. #Person1# asks if they can communicate with people of English-speaking countries, and #Person2# replies they think so. #Person1# asks if they believe their English is good enough to do desk work, and #Person2# replies they believe their English is sufficient to work in an American firm."}
{"answers": ["Andy calls to find Naomi. Nancy will deliver the message that he has called.", "Andy calls but Naomi isn't there.", "Andy leaves a message to Naomi by phone."], "prediction_text": "#Person1# answers the phone and #Person2# identifies herself as Andy's friend. #Person1# takes the message and #Person2# thanks her before hanging up."}
{"answers": ["#Person1#'s flight to New York has been cancelled and he is anxious. #Person2# will help #Person1# to book a tomorrow's flight with 50 % discount.", "#Person1#'s flight is cancelled and he is annoyed, so #Person2# helps him book another flight and gives him a discount.", "#Person1# is very angry about the cancelled flight and he cannot get to New York today. #Person2# helps him book tomorrow's airline."], "prediction_text": "#Person1# is concerned about the cancellation of flight 513 and asks about booking on another airline. #Person2# tells #Person1# that there are many people trying to get to New York and offers a 50% discount as an apology."}
{"answers": ["#Person1# and #Person2# go to Burger Queen for lunch, but there's a long line and they have to wait. #Person2# is going to order French fries that #Person1# recommends.", "#Person1# invites #Person2# to have lunch at Burger Queen. They wait in line to order food, and #Person1# recommends some food to #Person2#.", "#Person1# and #Person2# will go to Burger Queen for lunch and eat cheese burgers and French fries. They will have to wait for good food."], "prediction_text": "#Person1# and #Person2# want to go to Burger Queen for lunch, and #Person1# recommends their cheese burgers and milkshakes. #Person2# agrees and mentions that their French fries are also good, but they are worried about eating too much fried food. #Person1# tells #Person2# to be patient and enjoy their meal."}
{"answers": ["#Person1# asks #Person2# to pay excess luggage charge.", "#Person1# will pays for the overweight luggage.", "#Person2# pays for #Person2#'s fragile luggage."], "prediction_text": "#Person1# tells #Person2# that their luggage is over 8 kilograms and #Person2# asks for a fragile label to avoid paying excess luggage charge."}
{"answers": ["#Person1# wants to drink a soda because of thirst. #Person2# suggests #Person1# drink water instead of soda when dehydrated.", "#Person1# is thirsty and #Person2# suggests #Person1# drink water instead of soda.", "#Person2# tells #Person1# soda doesn't quench the thirst. #Person1# will drink water as #Person2# suggests."], "prediction_text": "#Person1# is thirsty and asks #Person2# for something to drink. #Person2# suggests water instead of soda, explaining that soda is bad for you. #Person1# agrees to drink water."}
{"answers": ["Mr. Green has a meeting with Manager Liang tomorrow morning, but Mr. Green has to go abroad today. Wang Miao says Mr. Green will make another appointment.", "Wang Miao informs #Person1# of the change of Manager Liang and Mr. Green's appointment.", "Mr. Green has to cancel the appointment with Mr. Liang and will make another one later."], "prediction_text": "Wang Miao from AB company calls Manager Liang to inform him that Mr. Green, the CEO of AB company, has an urgent case and cannot keep his appointment with Manager Liang tomorrow. Wang Miao conveys Mr. Green's apologies and mentions that Mr. Green will make another appointment when Manager Liang returns."}
{"answers": ["#Person1# buys a pair of enameled leather shoes from #Person2#.", "#Person1# buys shoes from #Person2#.", "#Person1# will take the leather shoes."], "prediction_text": "#Person1# wants a pair of enameled leather shoes in size 30, but the heel is a little tight. #Person2# tells #Person1# that shoes will stretch with use. #Person1# decides to buy the shoes."}
{"answers": ["Benjamin has trouble writing a project report. #Person1# teaches him that a good report involves a final conclusion on his work of this year, experiences in dealing with some emergent events, plan for the next year, and the format.", "Benjamin hasn't finished his project report so #Person1# helps him. #Person1# tells him about the content and format of a report, and agrees to teach him how to work Microsoft Words.", "Benjamin hasn't finished a report involving a final conclusion of this year, future plans and more details. #Person1# will help him learn how to work Microsoft Words."], "prediction_text": "Benjamin is having trouble writing his project report and asks #Person1# for help. #Person1# explains the report must include a final conclusion and a plan for the next year, and that there is no fixed format for it. #Person1# offers to help Benjamin learn how to use Microsoft Word."}
{"answers": ["#Person2# calls Pizza House to deliver a thin crust seafood pizza to holyrood 9A.", "Marty helps #Person2# order a thin crust seafood pizza. The order will be delivered to holyrood 9A in thirty minutes or it's free.", "#Person2# orders a pizza delivery at Pizza House. Marty promises it will arrive in thirty minutes or it's free."], "prediction_text": "Marty takes an order from #Person2# for a medium pizza with pepperoni, olives, and extra cheese. #Person1# offers a two-for-one special on large pizzas and #Person2# declines. #Person2# orders a seafood pizza with a thin crust and gives Marty her address."}
{"answers": ["#Person1# is ordering dinner, but the wanted tomato soup is not being served today. #Person1# agrees to order egg soup as #Person2#'s suggestions.", "#Person2# helps #Person1# to order prawn cocktail, egg soup, steak, black coffee and mineral water.", "#Person1# orders some food for dinner with #Person2#'s help."], "prediction_text": "#Person1# orders dinner at a restaurant. #Person2# shows the menu and suggests an alternative to tomato soup. #Person1# agrees to try the egg soup and orders a glass of mineral water."}
{"answers": ["#Person1# is introducing a house to #Person2#, which has a living room, a dining room, a kitchen, two washrooms, and three bedrooms.", "#Person1# introduces the setting of the room to #Person2#.", "#Person1# is introducing a house to #Person2#."], "prediction_text": "#Person1# tells #Person2# about the house, including the living room, dining room, bedrooms, kitchen, and washrooms."}
{"answers": ["#Person2# claims #Person2# bought a personal stereo from #Person1#'s shop but the product has two problems that #Person2# is not happy with. #Person1# offers to exchange it for another one, but #Person2# has a problem with the receipt.", "#Person2# tells #Person1# there are problems with #Person2#'s personal stereo bought from #Person1#'s store, but #Person1# thinks it was #Person2#'s responsibility to check it. Finally #Person1# agrees to exchange it.", "#Person2# bought a personal stereo at #Person1#'s shop, but #Person2# found there was a scratch and other people could hear noise from it. #Person1# will exchange it for another model if #Person2# can provide the receipt."], "prediction_text": "#Person1# is at a store and #Person2# comes in to complain about a personal stereo they bought 3 days ago. #Person2# shows the scratch on the front and says they are not happy with the noise it makes. #Person1# offers to exchange it for another model if they are not happy with it. #Person2# says there is a problem with the receipt."}
{"answers": ["#Person1# and #Person2# are talking about the dormitory deposit for next year. #Person2# wants to live off campus because #Person2# wants a quiet place. #Person1# thinks it's too expensive compared with living in the school dorm room.", "#Person1# pushes #Person2# to pay for the dormitory soon, but #Person2# is thinking about living off campus. They analyze the advantages and disadvantages of living on and off campus.", "#Person2# is not sure whether to rent a house with a higher cost or live on campus with a lower cost."], "prediction_text": "#Person1# and #Person2# are talking about the housing office and the deadline for making dorm deposits. #Person2# is considering living off campus instead of staying in the dorm due to noise. #Person1# suggests studying in the library to save money."}
{"answers": ["#Person1# finds a note on a poetry book and thinks maybe it belongs to someone famous. #Person2# still thinks #Person2#'s is better.", "#Person1# speculates the signature on the book is valuable, but #Person2# thinks it is somewhat impractical.", "#Person1# will buy a book with a signature because #Person2# thinks it belongs to someone famous. #Person2# thinks it is somewhat impractical."], "prediction_text": "#Person1# and #Person2# are in a secondhand bookstore. #Person1# finds an old book of children's stories and shows it to #Person2#. #Person2# tells #Person1# that some of the books are not old, and #Person1# finds a book with a note from 1893. #Person1# wants to know if it's worth something and #Person2# is skeptical."}
{"answers": ["#Person2# is ordering food in Beijing Tasty Restaurant and #Person2# tells #Person1# to get the food ready in 20 minutes.", "#Person1# helps #Person2# to find a table in the centre and order some food at Beijing Tasty Restaurant.", "#Person2# comes to a restaurant and orders some food, waiting for a friend."], "prediction_text": "#Person1# greets #Person2# at Beijing Tasty Restaurant and shows them to their table. #Person2# orders food and asks for 20 minutes to wait for their friend."}
{"answers": ["#Person1# will send #Person1#'s son an important certificate by certified mail. And #Person2# suggests #Person1# send a watch by registered mail.", "#Person2# suggests #Person1# send a certificate by certified mail and a watch by registered mail.", "#Person1# will send a certificate by certified mail and a watch by registered mail."], "prediction_text": "#Person1# wants to make sure a letter with an important certificate reaches his son, and #Person2# suggests sending it by certified or registered mail."}
{"answers": ["Sally is reading a letter from Tom to #Person1#. Tom invites the new couple to visit him.", "Sally reads the letter from Tom to #Person1#. The letter invites them to visit Tom.", "Tom writes to Sally and John and asks them to call him when they arrive in town."], "prediction_text": "#Person1# gives #Person2# a letter from Tom. The letter is a short note in reply to their letter and Tom is happy to hear that they will be visiting him in January."}
{"answers": ["#Person1# has difficulty getting access to the computers in the library to do #Person1#'s assignment.", "#Person1# and #Person2# talk about the difficulty of not having a personal computer.", "#Person1# is frustrated, having no access to the computers."], "prediction_text": "#Person1# is having trouble accessing the library's computers for an assignment, and #Person2# sympathizes with them."}
{"answers": ["#Person2# wants to have a picnic this weekend. #Person1# says it's going to rain, but #Person2# doesn't believe it.", "#Person1# and #Person2# talk about the weather and #Person2# invites #Person1# to have a picnic.", "#Person1# and #Person2#'d have a picnic this weekend but #Person1# is afraid of the rainy weather."], "prediction_text": "#Person1# and #Person2# are having a conversation about the weather. #Person1# likes autumn, while #Person2# prefers summer. They plan to have a picnic the following weekend, despite the weather forecast."}
{"answers": ["#Person1# wants to find some general information on computers. #Person2# will show #Person1# how to find the magazines dealing with computers.", "#Person2# helps #Person1# to find some general information on computers.", "#Person2# is helping #Person1# to find some general information on computers."], "prediction_text": "#Person1# goes to the Reference Desk and asks for general information on computers. #Person2# shows #Person1# the magazines dealing with computers."}
{"answers": ["#Person2# is at French Garden Restaurant and he orders a bottle of water, a tuna fish sandwich and a bowl of vegetable soup with the help of #Person3#.", "#Person1# helps #Person2# find a seat and #Person3# takes #Person2#'s order.", "#Person2# orders some food with #Person3#'s help in a restaurant."], "prediction_text": "#Person1# greets #Person2# at the French Garden Restaurant and seats them. #Person3# takes their order and #Person2# orders a bottle of water and a tuna fish sandwich and a bowl of vegetable soup."}
{"answers": ["#Person2# orders a glass of lemonade, barbeque wings, and baby-back ribs.", "#Person1# helps #Person2# to order a drink and some food.", "#Person2# orders some food and a drink with #Person1#'s help."], "prediction_text": "#Person1# offers to get #Person2# a drink and asks if they want an appetizer. #Person2# orders barbeque wings and is ready to order their food."}
{"answers": ["#Person2# wants to have his coffee with his dinner and his order for dessert later.", "#Person1# serves #Person2# at dinner.", "#Person2# orders some water to drink and will have his coffee with dinner."], "prediction_text": "#Person1# offers #Person2# water and asks if they would like their coffee with dinner or later. #Person2# requests their coffee with their dinner and asks #Person1# to take their order for dessert later."}
{"answers": ["#Person1# and #Person2# are playing burning Questions.", "#Person1# and #Person2# ask and answer each other's questions about love life and future in turn.", "#Person1# and #Person2# are asking each other some imaginary questions."], "prediction_text": "#Person1# and #Person2# are asking each other questions. #Person1# would go on a trip around the world if they won the lottery, and #Person2# would go with them. #Person1#'s mom would faint if they told her they were getting married."}
{"answers": ["#Person1# shows Jack a picture of #Person1#'s new puppy.", "#Person1# shows Jack a picture of #Person1#'s new puppy.", "#Person1# is showing a picture of a puppy to Jack."], "prediction_text": "#Person1# shows #Person2# a picture of their new puppy and they chat about how cute the puppy is."}
{"answers": ["#Person1# is begging #Person2# to buy a new bike for #Person1#.", "#Person1# wants a new bike from #Person2#, but she refuses. #Person1# promises to buy a plane for #Person2#.", "#Person1# is envious that Tom has a bike much better than #Person1#'s. #Person1# begs #Person2# for one."], "prediction_text": "#Person1# tells #Person2# that Tom has a new bike and is showing off, while #Person2# mentions that they have to wait for the bus every morning and #Person1# replies that they will buy a car one day and a plane for #Person2#."}
{"answers": ["#Person2# likes a skirt at #Person1#'s store, but thinks it too expensive.", "#Person1# shows #Person2# a skirt. #Person2# likes its color but thinks it's ridiculously expensive.", "#Person2# likes a skirt but it is ridiculously expensive."], "prediction_text": "#Person1# shows #Person2# a skirt and #Person2# wants to try it on. #Person1# allows it and #Person2# says the price is too high."}
{"answers": ["#Person2#'s city was just a small insignificant village 200 years ago. It then grew into a key industrial centre for large deposits of coal and has absorbed many villages nearby.", "#Person2# explains how the city grew into a large place by selling coal and absorbing villages, and introduced old buildings to #Person1#.", "#Person1# and #Person2# are talking about the history of how #Person2#'s city gets into an industrial centre."], "prediction_text": "#Person1# asks #Person2# if their city is a historical place, and #Person2# explains that it wasn't always a large city, but grew due to coal deposits and became an industrial center. #Person1# asks if there are any buildings older than 200 years, and #Person2# confirms that there are several old buildings from the villages that still exist, including pubs and a castle."}
{"answers": ["#Person1# and #Person2# are concerned about environmental problems. #Person1# thinks that developing countries care more about economic development than environmental protection. #Person2# would like to join an organization committed to protecting the environment.", "#Person1# and #Person2# talk about the current environmental problems in the world. They analyze the world leaders' attitudes and actions towards these problems and discuss what they could do to solve them.", "#Person1# and #Person2# are talking about environmental problems in the world today. They think everyone is looking at the issue in the short term and damaging the environment leads to conflict between people."], "prediction_text": "#Person1# and #Person2# discuss environmental problems and their impact on the world. #Person1# suggests joining an organization to help protect the environment, and #Person2# expresses interest in doing so."}
{"answers": ["Dennis pretended to be a girl in online chat rooms. He's going to meet a guy who thinks he is a girl on Friday night. #Person2# suggests that he might be beaten.", "Dennis tells #Person2# he pretended to be a young girl to chat, and he found it effective, but #Person2# thinks he is sick.", "Dennis has been pretending as a girl and talking to people in chat rooms. He'll meet a guy who thinks him is Miss Right. #Person2# thinks Dennis might get beaten."], "prediction_text": "#Person1# pretends to be a 16-year-old girl in chat rooms and has over 100 people wanting to talk to her. #Person2# is concerned about #Person1#'s online friend who thinks #Person1# is a girl."}
{"answers": ["Nathan is going to Chicago for his practice in Chicago Tribune. His mother is worried about him and keeps asking him questions about this internship. Nathan says he'll work with a regular writer to get experience and she's finally at ease.", "Nathan's mum asks him about his coming internship in Chicago and feels worried about him, but Nathan is confident and positive. Nathan comforts her that he would be fine.", "Nathan is going to Chicago for practice and he is confident that he will do a good job and learn from others. Though worried at first, his mom thinks it's a great opportunity for him."], "prediction_text": "Nathan is almost ready to go to Chicago for his summer practice and will be missed by his mom. Nathan is excited about his summer and is not worried about working for the Chicago Tribune, as he is not afraid of hard work and is social. Nathan's mom is worried about him being stuck in a backroom and not getting any on the job training."}
{"answers": ["#Person2# wants to rent a silver Toyota Carola for 3 days. #Person1# helps #Person2# go through the procedure and notifies #Person2# of return time.", "#Person2# rents a car from #Person1#. #Person1# helps #Person1# go through the formalities and tells #Person1# the return time.", "#Person2#'d like to rent a silver Toyota Carola and #Person1# helps #Person2# go through procedures."], "prediction_text": "#Person1# helps #Person2# rent a Toyota Carola for 3 days. #Person2# has never rented a car before and chooses the silver car. #Person1# asks for #Person2#'s ID card and copies it, then gives the keys to #Person2#. #Person2# asks about the return time and #Person1# tells her to bring the car back by noon on the third day."}
{"answers": ["#Person1# will have a business trip to New York. #Person2# suggests #Person1# visit the UN building, Broadway, Columbia University, and New York University.", "#Person2# recommends some scenic spots and universities in New York to #Person1#. #Person2# also offers #Person1# suggestions to find more information.", "#Person1# is going to New York. #Person2# introduces some scenic spots and universities to #Person1#."], "prediction_text": "#Person1# is going to New York to sign a business contract and wants to know what to see while there. #Person2# suggests visiting the UN building and Broadway, and also recommends Columbia University and New York University as excellent schools in the city. #Person1# asks for a map of the city and is told to stop at the visitor's office near the train station for more information."}
{"answers": ["#Person2# will develop #Person1#'s roll of film in six hours.", "#Person2# will spend six hours developing #Person1#'s roll of film.", "#Person2# can develop #Person1#'s roll of film in six hours."], "prediction_text": "#Person1# asks #Person2# if they can develop a roll of film, and #Person2# replies that it takes 6 hours, but they can do it in 3 hours if needed. #Person1# decides to come back the next day to have it developed."}
{"answers": ["#Person1#'s flight has been delayed because of heavy rain and #Person2# suggests #Person1# listen to the announcement for further information.", "#Person1# inquires the reason and the extent of the flight delay. #Person2# can't tell because of the changeable weather.", "#Person1#'s flight is delayed. #Person2# can't tell further information about the delay because of the changeable weather."], "prediction_text": "#Person1#'s flight has been delayed due to heavy rain, and #Person2# tells #Person1# the extent of the delay and that it is hard to say when the rain will stop."}
{"answers": ["#Person1# wants to go to Peking University. #Person2# suggests asking the policeman.", "#Person2# suggests #Person1# ask the policeman the way to Peking University.", "#Person1# asks #Person2# how to get to Peking University. #Person2# suggests asking the policeman."], "prediction_text": "#Person1# asks #Person2# where Peking University is on a map and how to get there, but #Person2# is unable to help and suggests asking a policeman instead."}
{"answers": ["#Person1# keeps asking what the paper says. #Person2# asks #Person1# to read the paper.", "#Person1# asks #Person2# what the paper says. #Person2# asks #Person1# to read the paper.", "#Person2# is tired of #Person1#'s questions about the paper. #Person2# asks #Person1# to read the paper."], "prediction_text": "#Person1# asks #Person2# about the news, weather, and sports. #Person2# provides the information."}
{"answers": ["#Person1# thinks computer games are violent but #Person2# doesn't agree.", "#Person1# and #Person2# have different ideas on computer games.", "#Person1# hates computer games for its violence and bad effect but #Person2# disagrees."], "prediction_text": "#Person1# hates computer games because they are too violent, while #Person2# thinks they are fine and can be educational for boys."}
{"answers": ["#Person1# suggests having beers after dinner. Jim refuses. They decide to go to the gym to meet their friends.", "#Person1# feels bored at home and asks Jim go for a beer. Jim refuses and suggests going to the gym and meeting friends.", "Jim suggests #Person1# go to the gym to meet friends instead of drinking beers after dinner."], "prediction_text": "#Person1# and #Person2# are deciding what to do after dinner. #Person1# suggests going for beers, but #Person2# thinks it's not good for their fitness. #Person1# agrees and suggests going for a walk to the gym to play songs and meet friends."}
{"answers": ["#Person1# and #Person2# decide to order chicken feet and wine.", "#Person1# and #Person2# are ordering food and drinks.", "#Person1# and #Person2# order chicken feet and wine."], "prediction_text": "#Person1# and #Person2# go to a restaurant and decide to order the steamed prawns and the chicken feet. #Person1# prefers wine, so they order a bottle of the house red."}
{"answers": ["#Person1# wants to know #Person2#'s experience in guiding.", "#Person1# asks #Person2# about experience in guiding .", "#Person2# tells #Person1# #Person2# has few guiding experiences."], "prediction_text": "#Person1# asks #Person2# if they have any experience guiding, and #Person2# replies that they have some experience guiding foreign tourists in China, including showing them around places of interest such as Huangguoshu Falls."}
{"answers": ["#Person1# confirms Jack's reservation for a double for three nights. Jack checks in.", "Jack checks in at #Person1#'s hotel.", "Jack has a hotel reservation and #Person1# helps him check in."], "prediction_text": "#Person1# helps #Person2# fill out a form for a reservation. #Person2# confirms the reservation details."}
{"answers": ["Mark's absence was the result of a hangover. Bill's wife had a baby girl last night. Mark and Jill decide to celebrate it tomorrow.", "Mark did't come in because of a hangover. Jill calls him and tells him news about Bill's newborn daugther. They decide to celebrate it tomorrow.", "Mark didn't come in today because of a terrible hangover. Jill and he will celebrate the birth of Bill's new baby tomorrow."], "prediction_text": "Jill calls Mark to ask why he didn't come to work, and Mark explains that he had a birthday party last night and is hungover. Jill tells Mark that Bill's wife had a baby girl and passed out cigars to everyone at the office, and Mark says he will try to make it to the celebration the next day."}
{"answers": ["#Person1# wants to invite #Person2# out. #Person2# refuses.", "#Person2# refuses #Person1# 's invitation to go out.", "#Person2# refuses #Person1#'s invitations to go out."], "prediction_text": "#Person1# invites #Person2# to go out, but #Person2# declines and explains that her husband would not approve."}
{"answers": ["#Person1# and #Person2# both enjoy watching the baseball game in a great place with great fans.", "#Person1# and #Person2# watch a baseball game with great fans in a comfortable place.", "#Person1# and #Person2# are watching a baseball game with great fans in a great place."], "prediction_text": "#Person1# and #Person2# are at a baseball game. #Person1# asks about the score and #Person2# tells him that Baltimore is winning 5-4 after Cal Ripen hit a home run. #Person1# agrees that it is a great place to watch a game and #Person2# agrees."}
{"answers": ["#Person2# thanks #Person1# for #Person1#'s help.", "#Person2# appreciates for #Person1# 's kind help.", "#Person2# appreciates #Person1#'s help."], "prediction_text": "#Person1# helps #Person2# with something and #Person2# thanks #Person1# for their help."}
{"answers": ["#Person2#'s country exploits, exports, and imports natural resources. #Person1# thinks the government in #Person2#'s country should invests in infrastructure. #Person2# agrees.", "#Person2# talks about #Person2#'s country's importing and exporting natural resources and the new-found deposits of precious stone. #Person1# thinks the government in #Person2#'s country should invest in infrastructure. #Person2# agrees.", "#Person1# and #Person2# talk about exported and imported natural resources in #Person2#'s country. #Person1# thinks the government in #Person2#'s country should invest in infrastructure. #Person2# agrees."], "prediction_text": "#Person1# asks #Person2# about the country's natural resources and #Person2# explains that the country exports coal, copper, and iron, but imports oil, iron, and aluminium. #Person1# also mentions the discovery of precious stones and the importance of investing in infrastructure."}
{"answers": ["#Person1# is going to an exhibition tomorrow. #Person2# knows little about art or sculpture and decides to go with #Person1#. They will meet at bus stop at 12:30.", "#Person2# cannot understand abstract art so #Person2# decides to go to an art exhibition of greek and roman sculpture with #Person1# tomorrow.", "#Person2# who hardly knows anything about art or sculpture will go to an exhibition with #Person1# tomorrow. They will meet at bus stop at 12:30."], "prediction_text": "#Person1# loves going to art galleries, particularly when they are holding an exhibition of abstract art. #Person2# is not an art aficionado and finds it hard to understand the meaning of some paintings. #Person1# is going to an exhibition of Greek and Roman sculpture the next day and invites #Person2# to join them."}
{"answers": ["#Person1# helps #Person2# to return a book and check out a video in the library.", "with #Person1#'s help, #Person2# returns a book and checks out a video in the library.", "#Person1# is helping #Person2# return a book and check out a video in the library."], "prediction_text": "#Person1# helps #Person2# return a book and check out a video. #Person1# reminds #Person2# to take proper care of the video so they won't be fined if they damage it."}
{"answers": ["#Person1# teaches #Person2# the rules and terms of bowling.", "#Person1# explains the rules of bowling game to #Person2#.", "#Person1# is telling #Person2# how to play bowling."], "prediction_text": "#Person1# asks #Person2# if they have played bowling before and #Person2# wants to know more about it before playing."}
{"answers": ["#Person2# wants to know about a new financial program. #Person1# can provide a tailored consultation service for him over the phone or Internet.", "#Person2# consults the #Person1# about the transfer service and additional benefits for stock investors. #Person1# says a tailored consultation service can be provided over the phone or Internet.", "#Person2# asks #Person1# about a new service and additional benefits as a stock investor. #Person1# can provide an online tailored consultation service."], "prediction_text": "#Person1# and #Person2# are at a bank. #Person2# is interested in the new service that allows transferring money between banks and securities companies. #Person1# explains the program and tells #Person2# that investors can get additional benefits and that the consultation service is available over the phone or internet."}
{"answers": ["#Person1# would like to store his luggage in #Person2#'s place to hit some places, but there is a deposit. He only has 30 minutes to consider.", "#Person1# wants to hit some place, but he hesitates about lefting the luggage in #Person2#'s place because of an extra deposit.", "#Person1# wants to hit some place. So he needs leaving the luggage in #Person2#'s place, but there is a deposit. He only has 30 minutes to consider."], "prediction_text": "#Person1# wants to leave his luggage at the hotel, but #Person2# tells him there is a small charge and a deposit required. #Person1# is hesitant but #Person2# assures him that presenting a VISA will cover the deposit."}
{"answers": ["#Person1# wants to know the charge at #Person2#'s buffet party.", "#Person1# asks the price of the buffet and the charges for drinks.", "#Person2# tells #Person1# the charge policy at #Person2#'s buffet."], "prediction_text": "#Person1# asks how many people are in #Person2#'s party and how much it costs for the buffet. #Person1# tells #Person2# to go to the tables for cold dishes and vegetables and the hot dishes are on the other side. #Person2# asks if there are any extra charges for drinks."}
{"answers": ["#Person2# wants to inquire about a loan for studying abroad. #Person1# explains the policy and age limitations to #Person2#. #Person2# will decide later.", "#Person2# wants to inquire about a loan for overseas study and asks #Person1# the requirements of the application. #Person2# will decide later.", "#Person2# asks #Person1# information about personal loans for studying abroad. #Person2# fits the requirements nicely and will decide later."], "prediction_text": "#Person1# explains the Personal Loan for Studying Abroad to #Person2#, who is interested in applying but unsure if they are eligible. #Person1# explains that the loan can be taken out by the person studying abroad themselves, or their direct relative or spouse, and there are no age limitations."}
{"answers": ["#Person2# gives a book with English songs to #Person1#.", "#Person2# gives #Person1# a book which has English songs.", "#Person2# gives #Person1# a book with English songs."], "prediction_text": "#Person1# asks #Person2# for English songs, and #Person2# tells #Person1# that they are in a book and will need a few minutes to find them."}
{"answers": ["#Person1# asks about #Person2#'s writing experience.", "#Person2# tells #Person1# #Person2#'s writing experience.", "#Person1# is interviewing #Person2#. #Person2# tells some writing experience."], "prediction_text": "#Person1# meets with #Person2# and is impressed with their writing experience. #Person2# explains why they are interested in the position at their paper."}
{"answers": ["#Person1# wants to know the result of a interview. Jack suggests writing a polite and brief email to the company.", "#Person1# wants to know the interview results. #Person2# suggests writing an email that is polite and brief. And #Person1# should pay attention to the reply.", "#Person1# is asking Jack how to write a letter of inquiry. Jack suggests writing it politely and briefly, and paying attention to the reply."], "prediction_text": "#Person1# wants to ask for the interview results and decides to write a letter of inquiry to the company. #Person2# suggests sending an email and advises #Person1# to be brief and pay attention to any replies."}
{"answers": ["#Person1# praises #Person2#'s spacious courtyard and asks about why corn ears are hanging on the tree. #Person2# explains.", "#Person2# shows #Person2#'s house and courtyard to #Person1# and introduces the corn ears hunging on the tree.", "#Person1# is visiting #Person2#'s spacious house. #Person1# finds it interesting that corn ears are hanging on the tree branch."], "prediction_text": "#Person1# and #Person2# are at #Person2#'s house, and #Person1# wants to take a look around. #Person2# shows #Person1# the courtyard and explains the reason for hanging corn ears in a tree."}
{"answers": ["#Person1# wants to store her luggage when visiting New York, but there is a deposit. #Person2# reminds her there's little time to think about.", "#Person1# wants to leave the luggage at the hotel when visiting New York but wants to think about for a second because of deposit.", "#Person1# wants to leave the baggage at the hotel but is told she has to pay a deposit. So she wants to think about for a second."], "prediction_text": "#Person1# is checking out of the hotel and wants to know what to do with her baggage while she visits more tourist spots. #Person2# suggests leaving the baggage in storage with a deposit, but #Person1# is hesitant."}
{"answers": ["Brandon is signing up for a website to improve his writing skills. #Person1# thinks this website is stealing personal information and tries to stop Brandon. But Brandon doesn't believe.", "Brandon is signing up for a free website to improve writing skills but it requires personal information. #Person1# thinks the website cannot be trusted but Brandon doesn't believe so. Therefore, #Person1# shuts down Brandon's computer to stop him.", "Brandon is signing up for a free website that helps people improve their writing skills. #Person1# thinks it's fishy to collect for people's credit card information, and stops Brandon."], "prediction_text": "#Person1# is skeptical of a new Web site that asks for personal information to improve one's writing skills, and #Person2# tries to convince #Person1# that it is trustworthy."}
{"answers": ["#Person1# and #Person2# think their old lab building cannot satisfy their need, but they believe their headmaster will solve it.", "#Person1# and #Person2# talk about the possibility of building a new lab building and believe the headmaster will ask the local government for help.", "#Person1# and #Person2# think their headmaster may plan to build a new lab building, and will ask the local government for financial support."], "prediction_text": "#Person1# and #Person2# are discussing the possibility of building a new lab building at their school. #Person1# thinks it's necessary, but #Person2# thinks there are financial problems that need to be solved first."}
{"answers": ["Kate is happy to use the credit card because it makes it easy to spend money. Henry reminds her to be careful and pay the money back.", "Kate is happy with the credit card which as it makes it easy to spend money. Henry warns about the safety and repayment problem.", "Kate starts to use the credit card which makes it easy to spend money. Henry reminds her to be careful and pay the money back."], "prediction_text": "Kate is happy she started carrying the credit card given by the bank, and Henry reminds Kate to be careful with the card and the bank's limit of $1500."}
{"answers": ["#Person1# asks Mr. Smith for arrangements of tomorrow. Mr. Smith decides to visit a village in the mountains.", "#Person1# introduces two different villages to visit and Mr. Smith decides to visit the village in the mountains.", "#Person1# is asking which village Mr. Smith wants to visit so they can make arrangements. Mr. Smith chooses the village in the mountains."], "prediction_text": "#Person1# tells #Person2# about a visit to a village planned for the next day and asks for their preference. #Person2# prefers the village in the mountains and #Person1# tells them they will have to get up early to get there."}
{"answers": ["#Person1# interviews Vet, a high school girl who has become a mother. She shows her love to her daughter and talks about her future plans and gives advice to teens.", "Vet tells her own experience as a young mother and how her daughter changes her. Then she tells #Person1# her future goals and dreams. And she also gives teens advice.", "#Person1# is interviewing Vet and asking her about the ways she deals with stress as a young mother, feelings of having a baby, plans for the future, her favorite quote, and advice for teens."], "prediction_text": "#Person1# and #Person2# talk about how they deal with stress and depression, how having a baby has affected their goals and dreams, their favorite part of having a daughter, their future plans and goals, and their favorite quote. #Person2# also gives advice to teens."}
{"answers": ["#Person2# talks about #Person2#'s hobby of collecting stamps. And they all agree that stamps collecting gives more pleasure than becoming wealthy.", "#Person2# describes #Person2#'s own stamp collecting hobby and the first postage stamps to #Person1#. They all agree that stamps collecting gives much pleasure than becoming wealthy.", "#Person2# talks about #Person2#'s interest in collecting stamps and the first postage stamps with #Person1#. It gives #Person2# much pleasure. They all agree that stamps collecting gives much pleasure than becoming wealthy."], "prediction_text": "#Person1# asks #Person2# when they started collecting stamps and #Person2# replies that they were ten years old. #Person1# finds it interesting and #Person2# tells them about the history of postage stamps and shows them a picture of the first two stamps issued. #Person1# believes that collecting stamps brings happiness that not all wealthy people can enjoy."}
{"answers": ["#Person1# interviews Tom about his experience to start a company selling green products, how to live a mildly green life and to learn from mistakes.", "#Person1# inquires Tom about his successful business experience and his own green life and how to learn from mistakes.", "#Person1# is interviewing Tom about how he had his own company, how he found the money, what aspects of his life are green, and if he has done something wasteful."], "prediction_text": "Tom, a successful businessman, talks about his journey to becoming a businessman and how he started his own company. He also discusses the green aspects of his life and how he learned from his mistakes."}
{"answers": ["Detective Chief Superintendent John Day is trying to figure out the murdered man's movements in the short gap between he left the club and got murdered, but he only has three witnesses and hasn't been able to identify more.", "John Day describes known details of a murder case which happened in a pedestrian subway at midnight and answers #Person1#'s questions to tell what he know about the case. But he hasn't be able to identify more, even he knows three witnesses.", "John Day is telling #Person1# what they've known of the dead man's movements in the early hours of the morning, and there may be three witnesses, two girls and a man, but he hasn't been able to identify more."], "prediction_text": "Detective Chief Superintendent John Day explains to #Person1# that a man in his forties was found dead in a pedestrian subway in Neasden, and the investigation is ongoing. #Person2# tells #Person1# that the man left a drinking club in Neasden Lane at 1:30 a.m. and was found dead at 2:15 a.m., with two girls and a drunk man seen in the area. #Person2# cannot identify the people seen in the area at this stage."}
{"answers": ["Sherry is going to Quebec for a friend's wedding and sightseeing. Bob tells her some basic knowledge of Quebec, describes the beautiful scenery, and suggests practicing French.", "Sherry will attend a wedding in Montreal and wants to go sightseeing in nearby cities. Bob gives her some basic knowledge about the cities and Sherry cannot wait to go.", "Sherry will go to Montreal, Quebec next summer. Bob suggests she practice French since Montreal is the third largest French-speaking city. Sherry will also visit a friend in Quebec City."], "prediction_text": "Sherry plans to visit Quebec next summer for a friend's wedding and sightseeing. Bob corrects her that Quebec City is not the capital of the province, but the capital is Quebec City. Bob also advises Sherry to practice her French before going to Montreal, which is the third largest French-speaking city in the world."}
{"answers": ["Tom suggests going swimming, but Jane refuses. They decide to meet at the Grill to eat and then go to study together in the library.", "Tom suggests going swimming but Jane refuses beacuse of her paper due. After discussion, Jane finnally agrees to have dinner with him and then go to study together in the library.", "Toms invites Jane to go swimming but Jane refuses because of a paper due. At last, Jane accepts his suggestion to have dinner and then study together in the library."], "prediction_text": "Tom wants to go swimming at the Student Center with Jane, but Jane has a paper due and can't spare the time. Tom suggests going to the Grill for dinner instead, and Jane agrees to meet him there at 6 pm."}
{"answers": ["#Person2# suggests that #Person1# should listen to the doctor.", "#Person2# suggests #Person1# follow the medical instruction.", "#Person2# suggests that #Person1# should follow the doctor's instruction."], "prediction_text": "#Person1# is experiencing back pain and #Person2# suggests taking the medicine three times a day as directed."}
{"answers": ["#Person1# borrows 200 yuan from Fred and will give it back when having money.", "#Person1# borrows 200 yuan from Fred to pay the mortgage of a computer and will pay back when having money.", "Fred lends #Person1# 200 yuan to pay #Person1#'s mortgage. #Person1# will pay back when having money."], "prediction_text": "#Person1# needs $200 to pay the mortgage and asks #Person2# for a loan. #Person2# lends #Person1# $200 and #Person1# promises to pay it back."}
{"answers": ["#Person1# wants to improve handwriting but can't see the progress. #Person2# tells #Person1# the most important is perseverance.", "#Person2# encourages #Person1# to persevere with handwriting practice when #Person1# complains about little improvement in handwriting.", "#Person2# encourages #Person1# to keep practice handwriting, even though #Person1# hasn't got too much improvement now."], "prediction_text": "#Person1# is frustrated with their handwriting and #Person2# encourages them to be patient and persistent in practicing. #Person1# agrees to stop complaining and stick to it every day."}
{"answers": ["#Person2# is curling eyelashes. #Person1# feels dangerous, but #Person2# doesn't think so.", "#Person1# is afraid that eyelash curler may hurt #Person2#'s eyes.", "#Person1# thinks the eyelash curler is very dangerous, but #Person2# doesn't think so."], "prediction_text": "#Person1# is skeptical of #Person2#'s eyelash curling method and thinks it looks painful."}
{"answers": ["#Person1# introduces the weekend driving courses to #Person2#. #Person2# feels satisfied with its settings.", "#Person2# consults #Person1# about the training time, training chances and coach's experience of the driving courses. #Person2# thinks weekend courses are good.", "#Person2# is asking #Person1# some information about the driving courses. #Person2# thinks weekend courses are good."], "prediction_text": "#Person1# is asked about the driving courses and #Person2# is interested in the weekend courses. #Person1# explains the details of the courses and #Person2# is pleased with the information."}
{"answers": ["Tina plays the piano well because she has an excellent teacher. She agrees to introduce #Person1# to her teacher.", "Tina plays the piano well because she has an excellent teacher. #Person1# wants to learn piano from that teacher and Tina will introduce her teacher.", "Tina plays the piano well because of her excellent teacher and she agrees to introduce #Person1# to her teacher ."], "prediction_text": "Tina has been learning the piano for 8 years and still continues to learn from her teacher who is from the UK. Tina agrees to introduce #Person1# to her teacher this Saturday."}
{"answers": ["#Person2# introduces #Person2#'s strength, weakness in #Person2#'s work and career planning to #Person1#. #Person1# thinks #Person2#'s background and personality suit the job well.", "#Person1# evaluates #Person2#'s ability and personality by asking #Person2# 's strengths, weaknesses and career planning. #Person1# finds #Person2# is a suitable candidate.", "#Person1# is interviewing #Person2#. #Person1# thinks #Person2# answers #Person1#'s questions beautifully, and is the person they want."], "prediction_text": "#Person1# asks #Person2# about their strengths and weaknesses, and #Person2# answers. #Person1# thinks #Person2# might be the person they are looking for."}
{"answers": ["Stephanie has a headache before the report due. George suggests that she should ask for more time, and he is willing to help with the report.", "Stephanie has a headache but doesn't see a doctor because of the report due and George is willing to help with the report and suggests she ask the board for more time", "Stephanie has a headache but doesn't see a doctor because of the due. George suggests that she should ask the board for more time and he is willing to help."], "prediction_text": "#Person1# notices that #Person2# looks pale and asks if she is okay. #Person2# explains that she has a bad headache and hasn't been sleeping well, and she needs to finish a report for Miss Jenkins but doesn't want to miss the board meeting next Monday. #Person1# suggests that #Person2# talk to Miss Jenkins and ask if she can hand in the report on Friday morning instead."}
{"answers": ["#Person1# and David's father want to have a quiet Christmas at a country hotel, but David thinks it is boring.", "David complains about the boring Christmas at a country hotel. But #Person1# and David's dad feel nice.", "David feels it boring to stay at a country hotel, while his father and #Person1# enjoy a quiet Christmas."], "prediction_text": "#Person1# and #Person2# are looking out the window and see ice on the lake. #Person1# suggests going skating, but #Person2# is not in the mood. #Person1# mentions that they will not be going back to the city until January 3rd and #Person2# expresses disappointment at spending Christmas at a country hotel."}
{"answers": ["Bob and #Person1# talk about the last weekend and decide to play a game together this weekend.", "#Person1# and Bob share their last weekend's activities. And they decide to play a game this weekend.", "Bob and #Person1# talk about what they did last weekend. And they decide to play a game together this weekend."], "prediction_text": "Bob visited a friend and went to a dance party, but didn't enjoy it. #Person1# watched TV and went shopping on Saturday, and played tennis on Sunday. Bob asks #Person1# if he can play tennis with him sometime this weekend."}
{"answers": ["#Person1# has no jobs but is unwilling to go home. #Person2# lends $50 to #Person1#.", "#Person2# asks #Person1#'s situations about #Person2# job and lends #Person1# $50 to wish #Person1# good luck.", "#Person1# borrows $50 from #Person2# and will pay back once #Person1# gets a job."], "prediction_text": "#Person1# asks #Person2# for $50 loan and explains that he is looking for a job and goes to the employment office every day. #Person2# agrees to lend #Person1# the money."}
{"answers": ["#Person2#, chairman of the conference, isn't wearing required suits at #Person1#'s restaurant. #Person1#'ll puts off the conference so that #Person2# can change clothes.", "#Person2# needs to change clothes to enter the conference and #Person1# offers #Person2# the convenience by putting the conference off.", "#Person2# has to return to the hotel and wear a jacket and a tie. #Person1# helps put off the conference."], "prediction_text": "#Person1# tells #Person2# that guests are required to wear a jacket and a tie in the restaurant, #Person2# asks to borrow a jacket and a tie, #Person1# says no, #Person2# goes to fetch his own, and #Person1# agrees to postpone the conference to 9:30 AM."}
{"answers": ["#Person2# tells the trips and sports activities of the holiday camp to #Person1#. The kids enjoyed the holiday camp.", "Though some were homesick, the kids and #Person2# enjoyed their tour to Edinburgh.", "#Person2# tells the trips and sports activities of the holiday camp to #Person1#. The kids enjoyed camp and would like to go next year."], "prediction_text": "#Person1# asks #Person2# about the children's experience at the holiday camp. #Person2# explains that some of the younger children were initially homesick, but soon recovered and had a great time. #Person2# mentions some of the activities they did, including a visit to the Holyrood Castle and sports such as climbing and horse-riding. #Person2# also mentions that they had a barbeque on the last day, which was the children's favorite and they all promised to get together again next year."}
{"answers": ["#Person2# asks his daughter about college admission requirements because he wants to take some classes in computer programming.", "#Person2# asks his daughter about admission requirements of college, becasue he wants to learn computer programming.", "#Person2# wants to know admission requirement of college, because he is thinking of learning computer programming."], "prediction_text": "#Person1# asks #Person2# why he was asking about college admission requirements, and #Person2# reveals he is considering quitting his job and starting a business in computer programming. #Person1# expresses interest in helping with the family business in the future."}
{"answers": ["Kalina calls Professor Clark that she needs to take this week off because she had a concussion.", "Kalina calls Professor Clark and asks for leave because of a car concussion.", "Kalina calls Professor Clark to ask for a week off as Kalina had a car accident."], "prediction_text": "Kalina calls Professor Clark to inform him that she will miss a few days of school due to a car accident. Professor Clark expresses concern and tells Kalina to take the necessary time off."}
{"answers": ["#Person2# tells Tenants Advocacy Resource Center about the problem with #Person2#'s house owner. #Person2# tells #Person1# it always takes #Person2#'s house owner a long time to repair and they can't agree on the repair costs.", "#Person2# calls Tenants Advocacy Resource Center to report the problem with #Person2#'s house owner. #Person2# says he always delays to make repairs and is not willing to pay for it.", "#Person2# calls #Person1# to complain about #Person1#'s house owner. #Person2# claims it always takes the house owner a long time to repair and they can't agree on the repair costs."], "prediction_text": "#Person1# at Tenants Advocacy Resource Center listens to #Person2#'s problem with their house owner, who is taking too long to fix repairs and is also unwilling to pay for them. #Person1# advises #Person2# to document everything and seek legal help."}
{"answers": ["#Person1# helps #Person2# collect an Export L /C from Tokyo.", "#Person2# looks for L/C and gets the expected one from #Person1#.", "#Person2# is sent over from Turner Interiors to collect the Export L/C from #Person1#."], "prediction_text": "#Person1# works at a company that provides L/Cs. #Person2# comes to collect an Export L/C from Tokyo for Sayuri Beds. #Person1# helps #Person2# sign the necessary forms."}
{"answers": ["Charlie and Myrdal are going to hotdog stand to find Myrdal's disappeared purse.", "Charlie suggests going back to the hotdog stand when Myrdal cannot find the purse.", "Charlie helps Myrdal to think over where Myrdal lost the wallet."], "prediction_text": "#Person1# lost their purse and #Person2# suggests going back to the hotdog stand to look for it."}
{"answers": ["#Person1# has been washing dishes for over a week and #Person2# will do the dishes next week.", "#Person1# has been washing dishes for over a week and #Person2# agrees to take #Person2#'s turn to wash next week.", "#Person2# will do the agreement made with #Person1# on taking turns washing the dishes."], "prediction_text": "#Person1# has been washing the dishes for a week and wants #Person2# to take their turn. #Person2# agrees to do the dishes."}
{"answers": ["#Person1# and #Person2# are talking about where their relationship is going with a gun in #Person1#'s hand.", "#Person1#'s is talking about where the relationship with #Person2# is going with a gun in hand.", "#Person1# asks where the relationship with #Person2# is going and #Person2# gives the answer."], "prediction_text": "#Person1# wants to know where the relationship is going and asks #Person2#. #Person2# tells #Person1# that they love each other and have had fun together."}
{"answers": ["#Person2# thinks that unemployment gives rise to social problems, and #Person1# thinks that it's difficult to handle it efficiently.", "#Person1# and #Person2# agree that the government cannot handle the unemployment problem efficiently.", "#Person1# and #Person2# think that it's hard for the government to handle the social problems caused by unemployment."], "prediction_text": "#Person1# and #Person2# discuss the social problems faced by the government and agree that unemployment is a major issue."}
{"answers": ["#Person2# suggests #Person1# get a costume of a Canadian Mountie for a dress party. They will go to the shopping center for that at ten o'clock on Saturday.", "#Person2# gives advice to #Person1# on what to wear at the party and plans to buy the costume with #Person1# in the shopping center at ten o'clock on Saturday", "#Person1# doesn't know what to wear and where to get a costume for a country theme party while #Person2# knows and will help #Person1#."], "prediction_text": "#Person1# needs help finding a costume for a fancy dress party and asks #Person2# to come shopping with them. #Person2# suggests a cowgirl or Canadian Mountie costume and offers to take #Person1# to the shopping center near their house."}
{"answers": ["#Person1# and #Person2# go to a nightclub in Beijing and dance there. #Person1# prefers fast dances to slow dances.", "#Person1# and #Person2# go to the nightclub to dance and #Person1# prefers fast dances to slow dances.", "#Person2# takes #Person1# to a nightclub, and #Person1# has fun with fast dances."], "prediction_text": "#Person1# and #Person2# go to a nightclub in Beijing. #Person1# likes to dance to fast music and disco dancing is their favorite. #Person2# suggests they try a slow dance, a waltz, and gets #Person1# a drink."}
{"answers": ["#Person1# wants to have a farm and gets away from working 9 to 5 in an office. #Person2#'d visit #Person1#.", "#Person2# thinks that #Person1#'s plan to run a farm and become a farmer is different from the current job, and #Person2# will come and visit #Person1# on a farm.", "#Person1# decides to own a farm and live a different life while #Person2# thinks it's difficult."], "prediction_text": "#Person1# wants to have a farm, but #Person2# tells him it would be hard work and he needs training before becoming a farmer. #Person1# loves being in the countryside with farm animals and green fields and invites #Person2# to visit him on his farm."}
{"answers": ["The high price of a two-for-one happy hour special shocks #Person1#.", "#Person1# orders drinks and food but finds them expensive.", "#Person1# orders some food at #Person2#'s but finds the price is higher than expected."], "prediction_text": "#Person1# orders a pint of Heineken and half a pint of Budweiser at a happy hour special, and the bartender tells him the total is $80. #Person1# is surprised."}
{"answers": ["Mary argued with Ann because Ann canceled their trip because of her boyfriend's plans. After talking with #Person1#, Mary thinks she was selfish and call later to patch things up.", "Mary was angry with Ann because Ann canceled their plan because of Ann's boyfriend. #Person1# comforts Mary and she will patch things up later.", "Mary tells #Person1# about the argument she had with Ann, and after talking with #Person1#, she decides to call Ann to patch things up."], "prediction_text": "Mary and John have an argument with Ann about their plans to go to the beach. Mary is upset and John tells her to be more understanding and not let their friendship be affected by a trivial thing."}
{"answers": ["#Person1# asks #Person2# for directions.", "#Person1# asks #Person2# where to buy cigarettes, souvenirs, and the men's room.", "#Person1# asks #Person2# for some locations."], "prediction_text": "#Person1# asks for directions to buy cigarettes and souvenirs, and #Person2# provides the information. #Person1# also asks for the location of the men's room."}
{"answers": ["Tom and Catherine are talking about American fast food culture. They think Americans need to find a way to make the most of their fast foods.", "Catherine and Tom talk about American fast-food culture inspired by a movie, and they think Americans need to find a way to make the most of their fast foods.", "Catherine and Tom discuss the movie Fast Food Nation and the opinions towards fast-food culture."], "prediction_text": "Catherine and Tom discuss the movie Fast Food Nation and its themes, including the prevalence of fast food restaurants in America and the culture surrounding them. Catherine thinks that while some healthy options are available, they are not well-received by consumers who prioritize convenience."}
{"answers": ["#Person2# is holding everyone up, because #Person2# is standing back from the door and asking #Person1# whether this is the right bus to take.", "#Person2# keeps asking #Person1# the bus route while #Person1# is busy managing the space for other passengers.", "#Person2# asks #Person1# about the right bus to the places, but #Person1# wants #Person2# to move and let the passengers off."], "prediction_text": "#Person1# tells #Person2# to move to the back of the bus and pay the fare, while #Person2# asks about the route and #Person1# tells #Person2# to watch out for themselves."}
{"answers": ["#Person1# and #Person2# are talking about the terrorist attacks on 9-11, which was nightmarish for #Person1#'s family.", "#Person1# and #Person2# remember the chaotic situation of New York after 9-11, and #Person1#'s uncle ended up dying in the tower.", "#Person1# tells #Person2# what was going on in New York City on 9-11 and what #Person1#'s family has suffered."], "prediction_text": "#Person1# and #Person2# are discussing the 9-11 terrorist attacks. #Person1# was in New York City and saw the aftermath from their parents' apartment building. #Person2# was in Beijing and didn't see the attack itself but heard about it later. #Person1# lost their uncle in the attack. #Person2# expresses their condolences."}
{"answers": ["Karl and his wife moved to Minnesota and greet their friendly neighbour Chuck Jones and tells him why they moved here.", "Karl and his wife newly moved to Minnesota. Karl greets his new neighbour Chuck and tells him the moving reason.", "Karl has the first chat with his neighbour Chuck and tells him why they moved to Minnesota."], "prediction_text": "Karl and Chuck are neighbors, Karl is from Chicago and moved to Minnesota for a more peaceful community and more space, and Chuck knows everyone in the town."}
{"answers": ["#Person1# helps #Person2# order a hard-boiled egg with dark toast and orange juice to follow.", "#Person1# helps #Person2# order one hard-boiled egg with dark toast and orange juice to follow.", "#Person1# helps #Person2# order a hard-boiled egg with dark toast and orange juice to follow."], "prediction_text": "#Person1# takes the order from #Person2# for a hard-boiled egg with toast and orange juice to be served later."}
{"answers": ["Steven will go over the household budget while #Person1# will go to bed first.", "#Person1# goes to sleep first while Steven plans to stay up to go over the household budget.", "Steven wants to stay up to go over the household budget when #Person1# is going to sleep."], "prediction_text": "#Person1# is tired and wants to go to bed, but #Person2# wants to stay up and go over the household budget. #Person1# asks #Person2# to close the door to the basement before going to bed."}
{"answers": ["#Person1# will meet Jane next Monday afternoon around three to kick around some ideas.", "#Person1# makes an appointment with Jane on next Monday afternoon around three to kick around some ideas.", "#Person1# makes an appointment with Jane before their meeting."], "prediction_text": "#Person1# wants to meet with Jane before next week's meeting to discuss ideas. Jane agrees to meet on Monday afternoon at 3."}
{"answers": ["#Person2# is looking for a nice gift to give his girlfriend Jess for their fifth anniversary. #Person1# gives him many suggestions and he finally decides to ask Jess to marry him.", "#Person2# wants to select a gift for his girlfriend and get some recommendations from #Person1#. After consideration and #Person1#'s encouragement, #Person2# decides to buy a proposal ring.", "#Person1# assists #Person2# in choosing a gift for Jess. #Person2# decides to buy a ring and propose on their fifth anniversary."], "prediction_text": "#Person1# helps #Person2# find a gift for his girlfriend, #Person2# decides to propose to her on their fifth anniversary, and #Person1# suggests looking at engagement rings."}
{"answers": ["#Person1# is looking for a new car. After #Person2# introduces the information and price of the Ford Focus, #Person1# decides to test it.", "#Person2# introduces the Ford Focus to #Person1# who wants to buy a new car. #Person2# describes every detail of the car and #Person1# decides to have a test drive.", "#Person2# gives #Person1# a full introduction of the Ford Focus #Person1# intends to test drive it."], "prediction_text": "#Person1# wants to trade in their old Ford Pinto for a new car. #Person2# shows #Person1# the Ford Focus, highlighting its features and fuel efficiency. #Person1# is pleased with the car and decides to buy it with 0% down payment and no interest for the first year."}
{"answers": ["Ballam asks for a position with 4,000 yuan starting salary from #Person1#, given that 3,000 yuan cannot support his family.", "Ballam is hired by Apple Corporation but he thinks the starting salary is low. After discussion, #Person1# agrees to raise the salary.", "#Person1# calls to inform Ballam that Ballam was going to be hired, and agrees to raise the starting salary after discussion."], "prediction_text": "#Person1# calls #Person2# to offer them a position as a computer engineer. #Person2# asks about the salary and #Person1# tells them the starting salary is 3,000 yuan a month, but if they work well they will be put on the regular staff after six months and their salary will be raised to 5,000 yuan a month. #Person2# accepts the offer and asks to start on Monday."}
{"answers": ["#Person1# is introducing the benefits of Network Settlement Service to #Person2#, and confirms when to get the paperwork done.", "#Person1# introduces the benefits #Person2# can get from the company and makes an appointment to sign all the papers.", "#Person1# tells #Person2# about the benefits of Network Settlement Service and confirms the time to get the paperwork done."], "prediction_text": "#Person1# explains the benefits of the Network Settlement Service to #Person2#, who is interested and wants to sign up. #Person1# suggests meeting at 10 am the next day to finalize the paperwork."}
{"answers": ["#Person1# is hungry again and #Person2# asks #Person1# to make something to eat.", "#Person1# is hungry but dont't know what to eat. #Person2# suggests a peanut butter and jelly sandwich.", "#Person1# might make a peanut butter and jelly sandwich to eat."], "prediction_text": "#Person1# is hungry and wants to get a snack, but #Person2# doesn't know what #Person1# wants to make. #Person2# suggests a peanut butter and jelly sandwich. #Person1# agrees."}
{"answers": ["#Person2# draws #Person1#'s blood to check white blood cell count.", "#Person2# helps #Person1# draw blood for testing white blood cell count.", "The doctor has #Person1#'s blood drawn by #Person2# to check white blood cell count."], "prediction_text": "#Person1# has to have his blood drawn for a white blood cell count test. #Person2# explains the test and how it will be done."}
{"answers": ["Steven calls Mr. Sellers and asks him what he should do to deal with a power failure.", "Mr. Sellers instructs Steven to solve the power failure by replacing bad fuses.", "Steven tries to do the repairment under Mr. Seller's instruction to solve power failure."], "prediction_text": "Steven, a tenant, calls Mr. Sellers about a power failure in his house. Mr. Sellers tells Steven to go to the basement and check the circuit box for blown fuses and replace them if necessary."}
{"answers": ["Paula is asking #Person2# to come over and talk with Ms. Connors about the rent.", "Paula has an economic problem with her landlord, Ms. Connors. #Person2# agrees to help when Paula talks to Ms. Connors.", "Paula turns to #Person2# for help to straighten out things between her and the landlord, Ms. Connors."], "prediction_text": "Paula is having a problem with Ms. Connors, the landlord of her rented house. The dishwasher broke down and Paula had the repair done and deducted the cost from the rent check. Ms. Connors is threatening to evict Paula and Paula asks #Person2# to come with her to meet with Ms. Connors tomorrow night at 8 pm. #Person2# agrees to help."}
{"answers": ["#Person1# and #Person2# think they need a handyman to fix the air conditioning, toilet, electrical wiring and gutters.", "#Person1# wants a handyman to fix the air conditioner and it reminds #Person1# and #Person2# to fix other things as well.", "#Person1# and #Person2# think they need a handyman to fix all malfunctions in their office."], "prediction_text": "#Person1# and #Person2# are discussing the issues with the office's air conditioning, bathroom toilet, electrical wiring, and parking lot flooding. #Person2# mentions that Frank told them the gutters outside are clogged."}
{"answers": ["#Person1# and #Person2# are looking for work and will think about the electrician apprentice program.", "#Person1# and #Person2# both get laid off and they want to find a job.", "#Person1# and #Person2# talk about their interviews after a lay-off and think about the electrician apprentice program."], "prediction_text": "#Person1# lost his job and is looking for work. #Person2# suggests the electrician apprentice program and invites #Person1# to check it out again."}
{"answers": ["#Person2# describes the relationship with their boss and the strengths of their boss to #Person1#.", "#Person2# tells the strength of their boss to #Person1#, and describes the relationship with their boss.", "#Person2# describes to #Person1# about the relationship with the boss and what their boss strengths lie on."], "prediction_text": "#Person1# and #Person2# discuss their boss's strengths and working relationship."}
{"answers": ["#Person1# is interviewing Mr. Tang for the tour guide's job.", "#Person1# interviews Mr. Tang about his certification as a tour guide,", "#Person1# interviews Mr.Tang about the related certification of being a tour guide."], "prediction_text": "Mr. Tang brings his Tour Guide Certificate to the interview and tells #Person1# he is still working in Youth International Travel Agency but is on holidays. Mr. Tang also mentions he is bilingual, speaking English and Russian."}
{"answers": ["#Person2# is bargaining with #Person1# over the price of a pure gold watch.", "#Person2# wants to buy a watch but thinks the price is steep so #Person1# bargains with #Person1#.", "#Person2# bargains with #Person1# on a gold watch."], "prediction_text": "#Person1# shows #Person2# some gold watches, #Person2# wants to look at them, #Person1# tells #Person2# the price, #Person2# thinks it's too expensive, #Person1# tells #Person2# the price is moderate, #Person2# decides to buy it."}
{"answers": ["Tom is asking Sara to take care of his son, since his daughter has a fever and they are taking her to the hospital.", "Tom asks Sara to help to take care of his son when he takes his daughter to the hospital.", "Tom calls Sara for her help to take care of his son Ken, because he is taking his daughter to the hospital."], "prediction_text": "Tom calls Sara to ask for help taking care of his son Ken since Maria has a high fever and they need to take her to the hospital. Sara agrees to take care of Ken."}
{"answers": ["Amy is talking about her first job experience and later job with #Person1#.", "Amy tells #Person1# about her first job experience and her current job.", "Amy shares her former work experience and her current job with #Person1#."], "prediction_text": "Amy worked as a secretary to the General Manager for two years, then was promoted to Personnel Manager. She enjoyed working with the people but left due to poor pay."}
{"answers": ["Andrew is on the diet according to a junk mail. His elder sister thinks he's scammed and hopes he could listen to her. She suggests he do exercises, eat smaller portions, eat well-balanced breakfast and follow up with smaller meals throughout the day, never eat late at night, throw away ice-cream, cut out the fast food, eat a lot of fresh fruits and vegetables, cut way back on sugar and drink water instead of sugary drinks. Andrew feels this is going to be tough.", "#Person1# is surprised to see Andrew put on so much weight but Andrew tells #Person1# that to lose weight, he signs up for a Wafu Diet online for $490. #Person1# thinks he's getting scammed and suggests that he should take more exercise, eat smaller portions, eat a well-balanced breakfast, cut off fast food and sugar and eat fresh fruits and vegetables", "Andrew's elder sister is surprised by Andrew's putting on so much weight and figures out Andrew has probably got scammed by a junk mail. She offers Andrew systematic suggestions on living healthy and losing weight."], "prediction_text": "#Person1# tells #Person2# that they need to change their eating habits to lose weight. #Person1# suggests smaller portions, a balanced breakfast, and no late-night eating. #Person1# also tells #Person2# to cut back on sugar and fast food, and drink more water. #Person2# is skeptical and worried about the change."}
{"answers": ["Mr. Sonders interviews Mary for college admission. Mary makes an impression on them.", "Greg Sonders from Brown College calls the applicant Mary to ask whether she is interested in college sports and will make a further decision later.", "Greg Sonders calls Mary to ask whether Mary is interested in sports and tells Mary to wait for final admission decision later."], "prediction_text": "Greg Sonders from Brown College calls Mary to inquire about her interest in college sports. Mary mentions that she played high school basketball and holds her school's all-time record for points scored in a game, and also plays volleyball."}
{"answers": ["#Person1# takes a taxi to the railway station in the rush hour.", "#Person1# stops a taxi to the railway station and asks the driver to be slow and careful.", "#Person1# tells #Person2# to drive slowly and carefully to the railway station in the rush hour."], "prediction_text": "#Person1# wants to go to the railway station and asks the taxi driver how long it will take and if it is the rush hour. The driver confirms it is the rush hour and will take about 20 minutes to get to the station."}
{"answers": ["#Person1# is asking Brian about his life in the U.S.", "#Person1# asks Brian about his experience of living in the US.", "#Person1# asked Brian about his experience in the U.S."], "prediction_text": "Brian speaks English well and learned it in college. He has been in the U.S. for 3 weeks and his wife arrived yesterday. Brian has never been to California or Las Vegas before."}
{"answers": ["#Person2# wants to do an annual physical examination to apply for new health insurance and says #Person2#'s breathing is not good. #Person1# explains the items and will do tests on #Person2#'s breathing.", "#Person1# explains the checking items in #Person2#'s annual physical examination and will do test to look into #Person2#'s breathing.", "#Person2# is going through an annual physical examination to apply for new health insurance, and #Person2# asks #Person1# to look into the breathing."], "prediction_text": "#Person1# is a doctor and #Person2# is a patient who is there for an annual physical to qualify for new health insurance. #Person1# explains the basic physical exam and #Person2# mentions having trouble breathing, which #Person1# plans to test for."}
{"answers": ["#Person1# and #Person2# are talking about what they will do if they won the Pools. #Person1# will go round the world, and #Person2# will buy a big house for the family. Finally, come down to earth and have another drink.", "#Person1# wants to go around the world while #Person2# wants to buy a big house, if win the Pools. But they come down to the earth finally.", "#Person1# and #Person2# are drinking while discussing what to do if they won the Pools. #Person1# wants to go around the world while #Person2# wants to buy a big house."], "prediction_text": "#Person1# and #Person2# are talking about what they would do if they won the lottery. #Person1# would travel around the world, while #Person2# would buy a big house and a mink coat. They both agree that they are not likely to win the lottery and decide to have another drink."}
{"answers": ["#Person1# is not ready for marriage. #Person2# reminds him of what he said a month ago and what will happen if he leaves Amy. After listening to #Person2#, #Person1# is ready for marriage.", "#Person1# feels anxious about future marriage and sweats a lot. #Person2# helps #Person1# to think through the current situation and get ready for the marriage.", "#Person1# is freaking out before his marriage with Amy and #Person2# helps him get over it by helping him think through all the details."], "prediction_text": "#Person1# is freaking out about getting married and #Person2# tries to calm him down and tells him to think about it and that he loves Amy and should not leave her at the altar."}
{"answers": ["#Person1# needs to get the new business cards the same as the old ones, and can pick up in three days by paying a little extra.", "#Person1# comes to #Person2# to print up new business cards according to the old ones. #Person2# promises to get them ready in three days if #Person1# pays a little extra.", "#Person1# asks #Person2# to print up new business cards according to the old ones, which #Person2# promises to get ready in three days if #Person1# pays a little extra."], "prediction_text": "#Person1# needs business cards printed and #Person2# tells him how many he needs and how long it will take to get them. #Person1# fills out a form and gives #Person2# his old card to compare the new ones to. #Person2# tells #Person1# that he can pick them up in one week, but #Person1# wants them in three days and is willing to pay extra."}
{"answers": ["According to the schedule planned by Brian, #Person1# will be picked up at 6, meet the Managing Director at the airport and attend the conference at noon.", "Brian arranges the working schedule for #Person1# and reports the detailed time and arrangements to #Person1#.", "Brian completes some arrangments for #Person1#'s business trip and the schedule after #Person2# gets to the destination."], "prediction_text": "Brian has made the reservations for the trip, but there are still some details to finalize. Brian will pick Brian up at 6:00 AM and Brian will meet the Managing Director at the airport."}
{"answers": ["Paul cannot go to Finland with his parent because he has much work to do. He will go to #Person1#'s house for Thanksgiving dinner with a nice bottle of wine.", "Paul will go to celebrate Thanksgiving Day with #Person1#'s family and he decides to take a bottle of wine as the gift after discussion with #Person1#.", "#Person1# invites Paul to have Thanksgiving dinner because he can't go to Finland with his parents. Paul decides to bring a bottle of wine as the gift."], "prediction_text": "Paul is invited to Thanksgiving dinner at #Person1#'s house and agrees to bring a pumpkin pie. #Person1# mentions the number of people who will be attending and Paul offers to bring a bottle of wine."}
{"answers": ["John is asking Susan to help him show his cousin around on Saturday, because he is on duty that day. Susan will probably take her to a music festival if it's not snowy.", "John asks Susan to help him show his cousin around because he is on duty in the library. Susan asks his cousin's hobbies and they decide to choose the visiting place according to the weather.", "John asks Susan to help look after his cousin who will come to have a college interview and they have also made a backup plan if there was a snowstorm."], "prediction_text": "John needs Susan's help to take care of his cousin who is visiting for the weekend. Susan agrees to help and they plan to meet for dinner later."}
{"answers": ["Ben and #Person1# are talking about the evening courses. They finally decide to have Indian cooking and will make a call for that.", "Ben and #Person1# are looking for a suitable evening class for Ben, and they are interested in Indian cooking after comparison.", "#Person1# and Ben discuss what evening classes they shall get and come into an agreement to have an Indian cooking course for their interest."], "prediction_text": "#Person1# shows Ben the information about the evening classes and they discuss the different courses. #Person1# is interested in the photography course but thinks it's too expensive, while #Person2# is more interested in the Indian cooking course."}
{"answers": ["#Person1# and #Person2# decide to go to Carrots where can meet everyone's requirements with others tomorrow evening.", "#Person1# and #Person2# discuss the restaurant to eat with friends and find that the suitable restaurant isn't open today, so they decided to go tomorrow evening instead.", "To better cater to more people's taste, #Person1# and #Person2# discuss which restaurant they should go to."], "prediction_text": "#Person1# suggests going to the Pizza Experience for dinner, but #Person2# reminds him they went there last week and suggests other options. #Person1# suggests the Steakhouse, but #Person2# mentions that Bridget doesn't eat meat, so #Person1# suggests the Kingfisher, but #Person2# mentions that Arthur doesn't eat fish, so #Person1# suggests the Carrots, but #Person2# reminds him it's not open on Thursdays."}
{"answers": ["Gene will take a driving test to get a license, given that Gene has to drive living in LA. Gene will buy a twenty sixteen Honda Accord for its safety features.", "Gene is learning to drive and wants to buy a sixteen Honda Accord after passing the test.", "Gene is taking a new job in LA, which requires Gene to drive. So Gene has been taking driving lessons and has picked out a car for its safety features."], "prediction_text": "Gene is taking his driving test on Saturday, and he plans to buy a 2016 Honda Accord once he passes. Gene explains the safety features of the car, including a back view camera and lane departure warning."}
{"answers": ["#Person1# buys a pen from #Person2# and pays by charge.", "#Person1# takes the pen in the brown case which writes smoothly at #Person2#'s shop via charge.", "#Person1# buys a pen from #Person2# and pays via charge."], "prediction_text": "#Person1# wants to try a pen and #Person2# gives it to him to try. #Person1# likes it and decides to buy it. #Person2# asks if #Person1# wants to pay by cash or charge."}
{"answers": ["#Person2# tells #Person1# that the company raised a lot of money to contribute to cancer research, and the marathon event was also a great deal for our company", "#Person2# tells #Person1# that the company not only raised a lot of money sponsoring a marathon for the American Cancer Society but also established a good image out of this fundraiser event.", "#Person1# asks about the fundraiser of #Person2#'s company. #Person2#'s company with other companies sponsored a marathon for the American Cancer Society, which was good for the advertising campaign."], "prediction_text": "#Person1# asks #Person2# about the fundraiser event for the American Cancer Society and #Person2# explains how the event went well and how the company gained exposure and contributed to a good cause."}
{"answers": ["#Person1# asks #Person2# for the way to Peak Tram and writes it down.", "#Person1# asks #Person2# for directions to Peak Tram and writes it down.", "#Person1# asks #Person2# for the way to Peak Tram. #Person2# tells so and is asked to repeat so #Person1# can write it down."], "prediction_text": "#Person1# asks #Person2# for directions to the Peak Tram, and #Person2# provides detailed instructions. #Person1# asks for a pencil to write down the directions."}
{"answers": ["#Person1# shows #Person2# the sandalwood fans. #Person2# buys two small ones and a big one.", "#Person1# shows #Person2# some sandalwoods, #Person2# likes it and buys some.", "#Person2# wants some traditional Chinese arts and crafts. #Person1# shows her sandalwood fan and she buys some."], "prediction_text": "#Person1# shows #Person2# traditional Chinese arts and crafts, including sandalwood fans. #Person2# buys two small fans and a big one."}
{"answers": ["#Person1# asks #Person2# about the factors influencing the company.", "#Person1# asks #Person2# for the factors that influence the company and the perspective of the corporation.", "#Person1# and #Person2# talk about external and internal factors influencing the corporation."], "prediction_text": "#Person1# asks #Person2# about the factors that impact the corporation, and #Person2# mentions external factors such as political, economic, social, and technological factors, as well as internal factors such as human resources, team spirit, innovation spirit, and coordination between departments."}
{"answers": ["#Person1# and #Person2# negotiate the time to start working on the subject.", "#Person1# and #Person2# negotiate the timing of the preparatory meeting for a future subject.", "#Person1# and #Person2# will start a preparation meeting at 3:00. The project may run a year."], "prediction_text": "#Person1# wants to know when they can start working on a project, and #Person2# suggests a meeting at 2:00, which #Person1# is unable to attend, but agrees to come by at 3:00. #Person1# also mentions that the project may take a year to complete."}
{"answers": ["#Person1# suggests a tour according to #Person2#'s preference.", "#Person2# asks #Person1# for some tour advice on the natural landscape.", "#Person2# wants a short tour in the natural landscape under #Person1#'s suggestion."], "prediction_text": "#Person1# helps #Person2# plan a one-day tour along a river. #Person2# prefers the natural landscape."}
{"answers": ["#Person1# helps #Person2# to order some food.", "#Person2# orders some food with #Person1#'s assistance.", "#Person2# orders a beef-burger, french fries, and a banana milkshake at #Person1#'s shop."], "prediction_text": "#Person1# works at a fast food restaurant and takes an order from #Person2# for a beef-burger, French fries, and a milk shake. #Person2# chooses the banana flavor."}
{"answers": ["#Person1# and #Person2# are appreciating lanterns. #Person2# explains the customs of the Lantern Festival and Chinese characters on the lanterns to #Person1#.", "#Person1# and #Person2# participate in the Lantern Festival appreciation performance and enjoy the activities.", "#Person1# and #Person2# are appreciating lanterns. #Person1# asks about the Lantern Festival and #Person2# talks about the custom and puzzles on lanterns."], "prediction_text": "#Person1# and #Person2# are at the Lantern Festival and appreciate the lighted lanterns and gala performances. #Person1# is curious about the Chinese characters on the lanterns and #Person2# explains they are puzzles and Chinese poems."}
{"answers": ["#Person1# invites Tina to a celebration of #Person1#'s passing the interview.", "#Person1# and Tina plan to celebrate for #Person1#'s going to the final round interview.", "#Person1# has passed the first two rounds of the interview and invites Tina to celebrate it."], "prediction_text": "#Person1# has successfully passed the first two rounds of interviews with ABC Company and will go to the final round next Monday. #Person2# congratulates #Person1# and suggests celebrating at a cafe."}
{"answers": ["Tim and Karren say goodbye.", "Tim and Karren say farewell words after meeting.", "Tim and Karren say goodbye to each other."], "prediction_text": "Tim is leaving and says goodbye to Karren."}
{"answers": ["#Person1# informs #Person2# of the meeting and helps #Person2# pick up some stationery. #Person1# also shares the experience of working and meetings.", "#Person1# helps #Person2# find the things that #Person2# wants. #Person1# finds it comfortable working in the office.", "#Person1# and #Person2# will have a department meeting. #Person1# tells #Person2# how to get stationeries and introduces about the meeting."], "prediction_text": "#Person1# tells #Person2# that Julie, the receptionist, can help with stationery and a calendar, and that the photocopier is near #Person1#'s office. #Person1# enjoys working in the office and mentions that the department meetings are used to discuss client assignments and other work-related matters."}
{"answers": ["#Person1# asks #Person2# to teach #Person1# how to use the bath.", "#Person1# calls #Person2# for getting to know how to use buttons and switches so #Person1# can take a bath.", "#Person1# has trouble with the bath. #Person2# teaches #Person1# how to use it."], "prediction_text": "#Person1# is having trouble using the buttons in their bathroom and asks #Person2# for help. #Person2# agrees to explain how to use them."}
{"answers": ["#Person1# asks #Person2# for advice to choose books.", "#Person1# wants to choose a book to read on the train and #Person2# recommends one by a Japanese novelist.", "#Person1# wants some books to kill time and #Person2# suggests a novel."], "prediction_text": "#Person1# asks #Person2# for advice on books for killing time on the train. #Person2# suggests novels and offers to show #Person1# the latest book by a famous Japanese novelist."}
{"answers": ["#Person1# asks #Person2# to help teach #Person1#'s friend English and #Person2# agrees.", "#Person2# agrees to help #Person1#'s friend, a new emigrant who had never been to school, to learn English.", "#Person1# asks if #Person2# could teach #Person1#'s friend, a 78 new emigrant man, to read English. #Person2# will see him at dinner."], "prediction_text": "#Person1# wants #Person2# to teach #Person1#'s friend how to read, who is 78 years old and a new emigrant from South America and doesn't know how to read or speak English. #Person2# agrees to teach him after dinner."}
{"answers": ["#Person1# buys a ticket under #Person2#'s guidance.", "After filling the booking form and paying, #Person1# picked up the ticket with #Person2#'s assistance.", "#Person1# wants to pick up a ticket at #Person2#'s and pays by card."], "prediction_text": "#Person1# goes to pick up their ticket and is given a form to fill out. The ticket costs $800 and #Person1# pays by card."}
{"answers": ["#Person1# invites #Person2# to make dinner together, but #Person2# thinks #Person2# is bad at cooking. Thus, #Person1# teaches #Person2# to cook spicy chicken.", "#Person1# teaches #Person2# to cook chicken for #Person2#'s original thought of having frozen pizza is unhealthy.", "#Person1# asks #Person2# what for dinner and wants to cook something healthy when #Person2# suggests frozen pizza. #Person2# says #Person2# is not good at cooking, but #Person1# could teach #Person2# to make spicy chicken curry with rice."], "prediction_text": "#Person1# suggests cooking dinner together and offers to teach #Person2# how to cook spicy chicken curry. #Person2# is hesitant but agrees to try, and #Person1# takes charge of preparing the ingredients and cooking the meal."}
{"answers": ["#Person2# teaches #Person1# how to use the IC phone and the coin phone to call.", "#Person2# helps #Person1# distinguish two phones on the booth and tells #Person1# how to use them.", "#Person1#'s confused about how to use two different phones. #Person2# tells #Person1# how to use them."], "prediction_text": "#Person1# is confused about the two phones in the booth and #Person2# explains how to use them, including inserting an IC card or using the coin phone."}
{"answers": ["#Person2# introduces EDD's popular services to #Person1#. #Person2# tells #Person1# that EDD provided computers, job information, workshops, and job fairs.", "#Person2# offers #Person1# some information about getting a job including the available services, workshops, and useful places.", "#Person1# consults #Person2# about EDD's services of seeking jobs. #Person2# tells #Person1# that EDD could provide information and workshops and #Person1# could go Job fairs for jobs."], "prediction_text": "#Person1# asks about EDD's services to help them find a job, and #Person2# explains that they have an area with computers for job searching and provide workshops on resume writing and interviewing. #Person1# also asks about other places to look for work, and #Person2# mentions job fairs."}
{"answers": ["#Person1# asks Tom for his opinion on second-hand goods and Tom suggested #Person1# being careful.", "#Person1# asks Tom's opinion about buying a second-hand car and Tom thinks #Person1# should be careful.", "#Person1# asks Tom about second-hand goods because #Person1# wants to buy a second-hand computer. Tom tells #Person1# it might be good, but #Person1# needs to be careful."], "prediction_text": "Tom is considering buying a second-hand computer, and John shares his experience of buying a second-hand car that is still in good condition and cost him $300. Tom wonders if second-hand goods are as good as new ones."}
{"answers": ["Morgan tells #Person1# that Chinese people seldom take leftover food home and #Person1# thinks it's a waste. Morgan suggests #Person1# take the leftover pig feet home. #Person1# decides to try to do it once.", "#Person1# asks Morgan about the food ordering tradition and thinks it's wasteful for Chinese not to take leftover home.", "#Person1# asks about people taking left-over food in China. Morgan tells #Person1# it's not common and explains this is a tradition. As #Person1#'s parents grew up eating pig's feet, #Person1# thinks the pig feet are OK."], "prediction_text": "#Person1# asks #Person2# if many Chinese people take their leftover food home from a restaurant, and #Person2# explains that it is not common in most cities in China. #Person1# expresses disappointment that the food goes to the dump, and #Person2# explains that ordering a lot of food is a tradition in China. #Person1# suggests taking the leftovers home, but #Person2# offers the last pig's foot to #Person1# instead."}
{"answers": ["Harry tells #Person2# that the bag was overpriced in a street market. #Person2# suggests that Harry should bargain with the seller and consult the price tags in the supermarket first.", "#Person2# suggests Harry consult the price tags in the supermarket first and then negotiate for a fair deal after Harry bought a bag overpriced and got a terrible feeling.", "Harry is mad because he bought a bag overpriced at a street market in China. #Person2# says it's common and people should know the real worth and then negotiate for a fair deal."], "prediction_text": "Harry bought a bag at a street market in China and was ripped off. He tells #Person2# about the incident and #Person2# advises Harry to research the prices of items beforehand and negotiate for a fair deal."}
{"answers": ["#Person1# asks #Person2# about #Person2#'s mastery of English and other languages.", "#Person1# makes requires about #Person2#'s English standard.", "#Person1# tells English is important in #Person1#'s company. #Person2# introduces #Person2#'s English education and other language capabilities"], "prediction_text": "#Person1# asks #Person2# about their English education and language skills. #Person2# replies that they have studied English since junior high school and majored in English in college, and also speaks French and Japanese."}
{"answers": ["#Person1# helps #Person2# to open a new account. #Person1# answers #Person2#'s questions about the debit card, the maximum amount in an overdraft, and the penalty for having an overdraft.", "#Person2# brings some documents to #Person1#'s to open up a new account with a debit card which allows for the maximum $1000 overdraft.", "#Person2# wants to open a checking account at #Person1#'s bank. #Person1# tells #Person2# about the benefits of #Person1#'s bank. After #Person1#'s assistant looks over the documents, #Person1# tells #Person2# will receive the card and PIN in three weeks."], "prediction_text": "#Person1# helps #Person2# open a new checking and savings account. #Person1# explains the benefits of the accounts and answers #Person2#'s questions about the overdraft limit and interest rate."}
{"answers": ["#Person2# answers #Person1#'s questions about getting special discount coupons and how to use them.", "#Person1# asks #Person2# about the instructions for getting and using the discount coupons.", "#Person2# tells #Person1# how to get special discount coupons and how to use them. #Person1# buys 9 bags of sugar and gets 3 coupons."], "prediction_text": "#Person1# wants to know how to get special discount coupons and #Person2# explains that buying more goods will give more discount coupons. #Person1# asks about the discount amount and expiration date, and #Person2# answers that the coupon can be used for at least one year. #Person1# decides to buy 9 bags of sugar to get 3 coupons."}
{"answers": ["Mrs. Robinson thanks Steve for looking after Johnny and cleaning the house.", "Steve broke all the dishes while looking after Johnny. He's leaving the house when facing Mrs. Robinson's appreciation.", "Steve helps look after Jonny and house-keeping. Mrs. Robinson appreciates it."], "prediction_text": "#Person1# thanks #Person2# for looking after Johnny and cleaning the kitchen. #Person2# has to leave."}
{"answers": ["Mr. Smith asks #Person1# to help find his train ticket, but #Person1# finds out that Mr. Smith didn't have any proof of purchase. Thus Mr. Smith brings another ticket in the end.", "#Person1# doesn't find Mr. Smith's proof of transaction after Mr. Smith lost his train ticket. Mr. Smith successfully purchases another ticket with #Person1#'s help.", "Mr. Smith lost his ticket to Shanghai and couldn't provide proof of purchase. #Person1# helps to buy another soft sleepier ticket and waive some money for him."], "prediction_text": "#Person1# helps #Person2# find his lost train ticket and #Person2# purchases another ticket for 419.6 RMB."}
{"answers": ["Ann recommends #Person1# to do the well-being in the air program on the way to New York. Ann introduces the program in detail and #Person1# seems interested in it.", "Ann suggests #Person1# take well-being in the air programme including drinking mineral water and exercising so that #Person1# could get over jet lag.", "Ann had a business trip to the states and #Person1# will have a trip too. Ann suggests #Person1# do well-being in the air program. She did it on her flight to New York, eating light and healthy, doing exercise, and she felt good after her arrival."], "prediction_text": "#Person1# asks #Person2# about their trip to the states and #Person2# tells #Person1# about the well-being program they did on the flight and how it helped them not feel jet lagged. #Person1# finds it interesting and wonders if it's a choice between the program and drinking champagne."}
{"answers": ["Mary tells Tom that she found a job, but she still wants a different one. Tom recommends his father's company.", "Mary is considering changing a job. Tom tells her that his father's company happens to have the hiring plan in the near future.", "Mary needs to work at McDonald's so she cannot go to John's party, but she wants a different job. Tom father's company offers a job and Mary is willing to try."], "prediction_text": "Tom asks Mary if she is going to John's party, but Mary tells Tom that she has found a job and can't go. Tom tells Mary that his father's construction company will need people to work for the summer and he will tell his father about it."}
{"answers": ["Harry tells #Person1# that he was almost hit by a car and he will be more careful next time.", "Harry tells #Person1# that he crossed the street while reading the newspaper and almost got hit by a car.", "Harry tells #Person1# he was almost hit by a car and he will be more careful next time."], "prediction_text": "Harry had a close call with a car while crossing the street and is now more cautious about his surroundings."}
{"answers": ["Mr. Kayne shares with the audience about how he took over and established his bicycle shop as a salesman and how he's running his business now.", "Mr. Steven Kayne is invited to share his experience of establishing a bicycle shop by a business program.", "#Person1# interviews Mr. Kayne why he ran a bicycle shop. He says he loves it to be his own boss. His friends helped him and they could play when there were no customers."], "prediction_text": "Mr. Kayne talks about why he wanted to open his own bicycle shop, how he got a business loan, and how he has hired friends to help him out."}
{"answers": ["A couple is arguing about whether they are middle-aged. #Person1# thinks they are middle-aged but #Person2# disagrees.", "#Person2# disagrees with #Person1# on the definition of middle age.", "#Person1# thinks #Person1# and #Person2# are a middle-aged couple. #Person2# doesn't agree and they argue about it."], "prediction_text": "#Person1# and #Person2# are discussing what middle-aged means. #Person1# thinks they are middle-aged, but #Person2# disagrees."}
{"answers": ["#Person2# orders the apple crisp, chocolate mousse cake and tea. #Person2# will share with #Person2#'s friends.", "#Person2# orders some desserts and tea after a perfect meal and #Person2# will share with friends..", "#Person2# thinks #Person2#'s meal as perfect and orders dessert and tea to share with friends."], "prediction_text": "#Person1# asks #Person2# if their meal was good, and #Person2# replies that it was perfect. #Person1# offers to bring them a dessert, and #Person2# chooses the spicy rum apple crisp. #Person1# asks if they would like to split another one, and #Person2# agrees. #Person1# prepares their desserts and has someone bring them their drinks."}
{"answers": ["#Person1# and #Person2# flip a coin to decide which one should get the bottom bunk and #Person2# wins. They negotiate the setting of other things and #Person1# gets the desk by the window. They start to unpack.", "#Person1# and #Person2# initiate a plan to divide the furniture in the room. #Person1# takes the bottom bunk by flipping a coin whereas #Person1# takes the better stereos and the nicer desk.", "#Person2# has the bottom bunk by winning the coin flip with #Person1#. They decide to use #Person1#'s stereos because it's better. #Person1# has the desk by the window for it's fair. #Person1# will go out for food and come back to unpack after #Person2# has done."], "prediction_text": "#Person1# and #Person2# are deciding how to arrange their shared room. They decide to flip a coin for the bottom bunk, and #Person1# gets it. They also decide to use #Person1#'s stereo and place it according to a plan. #Person2# gets the desk by the window."}
{"answers": ["#Person2# helps Mr. Murray get a library card and reminds him of library rules.", "Mr. Murray has a library card after filling out the application and checked license by #Person2#.", "Mr. Murray wants a library card. #Person2# issues one to him after checking his application and driver license."], "prediction_text": "#Person1# wants to get a library card and #Person2# gives #Person1# an application to fill out at the counter. #Person2# asks for #Person1#'s driver's license and explains the rules and fines of the library."}
{"answers": ["#Person1# and #Person2# talk about working days. #Person2# has lunch breaks and doesn't mind cubicles, but #Person1# doesn't have coffee breaks and doesn't like cubicles.", "#Person2# describes how the working days are like, including the working hours and working contents, to #Person1#.", "#Person2# has a typical 9 to 5 job and gets a bonus each month if #Person2# works overtime. #Person2#'s boss doesn't mind coffee break but #Person1#'s boss does. They both work in a cubicle."], "prediction_text": "#Person1# asks #Person2# about their working day and #Person2# replies that they have a typical 9 to 5 job with a bonus, no overtime, an hour-long lunch break, and little paperwork. #Person1# mentions that their boss gets annoyed if they take coffee breaks, and #Person2# replies that they work in a cubicle but don't mind it."}
{"answers": ["Nick asks Alice how to use washing machines and dryers. Alice teaches him and she's surprised to find that he never washed clothes before. Nick tells her he is from Taiwan, where the mother does everything for the kids. Alice will help Nick to learn how to live on his own.", "Nick is new here and he has never done washing himself. So he asks a girl called Alice for help. Alice's surprised but still teaches him how to use the machines. Nick tells her his mother used to do everything for him.", "Nick has never used washing machines before, and he turns to a girl for help. Nick appreciates Alice's help and tells her his mother used to do washing for him. Alice will help him be more independent."], "prediction_text": "Nick has never used a washing machine before and Alice helps him use it. Nick explains that in Taiwan, children are expected to study hard and their mothers do everything for them, while Alice started doing her own laundry at age 12. Alice offers to help Nick learn how to use the machines."}
{"answers": ["#Person1# and #Person2# ask about each other's recent situation.", "#Person1# asks about the things that took up #Person2#.", "#Person1# is busy while #Person2# is flexible."], "prediction_text": "#Person1# and #Person2# are having a casual conversation, asking each other about their activities and exchanging farewells."}
{"answers": ["#Person1# tells Katie about her evaluation and expresses expectations for Katie's future work.", "#Person1# reviews Katie's working evaluation and comes up with new requirements for #Person2#.", "#Person2# talks about Katie's evaluation and wants Katie to do better when she doesn't have customers."], "prediction_text": "#Person1# and #Person2# are discussing Katie's evaluation. #Person1# mentions that Katie is always available to work but has arrived late several times, but has improved. #Person1# also mentions that Katie daydreams when she doesn't have a customer, and asks Katie to do other tasks without being asked."}
{"answers": ["#Person1# and #Person2# discuss grandpa's birthday. They decide on where and when to hold the party and what food and gift to prepare.", "#Person1# and #Person2# are planning a surprise party for grandpa's birthday and talk about what presents they should get him.", "It's #Person1# and #Person2#'s grandpa's birthday next week. #Person1# and #Person2# decide to have a party with jazz, pizza at their house. #Person1# and grandma will make food. #Person2# will get him a book. The party will be on Saturday."], "prediction_text": "#Person1# wants to have a surprise party for grandpa's birthday next week and suggests having it at their house. #Person2# agrees and suggests making pizzas and salad, and #Person1# thinks a book would be a good gift since grandpa reads a lot. #Person1# and #Person2# decide to have the party on Saturday."}
{"answers": ["Gian and Gina introduce themselves to each other. Gian introduces Gina to Robert.", "Gian and Gina introduce themselves to each other and Gian introduces the friend Robert to Gina.", "Gian tells Gina she is a self-employed journalist and introduces Robert to Gina."], "prediction_text": "Gian Luca Donatelli introduces himself to Gina, who is from Finland and is a journalist researching for an article on internet service providers. Gian Luca wants to introduce Gina to his friend who works for an Italian service provider."}
{"answers": ["Kathy complains about the noise and #Person2# explains it's because it's the year of the 17-year bird calls. Then they discuss where the birds live.", "Kathy thinks the countryside in the North Carolina Mountains is so noisy as birds come out at this period and #Person2# only hears those birds when #Person2# was in Atlanta", "Kathy is annoyed at the noise of bird calls. The birds only come out when it is cool and live in trees mostly. They are not in cities and maybe just in the East."], "prediction_text": "#Person1# is surprised by the noise of birds in the North Carolina Mountains, and #Person2# explains that it is a 17-year cycle and the birds only come out after a big storm, and they will be quiet after 4 or 5 weeks. #Person1# is worried about walking under the trees in the forest due to bird waste."}
{"answers": ["#Person1# helps #Person2# to order a sandwich, soup, and a drink for lunch.", "#Person2# orders a turkey sandwich with cheese and a beef with vegetable soup for lunch.", "#Person2# orders a turkey sandwich with cheese and a beef with vegetable soup at Danny's."], "prediction_text": "#Person1# takes the order of #Person2# at Danny's. #Person2# orders a turkey sandwich with cheese, soup, and a drink."}
{"answers": ["#Person1# checks James' packing situation and asks him about his jacket and camera. #Person1# asks James to pack cookies and to give them to his grandparents.", "James is waiting for David's return on his black jacket while #Person1# is examing other stuff that James is supposed to take for travel.", "James is waiting for David for his black jacket he lent to David before he goes to travel. He wants to wear it to take pictures. #Person1# asks James to take the cookies because his grandparents like to eat."], "prediction_text": "James is watching TV and packing his bag, but he is worried about being late for his train. He asks his friend if he has finished packing and if he has packed the cookies he made for his grandparents. James' friend tells him he will take the cookies with him and that David will arrive in about 10 minutes."}
{"answers": ["#Person1# and Ted talk about holidays. Ted will stay at home and #Person1# will go to China.", "Ted and #Person2# are discussing the place where they are going to take a holiday.", "Ted's wife will stay with her parents for a few weeks, but Ted doesn't like living with them. #Person1# will go to China with her husband."], "prediction_text": "#Person1# asks #Person2# where he is going for his holiday, and #Person2# replies that he hasn't decided yet. #Person1# suggests that he could go with his wife to her parents' countryside home, but #Person2# prefers to stay at home instead. #Person1# then mentions that she will spend a few weeks in China with her husband."}
{"answers": ["#Person1# wants to go to the cinema. Dad agreed and they will go to McDonald's after the movie.", "Dad agrees with #Person1#'s advice on going to the cinema and McDonald's this afternoon.", "#Person1# wants to watch No Country for Old Men with #Person1#'s dad. They will go to the cinema with #Person1#'s mom and eat at McDonald's after the movie."], "prediction_text": "#Person1# wants to go to the cinema with #Person2#, but #Person2# is unsure about the film showing. #Person1# checks the newspaper and finds that \"No Country for Old Men\" is playing. #Person2# suggests taking #Person1#'s mom along and going to McDonald's after the movie."}
{"answers": ["#Person2# tells #Person1# that #Person2#'s house was robbed.", "#Person2#'s home was robbed last night.", "#Person2# was robbed and called the police."], "prediction_text": "#Person1# notices #Person2# looking sad and asks if something is wrong. #Person2# explains that their house was robbed and all the furniture was stolen. #Person1# suggests calling the police."}
{"answers": ["Jack invites Charlie to play a new video game and Charlie is interested.", "Jack asks Charlie to come over and play the new video games with him.", "Jack invites Charlie to play video games after he picks up his dad. Charlie agrees."], "prediction_text": "Jack invites Charlie to his house after school to play a new video game, but Charlie needs to finish his homework first."}
{"answers": ["#Person2# explains to #Person1# about how #Person2# got interested in country music and started the program.", "#Person2# shares #Person2#'s career in the past days that is fully intertwined with country music.", "#Person2# tells #Person1# about #Person2#'s own experience about buying more country music than other kinds, starting Golden Country Time at CBC, and writing articles about country music."], "prediction_text": "#Person1# asks #Person2# how he got interested in country music, and #Person2# explains how he started working for a radio station and built his own library."}
{"answers": ["Alice guides #Person1# to use the washing machine and the dryer. Alice began to wash her clothes at the age of 12, but #Person1# has never done that before.", "#Person1# asks Alice how to use the washing machine and the right way to wash clothes on campus. #Person1# tells Alice it's #Person1#'s mother who does all the washing at home.", "#Person1# doesn't know how to use the washing machine and asks Alice for help. Alice tells #Person1# how to use it. #Person1#'s never washed clothes before because #Person1#'s mother did the washing for #Person1#."], "prediction_text": "Alice helps a new student, #Person1#, use the washing machines and dryers. #Person1# has never used a washing machine before and Alice explains how to use them. #Person1# has never washed clothes before and Alice tells her that American children are more independent."}
{"answers": ["Steve is looking for a new place to live and Matthew thinks his neighbor Mrs.Thou might help.", "Matthew and Steve meet after a long time. Steve is looking for a new place to live. Mathew's neighbor is moving out and They might be neighbors again.", "Steve has been looking for a place to live. Matthew indicates that his neighbor could be possibly available soon and Steve can go to have a look."], "prediction_text": "#Person1# and #Person2# haven't seen each other in a year and #Person1# is looking for a new place to live. #Person2# remembers a neighbor who has a spare apartment and offers to help #Person1# find it."}
{"answers": ["Frank invites Besty to the party to celebrate his big promotion. Besty couldn't wait for the party.", "Frank invites Betsy to the big promotion party and Betsy is happy to go.", "Frank invites Betsy to his party for his promotion and he can afford a big house now. Betsy is willing to go."], "prediction_text": "#Person1# tells #Person2# about his promotion and party, and #Person2# expresses excitement to attend. #Person1# mentions the party will be on Saturday and hopes to have 150 people attend, while #Person2# understands #Person1#'s concerns about work-life balance."}
