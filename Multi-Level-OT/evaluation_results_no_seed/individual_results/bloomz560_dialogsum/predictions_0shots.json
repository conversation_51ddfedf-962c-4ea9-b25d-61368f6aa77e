{"answers": "<PERSON><PERSON> <PERSON> helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "prediction_text": "The memo is in the office."}
{"answers": "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks <PERSON><PERSON> to send out a memo to all employees by the afternoon.", "prediction_text": "The memo is in the office."}
{"answers": "Ms<PERSON> takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists.", "prediction_text": "The memo is in the office."}
{"answers": "#Person2# arrives late because of traffic jam. #Person1# persuades #Person2# to use public transportations to keep healthy and to protect the environment.", "prediction_text": "The car is getting a lot more noisy."}
{"answers": "#Person2# decides to follow #<PERSON>1#'s suggestions on quitting driving to work and will try to use public transportations.", "prediction_text": "A traffic accident happened near the Carrefour intersection."}
{"answers": "#Person2# complains to #Person1# about the traffic jam, #Person1# suggests quitting driving and taking public transportation instead.", "prediction_text": "A traffic accident happened near the Carrefour intersection."}
{"answers": "#Person1# tells Kate that <PERSON>sha and <PERSON> get divorced. <PERSON> is surprised because she thought they are perfect couple.", "prediction_text": "<PERSON>, the mother, and the father are getting divorced."}
{"answers": "#Person1# tells <PERSON> that Masha and Hero are getting a peaceful divorce. <PERSON> feels surprised and asks about their kids.", "prediction_text": "<PERSON>, the mother, and the father are getting divorced."}
{"answers": "#Person1# and <PERSON> talk about the divorce between Masha and Hero. <PERSON> feels surprised because she thought they are well matched", "prediction_text": "Kate, Masha, and Hero are getting divorced."}
{"answers": "#Person1# and Brian are at the birthday party of Brian. Brian thinks #Person1# looks great and is popular.", "prediction_text": "Happy birthday!"}
{"answers": "#Person1# attends Brian's birthday party. Brian thinks #Person1# looks great and charming.", "prediction_text": "Happy birthday!"}
{"answers": "#Person1# has a dance with Brian at Brian's birthday party. Brian thinks #Person1# looks great and is popular.", "prediction_text": "Happy birthday!"}
{"answers": "#Person1# is surprised at the Olympic Stadium'volume, capacity and interior setting to #Person1#.", "prediction_text": "The Olympic Park is to be finished in June."}
{"answers": "#Person2# shows #Person1# around the constructing Olympic stadium and introduces the stadium.", "prediction_text": "The Olympic Park is to be finished in June."}
{"answers": "#Person2# introduces the Olympic Stadium's finishing time, capacity and interior setting to #Person1#.", "prediction_text": "The Olympic Park is to be finished in June."}
{"answers": "#Person1# wants to create a company and is going to write a business plan. #Person2# gives #Person1# suggestions on how to summarise business ideas, describe the service, differ from competitors and attract investment in a good business plan. #Person1# decides to stick to the old job.", "prediction_text": "#Person1#: I am going to start a business."}
{"answers": "#Person1# abandons the idea of creating a company after #Person2# explains what a business plan includes. #Person2# specifies that a well-written business plan includes an executive summary, goods and service description, strategy and implementation, and financial analysis.", "prediction_text": "#Person1#: I am going to start a business."}
{"answers": "#Person1# wants to start #Person1#'s own business, but #Person2# warns #Person1# of the hassle. #Person2# tells #Person1# what is needed in a business plan and #Person1# decides to stick to the old job for now.", "prediction_text": "#Person1#: I am going to start a business."}
{"answers": "#Person2# feels itchy. #Person1# doubts it is chicken pox and asks #Person2# to get away. #Person2# doesn't believe it.", "prediction_text": "A rash, a rash, a rash!"}
{"answers": "#Person1# suspects that #Person2# has chicken pox and wants to keep away from #Person2#. #Person2# thinks #Person1# is exaggerating.", "prediction_text": "A rash, a rash, a rash!"}
{"answers": "#Person1# thinks #Person2# has chicken pox and warns #Person2# about the possible hazards but #Person2# thinks it will be fine.", "prediction_text": "A rash or an allergy?"}
{"answers": "#Person2# is checking out and asks #Person1# for the bill. #Person1# gives #Person2# a wrong bill at first then corrects it.", "prediction_text": "A man is in a room, and he is asking a question. He is in the room, and he is in a room."}
{"answers": "#Person1# helps #Person2# correct a mischarged bill on laundry service and helps #Person2# check out.", "prediction_text": "A man is in a room, and he is asking a question. He is in the room, and he is in a room."}
{"answers": "#Person2# finds #Person2# being mischarged. #Person1# corrects the bill and #Person2# pays for it.", "prediction_text": "A man is in a room, and he is asking a question. He is in the room, and he is in a room."}
{"answers": "#Person1# is begging Steven to persuade his wife not to divorce him. Steven agrees.", "prediction_text": "Two men are talking about a marriage."}
{"answers": "Steve will try to persuade #Person1#'s wife not to divorce #Person1# as #Person1# swears to remain faithful forever.", "prediction_text": "Two men are talking about a marriage."}
{"answers": "#Person1# begs Steven's to persuade #Person1#'s wife not to divorce him, and Steven agrees.", "prediction_text": "Two men are talking about a marriage."}
{"answers": "#Person1# and #Person2# are talking about Abraham Lincoln. They think he was a noble man.", "prediction_text": "Person 1"}
{"answers": "#Person2# admires Abraham Lincoln for his perseverance, courage and humility.", "prediction_text": "Person 1"}
{"answers": "#Person1# and #Person2# talk about Abraham Lincoln and his glorious history. They both admire him.", "prediction_text": "Person 1"}
{"answers": "#Person2# plans to have a trip in Hebei but #Person1# says there are sandstorms in there.", "prediction_text": "The first person to go to Hebei is the person in the first row."}
{"answers": "#Person1# tells #Person2# Hebei was experiencing severe sandstorms and sandstorms threaten people's health.", "prediction_text": "The first person to go to Hebei is the person in the first row."}
{"answers": "#Person2# wants to travel to Hebei but #Person1# informs #Person2# of terrible sandstorms there.", "prediction_text": "The people in Hebei are suffering from severe sandstorms."}
{"answers": "#Person2# comes to the birthday party of Francis and sends him a remote car model.", "prediction_text": "Francis, a car model, invited me to his birthday party."}
{"answers": "#Person2# gives Francis a car model that both of them love as a birthday gift.", "prediction_text": "Francis, a car model, invited me to his birthday party."}
{"answers": "#Person2# gives Francis a nice car model as a birthday gift and Francis appreciates it.", "prediction_text": "Francis, a car model, invited me to his birthday party."}
{"answers": "Tony got caught cheating and feels ashamed. Steven tells him to learn from it and study hard.", "prediction_text": "A teacher told a story to a class."}
{"answers": "Tony tells Steven that he failed a class because of cheating and he feels awful.", "prediction_text": "A teacher told a story to a class."}
{"answers": "Tony tells Steven that he cheated on the exam and realized his mistake.", "prediction_text": "A teacher told a story to a class."}
{"answers": "#Person1# is catching a train. Tom asks #Person1# not to hurry.", "prediction_text": "Tom is in the train."}
{"answers": "#Person1# is in a hurry to catch a train. Tom tells #Person1# there is plenty of time.", "prediction_text": "Tom is in the train."}
{"answers": "#Person1# is rushing to catch a train but Tom thinks it isn't necessary.", "prediction_text": "Tom is in the train."}
{"answers": "#Person1# wants to adjust #Person1#'s life and #Person2# suggests #Person1# be positive and stay healthy.", "prediction_text": "A man and a woman are talking."}
{"answers": "#Person1# has a bad lifestyle. #Person2# kindly advises #Person1# to be positive.", "prediction_text": "A man and a woman are talking."}
{"answers": "#Person2# hopes #Person1# will become healthy and positive.", "prediction_text": "Person 1"}
{"answers": "#Person1# tells #Person2# that Ruojia is married and will have a party tonight. #Person2#'s surprised to know that. They will bring their gifts to bless her.", "prediction_text": "Ruojia and her friends are going to a party tonight."}
{"answers": "#Person2# is surprised to know from #Person1# that Ruojia is married. Then #Person2# finds Ruojia has sent an email about it. They will go to Ruojia's party and give their presents to her.", "prediction_text": "Ruojia and her friends are going to a party tonight."}
{"answers": "#Person2# is surprised that Ruojia's married. #Person1# and #Person2# will go to her party and give their presents to wish her a happy marriage.", "prediction_text": "Ruojia and her friends are going to a party tonight."}
{"answers": "#Person2# at first thinks #Person1#'s behaviour cruel but finally joins #Person1#.", "prediction_text": "#Person1#: I know you are."}
{"answers": "#Person1# plans on playing a trick to others. #Person2# thinks it's cruel at first but then joins.", "prediction_text": "#Person1#: I know you are."}
{"answers": "#Person1# is about to make a prank. #Person2# thinks it's cruel at first but then joins.", "prediction_text": "#Person1#: I know you are."}
{"answers": "Mike is describing his sister to #Person1#.", "prediction_text": "The two men are talking."}
{"answers": "Mike describes to #Person1# his sister's characters and personality.", "prediction_text": "The two men are talking."}
{"answers": "#Person1# asks Mike about his sister.", "prediction_text": "The two men are talking."}
{"answers": "#Person1# feels sick and #Person2# gives #Person1# a check-up.", "prediction_text": "A man is in a hospital, and he is a little dizzy."}
{"answers": "#Person2# finds that #Person1# has a fever and says #Person1# should've called in sick earlier.", "prediction_text": "A man is in a hospital, and he is a little dizzy."}
{"answers": "#Person2# checks #Person1#'s physical condition and finds #Person1# has a fever.", "prediction_text": "A man is in a hospital, and he is a little dizzy."}
{"answers": "#Person1# helps #Person2# to choose a new phone.", "prediction_text": "Person1 wants to buy a new mobile phone. Person2 wants to make a video call."}
{"answers": "#Person2# wants to buy a new mobile phone from #Person1#.", "prediction_text": "Person1 wants to buy a new mobile phone. Person2 wants to make a video call."}
{"answers": "#Person2# wants to buy a new mobile phone from #Person1#.", "prediction_text": "Person1 wants to buy a new mobile phone. Person2 wants to make a video call."}
{"answers": "Frank got a new job and is telling Judy not only the heavy schedule but also the benefits of this job.", "prediction_text": "Two people are working for the Post Office."}
{"answers": "Frank explains to Judy the reason why he took a job at the Post office is that the government offers excellent health insurance benefits for its employees.", "prediction_text": "Two people are working for the Post Office."}
{"answers": "Frank describes his new job's schedule and insurance at the Post Office to Judy.", "prediction_text": "Two people are working for the Post Office."}
{"answers": "#Person2# is describing the educational background and previous working experience to #Person1# in a job interview.", "prediction_text": "Person 1, a computer programmer, is a good communicator, a good writer, a good computer programmer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer"}
{"answers": "#Person2# is being interviewed by #Person1#. #Person2# is equipped with a bunch of valuable office skills.", "prediction_text": "Person 1, a computer programmer, is a good communicator, a good writer, a good computer programmer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer"}
{"answers": "#Person1# asks about #Person2#'s special skills, training, previous working experience and other qualification for the job.", "prediction_text": "Person 1, a computer programmer, is a good communicator, a good writer, a good computer programmer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer, a good computer engineer"}
{"answers": "#Person1# is not satisfied with the steak and #Person2# will change it.", "prediction_text": "Person1, Person2, and Person3 are talking."}
{"answers": "#Person2# will change a steak for #Person1# as that one doesn't satisfy #Person1#.", "prediction_text": "Person1, Person2, and Person3 are talking."}
{"answers": "#Person1# is not satisfied with #Person1#'s steak and asks to change one, and #Person2# agrees.", "prediction_text": "Person1, Person2, and Person3 are talking."}
{"answers": "#Person1# congratulates Tom for achieving the Nobel Prize.", "prediction_text": "Tom, Tom, Tom!"}
{"answers": "#Person1# tells Tom that his novel has won the Nobel Prize.", "prediction_text": "Tom, Tom, Tom!"}
{"answers": "#Person2# tells Tom he won the Nobel Prize.", "prediction_text": "Tom, Tom, Tom!"}
{"answers": "#Person1# is interviewing #Person2# about #Person2#'s ability and previous experience.", "prediction_text": "Person1 is a student engineer. Person2 is a science major."}
{"answers": "#Person1# asks #Person2# about #Person2#'s capacities and past working experience during an interview.", "prediction_text": "Person1 is a student engineer. Person2 is a science major."}
{"answers": "#Person1# asks #Person2# about #Person2#'s qualification for the job.", "prediction_text": "Person1 is a student engineer. Person2 is a science major."}
{"answers": "#Person1# and #Person2# are talking about some personal facts of drinking. #Person2# drinks a lot, while #Person1# cannot drink much for health reasons. They decide to have a drink together tomorrow night.", "prediction_text": "Person1 and Person2 are talking about how much they can drink. Person1 is a light weight. Person2 is a big drinker."}
{"answers": "#Person2#, a heavy drinker, invites #Person1#, a light drinker to go out the next day. #Person2# chooses a place that has a special on pitchers.", "prediction_text": "Person1 and Person2 are talking about how much they can drink. Person1 is a light weight. Person2 is a big drinker."}
{"answers": "#Person1# and #Person2# talk about their drinking capacity and their drinking habits. They decide to have a drink together tomorrow night.", "prediction_text": "Person1 and Person2 are talking about how much they can drink. Person1 is a light weight. Person2 is a big drinker."}
{"answers": "May is helping her mother to do some preparation for the picnic.", "prediction_text": "Daniel and Daniel's parents are going to the picnic."}
{"answers": "May's mother asks May for help in preparing for a picnic. May gives her a hand.", "prediction_text": "Daniel and Daniel's parents are going to the picnic."}
{"answers": "Mom asks May to help to prepare for the picnic and May agrees.", "prediction_text": "Daniel and Daniel's parents are going to picnic."}
{"answers": "Muriel Douglas and James meet each other and talk about what they have done during the holiday.", "prediction_text": "Muriel Douglas, a new account manager, is meeting with us."}
{"answers": "James and Muriel are talking while waiting for Susan, Muriel's associate. They talk about how they spent the holiday with their families.", "prediction_text": "Muriel Douglas, a new account manager, is meeting with us."}
{"answers": "Muriel Douglas and James send greetings and talk about their holiday before the meeting. Douglas stayed in L.A., while James went to Tahoe.", "prediction_text": "Muriel Douglas, a new account manager, is meeting with us."}
{"answers": "#Person1# wants to withdraw money from an ATM, but the ATM automatically transfers 10000 USD to the World Wildlife Foundation after confirming the withdrawal option. #Person1# gets mad and somehow locked in.", "prediction_text": "A man is being asked to withdraw his money from a bank."}
{"answers": "#Person1# run out of money because of a girl, and is withdrawing money from an ATM. But the ATM seems to go wrong and transfers #Person1#'s money to the World Wildlife Foundation, driving #Person1# crazy.", "prediction_text": "A man is being asked to withdraw his money from a bank."}
{"answers": "#Person1# is withdrawing money from an ATM. But the ATM wrongly transfers #Person1#'s money to the World Wildlife Foundation. It drives #Person1# crazy.", "prediction_text": "A man is being asked to withdraw his money from a bank."}
{"answers": "#Person2# tells #Person1# #Person2#'s communication strategy.", "prediction_text": "The two people are talking about the social media. Person1 is a social person. Person2 is an outgoing person."}
{"answers": "Since #Person2# is very social, #Person1# asks for the communication strategy.", "prediction_text": "The two people are talking about the social media. Person 1 is a social person. Person 2 is an outgoing person."}
{"answers": "#Person2# shares #Person2#'s communication strategy with #Person1#.", "prediction_text": "The two people are talking about the social media. Person 1 is a social person. Person 2 is an outgoing person."}
{"answers": "Mr. Polly is tired and wants a break from work. #Person1# cannot buy a bottle of soft drink for him.", "prediction_text": "Two people are talking about a problem."}
{"answers": "Mr. Polly wants to get a break from work and he asks #Person1# to buy a drink for him, but #Person1# refuses.", "prediction_text": "Two people are talking about a problem."}
{"answers": "Mr. Polly asks #Person1#'s help to buy a drink, but #Person1# refuses.", "prediction_text": "Two people are talking about a problem."}
{"answers": "Francis and Monica are discussing when to work on the financial report.", "prediction_text": "The report is in the form of a report."}
{"answers": "Francis and Monica manage to find time to work on a report together.", "prediction_text": "The report is in the form of a report."}
{"answers": "Francis and Monica negotiate on the time to work on the report.", "prediction_text": "The report is in the form of a report."}
{"answers": "#Person1# joins #Person2#'s interview workshop. They discuss the tips to improve their interview performance.", "prediction_text": "Person1, Person2, and Person3 are all learning the same things."}
{"answers": "#Person1# takes an interview workshop. #Person2# offer #Person1# some useful tips on getting ready for an interview.", "prediction_text": "Person1, Person2, and Person3 are all learning the same things."}
{"answers": "In the workshop, #Person2# offer #Person1# some suggestions on how to perform well in interviews.", "prediction_text": "Person1, Person2, and Person3 are all learning the same things."}
{"answers": "#Person1# and Mike are discussing what kind of emotion should be expressed by Mike in this play. They have different understandings.", "prediction_text": "Let's start with the first person."}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "Let's start with the first person."}
{"answers": "#Person1# wants Mike to act more angry, but Mike thinks he should act both angry and sad.", "prediction_text": "Let's start with the first person."}
{"answers": "#Person1# greets Mrs. Todd and then they say goodbye to each other.", "prediction_text": "Jane and her husband are in a shopping trip."}
{"answers": "#Person1# comes over to say hello to Mrs. Todd, and will go shopping afterwards.", "prediction_text": "Jane and her husband are in a shopping trip."}
{"answers": "#Person1# visits Mrs. Todd, and Mrs. Todd appreciates that.", "prediction_text": "People are coming to see Jane."}
{"answers": "Bill is tired. Bill and #Person1# talk about Bill's brother.", "prediction_text": "The man in the car is tired."}
{"answers": "#Person1# has a chat with Bill, and learns updates about Bill and his brother. #Person1# asks for time because #Person1# will go meet a friend.", "prediction_text": "The man in the car is tired."}
{"answers": "#Person1# suggests Bill take it easy and asks him about his brother. #Person1# also synchronizes the time with Bill.", "prediction_text": "The man in the car is tired."}
{"answers": "Simon and Cleo have different opinions towards the demonstration to help stop the spread of nuclear weapons. Cleo thinks it is useless, while Simon considers that Cleo should go to the demonstration.", "prediction_text": "Simon, Simon, Simon."}
{"answers": "Cleo has no intention to attend the demonstration to help stop the spread of nuclear weapons, because Cleo hates police standing by with tear gas. Simon tries to change Cleo's mind but it doesn't work.", "prediction_text": "Simon, Simon, Simon."}
{"answers": "Simon and Cleo argue the effectiveness of demonstrations. Simon thinks they are helpful, but Cleo disagrees and refuses to go to the demonstration of helping stop the spread of nuclear weapons with Simon.", "prediction_text": "Simon, Simon, Simon."}
{"answers": "#Person2# let in someone and #Person1# thinks it was #Person2#'s fault.", "prediction_text": "#Person1#: I don't know, I have a question."}
{"answers": "#Person1# blames #Person2# for letting someone in without much discretion.", "prediction_text": "#Person1#: I don't know if I can be a good person"}
{"answers": "#Person1# advises #Person2# not to let anyone in casually.", "prediction_text": "#Person1#: I don't know if I can be a good person"}
{"answers": "Mark wants to borrow Maggie's class notes. Maggie suggests Mark copy them in the library and invites him to be study partners.", "prediction_text": "The two people are going to the library."}
{"answers": "Mark asks Maggie for her history notes because Mark has been too tired in class. They become study partners at the end.", "prediction_text": "The two people are going to the library."}
{"answers": "Mark asks Maggie for her notes because Mark's notes aren't good. They decide to be study partners and study together.", "prediction_text": "The two people are going to the library."}
{"answers": "#Person2# wants to take a course of Professor Turner and Professor agrees if #Person2# achieves the allowance of Professor Burman.", "prediction_text": "A junior is taking a geology course."}
{"answers": "#Person2#, a junior student, wants to enroll in Professor Turner's course for seniors very much. Professor Turner will ask for another professor's opinion.", "prediction_text": "A junior is taking a geology course."}
{"answers": "#Person2# wants to enroll in Professor Turner's course, and Professor Turner agrees to consider his application.", "prediction_text": "A junior is taking a geology course."}
{"answers": "#Person1# wants to change the broken pendant in #Person2#'s shop.", "prediction_text": "Person1 and Person2 are going to the hotel to show the pendant."}
{"answers": "#Person1# goes back to #Person2#'s shop to replace a broken pendant.", "prediction_text": "Person1 and Person2 are going to the hotel to show the pendant."}
{"answers": "#Person1# wants a product changed from #Person2#, and #Person2# agrees.", "prediction_text": "Person1 and Person2 are going to the hotel to show the pendant."}
{"answers": "#Person1# is showing the new clothes to #Person2#, but #Person2# is busy watching the games. They quarrel and get angry.", "prediction_text": "Person1 and Person2 are trying to have a conversation."}
{"answers": "#Person1# and #Person2# have a serious quarrel over whether shopping for clothes or watching a sports game is more important.", "prediction_text": "Person1 and Person2 are trying to have a conversation."}
{"answers": "#Person1# asks #Person2#'s opinion on new clothes, but #Person2# only focuses on the game and feels annoyed. They are both angry and argue.", "prediction_text": "Person1 and Person2 are trying to have a conversation."}
{"answers": "#Person1# gives suggestions on job choices to #Person2#. #Person2# likes interactive media.", "prediction_text": "Person1 wants to work in the media. Person2 wants to work in the media."}
{"answers": "#Person1# and #Person2# are looking at jobs. #Person2# is interested in the media industry.", "prediction_text": "Person1 wants to work in the media. Person2 wants to work in the media."}
{"answers": "#Person1# offers some job choices to #Person2#, but #Person2# doesn't like them.", "prediction_text": "Person1 wants to work in the media. Person2 wants to work in the media."}
{"answers": "#Person2# feels bored with the meeting. #Person1# gives #Person2# some suggestions on how to entertain but finally asks #Person2# to keep quiet.", "prediction_text": "A man is getting up to speak."}
{"answers": "#Person1#, a reporter, takes #Person2# to a forum. #Person2# is bored and tries to find a pastime.", "prediction_text": "A man is getting up to speak."}
{"answers": "#Person2# tells #Person1# that #Person2# is bored and explains the reasons, so #Person1# suggests #Person2# read some old newspapers and be quiet.", "prediction_text": "A man is getting up to speak."}
{"answers": "Sarah is considering moving. #Person1# gives her advice on buying a house.", "prediction_text": "Sarah and her husband are thinking about moving."}
{"answers": "Sarah wants to move. #Person1# suggests buying a cheaper house and offers information on possible options.", "prediction_text": "Sarah and her husband are thinking about moving."}
{"answers": "Sarah tells #Person1# that she can't afford a new house. #Person1# says #Person1#'s sister-in-law bought a cheap house and suggests Sarah should buy a house far from the city center.", "prediction_text": "Sarah and her husband are thinking about moving."}
{"answers": "#Person1# introduces Mark Leach to this week's People You Meet. Mark Leach describes his job responsibilities and gives advice to people coming to Britain.", "prediction_text": "Today, we are presenting to you a new guest, Mark Leach."}
{"answers": "#Person1# hosts the week's People You Meet, featuring Mark Leach. Mark introduces his work as an information officer in London.", "prediction_text": "Today, we are presenting to you a new guest, Mark Leach."}
{"answers": "Mark Leach introduces his information service and his work to the audience. He also offers suggestions for people coming to Britain.", "prediction_text": "Today, we are presenting to you a new guest, Mark Leach."}
{"answers": "Lin Fang and Lucy are talking about their favourite subjects.", "prediction_text": "People are talking about English."}
{"answers": "Lin Fang and Lucy are talking about how they like different subjects.", "prediction_text": "People are talking about English."}
{"answers": "Lucy likes English and P.E. best, but Lin Fang's favorite is Chinese and Science.", "prediction_text": "People are talking about their favorite subjects."}
{"answers": "James comes to help Mrs. Thomas to do some housework on the weekend. James is saving up to buy a bike.", "prediction_text": "The man in the car is helping the man in the car."}
{"answers": "James helped Mrs. Thomas with housework since she has broken her legs, and he wanted to save money for a bike.", "prediction_text": "The man in the car is helping the man in the car."}
{"answers": "James helps Mrs. Thomas to do chores because James wanted to save money and Mrs. Thomas is incapable.", "prediction_text": "The man in the car is helping the man in the car."}
{"answers": "#Person1# and #Person2# are talking about the low temperature at night, although spring has come.", "prediction_text": "People are getting a bit more tired of the heat."}
{"answers": "#Person1# and #Person2# agree that it still felt very cold in spring.", "prediction_text": "People are getting a bit more tired of the heat."}
{"answers": "#Person1# and #Person2# talk about the weather and how to keep warm.", "prediction_text": "People are getting a bit more tired of the heat."}
{"answers": "#Person1# and Mike are discussing what kind of emotion should be expressed by Mike in this play. They have different understandings.", "prediction_text": "Let's start from the beginning."}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "Let's start from the beginning."}
{"answers": "#Person1# wants Mike to act more angry, but Mike thinks he should act both angry and sad.", "prediction_text": "Let's start from the beginning."}
{"answers": "#Person1# takes a taxi to the Friendship Hotel for something important.", "prediction_text": "Two people are going to a friend's hotel."}
{"answers": "#Person2# drives #Person1# to the Friendship Hotel as fast as #Person2# can be.", "prediction_text": "Two people are going to a friend's hotel."}
{"answers": "The taxi driver takes #Person1# to the Friendship Hotel quickly.", "prediction_text": "Two people are going to a friend's hotel."}
{"answers": "#Person1# doesn't have any change for the bus because #Person1# bought some chips. #Person2# suggests #Person1# get a transfer from the bus driver.", "prediction_text": "Two people are talking. They are talking about the bus."}
{"answers": "#Person1#'s broke, so #Person2# suggests #Person1# get a free bus transfer.", "prediction_text": "Two people are talking. They are talking about the bus."}
{"answers": "#Person1#'s broke, so #Person2# suggests #Person1# get a transfer from the bus driver to go home.", "prediction_text": "Two people are talking. They are talking about the bus."}
{"answers": "#Person2# tells #Person1# information about their company and its surroundings.", "prediction_text": "#Person1#: How long does it take to get to downtown from here?"}
{"answers": "#Person1# asks #Person2# about the company's surroundings, location, and staff restaurant.", "prediction_text": "#Person1#: How long does it take to get to downtown from here?"}
{"answers": "#Person2# introduces the surroundings and information about their company to #Person1#.", "prediction_text": "#Person1#: How long does it take to get to downtown from here?"}
{"answers": "#Person1# and #Person2# are gossiping about Lulu and Vic's break-up.", "prediction_text": "A man and a woman are in a relationship."}
{"answers": "#Person1# and #Person2# are talking about Lulu and Vic's breakup.", "prediction_text": "A man and a woman are in a relationship."}
{"answers": "#Person1# and #Person2# talk about Lulu and Vic's breakup.", "prediction_text": "A man and a woman are in a relationship."}
{"answers": "Dave Thomson calls Jim but Jim went out. Sally will tell Jim Dave called.", "prediction_text": "Jim is not in."}
{"answers": "Dave calls Jim but Jim is out. Sally tells Dave Jim's whereabouts.", "prediction_text": "Jim is not in."}
{"answers": "Dave Thomson calls to speak to Jim but Jim is out.", "prediction_text": "Jim is not in."}
{"answers": "#Person2# shows #Person1# the way to City Hall patiently.", "prediction_text": "A man is following a man."}
{"answers": "#Person1# asks #Person2# the way to City Hall and #Person2# answers with patience.", "prediction_text": "A man is following a man."}
{"answers": "#Person2# patiently shows #Person1# the way to City Hall.", "prediction_text": "A man is following a man."}
{"answers": "#Person1# lost #Person1#'s passport. #Person2# suggests #Person1# fill in the lost property report and inform the embassy of the situation so they can issue a new passport in case it cannot be found.", "prediction_text": "Person1, Person2, and Person3 are in a hotel room."}
{"answers": "#Person1# asks #Person2# for help to find #Person1#'s passport. #Person2# checks the shoe department but finds nothing. Thus, #Person2# lets #Person1# fill in a report and contact the embassy.", "prediction_text": "Person1, Person2, and Person3 are in a hotel room."}
{"answers": "#Person1# lost the passport. #Person2# suggests #Person1# fill in the lost property report and contact the embassy.", "prediction_text": "Person1, Person2, and Person3 are in a hotel room."}
{"answers": "Leah informs Nathaniel that Ms. Collins will not be able to call him back until next week, so he needs to book an appointment with someone else from the International Student Office. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "Nathaniel and Leah are meeting in the International Student Office."}
{"answers": "Leah informs Nathaniel that Ms. Collins won't be available for their appointment and explains the reason. Then Nathaniel decides to speak with someone else.", "prediction_text": "Nathaniel and Leah are meeting in the International Student Office."}
{"answers": "The International Student Office calls Nathaniel to inform him that he has to book another appointment with someone else as Ms. Collins is not available. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "Nathaniel and Leah are meeting in the International Student Office."}
{"answers": "#Person1# and #Person2# feel scared when heard Sarah insists on marrying Dick.", "prediction_text": "A man and a woman."}
{"answers": "#Person1# and #Person2# are shocked about Sarah's love for Dick.", "prediction_text": "A man and a woman."}
{"answers": "#Person1# and #Person2# are surprised to know Sarah wants to marry Dick.", "prediction_text": "A man and a woman."}
{"answers": "#Person1# and #Person2# are at a nice party. #Person2# has a crush on Janice but is afraid of talking to her. #Person1# wishes #Person2# good luck.", "prediction_text": "A party is on!"}
{"answers": "#Person1# and #Person2# talk about girls at a party and #Person2# tells #Person1# that #Person2# likes Janice but he feels nervous talking to girls. #Person1# encourages #Person2#.", "prediction_text": "A party is on!"}
{"answers": "#Person1# and #Person2# are at an exciting party. #Person2# has a crush on Janice and #Person1# encourages him to have a try to talk to her.", "prediction_text": "A party is on!"}
{"answers": "Jack tells #Person1# that business communication is his favorite last year and #Person1# will check it.", "prediction_text": "Person1 and Person2 are talking about their favorite class. Person1 is a business communication teacher. Person2 is a social science teacher."}
{"answers": "#Person1# and Jack talk about Jack's classes last year.", "prediction_text": "Person1 and Person2 are talking about their favorite class. Person1 is a business communication teacher. Person2 is a social science teacher."}
{"answers": "#Person1# and Jack are talking about Jack's favorite classes.", "prediction_text": "Person1 and Person2 are talking about their favorite class. Person1 is a business communication teacher. Person2 is a social science teacher."}
{"answers": "#Person1# and #Person2# are talking about the weather in different seasons in Beijing. #Person2# reminds #Person1# to put on more clothes in the evening and not to catch a cold.", "prediction_text": "People are talking about the weather."}
{"answers": "#Person1# and #Person2# are talking about the weather in Beijing. #Person2# tells #Person1# it's cold in the evening around this time.", "prediction_text": "People are talking about the weather."}
{"answers": "#Person1# and #Person2# are talking about the heavy rain last night and the weather in Beijing.", "prediction_text": "People are talking about the weather."}
{"answers": "#Person1# invites #Person2# to watch a movie together that night. #Person2# wants to see nothing but a war movie.", "prediction_text": "Person1 and Person2 are going to see a horror movie."}
{"answers": "#Person1# and #Person2# talk about what kind of movies to watch and they decided to watch a war movie.", "prediction_text": "Person1 and Person2 are going to see a horror movie."}
{"answers": "#Person1# invites #Person2# to movie and #Person2# wants to see a war movie.", "prediction_text": "The two people are going to see a horror movie tonight."}
{"answers": "Adam is showing #Person1# around his school. #Person1# envies Adam and hoped #Person1# could enter this school one day.", "prediction_text": "Two people are talking about the school."}
{"answers": "Adam shows #Person1# around his school and introduces the library, the new classroom building, and the swimming pool.", "prediction_text": "Two people are talking about the school."}
{"answers": "Adam shows #Person1# around the school and introduces buildings and facilities in the school to #Person1#.", "prediction_text": "Two people are talking about the school."}
{"answers": "#Person1# and #Person2# feel happy because #Person1# is pregnant.", "prediction_text": "The person in the story is a mother."}
{"answers": "#Person1# and #Person2# are happy about #Person1#'s pregnancy.", "prediction_text": "The person in the story is a mother."}
{"answers": "#Person1# is pregnant. She and #Person2# feel happy.", "prediction_text": "The person in the story is a mother."}
{"answers": "#Person1# and #Person2# think John's fallen for a girl.", "prediction_text": "A man and a girl."}
{"answers": "#Person1# and #Person2# talk about John's dating life.", "prediction_text": "A man and a girl."}
{"answers": "#Person1# and #Person2# are talking about a couple.", "prediction_text": "A man and a girl."}
{"answers": "#Person1# is giving #Person2# some advice for upgrading #Person2#'s system, such as adding a painting program and a faster processor.", "prediction_text": "Person1 and Person2 are discussing the need to upgrade their software. Person1 is a software developer, and Person2 is a computer engineer."}
{"answers": "#Person1# tells #Person2# how to upgrade #Person2#'s system for better software and hardware.", "prediction_text": "Person1 and Person2 are discussing the need to upgrade their software. Person1 is a software developer, and Person2 is a computer engineer."}
{"answers": "#Person1# teaches #Person2# how to upgrade software and hardware in #Person2#'s system.", "prediction_text": "Person1 and Person2 are discussing a new software program. Person1 is a software engineer. Person2 is a software engineer."}
{"answers": "#Person1# is driving #Person2# to an inn. They talk about their careers, ages, and where they was born.", "prediction_text": "A. The Holiday Inn"}
{"answers": "#Person1# drives #Person2# to an inn and they have a talk. #Person2# is 26 and had a business trip to China. #Person1# is 40 years old American.", "prediction_text": "A. The Holiday Inn"}
{"answers": "#Person1# drives #Person2# from the airport to an inn and they have a casual talk about themselves.", "prediction_text": "A. The Holiday Inn"}
{"answers": "#Person1# wants to lose weight. #Person2# suggests #Person1# take an exercise class to exercise more.", "prediction_text": "Person1, Person2, Person3, Person4, Person5, Person6, Person7, Person8, Person9, Person10, Person11, Person12, Person13, Person14, Person15, Person16, Person17, Person18, Person19, Person20, Person21, Person22, Person23, Person24, Person25, Person26, Person27, Person28, Person29, Person30, Person31, Person32, Person33, Person34, Person35, Person36, Person37, Person38, Person39, Person40, Person41, Person42, Person43, Person44, Person45, Person46, Person47, Person48"}
{"answers": "#Person2# offers #Person1# some suggestions to lose weight.", "prediction_text": "Person1, Person2, Person3, Person4, Person5, Person6, Person7, Person8, Person9, Person10, Person11, Person12, Person13, Person14, Person15, Person16, Person17, Person18, Person19, Person20, Person21, Person22, Person23, Person24, Person25, Person26, Person27, Person28, Person29, Person30, Person31, Person32, Person33, Person34, Person35, Person36, Person37, Person38, Person39, Person40, Person41, Person42, Person43, Person44, Person45, Person46, Person47, Person48"}
{"answers": "#Person2# gives #Person1# some suggestions on how to lose weight.", "prediction_text": "Person1, Person2, Person3, Person4, Person5, Person6, Person7, Person8, Person9, Person10, Person11, Person12, Person13, Person14, Person15, Person16, Person17, Person18, Person19, Person20, Person21, Person22, Person23, Person24, Person25, Person26, Person27, Person28, Person29, Person30, Person31, Person32, Person33, Person34, Person35, Person36, Person37, Person38, Person39, Person40, Person41, Person42, Person43, Person44, Person45, Person46, Person47, Person48"}
{"answers": "James reserves a dining room for eight at a restaurant. #Person1# will ask the waitress to show him the way.", "prediction_text": "The restaurant is not open."}
{"answers": "#Person1# confirms Jame's reservation at the restaurant and asks a waitress to show him the way.", "prediction_text": "The restaurant is not open now. Please see the waitress."}
{"answers": "James has reserved a dining room and #Person1#'ll asks the waitress to show him the way.", "prediction_text": "The restaurant is not open now. Please see the waitress."}
{"answers": "#Person1# is visiting a large plant and #Person2# introduces its basic information.", "prediction_text": "Two people are working in a plant."}
{"answers": "#Person2# introduces the area of the plant, its history, size, and function to #Person1#.", "prediction_text": "Two people are working in a plant."}
{"answers": "#Person1# and #Person2# visit a plant. #Person2# introduces its occupy, history, and employee number.", "prediction_text": "Two people are working in a plant. The first is a man, the second a woman."}
{"answers": "#Person1# is asking Rebecca about her working experience. She worked for York Herald as an intern and then as a junior local news reporter. After that, she began to work for London Weekly.", "prediction_text": "#Person1#: I was a junior news reporter in the York Herald."}
{"answers": "Rebecca tells #Person1# about her working experiences. Rebecca first worked for the York Herald after her graduation, and then for the London Weekly.", "prediction_text": "#Person1#: I was a junior news reporter in the York Herald."}
{"answers": "#Person1# interviews Rebecca and asks her about her previous working experiences.", "prediction_text": "#Person1#: I was a junior news reporter in the York Herald."}
{"answers": "#Person1# and #Person2# will do a group report, so they plan to buy something to make posters. They make a shopping list before they go and find items according to signs on shelves.", "prediction_text": "#Person1#: I need to do a group report tomorrow."}
{"answers": "#Person1# and #Person2# make a shopping list and #Person1# helps #Person2# to find goods to make a poster for their group report tomorrow.", "prediction_text": "#Person1#: I need to do a group report tomorrow."}
{"answers": "#Person1# and #Person2# are preparing for the group report tomorrow and making a shopping list of things used to make the posters.", "prediction_text": "#Person1#: I need to do a group report tomorrow."}
{"answers": "Mary is tired because of finding jobs in the market. #Person1# suggests Mary apply for jobs on the Internet and says she could place her information on the Job Wanted channel on a recruitment web. Mary will try at once.", "prediction_text": "Mary:"}
{"answers": "#Person1# advises Mary to try applying for a job online and teaches her how to do it. Mary will try it.", "prediction_text": "Mary, a new employee, is tired."}
{"answers": "Mary goes to the personnel market every day and is tired. #Person1# suggests she go job hunting online. Mary will try it.", "prediction_text": "Mary, a new employee, is tired."}
{"answers": "#Person2# is making a shopping budget to save the rest of the money. #Person1# thinks it's a good idea.", "prediction_text": "Person1 and Person2 are working on a budget."}
{"answers": "#Person2# tells #Person1# #Person2# plan to save money and #Person1# thinks it is good.", "prediction_text": "Person1 and Person2 are working on a budget."}
{"answers": "#Person2# introduces the shopping budget which is used to save money to #Person1#. #Person1# thinks it's good.", "prediction_text": "Person1 is trying to make a shopping budget."}
{"answers": "Jane is going to the hospital to see Susan. Henry tells her to catch the number 13.", "prediction_text": "Jane is going to the hospital."}
{"answers": "Jane will go to the hospital to visit Susan. Henry tells her she can take the number 13.", "prediction_text": "Jane is going to the hospital."}
{"answers": "Jane is going to visit Suman in the hospital, Henry suggests she catch a number 13 to get there.", "prediction_text": "Jane is going to the hospital."}
{"answers": "#Person1# wants to talk to #Person2# about the sales projections for next year. They decide to meet on Tuesday at 2:30 the next week.", "prediction_text": "#Person1#: Hi, I am a sales manager. I am going to the office to set up a spreadsheet."}
{"answers": "#Person1# and #Person2# negotiate on a time to talk about the sales projections for next year. They decide to meet next Tuesday.", "prediction_text": "#Person1#: Hi, I am a sales manager. I am going to the office to set up a spreadsheet."}
{"answers": "#Person1# and #Person2# are scheduling to talk about the sales projections for next year and they decide to meet next Tuesday.", "prediction_text": "#Person1#: Hi, I am a sales manager. I am going to the office to set up a spreadsheet."}
{"answers": "#Person1# is going to New York for the first time. #Person2# suggests #Person1# use a personal tour guide service to make #Person1#'s trip plan.", "prediction_text": "Person1 is a tour guide."}
{"answers": "#Person2# recommends a personal tour guide service to #Person1#.", "prediction_text": "Person1 and Person2 are going to New York. Person1 is a tour guide. Person2 is a personal tour guide."}
{"answers": "#Person2# introduces #Person1# a website for personal tour guide service.", "prediction_text": "Person1 and Person2 are going to New York. Person1 is a tour guide. Person2 is a personal tour guide."}
{"answers": "#Person1# is interviewing #Person2#. They discuss department #Person2# wants to work in, salary, and fringe benefits.", "prediction_text": "A clerk in a company is a part of the company."}
{"answers": "#Person1# asks #Person2# how well #Person2# knows about the company, #Person2#'s ideal department, and current salary. #Person2# also asks #Person1# about the company's benefits and insurance.", "prediction_text": "A clerk in a company is a part of the company."}
{"answers": "#Person1# interviews #Person2# about the understanding of this company, personal preference, and salary expectation. #Person2# asks about the fringe benefits.", "prediction_text": "A clerk in a company is a part of the company."}
{"answers": "#Person1# comes to sign an agreement but it isn't ready until evening. So #Person1# is going through the draft.", "prediction_text": "The draft of the agreement is here."}
{"answers": "#Person1# is checking the draft of an agreement and #Person2# will finish the agreement this evening.", "prediction_text": "The draft of the agreement is here."}
{"answers": "#Person2# gives #Person1# an agreement draft and #Person1# thinks it good. #Person2# will get the agreement ready this evening.", "prediction_text": "The draft of the agreement is here."}
{"answers": "#Person1# rent a car from ABC Rent-a-car Company this morning and met an accident. #Person2# will call an ambulance and police for #Person1#.", "prediction_text": "People are in a car accident near the border."}
{"answers": "#Person1# tells ABC Rent-a-car Company that #Person1# had a car accident. #Person2# will call an ambulance and the police.", "prediction_text": "People are in a car accident near the border."}
{"answers": "#Person1# calls ABC Rent-a-car Company to report #Person1#'s car accident. #Person2# will call an ambulance and the police.", "prediction_text": "People are in a car accident near the border."}
{"answers": "#Person1# is lost on the way to the school clinic. #Person2# shows #Person1# the correct direction.", "prediction_text": "A man is in a traffic light."}
{"answers": "#Person1# lost #Person1#'s way to the school clinic, #Person2# guides #Person1#.", "prediction_text": "A man is in a traffic light."}
{"answers": "#Person1# is lost, #Person2# shows the direction to the school clinic.", "prediction_text": "A man is in a traffic light."}
{"answers": "#Person2# wants to change her room because the baggage elevator is too noisy. #Person1# is sorry and tells her #Person1# will manage it when there's a spare room.", "prediction_text": "A tour company is leaving tomorrow morning."}
{"answers": "#Person2# wants to change a room because it's too noisy, but #Person1# tells #Person2# there are no rooms left so #Person2# has to wait till tomorrow.", "prediction_text": "A tour company is leaving tomorrow morning."}
{"answers": "#Person2# cannot stand the noise near her room and #Person1# promises to change her room tomorrow as there is no room available today.", "prediction_text": "A tour company is leaving tomorrow morning."}
{"answers": "#Person1# is driving #Person2# to the Beijing hotel. #Person2# will attend a banquet at six o'clock in the International Hotel.", "prediction_text": "A man is waiting for a car. He wants to go to Beijing."}
{"answers": "#Person1# warmly welcomes #Person2# and drives #Person2# to the Beijing Hotel. Mr. Wu has arranged a banquet for #Person2# in the evening.", "prediction_text": "A man is waiting for a car. He wants to go to Beijing."}
{"answers": "#Person1# has arranged everything for #Person2# after #Person2# arrives in Beijing and invites #Person2# to a banquet.", "prediction_text": "A man is waiting for a car. He wants to go to Beijing."}
{"answers": "#Person1# gets lost on the way to Liu Ichang. #Person2# tells #Person1# to take Bus No. 713 because it's far.", "prediction_text": "A man is in a car, and he is in a city."}
{"answers": "#Person2# helps #Person1# to find out the location of Liu Ichang and tells #Person1# to take Bus No. 173.", "prediction_text": "A man is in a car, and he is in a city."}
{"answers": "#Person1# gets lost and #Person2# shows the way to Liu Ichang to #Person1#.", "prediction_text": "A man is in a car, and he is in a city."}
{"answers": "#Person2# finds that the computer has stopped running. #Person1# suggests #Person2# ring up the repairmen.", "prediction_text": "A computer is not working."}
{"answers": "#Person1# advises #Person2# to call the repairmen to fix #Person2#'s computer.", "prediction_text": "A computer is not working properly."}
{"answers": "#Person2#'s computer breaks down and #Person1# suggests ringing up the repairman.", "prediction_text": "A computer is not working properly."}
{"answers": "#Person2# wants to buy a gift for his mother's birthday. After knowing his mother's preference, #Person1# recommends a gold watch.", "prediction_text": "A man and a woman are looking for a gift for their mother."}
{"answers": "#Person1# helps #Person2# pick a gift for #Person2#'s mother, and #Person2# decides to buy a watch.", "prediction_text": "A man and a woman are looking for a gift for their mother."}
{"answers": "#Person2# is looking for a gift for his mother in a shop and #Person1# gives him some helpful suggestions.", "prediction_text": "#Person1#: Do you have a gift for your mother?"}
{"answers": "Ms. Ross and Mr. Fisher are having a coffee chat face to face. They talk about their career, child, life, and then get down to business. Ms. Ross will provide service for a presentation on Saturday 24 October in the morning held by Mr. Fisher. They are discussing the preparations as time, food, furniture, guest numbers. A problem is Mr. Fisher sent out 100 invitations but the room's capacity is only about 50.", "prediction_text": "The first person to come in is the manager."}
{"answers": "Ms. Ross helps Mr. Fisher make arrangements for a presentation. Mr. Fisher decides to serve a pre-lunch snack buffet and designs the room setting. Mr. Fisher isn't sure about how many people to come and Ms. Ross is worried that there might be too many people because the room is small.", "prediction_text": "The first person to come in is the manager."}
{"answers": "Ms. Ross and Mr. Fisher start the conversation with greetings. Then they discuss the preparation for a presentation. Ms. Ross confirms the details of the presentation, including schedule, food service. Ms. Ross is concerned that Mr. Fisher's sent too many invitations than the room capacity.", "prediction_text": "The first person to come in is the manager."}
{"answers": "#Person2# tells #Person1#, in Russia, things happen very fast except for bank business, while in Canada people are calmer.", "prediction_text": "In Canada, the government is more efficient, but in Russia, the people are more calm."}
{"answers": "#Person1# and #Person2# are talking about the differences between Russia and Canada. #Person2# tells #Person1# that almost everything in Russia happens fast while it's the opposite in Canada.", "prediction_text": "In Canada, the government is more efficient, but in Russia, the people are more calm."}
{"answers": "#Person1# and #Person2# are sharing their opinions towards how different is Russia from Canada in lifestyle, especially the speed in life.", "prediction_text": "In Canada, the government is more efficient, but in Russia, the people are more calm."}
{"answers": "#Person2# thought to travel to the Caribbean Sea. #Person1# went there last year and thinks the weather can sometimes be really bad.", "prediction_text": "The weather is bad in the Caribbean."}
{"answers": "#Person2# wanted to travel to the Caribbean Sea and but is worried about the storms.", "prediction_text": "The weather in the Caribbean is very bad."}
{"answers": "#Person2# hasn't decided to go to the Caribbean Sea on vacation because #Person2# is concerned about the storms there.", "prediction_text": "The weather in the Caribbean is very bad."}
{"answers": "#Person1# and #Person1#'s mother are preparing the fruits they are going to take to the picnic.", "prediction_text": "The two men are going to have a picnic."}
{"answers": "#Person1# and #Person2# prepare the fruits for the picnic.", "prediction_text": "The two men are going to have a picnic."}
{"answers": "#Person1# and #Person2# are preparing fruits for the picnic.", "prediction_text": "The two men are going to take some fruit."}
{"answers": "#Person1# wants to rent an economy car from #Person2# for $19 a day or $129 a week.", "prediction_text": "Person 1 is asking for a car. Person 2 is asking for a driver's license."}
{"answers": "#Person1# rents a car from #Person2#", "prediction_text": "Person 1 is asking for a car. Person 2 is asking for a driver's license."}
{"answers": "#Person1# rents an economy car in #Person2#'s shop.", "prediction_text": "Person 1 is asking for a car. Person 2 is asking for a driver's license."}
{"answers": "#Person1# hates those who keep smiling at #Person1# and #Person2# is fed up with those who keep a straight face in front of #Person2#.", "prediction_text": "The person in the dialogue is a man."}
{"answers": "#Person2# is fed up with people with a straight face, and #Person1# hates people keeping smiling to #Person1#.", "prediction_text": "The person in the dialogue is a man."}
{"answers": "#Person1# and #Person2# hold different opinions towards people with different facial expressions.", "prediction_text": "The person in the dialogue is a man."}
{"answers": "#Person1# and #Person2# are talking about the good performance of their business for last year. #Person2# thinks the success is partly because of Wallace's contribution, and partly because of their new marketing strategy.", "prediction_text": "The company is finally starting to make money again."}
{"answers": "#Person1# and #Person2# talk about the success of their business and attribute the success to Wallace's contribution and the new marketing strategy. They hope good luck can last.", "prediction_text": "The company is finally starting to make money again."}
{"answers": "#Person1# and #Person2# are pleasant to see their business performance has improved last year. They think the credits should be given to Wallace and the new marketing strategy.", "prediction_text": "The company is finally starting to make money again."}
{"answers": "#Person2# left #Person2#'s backpack and wallet in a taxi and failed to contact the driver because #Person2# didn't have the number. #Person1# lends 50 dollars to #Person1# and will drive #Person2# home.", "prediction_text": "A taxi driver is in a car, and the driver is not there."}
{"answers": "#Person2# borrows some money from #Person1# because #Person2#'s backpack and wallet were lost and cannot take it back. #Person1# will drive #Person2# home.", "prediction_text": "A taxi driver is in a car, and the driver is not there."}
{"answers": "#Person2# lost #Person2#'s backpack, wallet, and money in a taxi. #Person1# lends #Person2# some money and will take #Person2# home.", "prediction_text": "A taxi driver is in a car, and the driver is not there."}
{"answers": "Steven and Lin just had a great meal. Then they talk about the different tipping cultures between America and China.", "prediction_text": "Lin, the waiter, is a man of a certain age, and he is a man of a certain culture."}
{"answers": "Steven buys Lin a magnificent dinner in America and they then talk about the tipping culture in China and America.", "prediction_text": "Lin, the waiter, is a man of a certain age, and he is a man of a certain culture."}
{"answers": "Steven treats Lin to a nice meal. Then they talk about the tipping cultures in their countries.", "prediction_text": "Lin, the waiter, is a man of a certain age, and he is a man of a certain culture."}
{"answers": "Bill is happy because he made a move to know his roommate today.", "prediction_text": "Bill: How can you hear so happy today?"}
{"answers": "#Person1# and Bill talk about Bill's roommate Brain Locker.", "prediction_text": "Bill: How can you hear so happy today?"}
{"answers": "Bill tells #Person1# that he has made a move to read of his roommate.", "prediction_text": "Bill: How can you hear so happy today?"}
{"answers": "#Person2# checks Tom Wilson's information and Tom pays his hotel and meal bill.", "prediction_text": "A man is trying to pay his bill."}
{"answers": "Tom Wilson pays for his bill for hotel and food by credit card.", "prediction_text": "A man is trying to pay a bill. He is in room 306."}
{"answers": "Tom Wilson pays the bill for his room and meal cost by credit card.", "prediction_text": "A man is trying to pay a bill. He is in room 306."}
{"answers": "Susan calls Carol to ask about the party time. But Carol is taking a shower so #Person1# answers for her.", "prediction_text": "Carol is calling."}
{"answers": "Carol is taking a shower when Carol calls her, so #Person1# answers the telephone and tells her the party time.", "prediction_text": "Carol is calling."}
{"answers": "Susan calls to ask Carol about the party time. #Person1# answers the phone and tells her.", "prediction_text": "Carol is calling."}
{"answers": "#Person1# thinks that she knows #Person2# somewhere, but #Person2# denies it.", "prediction_text": "People are talking about the same person."}
{"answers": "#Person1# thinks she has met #Person2# somewhere, but #Person2# thinks it's a mistake.", "prediction_text": "People are talking about the same person."}
{"answers": "#Person1# keeps asking where #Person2#'s from because she thinks she knows #Person2# but #Person2# denies it.", "prediction_text": "People are talking about the same person."}
{"answers": "#Person1# is crazy for Trump and voted for him. #Person2# doesn't agree with #Person1# on Trump and will vote for Biden.", "prediction_text": "Two people are talking about Trump."}
{"answers": "#Person1# is a crazy fan of Trump and wants him to be re-elected. #Person2# will vote for Biden.", "prediction_text": "Two people are talking about Trump."}
{"answers": "#Person1# is crazy for Trump and voted for him but #Person2# will vote for Biden.", "prediction_text": "Two people are talking about Trump."}
{"answers": "#Person1# doesn't know how to use the ATM. #Person2# teaches #Person1# step by step.", "prediction_text": "A man and a woman are talking."}
{"answers": "#Person1# doesn't know how to use an ATM. #Person2# teaches #Person1#.", "prediction_text": "A man and a woman are talking."}
{"answers": "#Person2# is teaching #Person1# how to use an ATM step by step because #Person1# doesn't know how to use.", "prediction_text": "A man and a woman are talking."}
{"answers": "Susan wants to find the memo. John has no idea where it is.", "prediction_text": "John is in a meeting with his secretary."}
{"answers": "Susan Miller is asking John if he knows where the memo is. John has no idea.", "prediction_text": "John is in a meeting with his secretary."}
{"answers": "Susan asks John where a memo is but John doesn't know.", "prediction_text": "John is in a meeting with a person."}
{"answers": "#Person1# invites Lily to picnic this weekend. Lily accepts.", "prediction_text": "People are going to picnic."}
{"answers": "#Person1# is inviting Lily to take part in their picnic this weekend, and she accepts.", "prediction_text": "People are going to picnic."}
{"answers": "#Person1# invites Lily to take part in their weekend picnic and Lily accepts.", "prediction_text": "People are going to picnic."}
{"answers": "#Person1# asks #Person2# about the table manners in China. #Person2# says there are many hazy rules that are different from Western. And #Person2# tells #Person1# stabbing chopsticks into a bowl resembles sacrifices for the death and is very inauspicious.", "prediction_text": "A foreigner is invited to a Chinese dinner."}
{"answers": "#Person1# and #Person2# are discussing the differences between China and Western feasts. There are so many rules on the Chinese table, and they both feel hazy about its etiquette.", "prediction_text": "A foreigner is invited to a Chinese dinner."}
{"answers": "#Person1# and #Person2# talk about the difference in table etiquette in China. They both feel hazy about Chinese table etiquette and wrong use of chopsticks can lead to people's enrage.", "prediction_text": "A foreigner is invited to a Chinese dinner."}
{"answers": "Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies.", "prediction_text": "Two people are talking about movies."}
{"answers": "Frank and Mary both like watching movies in their spare time. Mary usually rents movies at Movie Salon, and Frank is interested in signing up for its membership.", "prediction_text": "Two people are talking about a movie."}
{"answers": "Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies", "prediction_text": "Two people are talking about a movie."}
{"answers": "#Person2# wanted to join a small political party and thinks the smaller group can influence the larger one. #Person1# and #Person2# agree that most people don't fully understand politics.", "prediction_text": "A group of people are talking about the environment."}
{"answers": "#Person2# believes smaller political and pressure groups can influence large parties. #Person1# and #Person2# both agree that most people often don't understand politics fully.", "prediction_text": "A group of people are talking about the environment."}
{"answers": "#Person2# thought about joining a small party and thinks smaller political and pressure groups can influence larger ones. #Person1# and #Person2# agree most people don't understand political issues fully.", "prediction_text": "A group of people are talking about the environment."}
{"answers": "#Person1# apologizes for mistakes in goods. #Person1# will be responsible for Mr. Wilson's loss, and take measures to avoid such mistakes.", "prediction_text": "The first person is very sorry to have made a mistake."}
{"answers": "#Person1# feels sorry for Mr. Wilson's loss caused by #Person1# and assures that such mistakes will not happen again.", "prediction_text": "The first person is very sorry to have made a mistake."}
{"answers": "#Person1# apologizes for the loss caused by them to Mr. Wilson and assures that it will never happen again.", "prediction_text": "The first person is very sorry to have made a mistake."}
{"answers": "#Person1# asks #Person2# who saw the robbery some questions. #Person2# agrees to come to the station for more questioning.", "prediction_text": "A man in a black suit and a black hat is seen in a car."}
{"answers": "#Person1# asks #Person2# who saw a robbery some questions and #Person2# is willing to go to the station for more questioning.", "prediction_text": "A man in a black suit and a black hat is seen in a car."}
{"answers": "#Person2# tells #Person1# #Person2# witnessed the robbery and agrees to take more questions in the station.", "prediction_text": "A man in a black suit and a black hat is seen in a car."}
{"answers": "#Person1# and #Person2#'s parents are out on a date and will go out for dinner regularly.", "prediction_text": "A group of people are on a date."}
{"answers": "The parents of #Person1# and #Person2# are out on a date.", "prediction_text": "A group of people are on a date."}
{"answers": "#Person1# and #Person2#'s parent are out on a date to revive their old tradition.", "prediction_text": "A group of people are on a date."}
{"answers": "#Person1# has decided to go on a diet in the new year. Carol reasonably doubts it.", "prediction_text": "Two people are talking about their new year's resolutions."}
{"answers": "#Person1#'s decided to go on a diet for New Year's resolution. Carol doesn't believe #Person1# will stick to it.", "prediction_text": "Two people are talking about their new year's resolutions."}
{"answers": "#Person1# has decided to go on a diet in the new year. Carol doesn't believe #Person1# will stick to it", "prediction_text": "Two people are talking about their new year's resolutions."}
{"answers": "Karen Huang tried to register for Comp Lit 287 but failed because it's full. So Karen comes to the department office. #Person1# finds Karen is a Comp Lit major and the university has saved extra places for them. #Person1# gives Karen a special code and tells Karan how to use it to get into the class.", "prediction_text": "A new student, Karen Huang, is trying to register for Comp Lit 287."}
{"answers": "Karen Huang couldn't register for Comp Lit 287 so Karen comes to talk to #Person1# for a solution. #Person1# says it is full and will put Karen on the waiting list. But after #Person1# knows Karen majors in comparative literature, #Person1# gives her a special code to register for the class, as they've saved extra places for them.", "prediction_text": "A new student, Karen Huang, is trying to register for Comp Lit 287."}
{"answers": "Karen Huang wants to register for a class. #Person1# says it's full and will put Karen on the waiting list. But then #Person1# gives Karen a special code to register the class after #Person1# knows Karen is a Comparative Literature major.", "prediction_text": "A new student, Karen Huang, is trying to register for Comp Lit 287."}
{"answers": "#Person2# voluntarily shares an umbrella with #Person1# who doesn't bring the umbrella when it's rainy.", "prediction_text": "Two people are going to the Garden Hotel."}
{"answers": "#Person1# forgot to take an umbrella while it's raining. #Person2# shares an umbrella with #Person1#.", "prediction_text": "Two people are going to the Garden Hotel."}
{"answers": "#Person2# shares an umbrella with #Person1# when it's rainy.", "prediction_text": "Two people are going to the Garden Hotel."}
{"answers": "Jack gives Daisy a ride in his new car. Daisy praises it.", "prediction_text": "Jack is a new car."}
{"answers": "Jack bought a new car and invites Daisy to hop in and take a ride. Daisy thinks it's a good feeling.", "prediction_text": "Jack is a new car."}
{"answers": "Jack takes Daisy for a ride to experience his new car. Daisy thinks he makes a perfect choice.", "prediction_text": "Jack is a new car."}
{"answers": "#Person1# asks #Person2# the exact time when the fire broke out. #Person2# says it was about 10 o'clock.", "prediction_text": "A man and a woman are talking."}
{"answers": "#Person1# asks the exact time when the big fire broke out. #Person2# remembers that it was about 10 o'clock.", "prediction_text": "A man and a woman are talking."}
{"answers": "#Person1# asks #Person2# the exact time when the fire broke out. #Person2# says it was about 10 o'clock.", "prediction_text": "A man and a woman are talking."}
{"answers": "#Person2# doesn't have enough cash to pay 905 yuan and asks to cut the service charge. #Person1# refuses. So #Person2# pays by her husband's credit card because it can be accepted.", "prediction_text": "A man is in a hurry to get a service charge. He wants to get a receipt."}
{"answers": "#Person1# requires 905 yuan in total, but #Person2# only has 900 in cash and asks for a release. #Person1# refuses, so #Person2# pays by her husband's credit card because it can be accepted.", "prediction_text": "A man is in a hurry to get a service charge. He wants to get a receipt."}
{"answers": "#Person2# is asked to pay 905 yuan but she just has 900 in cash and asks for a release from #Person1#. #Person1# refuses. So #Person2# pays by her husband's credit card because it can be accepted.", "prediction_text": "A man is in a hurry to get a service charge. He wants to get a receipt."}
{"answers": "#Person1# describes the contents of the regular car wash package. #Person2# will take that.", "prediction_text": "Person1 and Person2 are talking about a new car wash."}
{"answers": "#Person1# introduces the content of regular car wash package and #Person2# accepts.", "prediction_text": "Person1 and Person2 are talking about a new car wash."}
{"answers": "#Person1# introduces the services included in regular car wash package and #Person2# will take it.", "prediction_text": "Person1 and Person2 are talking about a new car wash."}
{"answers": "Harry and his wife may go abroad during the holiday, but Harry's wife worries too much, so they may not go anywhere.", "prediction_text": "Harry and his wife are going to travel abroad."}
{"answers": "Harry and his wife may go abroad during the holidays. However, they might not go anywhere because his wife always worries too much.", "prediction_text": "Harry and his wife are going to travel abroad."}
{"answers": "Harry is not sure about the holiday plan of going abroad to spend the holiday, because his wife worries about things after they leave.", "prediction_text": "Harry and his wife are going to travel abroad."}
{"answers": "#Person1# is a new member who wants to use the machines. Johnson teaches #Person1# to get a card to keep track of the progress and record the suitable weight for each machine. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "Let's go through the first seven machines."}
{"answers": "Johnson is teaching #Person1# how to use the training machines. He introduces a training card that allows #Person1# to keep track of the weight used on each machine. #Person1# is exhausted when there are still 7 machines left. Johnson suggests #Person1# stop to train and go through the rest of the machines next time.", "prediction_text": "Let's go through the first seven machines."}
{"answers": "Johnson shows #Person1# the correct way to use weights machine and tells #Person1# using a card to keep track of exercise intensity. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "Let's go through the first seven machines."}
{"answers": "#Person1# and #Person2# are both unemployed. #Person2# suggests applying for the electrician program and #Person1# agrees.", "prediction_text": "Person1 and Person2 are working for a company. Person1 is a company manager. Person2 is a company manager."}
{"answers": "Both #Person1# and #Person2# lost their jobs. They would like to apply for the electrician program.", "prediction_text": "Person1 and Person2 are working for a company. Person1 is a company manager. Person2 is a company manager."}
{"answers": "#Person1# and #Person2# plan to apply for the electrician program after they were laid off.", "prediction_text": "Person1 and Person2 are working for a company. Person1 is a company manager. Person2 is a company manager."}
{"answers": "#Person1# asks something about #Person2#'s care with puppies and reminds #Person2# of the vet appointment.", "prediction_text": "The puppies are in a very good condition."}
{"answers": "#Person1# and #Person2# are talking about taking care of their puppies and the time of vet appointment.", "prediction_text": "The puppies are in a very good condition."}
{"answers": "#Person1# inquires about #Person2#'s care with the puppies and reminds #Person2# of the vet appointment.", "prediction_text": "The puppies are in a very good condition."}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay the rent. Ayden is short on cash but still lends $20 to #Person1#. They decide to have dinner at #Person1#'s place.", "prediction_text": "Ayden needs a loan."}
{"answers": "#Person1# borrows $ 20 from Ayden because #Person1# owes the landlady $ 200. Then #Person1# invites Ayden to have dinner at #Person1#'s place.", "prediction_text": "Ayden needs a loan."}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay for the house rent but Ayden lends $20 because of the lack of money. They decide to have dinner at #Person1#'s place.", "prediction_text": "Ayden needs a loan."}
{"answers": "#Person2# had been in the charity organization for 2 months. Social activities had a direct impact on #Person2#'s way of thinking.", "prediction_text": "Person 1."}
{"answers": "#Person2# had been in the charity organization for 2 months and the social activities influenced #Person2#'s way of thinking.", "prediction_text": "Person 1."}
{"answers": "#Person2# had been in the charity organization for 2 months. Social activities influenced #Person2#'s way of thinking.", "prediction_text": "Person1#: I have been in a charity organization for 2 months."}
{"answers": "#Person1#'s organization will make a decision in about one week and contact #Person2# via phone or e-mail.", "prediction_text": "Person1"}
{"answers": "#Person1# can inform #Person2# by phone or e-mail when the organization makes a decision in about one week.", "prediction_text": "Person1"}
{"answers": "#Person1# can tell #Person2# their organization's decision by phone or e-mail in about one week.", "prediction_text": "Person1"}
{"answers": "#Person2# recommends Moutan and Roast Beijing Duck. #Person1# will take that in the hotel.", "prediction_text": "A man in a red suit, a black hat, a red dress, a black tie, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a"}
{"answers": "#Person2# recommends #Person1# to have Roast Beijing Duck along with Moutan in the hotel. #Person1# will have a try.", "prediction_text": "A man in a red suit, a black hat, a red dress, a black tie, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a"}
{"answers": "#Person2# recommends Moutan and Roast Beijing Duck. #Person1# will have a try in the hotel.", "prediction_text": "A man in a red suit, a black hat, a red dress, a black tie, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a red umbrella, a"}
{"answers": "Angela calls Dan to make sure he's coming to the wedding with him next week and also asks him to invite Megan. They'll drive to the wedding together.", "prediction_text": "Dan and Megan are in a meeting."}
{"answers": "Angela calls to remind Dan that they are going to the wedding of her friend next week and asks him to invite Megan. The three of them will go together.", "prediction_text": "Dan and Megan are in a relationship."}
{"answers": "Angela reminds Dan to come to the wedding next week and asks him to invite Megan as well. They agree to go there together.", "prediction_text": "Dan and Megan are in a relationship."}
{"answers": "#Person2# has traditional Greek yogurt, which #Person1# thinks rather plain. #Person1# has an Italian tiramisu, which #Person2# thinks delicious. #Person1# goes and gets both of them a fried banana.", "prediction_text": "The two people are going to try a different type of Greek yogurt."}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. They both decide to have fried bananas and #Person1# will bring bananas back.", "prediction_text": "The two people are going to try a different type of Greek yogurt."}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. Then they want to have fried bananas and #Person1# goes out to buy some.", "prediction_text": "The two people are going to try a different type of Greek yogurt."}
{"answers": "Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week.", "prediction_text": "Person 1."}
{"answers": "Mr. Smith tells #Person1# that he graduated from Yale University with a B. A. in Economics, and he has worked in a bank for the last five years with $500 a week.", "prediction_text": "Person 1."}
{"answers": "Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week.", "prediction_text": "Person 1."}
{"answers": "#Person2# recommends Digital Barbie for #Person1#'s niece. #Person1# pays $32.42 for it by cash.", "prediction_text": "The person in the group is a little girl."}
{"answers": "#Person2# recommends #Person1# to buy a $32. 42 Digital Barbie for #Person1#'s niece. #Person1# pays it by cash.", "prediction_text": "The person in the group is a little girl."}
{"answers": "#Person1# paid $32.42 by cash to buy a Digital Barbie as a niece's gift after listening to #Person2#'s suggestion.", "prediction_text": "The person in the group is a little girl."}
{"answers": "#Person2# wants a pair of Jordan shoes in size 41.", "prediction_text": "A pair of sport shoes."}
{"answers": "#Person2# wants to buy a pair of Jordan shoes in size 41.", "prediction_text": "Person1 and Person2 are in a shopping session. Person1 wants a pair of sport shoes, Jordan, and Person2 wants a pair of sport shoes, Jordan, and a pair of sport shoes, Jordan, and a pair of sport shoes, Jordan."}
{"answers": "#Person2# wants to buy a pair of Jordan shoes in size 41.", "prediction_text": "Person1 and Person2 are in a shopping session. Person1 wants a pair of sport shoes, Jordan, and Person2 wants a pair of sport shoes, Jordan, and a pair of sport shoes, Jordan, and a pair of sport shoes, Jordan."}
{"answers": "#Person1# wants to go to the science museum but loses the way. #Person2# helps #Person1# buy the ticket and gives #Person1# directions.", "prediction_text": "The person who is in the train is a man."}
{"answers": "#Person1# is lost on the way to the science museum. #Person2# helps #Person1# to buy the tickets for the train and gives #Person1# directions.", "prediction_text": "The person who is in the train is a man."}
{"answers": "#Person2# helps #Person1# operate the ticket machine to buy a train ticket and tells #Person1# who loses the way how to go to the science museum.", "prediction_text": "The person who wants to go to the science museum is in a hurry."}
{"answers": "#Person1# asks Simon about his retirement and finds out Simon is on a trial scheme called phased retirement. He can work with his former company but manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "Person1 is a retired employee, and is on a project with a company."}
{"answers": "Simon tells #Person1# that he is on a scheme called phased retirement during which he had a six-month break from work, and after that he could apply for projects of the company he used to work for but manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "Person1 is a retired employee, and is on a project with a company."}
{"answers": "Simon tells #Person1# that he joined in a phased retirement scheme which offers retired people work positions from the former company, and he can manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "Person1 is a retired employee, and is on a project with a company."}
{"answers": "Rocky doesn't want to dance because no one at the party fits his type, but #Person1# likes different kinds of women from him. Finally, Rocky decides to go home to a TV dinner and his dog, Rusty", "prediction_text": "A man is in a party, and he is in a bad mood."}
{"answers": "Rocky wants a woman who's affectionate and fulfills his every need. #Person1# likes different kinds of women from him. Rocky doesn't mind and plans to go home and spend the night with a TV dinner and his dog, Rusty.", "prediction_text": "A man is in a party, and he is in a bad mood."}
{"answers": "Rocky has particular requirements and cannot find a suitable woman in the party, so he doesn't want to dance. #Person1# likes different kinds of women from him. He sticks with his views and decides to go home to a TV dinner and his dog, Rusty.", "prediction_text": "A man is in a party, and he is in a bad mood."}
{"answers": "#Person1# and #Person2# is talking about the heavy storm last night. #Person2# thinks the weather is terrible. #Person1# is positive towards that.", "prediction_text": "People in the area are all talking about a big storm."}
{"answers": "#Person1# and #Person2# are talking about the heavy storm last night, and #Person1#'s positive. #Person2# thinks the weather is terrible. #Person1# thinks they should not complain.", "prediction_text": "People in the area are all talking about a big storm."}
{"answers": "#Person2# complains the terrible storm last night and wishes for nice weather. #Person1# thinks they should not complain.", "prediction_text": "People in the area are all talking about a big storm."}
{"answers": "#Person1# wants to watch TV together with #Person2#, but #Person2# finds it boring. #Person2# suggests doing music together.", "prediction_text": "The two people are talking about the same things."}
{"answers": "#Person1# asks #Person2# to watch TV with #Person1# at home. However, #Person2# finds it boring and suggests doing music together.", "prediction_text": "The two people are talking about the same things."}
{"answers": "#Person1# wants #Person2# to watch TV with #Person1#. #Person2# thinks it is boring and suggests doing their music together.", "prediction_text": "Person1 and Person2 are talking about the same thing."}
{"answers": "Ben feels a bit nervous about his new school life. #Person1# cheers him up and tells him about the schedule of the new school.", "prediction_text": "The first class is to begin at 8 o'clock."}
{"answers": "Ben is nervous about his new school life. #Person1# is giving him some brief information about the school.", "prediction_text": "The first class is to begin at 8 o'clock."}
{"answers": "Ben is nervous about the upcoming new school life. #Person1# comforts him and tells him the school daily routine.", "prediction_text": "The first class is to begin at 8 o'clock."}
{"answers": "Adam's knee is getting better but #Person1# still suggests that he should not do any running today. Adam decides to watch the films of Michigan after practice and will be back tomorrow for full practice.", "prediction_text": "Adam is in a good condition, and he is ready to go to practice."}
{"answers": "Adam's knee is getting better but #Person1# suggests that he should not do any running today. Adam decides to see films of Michigan after practice and says he'll be back tomorrow for full practice.", "prediction_text": "Adam is in a good condition, and he is ready to go to practice."}
{"answers": "Adam's knee is getting better but #Person1# suggests that he should not do any running today. #Person1# says he can watch some films of Michigan after practice. Adam decides to go back tomorrow for full practice.", "prediction_text": "Adam is in a good condition, and he is ready to go to practice."}
{"answers": "#Person1# wants #Person2# to help to print up a copy. #Person2# accepts and edits it for #Person1#.", "prediction_text": "Person1 and Person2 are talking. Person1 is trying to get a copy of the printer. Person2 is trying to help."}
{"answers": "#Person1# asks for #Person2#'s help to print up a copy. #Person2# accepts and edits it for #Person1#.", "prediction_text": "Person1 and Person2 are talking. Person1 is writing a question to Person2."}
{"answers": "#Person1# asks for #Person2#'s help to print up a copy. #Person2# accepts and edits it for #Person1#.", "prediction_text": "Person1 and Person2 are talking. Person1 is writing a question to Person2."}
{"answers": "#Person2# lets #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "Person1 is in the room, and the curtain is up. Person2 is in the room, and the curtain is up."}
{"answers": "#Person2# suggests that #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "Person1 is in the room, and the curtain is up. Person2 is in the room, and the curtain is up."}
{"answers": "#Person2# lets #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "A man is helping a woman put a curtain up."}
{"answers": "Jack is available for a camping trip the weekend after next.", "prediction_text": "Jack is planning a weekend camping trip."}
{"answers": "Jack will be available for a weekend camping trip the weekend after next.", "prediction_text": "Jack is planning a weekend camping trip."}
{"answers": "The weekend after next is suitable for Jack to have a camping trip.", "prediction_text": "Jack is planning a weekend camping trip."}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells #Person2# she is pregnant. #Person2# feels happy. They go to the doctor. #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "A man and a woman are talking about a baby."}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells him that she's pregnant. They go to see the doctor. However, #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "A man and a woman are talking about a baby."}
{"answers": "#Person1# apologises to #Person2# after the quarrel and tells #Person2# she's pregnant. #Person2# feels so happy and they decide to see the doctor. After the pregnancy test about the date, #Person2# finds that it is not his baby, because he was away the first week of February at a training seminar.", "prediction_text": "A man and a woman are talking about a baby."}
{"answers": "#Person2# is worried about #Person2#'s daughter. #Person1# suggests #Person2# should encourage #Person2#'s daughter to make her own decision.", "prediction_text": "Person1 and Person2 are talking about their daughter."}
{"answers": "#Person1# advises #Person2# to let #Person2#'s daughter make her own decisions instead of worrying about her.", "prediction_text": "Person1 and Person2 are talking about a girl."}
{"answers": "#Person1# suggests #Person2# letting the child make own decision instead of worrying about her.", "prediction_text": "Person1 and Person2 are talking about a girl."}
{"answers": "#Person1# may lose #Person1#'s job because of a mistake that may cause a huge loss and a dishonest supervisor. #Person2# suggests #Person1# live with #Person2# and stop buying all the junk to save money if #Person1# loses the job. #Person1# agrees.", "prediction_text": "Person1 and Person2 are talking about the same situation. Person1 is a real investment adviser."}
{"answers": "#Person1# makes a big mistake which may cause a huge loss and #Person1# may lose this job because of the loss and #Person1#'s dishonest supervisor. #Person2# suggests that #Person1# move in with #Person2# until #Person1# find another job and stop buying all the junk, if #Person1# loses the job.", "prediction_text": "Person1 and Person2 are talking about the same situation. Person1 is a real investment adviser."}
{"answers": "#Person1# might lose the job because of a working mistake and is worried about financial problems after losing a job. #Person2# suggests #Person1# save money until #Person1# find another job, if #Person1# loses the job.", "prediction_text": "Person1 and Person2 are talking about the same situation. Person1 is a real investment adviser."}
{"answers": "#Person2#'s friend is visiting #Person2#. #Person2# invites him to give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "Person1 is a professor at Edinburgh University. He is a Ph.D. student. He is going to graduate in the end of this year."}
{"answers": "#Person2#'s friend is a Ph.D. student at the University of Edinburgh. He is visiting #Person2# and will give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "Person1 is a professor at Edinburgh University. He is a Ph.D. student. He is going to graduate in the end of this year."}
{"answers": "#Person2#'s friend is visiting #Person2# and will give a speech about multimodal text generation. #Person1# thinks it interesting and will attend it.", "prediction_text": "Person1 is a professor at Edinburgh University. He is a Ph.D. student. He is going to graduate in the end of this year."}
{"answers": "#Person1# suggests going to John's house, but #Person2# feels sick and decides to go to sleep.", "prediction_text": "The two men are going to go to John's house tonight."}
{"answers": "#Person2# prefers to stay at home and rest rather than go over to John's house tonight because #Person2# gets sick.", "prediction_text": "The two men are going to go to John's house tonight."}
{"answers": "#Person2# doesn't want to go to John's house tonight because of getting sick. #Person2# decides to go to sleep.", "prediction_text": "The two men are going to go to John's house tonight."}
{"answers": "Mr. Faber calls #Person1# to book a double room for 3 nights at York Hotel.", "prediction_text": "The person in the bar is in a double room."}
{"answers": "Mr. Faber books a double room for 3 nights from July 20th at York Hotel.", "prediction_text": "The person in the bar is in a double room."}
{"answers": "Mr. Faber books a double room for three nights from July 20th at York Hotel.", "prediction_text": "The person in the bar is in a double room."}
{"answers": "#Person1# wants a cheap single room. #Person2# recommends calling John Godfrey and see him on Saturday.", "prediction_text": "Person1 and Person2 are looking for a flat in South Derby."}
{"answers": "#Person1# is looking for a cheap single room. #Person2# recommends #Person1# to contact John Godfrey on Saturday.", "prediction_text": "Person1 and Person2 are looking for a flat in South Derby."}
{"answers": "#Person1# is looking for a cheap single room. #Person2# gives #Person1# John Godfrey's phone number that could help to find a room and suggests that they meet on Saturday.", "prediction_text": "Person1 and Person2 are looking for a flat in South Derby."}
{"answers": "#Person1# and #Person2# blame each other for the series of bad experiences during the journey.", "prediction_text": "#Person1#: I am the one who was driving when the police stopped us."}
{"answers": "#Person1# and #Person2# are blaming each other for lots of bad experience during the journey.", "prediction_text": "#Person1#: I am the one who was driving when the police stopped us."}
{"answers": "#Person1# and #Person2# quarrel and blame each other because of bad experiences during the journey.", "prediction_text": "#Person1#: I am the one who was driving when the police stopped us."}
{"answers": "Darlene calls to check the delayed order. Dan tells her the situation. Darlene will talk to Steve.", "prediction_text": "Dan is calling to see if the order of 100 computers is short. He is not sure if the factory is short of hands."}
{"answers": "Darlene calls Dan to check on the delay of the order. Dan explains to her the reason for the delay. Darlene decides to talk to Steven.", "prediction_text": "Dan is calling to see if the order of 100 computers is short. He is not sure if the factory is short of hands."}
{"answers": "Darlen calls Dan to check the delayed order of computers. Dan explains to her the reason for the delay. Darlene decides to talk to Steven.", "prediction_text": "Dan is calling to see if the order of 100 computers is short. He is not sure if the factory is short of hands."}
{"answers": "#Person2# tells #Person1# the founders and founding times of the Washington Post, the New York Times, and the Los Angeles Times.", "prediction_text": "The following is a list of the first newspapers in the United States."}
{"answers": "#Person1# and #Person2# are talking about the founding time and founder of the New York Times, the Washington Post, and the Los Angeles Times were founded.", "prediction_text": "The following is a list of the first newspapers in the United States."}
{"answers": "#Person2# tells #Person1# the founder and the founding time of the New York Times, the Washington Post, and the Los Angeles Times.", "prediction_text": "The following is a list of the first newspapers in the United States."}
{"answers": "#Person1# finds that #Person2# e-mail exceeds capacity and suggests #Person2# compress the email.", "prediction_text": "Person1 and Person2 are talking about a virus."}
{"answers": "#Person2#'s attachment exceeds the e-mail capacity, #Person1# suggests compressing it.", "prediction_text": "Person1 and Person2 are talking about a virus."}
{"answers": "#Person2# can't send out an email. #Person1# suggests #Person2#'s attachment be compressed.", "prediction_text": "Person1 and Person2 are talking about a virus."}
{"answers": "#Person2# tells #Person1# #Person2# enjoyed the weekend organized by #Person1#.", "prediction_text": "A group of people are in Changsha."}
{"answers": "#Person1# and #Person2# has enjoyed a nice weekend together.", "prediction_text": "A group of people are in Changsha."}
{"answers": "#Person1# invited #Person2# to spend the weekend and #Person2# has enjoyed it.", "prediction_text": "A group of people are in Changsha."}
{"answers": "#Person2# offers #Person1# suggestions about what to eat. #Person1# will go to Panda Express.", "prediction_text": "Person1 wants to get a burger. Person2 wants to get some food."}
{"answers": "#Person1# wants to eat something and #Person2# gives some advice. #Person1# finally goes to eat Chinese food.", "prediction_text": "Person1 wants to get a burger. Person2 wants to get some food."}
{"answers": "#Person1# is hungry. #Person2# says the Chinese food from Panda Express is good.", "prediction_text": "Person1 wants to get a burger. Person2 wants to get some food."}
{"answers": "Mary from Hans furniture tells Tom they decide not to employ him but Tom asks the store to reconsider him.", "prediction_text": "Tom and Marry are going to offer the sales position to someone else."}
{"answers": "Marry calls Tom to inform him that they decide to offer the sales position to someone else. Tom wants them to reconsider.", "prediction_text": "Tom and Marry are going to offer the sales position to another person."}
{"answers": "Tom wants Marry to reconsider the decision of offering the sales position to someone else instead of him.", "prediction_text": "Tom and Marry are going to offer the sales position to another person."}
{"answers": "#Person1# is angry about the crank calls.", "prediction_text": "A man is calling the police."}
{"answers": "#Person1# gets a crank call and is angry about it.", "prediction_text": "A man is calling the police."}
{"answers": "#Person1# receives a phone call but no one speaks.", "prediction_text": "A man is calling the police."}
{"answers": "#Person2# is being interviewed for a music teacher. #Person2# says #Person2# has degrees in music and specializes in classic music. Then #Person2# shares some research about classic music that can make people relaxed. #Person1# is very satisfied with #Person2#. #Person2# gives some suggestions on how to start listening to classical music.", "prediction_text": "A man and a woman are meeting in a bar."}
{"answers": "#Person1# interviews #Person2# for a music teacher position. #Person1# is very satisfied with #Person2#'s educational background in music and #Person2#'s understanding of classical music. After the interview, #Person2# suggests #Person1# can develop interests in classic music by listening to different classic music online.", "prediction_text": "A man and a woman are meeting in a bar."}
{"answers": "#Person2# is being interviewed for a music teacher. #Person1# is impressed by #Person2#'s background and knowledge of classical music. And #Person1# will try classical music.", "prediction_text": "A man and a woman are meeting in a bar."}
{"answers": "#Person2# likes his neibourhood girl who is popular. Although #Person1# analyses the disadvantages, #Person2# still decides to date with her.", "prediction_text": "The two people are meeting."}
{"answers": "#Person2# tells #Person1# that he falls in love with his neighbour and #Person1# thinks he has some competition and wishes him good luck.", "prediction_text": "The two people are meeting."}
{"answers": "#Person2# tells #Person1# he falls in love with the girl living under him and she's very popular. #Person2#'s going to call her now and invite her to dinner tonight.", "prediction_text": "The two people are meeting."}
{"answers": "#Person1# and #Person2# talk about Mirella's dressing style in the office which is different from others. The management decided to give Mirella formal clothes directly to warn her.", "prediction_text": "The following is a list of the main points of the following article."}
{"answers": "#Person1# thinks it's strange of Mirella wearing casually in the office. #Person2# thinks she was influenced by the casual atmosphere in California and tells #Person1# management decided to give her a new white dress to warn her.", "prediction_text": "The following is a list of the main things that the management of a company has decided to do."}
{"answers": "#Person1# and #Person2# talks about Mirella dressed casually after she came back from Silicon Valley. Managements put white shirts on her desk to warn her.", "prediction_text": "The following is a list of the main things that the management of a company has decided to do."}
{"answers": "#Person1# congratulates #Person2# on #Person2#'s new venture and expresses #Person1#'s willingness to help.", "prediction_text": "Person1 and Person2 are going to set up their own law office."}
{"answers": "#Person1# is willing to offer #Person2# help in setting up a law office.", "prediction_text": "Person1 and Person2 are going to set up their own law office."}
{"answers": "#Person2# is going to set up #Person2#'s own law office. #Person1# wishes #Person2# good luck.", "prediction_text": "Person1 and Person2 are talking about their new business. Person1 is a lawyer, and Person2 is a business owner."}
{"answers": "Peter tells Kate he played games and listened to music the whole last night.", "prediction_text": "The person in the middle of the conversation is Peter."}
{"answers": "Peter tells Kate he stayed up and he's tired now.", "prediction_text": "The person in the middle of the conversation is Peter."}
{"answers": "Peter tells Kate he stayed up. He's tired now and needs a rest.", "prediction_text": "The person in the middle of the conversation is Peter."}
{"answers": "#Person2# explains alternatives of sea transportation but #Person1# addresses the importance of boat in transoceanic cargo transportation.", "prediction_text": "People in the United States are not so much used to the use of ships and boats as they used to be."}
{"answers": "#Person1# and #Person2# are talking about the development of transportation and the importance of boats.", "prediction_text": "People in the United States are not so much used to the use of ships and boats as they used to be."}
{"answers": "#Person2# tells #Person1# that ships and boats have been giving places to other transportations because people's life is getting faster, but #Person1# thinks they are still important.", "prediction_text": "People in the United States are not so much used to the use of ships and boats as they used to be."}
{"answers": "Fanny had a nightmare because of the pressure from school applications. Her mother and Andy both comfort her.", "prediction_text": "A group of people are meeting."}
{"answers": "Fanny had a nightmare last night and her mother consoled her. Andy listens to her experience and thinks she has a good mother.", "prediction_text": "A group of people are meeting."}
{"answers": "Fanny had a bad dream last night. She is worried about getting into the University of Michigan. Andy comforts her.", "prediction_text": "A group of people are meeting."}
{"answers": "#Person1# and Ernie plan to start a band and they decide to play hip hop music.", "prediction_text": "The two students are going to be the coolest students on campus."}
{"answers": "#Person1# and Ernie are preparing to start the band at school.", "prediction_text": "The two students are going to be the coolest students on campus."}
{"answers": "#Person1# and Ernie start their own band on campus. #Person1# suggests they play Vanilla Ice songs.", "prediction_text": "The two students are going to play some hip hop music."}
{"answers": "#Person1# and #Person2# are discussing where to have fun, and they decide to go to the theater tonight.", "prediction_text": "Person 1 and Person 2 are going to a jazz club tonight."}
{"answers": "#Person1# and #Person2# are talking about what to do tonight and they finally decide to go to watch a show.", "prediction_text": "Person 1 and Person 2 are going to a jazz club tonight."}
{"answers": "#Person2# hasn't been to the theater for a long time, so #Person1# and #Person2# decide to make a reservation for a show at the Sanger Theater.", "prediction_text": "Person 1 and Person 2 are going to a jazz club tonight."}
{"answers": "#Person1# buys some nice clothes by credit card with #Person2#'s assistance.", "prediction_text": "Person1 and Person2 are in a meeting."}
{"answers": "#Person1# pays #Person2# by credit card for some clothes.", "prediction_text": "Person1 and Person2 are in a meeting."}
{"answers": "#Person1# purchases some clothes by credit card with #Person2#'s assistance.", "prediction_text": "Person1 and Person2 are in a meeting."}
{"answers": "Mr. Blake explains the training manuals cannot be sent today because they are still being copied.", "prediction_text": "The manuals are in the printer."}
{"answers": "#Person1# is transferring the message between Mr. Blake and Mr. Foster about the training manuals.", "prediction_text": "The manuals are at the printer."}
{"answers": "Mr. Foster wants the training manuals to be sent this afternoon but Mr. Blake explains that they haven't been printed out yet.", "prediction_text": "The manuals are at the printer."}
{"answers": "#Person2# tells David about #Person2#'s planned a long trip for #Person2#'s vacation. David thinks it's nice.", "prediction_text": "People are going to be there for a tour."}
{"answers": "David and #Person2# are talking about #Person2#'s plan for the vacation. David thinks it sounds good.", "prediction_text": "People are going to be there for a tour."}
