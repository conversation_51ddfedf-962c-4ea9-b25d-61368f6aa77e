#!/bin/bash

# Setup script to fix wandb permission issues permanently
# Source this script before running any wandb code: source setup_wandb_env.sh

echo "Setting up wandb environment to avoid permission issues..."

# Set base directory
BASE_DIR="/storage/nammt/KD-SLM/Multi-Level-OT"

# Override HOME directory completely
export HOME="/storage/nammt"
export USERPROFILE="/storage/nammt"

# Set all wandb-specific environment variables
export WANDB_DIR="$BASE_DIR"
export WANDB_CACHE_DIR="$BASE_DIR/.wandb_cache"
export WANDB_CONFIG_DIR="$BASE_DIR/.wandb_config"
export WANDB_DATA_DIR="$BASE_DIR/.wandb_data"
export WANDB_ARTIFACT_DIR="$BASE_DIR/.wandb_artifacts"
export WANDB_BASE_URL="https://api.wandb.ai"
export WANDB_CONSOLE="off"
export WANDB_SILENT="false"

# Create all necessary directories
mkdir -p "$BASE_DIR/.wandb_cache"
mkdir -p "$BASE_DIR/.wandb_config"
mkdir -p "$BASE_DIR/.wandb_data"
mkdir -p "$BASE_DIR/.wandb_artifacts"
mkdir -p "/storage/nammt/.config"
mkdir -p "/storage/nammt/.cache"

echo "Wandb environment setup complete!"
echo "HOME is now: $HOME"
echo "WANDB_DIR is now: $WANDB_DIR"
echo ""
echo "You can now run your wandb scripts without permission issues."
