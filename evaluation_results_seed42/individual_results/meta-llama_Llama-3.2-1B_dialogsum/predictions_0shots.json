{"answers": "#Person1# and #Person2# talk about pilgrims around the world, including Muslims' pilgrimage to mecca and Christians' pilgrimage to Canterbury or Vatican. #Person2# thinks faith heals people instead of magical places.", "prediction_text": "The pilgrims in this story are Muslims who are on a pilgrimage to Mecca. They are going to the holy city to perform the haj. The haj is a pilgrimage to Mecca, the holy city of Islam. The pilgrims are going to Mecca to perform the haj. The haj is a pilgrimage to Mecca, the holy city of Islam. The pilgrims are going to the holy city to perform the haj. The pilgrims are going to the holy city to perform the haj. The pilgrims are going to the holy city to perform the haj. The pilgrims are going to the holy city to perform the haj. The pilgrims are going to the holy city to perform the haj"}
{"answers": "#Person1# is sending a package with the help of #Person2#.", "prediction_text": "#Person1# wants to send a package by first-class mail. #Person2# wants to send it insured. #Person1# wants to get stamps and money orders. #Person2# wants to get stamps only."}
{"answers": "#Person1# invites <PERSON> to picnic this weekend. <PERSON> accepts.", "prediction_text": "<PERSON> and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't know where it is. Lily suggests they go around and have supper."}
{"answers": "#Person1# makes drinks for #Person2# at a bar.", "prediction_text": "A bartender is trying to sell a customer a drink. The customer is not sure what to order. The bartender suggests a drink that the customer likes. The customer orders the drink. The bartender recommends a drink that the customer likes. The customer orders the drink. The bartender recommends a drink that the customer likes. The customer orders the drink. The bartender recommends a drink that the customer likes. The customer orders the drink. The bartender recommends a drink that the customer likes. The customer orders the drink. The bartender recommends a drink that the customer likes. The customer orders the drink. The bartender recommends a drink that the customer likes. The customer orders the drink. The bartender recommends a drink that the customer likes. The customer orders the drink. The bartender"}
{"answers": "Lin Fang and Lucy are talking about their favourite subjects.", "prediction_text": "The two students are talking about their favorite subjects. Nancy likes English, but she finds it difficult. Lucy likes English, but she finds it easy. Nancy likes math, but she doesn't like it. Lucy likes math, but she doesn't like it. Nancy likes Chinese, but she doesn't like it. Lucy likes Chinese, but she doesn't like it. Nancy likes science, but she doesn't like it. Lucy likes science, but she doesn't like it."}
{"answers": "#Person2# tells #Person1# that he falls in love with his neighbour and #Person1# thinks he has some competition and wishes him good luck.", "prediction_text": "A man and a woman meet and start dating. The man is a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman"}
{"answers": "Mr. Smith asks #Person1# to help find his train ticket, but #Person1# finds out that Mr. Smith didn't have any proof of purchase. Thus Mr. Smith brings another ticket in the end.", "prediction_text": "A man is trying to get to shanghai, but he doesn't have a ticket. A woman is trying to get to shanghai, but she doesn't have a ticket. They meet and they talk. They talk about their tickets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets. They talk about their wallets."}
{"answers": "#Person1# congratulates #Person2# on #Person2#'s new venture and expresses #Person1#'s willingness to help.", "prediction_text": "A lawyer and a client discuss the advantages of having an office of one's own."}
{"answers": "#Person1# gets a crank call and is angry about it.", "prediction_text": "A man calls a woman who he knows is a police officer. He tells her that he knows who she is and that he knows she is a police officer. He tells her that he knows she is a police officer because he has been calling her for the past ten times. He tells her that he knows she is a police officer because he has been calling her for the past ten times. He tells her that he knows she is a police officer because he has been calling her for the past ten times. He tells her that he knows she is a police officer because he has been calling her for the past ten times. He tells her that he knows she is a police officer because he has been calling her for the past ten times. He tells her"}
{"answers": "#Person1# introduces the weekend driving courses to #Person2#. #Person2# feels satisfied with its settings.", "prediction_text": "The person who is interested in driving courses is asked about the courses. The person who is interested in driving courses is asked about the driving courses. The person who is interested in driving courses is asked about the driving courses. The person who is interested in driving courses is asked about the driving courses. The person who is interested in driving courses is asked about the driving courses. The person who is interested in driving courses is asked about the driving courses. The person who is interested in driving courses is asked about the driving courses. The person who is interested in driving courses is asked about the driving courses. The person who is interested in driving courses is asked about the driving courses. The person who is interested in driving courses is asked about the driving courses. The"}
{"answers": "#Person1# and #Person2# are talking about the most fundamental concepts underlying the accounting process.", "prediction_text": "The first is accounting entity, and the second is going concern. The third is measuring unit. The fourth is accounting period, and the fifth is objectivity."}
{"answers": "#Person1# and #Person2# are talking about the low temperature at night, although spring has come.", "prediction_text": "#Person1#: I think spring is finally here. #Person2#: Yep, it sure seems like it. However, it's still very cold at night. #Person1#: Yes, they turned the heat off 6 days ago. It's absolutely freezing in my apartment at night. I have to turn on the air conditioner to blow hot air in order to warm things up a little. #Person2#: Well, and if you are outside and is a bit of a breeze. It feels cold quickly. #Person1#: It sure does. I think I'm going to follow my cats example and just sit in the sum that shining in through the windows."}
{"answers": "Mark asks Maggie for her notes because Mark's notes aren't good. They decide to be study partners and study together.", "prediction_text": "A student and a teacher are studying in the library. The student is tired and wants to borrow the teacher's notes. The teacher is willing to help, but only if the student is awake."}
{"answers": "Joe doesn't think highly of Tom's new assistant while Tom does.", "prediction_text": "A man and a woman meet at a party. The man is impressed with the woman's assistant, but the woman is not impressed with the man's assistant."}
{"answers": "#Person1# and #Person2# are talking about the popular sports in their countries, including football, basketball, tennis, swimming, golf, extreme sports and rugby.", "prediction_text": "In this dialogue, #Person1# and #Person2# talk about their favourite sports. #Person1# says that football is popular in his country, but that it is not as popular as it used to be. #Person2# says that football is popular in his country, but that it is not as popular as it used to be. #Person1# says that tennis is popular in his country, but that it is not as popular as it used to be. #Person2# says that tennis is popular in his country, but that it is not as popular as it used to be. #Person1# says that golf is popular in his country, but that it is not as popular as it used to be. #"}
{"answers": "#Person1# thinks #Person2# has chicken pox and warns #Person2# about the possible hazards but #Person2# thinks it will be fine.", "prediction_text": "A man and a woman are talking about their children. The man says that his son is a biohazard and that he is contagious. The woman says that her son is allergic to oatmeal and that she thinks he is dying. The man says that he is serious and that he is not joking. The woman says that she is joking and that she is not serious. The man says that he is serious and that he is not joking. The woman says that she is serious and that she is not joking. The man says that he is serious and that he is not joking. The woman says that she is serious and that she is not joking. The man says that he is serious and that he is not joking. The woman says that she"}
{"answers": "#Person1# gives #Person2# a physical check and advises #Person2# to stay in hospital for a shoulder X-ray the next day.", "prediction_text": "A man and a woman are in a hospital bed. The man is lying on his back, and the woman is lying on her side. The man is wearing a white shirt and a black jacket. The woman is wearing a white shirt and a black jacket. The man is holding a white shirt and a black jacket. The woman is holding a white shirt and a black jacket. The man is holding a white shirt and a black jacket. The woman is holding a white shirt and a black jacket. The man is holding a white shirt and a black jacket. The woman is holding a white shirt and a black jacket. The man is holding a white shirt and a black jacket. The woman is holding a white shirt and a black jacket. The man"}
{"answers": "People in #Person2#'s country like football, basketball, tennis, and swimming. People in #Person1#'s country like golf. A few people like extreme sports. Both people like rugby.", "prediction_text": "In this dialogue, #Person1# and #Person2# talk about their favourite sports. #Person1# says that football is popular in his country, but that it is not as popular as it used to be. #Person2# says that football is popular in his country, but that it is not as popular as it used to be. #Person1# says that tennis is popular in his country, but that it is not as popular as it used to be. #Person2# says that tennis is popular in his country, but that it is not as popular as it used to be. #Person1# says that golf is popular in his country, but that it is not as popular as it used to be. #"}
{"answers": "Sue doesn't eat cakes and sandwiches at her birthday party to avoid being allergic. Bill is willing to order her some hot soup to make her feel better. They decide to go to the restaurant later.", "prediction_text": "A man and a woman are having a party. The man is having a hard time getting the woman to eat anything. The woman is having a hard time getting the man to eat anything. The man is having a hard time getting the woman to eat anything. The woman is having a hard time getting the man to eat anything. The man is having a hard time getting the woman to eat anything. The woman is having a hard time getting the man to eat anything. The man is having a hard time getting the woman to eat anything. The woman is having a hard time getting the man to eat anything. The man is having a hard time getting the woman to eat anything. The woman is having a hard time getting the man to eat anything."}
{"answers": "#Person2# tells #Person1# #Person2#'s writing experience.", "prediction_text": "The two people are in a meeting. The first person is the interviewer. The second person is the applicant. The interviewer asks the applicant a series of questions about his or her writing experience and the applicant answers them. The interviewer then asks the applicant a series of questions about his or her writing experience and the applicant answers them. The interviewer then asks the applicant a series of questions about his or her writing experience and the applicant answers them. The interviewer then asks the applicant a series of questions about his or her writing experience and the applicant answers them. The interviewer then asks the applicant a series of questions about his or her writing experience and the applicant answers them. The interviewer then asks the applicant a series of questions about his or her writing experience and the applicant"}
{"answers": "#Person1# forgot to take an umbrella while it's raining. #Person2# shares an umbrella with #Person1#.", "prediction_text": "A man and a woman are walking together. The man says, \"Excuse me, Sir, do you know what time it is?\" The woman says, \"Oh, it's almost eleven twenty.\" The man says, \"Thank you.\" The woman says, \"You're welcome.\" The man says, \"The rain is quite heavy, isn't it?\" The woman says, \"Yeah. I was in a hurry and I forgot my umbrella today.\" The man says, \"I was in a hurry and I forgot my umbrella today.\" The woman says, \"I have one. We could share it. Which way are you going?\" The man says, \"Oh, that's very kind of you. I am going to the Garden Hotel"}
{"answers": "#Person1# comes to sign an agreement but it isn't ready until evening. So #Person1# is going through the draft.", "prediction_text": "The agreement was signed."}
{"answers": "#Person2# instructs #Person1# on how to send a package by mail.", "prediction_text": "#Person1# wants to send a package by first-class mail. #Person2# wants to send it insured. #Person1# wants to get stamps and money orders. #Person2# wants to get stamps only."}
{"answers": "#Person2# talks about Katie's evaluation and wants Katie to do better when she doesn't have customers.", "prediction_text": "The manager is concerned about the employee's tardiness. The employee is concerned about the manager's expectations. The employee is concerned about the employee's work. The employee is concerned about the employee's work. The employee is concerned about the employee's work. The employee is concerned about the employee's work. The employee is concerned about the employee's work. The employee is concerned about the employee's work. The employee is concerned about the employee's work. The employee is concerned about the employee's work. The employee is concerned about the employee's work. The employee is concerned about the employee's work. The employee is concerned about the employee's work. The employee is concerned about the employee's work. The employee is concerned about the employee's work"}
{"answers": "#Person1# tells #Person2# that Ruojia is married and will have a party tonight. #Person2#'s surprised to know that. They will bring their gifts to bless her.", "prediction_text": "The two friends are talking about Ruojia's marriage. #Person1# says that Ruojia has got married. #Person2# asks him who the party is. #Person1# says that Ruojia has got married. #Person2# says that he didn't receive the email. #Person1# says that he saw the news from Ruojia's twitter. #Person2# says that he didn't receive the email. #Person1# says that he will bring a pair of wineglasses and a card to wish her happy marriage. #Person2# says that he will buy a tea set."}
{"answers": "#Person2# would like to go to Australia.", "prediction_text": "#Person1#: Have you been to Australia?"}
{"answers": "James and Muriel are talking while waiting for Susan, Muriel's associate. They talk about how they spent the holiday with their families.", "prediction_text": "A man and a woman meet for the first time. They exchange names and then the man asks the woman if she is Muriel Douglas. The woman replies that she is, and the man asks if she is James. The woman replies that she is, and the man asks if she is happy. The woman replies that she is, and the man asks if she has a nice holiday. The woman replies that she did, and the man asks if she likes skiing. The woman replies that she does, and the man asks if she would like to go skiing. The woman replies that she would, and the man asks if she would like to go to Tahoe. The woman replies that she would, and the man asks if she would like to"}
{"answers": "Amy is talking about her first job experience and later job with #Person1#.", "prediction_text": "Amy was a secretary in a company. She was promoted to the position of Personnel Manager. She enjoyed her job, but it was poorly paid. She left the company because she wanted to apply for a position in the Sales Department."}
{"answers": "#Person2# thinks #Person1#'s new suit is not worthwhile.", "prediction_text": "#Person1#: What do you think of my new suit?"}
{"answers": "#Person2# tells #Person1# the charge policy at #Person2#'s buffet.", "prediction_text": "The restaurant is a buffet restaurant. The owner is a person of the party. The owner has three people in the party. The owner wants to know how many people in the party. The owner wants to know the price of the buffet. The owner wants to know the location of the buffet. The owner wants to know the price of the drinks. The owner wants to know the price of the food. The owner wants to know the price of the alcohol. The owner wants to know the price of the cold dishes. The owner wants to know the price of the hot dishes. The owner wants to know the price of the soft drinks. The owner wants to know the price of the alcohol. The owner wants to know the price of the vegetables."}
{"answers": "#Person1# was too busy to reconfirm a fight reservation and needs to buy another ticket.", "prediction_text": "The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with"}
{"answers": "Peter stops watering the garden and will have tea with #Person1#, since it's raining.", "prediction_text": "The two people are talking about the weather. The first person says that it is very dry, and the second person says that it was very dry last summer. The first person says that he will have tea by himself, and the second person says that he will have tea by himself. The first person says that it is raining, and the second person says that it is raining. The first person says that he will have tea by himself, and the second person says that he will have tea by himself. The first person says that it is very dry, and the second person says that it was very dry last summer. The first person says that he will have tea by himself, and the second person says that he will have tea by himself. The"}
{"answers": "#Person1# and #Person2# are discussing where to have fun, and they decide to go to the theater tonight.", "prediction_text": "A man and a woman are talking about their trip to New Orleans. The man says he likes it here, and the woman says she has been to several clubs. The man says he has been to the theater, and the woman says she has not been to the theater. The man says he has been to the riverboat tour, and the woman says she has been to the riverboat tour twice this week. The man says he has not been to the theater, and the woman says she has not been to the theater. The man says he has not been to the theater, and the woman says she has not been to the theater. The man says he has not been to the theater, and the woman says she has not been to the"}
{"answers": "#Person2# likes a skirt at #Person1#'s store, but thinks it too expensive.", "prediction_text": "A man and a woman are having a conversation. The man is trying to sell a skirt to the woman. The woman is trying to find a skirt that is in fashion. The man is trying to sell the skirt to the woman. The woman is trying to find a skirt that is in fashion. The man is trying to sell the skirt to the woman. The woman is trying to find a skirt that is in fashion. The man is trying to sell the skirt to the woman. The woman is trying to find a skirt that is in fashion. The man is trying to sell the skirt to the woman. The woman is trying to find a skirt that is in fashion. The man is trying to sell the skirt to the woman. The woman is"}
{"answers": "Mike is describing his sister to #Person1#.", "prediction_text": "Mike and his sister have a conversation about their appearance. Mike says that his sister is tall and pretty, while his sister says that she is not as clever as Mike."}
{"answers": "#Person2# feels bored with the meeting. #Person1# gives #Person2# some suggestions on how to entertain but finally asks #Person2# to keep quiet.", "prediction_text": "The Daily News is a newspaper that is published in New York City. It is a paper that is known for its coverage of the city's social and political life. The paper is also known for its coverage of the city's sports teams. The paper is also known for its coverage of the city's crime and corruption. The paper is also known for its coverage of the city's politics. The paper is also known for its coverage of the city's economy. The paper is also known for its coverage of the city's environment. The paper is also known for its coverage of the city's education. The paper is also known for its coverage of the city's health and safety. The paper is also known for its coverage of the city's culture."}
{"answers": "It's #Person1# and #Person2#'s grandpa's birthday next week. #Person1# and #Person2# decide to have a party with jazz, pizza at their house. #Person1# and grandma will make food. #Person2# will get him a book. The party will be on Saturday.", "prediction_text": "The two people in this dialogue are planning a surprise birthday party for their grandfather. The first person, #Person1#, suggests that they have a party at their house, but the second person, #Person2#, suggests that they have it at a hotel. The first person suggests that they should have a party at their house because it is too cold for a garden party at this time of year. The second person suggests that they should have a party at a hotel because it is too expensive and it is too cold for a garden party at this time of year. The first person suggests that they should have a party on Friday because it is the best day for a party. The second person suggests that they should have a party on Saturday because it is"}
{"answers": "#Person1# asks #Person2# for the way to Peak Tram and writes it down.", "prediction_text": "A man and a woman are walking along Queen's Road. The man says, \"Excuse me.\" The woman says, \"Yes?\" The man says, \"Can you tell me the way to the Peak Tram, please?\" The woman says, \"Certainly. Go along Queen's Road...\" The man says, \"Along Queen's Road...\" The woman says, \"Yes, and turn right at the Hilton Hotel.\" The man says, \"Right at the Hilton.\" The woman says, \"Then go up Garden Road, past the Cathedral.\" The man says, \"The Cathedral?\" The woman says, \"Yes. Then cross the road at the next traffic lights. The Peak Tram is straight ahead. You can't miss it.\" The"}
{"answers": "#Person1# is withdrawing money from an ATM. But the ATM wrongly transfers #Person1#'s money to the World Wildlife Foundation. It drives #Person1# crazy.", "prediction_text": "A man is trying to withdraw money from a bank ATM, but the machine is talking to him like he is an idiot. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get"}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. Then they want to have fried bananas and #Person1# goes out to buy some.", "prediction_text": "#Person1#: It's time for desserts! Are you still hungry?"}
{"answers": "#Person1# and #Person2# talk about what kind of movies to watch and they decided to watch a war movie.", "prediction_text": "The two people in the conversation are discussing what they would like to see at the movies. The first person says that he would like to see a horror movie, but the second person says that he doesn't like horror movies. The first person says that he would like to see a detective movie, but the second person says that he doesn't like detective movies. The first person says that he would like to see a comedy, but the second person says that he doesn't like comedies. The first person says that he would like to see a love story, but the second person says that he doesn't like love stories. The first person says that he can't imagine that a girl like the second person would want to see a war movie, but"}
{"answers": "#Person1# and #Person2# feel happy because #Person1# is pregnant.", "prediction_text": "A man and a woman are talking about their pregnancy. The man is happy, but the woman is worried."}
{"answers": "#Person1# pushes #Person2# to pay for the dormitory soon, but #Person2# is thinking about living off campus. They analyze the advantages and disadvantages of living on and off campus.", "prediction_text": "The two friends are discussing their plans for the coming year. The first one is thinking of living off campus, while the second one is thinking of studying in the library. The first one is worried about the cost of living off campus, while the second one is worried about the cost of studying in the library. The first one is worried about the noise of the dormitory, while the second one is worried about the quiet of the library. The first one is worried about the cost of the dormitory, while the second one is worried about the cost of the library. The first one is worried about the noise of the dormitory, while the second one is worried about the quiet of the library. The first one is worried about the cost of the"}
{"answers": "Johnson shows #Person1# the correct way to use weights machine and tells #Person1# using a card to keep track of exercise intensity. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "#Person1#: Excuse me. Can you tell me how to get started using these machines?"}
{"answers": "#Person2# has been working overtime and only gets minimum wage. #Person1# suggests #Person2# either asking for more compensation or quitting, but #Person2# refuses in fear of not being able to find another job.", "prediction_text": "#Person1#: is working overtime again. #Person2#: I know, but I don't have a choice, my boss has complete control over our work schedules. #Person1#: Well, I hope they're paying you the big bucks for all this work... #Person2#: No, not exactly, I'm working for minimum wage. And they don't give extra pay for overtime. Overtime is our own time. #Person1#: What?! Isn't there a law against that, I think there is some legislation that requires employers to give their employees a fair rate of wages. You should know your rights! If you are putting in so many hours, they should give you some kind of compensation. #Person2"}
{"answers": "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks Ms. Dawson to send out a memo to all employees by the afternoon.", "prediction_text": "The new policy restricts all office communications to email correspondence and official memos. The use of Instant Messaging programs by employees during working hours is strictly prohibited. Employees who persist in using Instant Messaging will first receive a warning and be placed on probation. At second offense, the employee will face termination. Any questions regarding this new policy may be directed to department heads."}
{"answers": "#Person1# interviews Tom about his experience to start a company selling green products, how to live a mildly green life and to learn from mistakes.", "prediction_text": "Tom and his company are doing a great job of being green. They are using recycled materials, and they are making their own products. They are also working with other companies to make their products."}
{"answers": "#Person1# and #Person2# talk about the weather and #Person2# invites #Person1# to have a picnic.", "prediction_text": "The weatherman is wrong. It will rain."}
{"answers": "#Person1# helps #Person2# to return a book and check out a video in the library.", "prediction_text": "The library staff is trying to figure out how to handle a patron's request for a video. The patron is asking for a video, but also wants to return a book. The staff member is trying to figure out how to handle the situation. The patron is asking for a video, but also wants to return a book. The staff member is trying to figure out how to handle the situation. The patron is asking for a video, but also wants to return a book. The staff member is trying to figure out how to handle the situation. The patron is asking for a video, but also wants to return a book. The staff member is trying to figure out how to handle the situation. The patron is asking for a video, but also wants"}
{"answers": "#Person2# describes how the working days are like, including the working hours and working contents, to #Person1#.", "prediction_text": "#Person1# and #Person2# are both working in a cubicle. #Person1# says that he doesn't like it very much. #Person2# says that he doesn't mind it. #Person1# says that he works in a cubicle. #Person2# says that she works in a cubicle too. #Person1# says that he doesn't like it very much. #Person2# says that she doesn't mind it. #Person1# says that he works in a cubicle. #Person2# says that she works in a cubicle too. #Person1# says that he doesn't like it very much. #Person2# says that she doesn't mind it. #"}
{"answers": "#Person1# and #Person2# are shocked about Sarah's love for Dick.", "prediction_text": "The two friends, #Person1# and #Person2#, are discussing the marriage of their friend, #Person3#. #Person1# says that #Person3# is a very good girl, but #Person2# says that she is a very bad girl. #Person1# says that #Person3# is a very good girl, but #Person2# says that she is a very bad girl. #Person1# says that #Person3# is a very good girl, but #Person2# says that she is a very bad girl. #Person1# says that #Person3# is a very good girl, but #Person2# says that she is a very bad girl. #Person1# says"}
{"answers": "Gian and Gina introduce themselves to each other and Gian introduces the friend Robert to Gina.", "prediction_text": "The two participants introduce themselves and discuss their work. They also talk about the conference and the work of the service provider."}
{"answers": "#Person1# asks Tom for his opinion on second-hand goods and Tom suggested #Person1# being careful.", "prediction_text": "The second-hand goods are just as good as the new ones."}
{"answers": "#Person1# and #Person2# go to the nightclub to dance and #Person1# prefers fast dances to slow dances.", "prediction_text": "The two people are talking about night life in Beijing. The first person says that he likes to dance to fast music. The second person suggests that they go to a disco. The first person says that he likes to dance to slow music. The second person says that they should try a waltz. The first person says that he will sit out the next dance. The second person gets him a drink."}
{"answers": "#Person2# will change a steak for #Person1# as that one doesn't satisfy #Person1#.", "prediction_text": "A man is served a steak that is too well done. He asks for it to be changed, but the waiter is not happy about it."}
{"answers": "#Person1# and #Person2# both get laid off and they want to find a job.", "prediction_text": "The two people are having a conversation. The first person is looking for work, and the second person is looking for a job. The first person is looking for anything that will pay the mortgage, and the second person is looking for a job that will pay the mortgage. The first person has gone out on interviews, but has not heard back from them. The second person has seen the posting for the electrician apprentice program, and is interested in it."}
{"answers": "#Person1# begs Steven's to persuade #Person1#'s wife not to divorce him, and Steven agrees.", "prediction_text": "A man is in love with his secretary. He is afraid that his wife will find out about his affair. He is trying to convince his wife to stay with him."}
{"answers": "#Person1# thinks it's strange of Mirella wearing casually in the office. #Person2# thinks she was influenced by the casual atmosphere in California and tells #Person1# management decided to give her a new white dress to warn her.", "prediction_text": "The two people in the office are discussing the new dress code that was introduced by the management. The first person says that it is strange that the rest of the people in the office are allowed to wear more casual clothes, while Mirella is not. The second person says that it is not as bad as it seems, because Mirella is allowed to wear more casual clothes. The first person says that he heard that Mirella found a new dress on her desk this morning. The second person says that the dress is a sign that the management is trying to tell Mirella that her new style of dressing down is not acceptable."}
{"answers": "#Person1# and #Person2# are talking about what gifts to buy for their families from the Olympic souvenir store after the volleyball match.", "prediction_text": "The two friends have a lot of things to do after the volleyball match. They have a lot of ideas. They can go to the Olympic souvenir store to buy some gifts for their family."}
{"answers": "#Person1# asks about the things that took up #Person2#.", "prediction_text": "The dialogue is a simple conversation between two people. The first person (Person1) is talking to the second person (Person2). The first person is asking the second person what's new with him. The second person is replying that he is busy and has been eating and sleeping. The first person is asking the second person if he is busy and the second person is replying that he is busy and has been eating and sleeping. The first person is asking the second person if he is busy and the second person is replying that he is busy and has been eating and sleeping. The first person is asking the second person if he is busy and the second person is replying that he is busy and has been eating and sleeping. The first"}
{"answers": "Tom suggests going swimming, but Jane refuses. They decide to meet at the Grill to eat and then go to study together in the library.", "prediction_text": "Jane and Tom are having a conversation about their studies. Jane is worried about her upcoming test, and Tom is worried about his upcoming paper. They decide to go swimming at the Student Center, but Tom has a paper due on Friday, and he can't stay very long. They decide to go to the Grill for dinner, and Tom will stay until Jane is ready to go home."}
{"answers": "Mom asks May to help to prepare for the picnic and May agrees.", "prediction_text": "The two children are preparing for a picnic. The father asks the mother to help him take the things to the living room. The mother asks the son to help her. The son says he can manage it by himself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage"}
{"answers": "Lucy likes English and P.E. best, but Lin Fang's favorite is Chinese and Science.", "prediction_text": "The two students are talking about their favorite subjects. Nancy likes English, but she finds it difficult. Lucy likes English, but she finds it easy. Nancy likes math, but she doesn't like it. Lucy likes math, but she doesn't like it. Nancy likes Chinese, but she doesn't like it. Lucy likes Chinese, but she doesn't like it. Nancy likes science, but she doesn't like it. Lucy likes science, but she doesn't like it."}
{"answers": "Sarah tells #Person1# that she can't afford a new house. #Person1# says #Person1#'s sister-in-law bought a cheap house and suggests Sarah should buy a house far from the city center.", "prediction_text": "#Person1#: Have you ever thought of moving, Sarah?"}
{"answers": "#Person1# invites Peter to have tea, but Peter is watering the garden. Then it rains, Peter can get in and have tea with #Person1#.", "prediction_text": "The two people are talking about the weather. The first person says that it is very dry, and the second person says that it was very dry last summer. The first person says that he will have tea by himself, and the second person says that he will have tea by himself. The first person says that it is raining, and the second person says that it is raining. The first person says that he will have tea by himself, and the second person says that he will have tea by himself. The first person says that it is very dry, and the second person says that it was very dry last summer. The first person says that he will have tea by himself, and the second person says that he will have tea by himself. The"}
{"answers": "#Person1# wants to pick up a ticket at #Person2#'s and pays by card.", "prediction_text": "The person who is supposed to pay for the ticket is the one who is supposed to pay for the ticket. The person who is supposed to pay for the ticket is the one who is supposed to pay for the ticket. The person who is supposed to pay for the ticket is the one who is supposed to pay for the ticket. The person who is supposed to pay for the ticket is the one who is supposed to pay for the ticket. The person who is supposed to pay for the ticket is the one who is supposed to pay for the ticket. The person who is supposed to pay for the ticket is the one who is supposed to pay for the ticket. The person who is supposed to pay for the ticket is the one who is supposed to pay"}
{"answers": "Ms. Ross and Mr. Fisher start the conversation with greetings. Then they discuss the preparation for a presentation. Ms. Ross confirms the details of the presentation, including schedule, food service. Ms. Ross is concerned that Mr. Fisher's sent too many invitations than the room capacity.", "prediction_text": "#Person1#: Ah, good morning, Ms. Ross, do come in."}
{"answers": "Mr. Faber calls #Person1# to book a double room for 3 nights at York Hotel.", "prediction_text": "A man calls a hotel to book a room for three nights. The hotel asks for his name, and he gives it. The hotel asks for his arrival date, and he gives it. The hotel asks for his room type, and he gives it. The hotel asks for the number of people in his party, and he gives it. The hotel asks for the number of nights he will be staying, and he gives it. The hotel asks for the amount he will be paying, and he gives it. The hotel asks for his name, and he gives it. The hotel asks for his arrival date, and he gives it. The hotel asks for his room type, and he gives it. The hotel asks for the number of people in his"}
{"answers": "#Person2# is making a shopping budget to save the rest of the money. #Person1# thinks it's a good idea.", "prediction_text": "#Person1#: What are you working on?"}
{"answers": "#Person1# makes requires about #Person2#'s English standard.", "prediction_text": "#Person1#: Employees in this company have to have a good command of English. Do you think you are proficient in both written and spoken English? #Person2#: Yes, I think I am quite proficient in both written and spoken English. #Person1#: Well, then, please tell me about your English education. #Person2#: I have been studying English since junior high school. And I majored in English in college. #Person1#: I see, do you speak any other languages? #Person2#: Yes, I speak a little French and Japanese."}
{"answers": "#Person2# tells Mary about #Person2#'s terrible experience that a dog rushed at #Person2# and barked loudly when #Person2# was delivering milk.", "prediction_text": "A man and a woman are talking about a dog that ran after them. The man says that he knocked into a tree, and the dog ran after him. The woman says that she was lucky, because the dog didn't jump over the fence."}
{"answers": "#Person1# speculates the signature on the book is valuable, but #Person2# thinks it is somewhat impractical.", "prediction_text": "#Person1# and #Person2# are two friends who are both interested in books. #Person1# is interested in old books, and #Person2# is interested in new books. #Person1# is interested in old books because he likes to read old books. #Person2# is interested in new books because he likes to read new books. #Person1# is interested in old books because he likes to read old books. #Person2# is interested in new books because he likes to read new books. #Person1# is interested in old books because he likes to read old books. #Person2# is interested in new books because he likes to read new books. #Person1# is interested in old"}
{"answers": "Emily has her first paycheck in the States and there are a few things she doesn't understand. Susan explains the federal and state deductions to her. Emily thinks it might be the same in the UK but she just never paid much attention before.", "prediction_text": "In this dialogue, the narrator, Susan, is a customer at a local bank. She asks the teller, Emily, about her paycheck. Emily explains that the deductions are for federal and state income taxes, and that the health insurance and 401(K) deductions are for the bank's employee benefit programs. Emily also explains that the deductions for health insurance and the 401(K) are small, and that the bank's net pay is not as high as it would like."}
{"answers": "Catherine and Tom talk about American fast-food culture inspired by a movie, and they think Americans need to find a way to make the most of their fast foods.", "prediction_text": "The American fast food culture is a lifestyle that craves convenience. People just don't care whether it is healthy or not. But some healthy menu options are also served at fast food restaurants."}
{"answers": "#Person1# teaches #Person2# the rules and terms of bowling.", "prediction_text": "The dialogue is about bowling. The speaker is a person who has never played bowling before. The listener is a person who has played bowling before. The speaker asks the listener to tell him more about bowling. The listener tells the speaker that bowling is a game that has 10 frames. The speaker asks the listener to tell him more about bowling. The listener tells the speaker that bowling is a game that has 10 rounds. The speaker asks the listener to tell him more about bowling. The listener tells the speaker that bowling is a game that has 10 frames. The speaker asks the listener to tell him more about bowling. The listener tells the speaker that bowling is a game that has 10 rounds. The speaker asks the listener to tell him more"}
{"answers": "#Person1# and #Person2# talk about the difficulty of not having a personal computer.", "prediction_text": "The two people are frustrated with the way they are being treated by the library. They are both looking forward to the day when they can afford to get their own computers."}
{"answers": "#Person2# wants to know about requesting a loan. #Person1# introduces the policy of loan interest and credit assessment. #Person2# then leaves as he has a terrible credit score.", "prediction_text": "The bank is not interested in your credit score. They are only interested in your ability to pay back the loan."}
{"answers": "#Person1# and Bob share their last weekend's activities. And they decide to play a game this weekend.", "prediction_text": "The two friends had a conversation about their weekend activities. The first person said that he stayed at home and watched TV. The second person said that he went to a dance party. The first person said that he went shopping in the afternoon. The second person said that he won a game. The first person said that he played tennis on Sunday. The second person said that he had a game with him. The first person said that he had a good time. The second person said that he had a good time. The first person said that he had a good time. The second person said that he had a good time. The first person said that he had a good time. The second person said that he had a good time. The first person"}
{"answers": "Steven calls Mr. Sellers and asks him what he should do to deal with a power failure.", "prediction_text": "Steven, the tenant, is a bit confused about the power failure. He asks his landlord, Mr. Sellers, for help. Mr. Sellers explains to Steven that the power failure is caused by a blown fuse. He also tells Steven to check the fuse box in the basement. Steven goes down to the basement and finds the fuse box. He unscrews the burnt fuses and replaces them with the good ones. He calls Mr. Sellers again to check if the power is back on."}
{"answers": "Steven tries to do the repairment under Mr. Seller's instruction to solve power failure.", "prediction_text": "Steven, the tenant, is a bit confused about the power failure. He asks his landlord, Mr. Sellers, for help. Mr. Sellers explains to Steven that the power failure is caused by a blown fuse. He also tells Steven to check the fuse box in the basement. Steven goes down to the basement and finds the fuse box. He unscrews the burnt fuses and replaces them with the good ones. He calls Mr. Sellers again to check if the power is back on."}
{"answers": "Fanny had a nightmare because of the pressure from school applications. Her mother and Andy both comfort her.", "prediction_text": "Andy and Fanny are talking about their dreams. Andy had a bad dream and Fanny had a good dream. Andy is worried about getting into the University of Michigan. Fanny is worried about getting into the University of Michigan."}
{"answers": "#Person1# asks #Person2#'s opinion on new clothes, but #Person2# only focuses on the game and feels annoyed. They are both angry and argue.", "prediction_text": "#Person1#: What do you think of this one? #Person2#: Eh, so so. #Person1#: And this one? Too flashy? #Person2#: Nah, not too flashy. #Person1#: Uhg! And this sweater from my aunt? Isn't it hideous? #Person2#: I guess. #Person1#: Are you even listening? I'm trying to have a conversation with you. #Person2#: And I'm trying to watch the game, but you're yapping on about your new clothes! #Person1#: Well I have to decide which gifts to keep and which to exchange for better ones when I go to the Boxing Day sales this afternoon! #Person2#: Well could you"}
{"answers": "#Person1# and Mr. Simpson will have lunch together.", "prediction_text": "A man and a woman meet in a restaurant. The man is free, the woman is not. The man asks the woman if she is free. The woman says she is not. The man says he knows of a place where they can meet. The woman says she will be there at 12:30. The man says he will be there at 12:30. The woman says she will see him then. The man says he will see her then. The woman says she will see him then. The man says he will see her then. The woman says she will see him then. The man says he will see her then. The woman says she will see him then. The man says he will see her then. The woman"}
{"answers": "James helps Mrs. Thomas to do chores because James wanted to save money and Mrs. Thomas is incapable.", "prediction_text": "A man and a woman are talking about their jobs. The man is a gardener and the woman is a housekeeper. The man is cleaning up the yard and the woman is cleaning the house. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the"}
{"answers": "Steven wants to stay up to go over the household budget when #Person1# is going to sleep.", "prediction_text": "A man and a woman are having a conversation. The man is tired and wants to go to bed. The woman is tired and wants to stay up. The man says he can't because he has to do the household budget. The woman says she can't because she has to go over the household budget. The man says he can't because he has to close the basement door before he goes to bed. The woman says she can't because she has to close the basement door before she goes to bed. The man says he can't because he has to close the basement door before he goes to bed. The woman says she can't because she has to close the basement door before she goes to bed. The man says he can't because he"}
{"answers": "#Person2# cannot stand the noise near her room and #Person1# promises to change her room tomorrow as there is no room available today.", "prediction_text": "A woman is disturbed by the noise of the baggage elevator. She asks the hotel staff to change her room. The hotel staff is sorry but they don't have any spare rooms. The woman is not happy and leaves the hotel."}
{"answers": "Jack tells #Person1# that business communication is his favorite last year and #Person1# will check it.", "prediction_text": "#Person1# and #Person2# are both students at the same university. #Person1# is a freshman and #Person2# is a sophomore. #Person1# is in a political science class and #Person2# is in a business communication class. #Person1# is interested in taking a business communication class next semester. #Person2# is interested in taking a political science class next semester. #Person1# is interested in taking a political science class next semester. #Person2# is interested in taking a business communication class next semester. #Person1# is interested in taking a business communication class next semester. #Person2# is interested in taking a business communication class next semester. #Person1# is"}
{"answers": "Laura comes across #Person1# on the way to the gym, and they stop to talk about exercise habits. Unlike Laura, #Person1# prefers other exercises over going to the gym.", "prediction_text": "The two people are talking about their exercise habits. #Person1# says that he does not exercise regularly, but he does not like to go to the gym. #Person2# says that he does not exercise regularly either, but he does like to go to the gym. #Person1# says that he does not like to go to the gym because he is lazy. #Person2# says that he does not like to go to the gym because he is not interested in it. #Person1# says that he does not like to go to the gym because he does not like to exercise. #Person2# says that he does not like to go to the gym because he does not like to exercise. #Person1# says"}
{"answers": "#Person1# and #Person2# have a chat about the current rainy season which #Person1# can hardly endure while #Person2# has already been used to.", "prediction_text": "The weather is really miserable. It has been raining hard all day long. Yes, it's so cold. Do you think the rain is going to let up soon? Yes, I think so. The rainy season will be over soon. How long does the rainy season last? About two months. I can hardly stand these wet and cold days. It seems that it doesn't bother you very much. I'm used to this kind of days. But I prefer warm weather. Spring will come soon. And we will have some pleasant weather then. I hope so."}
{"answers": "#Person2# has a girlfriend in Thailand. They know each other on the Internet but never meet. #Person1# is surprised but congratulates #Person2#.", "prediction_text": "The Internet is making interaction much faster. People are getting to know each other much more quickly this way."}
{"answers": "#Person1# suggests having beers after dinner. Jim refuses. They decide to go to the gym to meet their friends.", "prediction_text": "The two friends had a good time at the gym. They played pingpong and danced. They also met some of their friends."}
{"answers": "#Person1# will send #Person1#'s son an important certificate by certified mail. And #Person2# suggests #Person1# send a watch by registered mail.", "prediction_text": "The two people are discussing the best way to send a package. The first person says that he wants to make sure that the package is received. The second person says that he wants to make sure that the package is insured. The first person says that he wants to send the package by registered mail. The second person says that he wants to send the package by certified mail. The first person says that he wants to send the package by registered mail. The second person says that he wants to send the package by certified mail. The first person says that he wants to send the package by registered mail. The second person says that he wants to send the package by certified mail. The first person says that he wants to send the package by registered mail."}
{"answers": "#Person1# asks #Person2# how to use the library. #Person2# offers specific directions.", "prediction_text": "The library is a place where people can read books and magazines. It has two sections: the reading section and the reference section. The reading section has books and magazines, and the reference section has books and journals. The library has two sections: the reading section and the reference section. The reading section has books and magazines, and the reference section has books and journals. The library has two sections: the reading section and the reference section. The reading section has books and magazines, and the reference section has books and journals. The library has two sections: the reading section and the reference section. The reading section has books and magazines, and the reference section has books and journals. The library has two sections: the reading section and the reference section."}
{"answers": "#Person2# offers #Person1# two ways to send the package and #Person1# chooses the cheaper one.", "prediction_text": "The dialogue above is a typical example of a conversation between a customer and a post office employee. The customer wants to send a package to Korea, but the employee is not sure how to do it. The customer asks for a quote, and the employee gives a quote. The customer then asks for a cheaper way to send the package, and the employee gives a quote. The customer then decides to go with the cheaper way, and the employee gives a quote. The customer then decides to go with the cheaper way, and the employee gives a quote. The customer then decides to go with the cheaper way, and the employee gives a quote. The customer then decides to go with the cheaper way, and the employee gives a quote. The customer then decides"}
{"answers": "#Person1# and #Person2# talk about the weather and how to keep warm.", "prediction_text": "#Person1#: I think spring is finally here. #Person2#: Yep, it sure seems like it. However, it's still very cold at night. #Person1#: Yes, they turned the heat off 6 days ago. It's absolutely freezing in my apartment at night. I have to turn on the air conditioner to blow hot air in order to warm things up a little. #Person2#: Well, and if you are outside and is a bit of a breeze. It feels cold quickly. #Person1#: It sure does. I think I'm going to follow my cats example and just sit in the sum that shining in through the windows."}
{"answers": "#Person2# wants to inquire about a loan for studying abroad. #Person1# explains the policy and age limitations to #Person2#. #Person2# will decide later.", "prediction_text": "#Person1#: Personal Loans, how may I help?"}
{"answers": "#Person2# appreciates for #Person1# 's kind help.", "prediction_text": "#Person1#: You're all set now?"}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay for the house rent but Ayden lends $20 because of the lack of money. They decide to have dinner at #Person1#'s place.", "prediction_text": "A man and a woman are having a conversation. The man is asking the woman for money. The woman is saying that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money"}
{"answers": "#Person1# agrees with #Person2#'s doctor that #Person2# should diet.", "prediction_text": "The doctor told the patient that he had been eating too much red meat. The patient asked the doctor for advice. The doctor told the patient to cut down on red meat. The patient agreed to do so."}
{"answers": "#Person2# has traditional Greek yogurt, which #Person1# thinks rather plain. #Person1# has an Italian tiramisu, which #Person2# thinks delicious. #Person1# goes and gets both of them a fried banana.", "prediction_text": "#Person1#: It's time for desserts! Are you still hungry?"}
{"answers": "#Person2# has the bottom bunk by winning the coin flip with #Person1#. They decide to use #Person1#'s stereos because it's better. #Person1# has the desk by the window for it's fair. #Person1# will go out for food and come back to unpack after #Person2# has done.", "prediction_text": "#Person1#: Alright, we need to have a plan here. First of all, which bed do you want?"}
{"answers": "Bill tells Jim that Dick's been sick.", "prediction_text": "#Person1#: Hi, Bill."}
{"answers": "#Person1# works as a toy salesperson and feels so tired recently because Christmas is coming, and everyone's shopping for presents.", "prediction_text": "The person who is tired is #Person1# and the person who is sorry is #Person2#. The person who is tired is talking about the fact that he has to work at 8:00 AM and that he is late. The person who is sorry is talking about the fact that the shopping center is very busy and that people are shouting and pushing him around. The person who is tired is talking about the fact that he is used to it and that he is used to selling gifts for kids before Christmas. The person who is sorry is talking about the fact that Christmas doesn't mean much to him now."}
{"answers": "#Person1# might make a peanut butter and jelly sandwich to eat.", "prediction_text": "#Person1#: I'm hungry."}
{"answers": "#Person1# prefers sending the package by post to by first class after being told the price and the duration by #Person2#.", "prediction_text": "The dialogue above is a typical example of a conversation between a customer and a post office employee. The customer wants to send a package to Korea, but the employee is not sure how to do it. The customer asks for a quote, and the employee gives a quote. The customer then asks for a cheaper way to send the package, and the employee gives a quote. The customer then decides to go with the cheaper way, and the employee gives a quote. The customer then decides to go with the cheaper way, and the employee gives a quote. The customer then decides to go with the cheaper way, and the employee gives a quote. The customer then decides to go with the cheaper way, and the employee gives a quote. The customer then decides"}
{"answers": "Kathy complains about the noise and #Person2# explains it's because it's the year of the 17-year bird calls. Then they discuss where the birds live.", "prediction_text": "The birds are noisy in the mountains, but they're not all year long. They come down to the forest to sing and do their special dances."}
{"answers": "#Person1# is taking off to restart a business. #Person1# expresses appreciation to #Person2# and asks #Person2# to take care of Pamela.", "prediction_text": "A man and a woman are talking on the phone. The man says he is leaving for a business trip. The woman says she is going to the airport to meet him. The man says he is leaving for a business trip. The woman says she is going to the airport to meet him. The man says he is leaving for a business trip. The woman says she is going to the airport to meet him. The man says he is leaving for a business trip. The woman says she is going to the airport to meet him. The man says he is leaving for a business trip. The woman says she is going to the airport to meet him. The man says he is leaving for a business trip. The woman says she is going to the"}
{"answers": "#Person2# at first thinks #Person1#'s behaviour cruel but finally joins #Person1#.", "prediction_text": "A man and a woman are in a car. The man is driving. The woman is sitting in the back seat. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The"}
{"answers": "#Person1# has no jobs but is unwilling to go home. #Person2# lends $50 to #Person1#.", "prediction_text": "A man and a woman are talking about their jobs. The man says he has a job, but he doesn't like it. The woman says she has a job, but she doesn't like it. The man says he is looking for a job, but he doesn't know where to look. The woman says she is looking for a job, but she doesn't know where to look. The man says he is looking for a job, but he doesn't know where to look. The woman says she is looking for a job, but she doesn't know where to look. The man says he is looking for a job, but he doesn't know where to look. The woman says she is looking for a job, but she doesn't"}
{"answers": "#Person2# has applied for a student visa in the states and tells #Person1# about the rules and the required documents of applying for a USA visa and why they are so strict.", "prediction_text": "The interviewee is a Chinese student who has applied for a student visa to study in the United States. He says that the application form is quite complicated and that it took him a few hours to fill it out. He also says that the hardest thing was getting all the necessary documents. The interviewee says that he had to show document relating to his financial status and of course his education, because he wants a student visa. He says that the embassy is very strict about it. He also says that he thinks that many people break the rules regarding their visa conditions."}
{"answers": "Mr. Steven Kayne is invited to share his experience of establishing a bicycle shop by a business program.", "prediction_text": "Mr. Steven Kayne, who has just taken over and established bicycle shop, tells us what made him want to run his own store. He says he loves racing bikes and fixing them. He knew then that as soon as he had enough money to get his own business going, he'll do it. He had his heart set on it and he didn't let anything stand in his way. When he went down to the bank and got a business loan, he knew he'd love being his own boss. Now his time is his own. He opens the store when he wants and leaves when he wants. He has hired a couple of friends of his who love biking as much as he does. They help him out a few days a week."}
{"answers": "#Person2# tells #Person1# that #Person2#'s house was robbed.", "prediction_text": "The dialogue is about a robbery. The speaker is upset because his house was robbed. The listener is worried about the speaker's safety. The speaker is waiting for the police to arrive. The listener is waiting for the police to arrive. The speaker is worried about the speaker's safety. The listener is worried about the speaker's safety. The speaker is waiting for the police to arrive. The listener is waiting for the police to arrive. The speaker is worried about the speaker's safety. The listener is worried about the speaker's safety. The speaker is waiting for the police to arrive. The listener is waiting for the police to arrive. The speaker is worried about the speaker's safety. The listener is worried about the speaker's safety. The speaker is"}
{"answers": "#Person2# admires Abraham Lincoln for his perseverance, courage and humility.", "prediction_text": "Abraham Lincoln was a great man. He was a great president, and he was a great man. He was a great man because he was a great president. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great"}
{"answers": "Lisa found that her husband was cheating on her. She asked him to tell her the truth or end the relationship that she would divorce him. #Person1# agrees with what she did.", "prediction_text": "A man is cheating on his wife. He tells his wife that he is seeing someone else. She asks him about the girl who called. He lies and says it was someone from work. She presses him on it. He breaks down and admits to a small indiscretion. She tells him she will divorce him if he doesn't tell her the truth or end the relationship with the girl. He agrees."}
{"answers": "#Person1#'s flight has been delayed because of heavy rain and #Person2# suggests #Person1# listen to the announcement for further information.", "prediction_text": "The dialogue is about a flight delay. The speaker is asking the person at the counter for the flight number. The person at the counter is telling the speaker that the flight has been delayed due to heavy rain. The speaker is asking the person at the counter for the delay time. The person at the counter is telling the speaker that the delay time is unknown at present. The speaker is asking the person at the counter for the weather forecast. The person at the counter is telling the speaker that the weather forecast is not available at present. The speaker is asking the person at the counter for the change in weather. The person at the counter is telling the speaker that the change in weather is not available at present. The speaker is asking the person at the"}
{"answers": "#Person1# is warning #Person2# not to do that or #Person2# will get into trouble, but #Person2# doesn't care.", "prediction_text": "#Person1#: You're going to get into a lot of trouble if you do that."}
{"answers": "#Person2# is going to travel around Europe and will buy #Person1# a souvenir.", "prediction_text": "The two friends are talking about their plans for the holiday. The first person says that he is going to travel around Europe. The second person says that he is going to visit the museums in Paris. The first person says that he will go to Milan. The second person says that he will go to Edinburgh. The first person says that he will buy souvenirs for his friend. The second person says that he has kept that in mind."}
{"answers": "#Person1# is afraid that eyelash curler may hurt #Person2#'s eyes.", "prediction_text": "The two people in this dialogue are arguing about the use of eyelash curlers. The first person, #Person1#, says that the eyelash curler is a form of torture, while the second person, #Person2#, says that it is just a simple way to curl the eyelashes. The two people are arguing about the use of the eyelash curler, and the first person says that it is a form of torture. The second person says that it is just a simple way to curl the eyelashes. The two people are arguing about the use of the eyelash curler, and the first person says that it is a form of torture. The second person says that it is just a simple way to curl the eyelashes"}
{"answers": "#Person2# borrows some money from #Person1# because #Person2#'s backpack and wallet were lost and cannot take it back. #Person1# will drive #Person2# home.", "prediction_text": "A man lost his wallet and his backpack. He called the taxi driver to ask for the receipt. The driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver"}
{"answers": "#Person1# shares the Alex's bachelor party plan with #Person2# and promises nothing will be too much. #Person2# doubts that and will also organize a bachelorette party.", "prediction_text": "A man and his friends are planning a bachelor party for a friend who is getting married. The man's friends are planning to have a lot of fun at the party."}
{"answers": "Leah informs Nathaniel that Ms. Collins will not be able to call him back until next week, so he needs to book an appointment with someone else from the International Student Office. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "Ms. Collins is not available today. Ms. Fonda is available at 11:30."}
{"answers": "#Person1# and David's father want to have a quiet Christmas at a country hotel, but David thinks it is boring.", "prediction_text": "The two friends are talking about their plans for Christmas."}
{"answers": "#Person1# wants to try real Chinese cuisine. #Person2# recommends several ones. #Person1# decides to go to Quanjude restaurant.", "prediction_text": "#Person1#: Oh, I'm starving. It's my first time to China. And I'd like to try some real Chinese cuisine. What would you recommend?"}
{"answers": "Rebecca tells #Person1# about her working experiences. Rebecca first worked for the York Herald after her graduation, and then for the London Weekly.", "prediction_text": "#Person1#: I see. And you were a journalist for a while?"}
{"answers": "#Person1# is a new member who wants to use the machines. Johnson teaches #Person1# to get a card to keep track of the progress and record the suitable weight for each machine. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "#Person1#: Excuse me. Can you tell me how to get started using these machines?"}
{"answers": "Frank got a new job and is telling Judy not only the heavy schedule but also the benefits of this job.", "prediction_text": "A man and a woman are talking about their jobs. The man says that he has a heavy work schedule, and the woman says that she has excellent health insurance benefits. The man says that he wants to work for the Post Office because it offers excellent health insurance benefits. The woman says that she wants to work for the Post Office because her children can get free medical care. The man says that he knows why he wants to work for the Post Office. The woman says that she knows why she wants to work for the Post Office."}
{"answers": "#Person1# and #Person2# are appreciating lanterns. #Person2# explains the customs of the Lantern Festival and Chinese characters on the lanterns to #Person1#.", "prediction_text": "The Lantern Festival is a Chinese festival celebrated on the 15th day of the first lunar month. It is a time for people to enjoy the lighted lanterns and the gala performances."}
{"answers": "Bill made a cake for Sue's birthday, but Sue is on a diet to avoid allergy. Bill offers to buy her something healthy to eat, but Sue is enjoying her party. So they will go afterwards.", "prediction_text": "A man and a woman are having a party. The man is having a hard time getting the woman to eat anything. The woman is having a hard time getting the man to eat anything. The man is having a hard time getting the woman to eat anything. The woman is having a hard time getting the man to eat anything. The man is having a hard time getting the woman to eat anything. The woman is having a hard time getting the man to eat anything. The man is having a hard time getting the woman to eat anything. The woman is having a hard time getting the man to eat anything. The man is having a hard time getting the woman to eat anything. The woman is having a hard time getting the man to eat anything."}
{"answers": "#Person1# asks the exact time when the big fire broke out. #Person2# remembers that it was about 10 o'clock.", "prediction_text": "The fire broke out at 10 o'clock at night."}
{"answers": "#Person1# and #Person2# are scheduling to talk about the sales projections for next year and they decide to meet next Tuesday.", "prediction_text": "#Person1# and #Person2# are both salespeople. #Person1# is trying to figure out how to use the spreadsheet #Person2# set up for him. #Person2# is trying to explain the spreadsheet to #Person1#."}
{"answers": "Muriel Douglas and James send greetings and talk about their holiday before the meeting. Douglas stayed in L.A., while James went to Tahoe.", "prediction_text": "A man and a woman meet for the first time. They exchange names and then the man asks the woman if she is Muriel Douglas. The woman replies that she is, and the man asks if she is James. The woman replies that she is, and the man asks if she is happy. The woman replies that she is, and the man asks if she has a nice holiday. The woman replies that she did, and the man asks if she likes skiing. The woman replies that she does, and the man asks if she would like to go skiing. The woman replies that she would, and the man asks if she would like to go to Tahoe. The woman replies that she would, and the man asks if she would like to"}
{"answers": "#Person1# takes a taxi to the Friendship Hotel for something important.", "prediction_text": "A taxi driver and a passenger are chatting. The passenger asks the driver to take him to a certain place. The driver says that he can't do it because he has to pay the meter. The passenger says that he can pay the meter. The driver says that he can't do it because he has to pay the meter. The passenger says that he can pay the meter. The driver says that he can't do it because he has to pay the meter. The passenger says that he can pay the meter. The driver says that he can't do it because he has to pay the meter. The passenger says that he can pay the meter. The driver says that he can't do it because he has to pay the meter. The passenger says"}
{"answers": "#Person2# tells #Person1# #Person1# can park at a parking structure and show #Person1# the direction.", "prediction_text": "The speaker is asking the second person to find a parking spot for him. The second person is asking the first person if he needs a spot on campus. The first person is asking the second person if the parking structure is full. The second person is asking the first person if it is full now. The first person is asking the second person if it is full now. The second person is asking the first person if it is full now. The first person is asking the second person if it is full now. The second person is asking the first person if it is full now. The first person is asking the second person if it is full now. The second person is asking the first person if it is full now. The first person is asking the"}
{"answers": "#Person1# calls #Person2#'s restaurant to ask about the food and opening hours.", "prediction_text": "The restaurant is open until 10 pm, but the last order for dinner is at 9:30 pm."}
{"answers": "Mr. Jones asks Julie the reasons Julie got absent for class the whole week and finds out Julie got food poisoning.", "prediction_text": "The two students were talking about their illnesses. The first student said that he had been feeling ill for a week. The second student said that he had been feeling ill for a few days. The first student said that he had been feeling better. The second student said that he was feeling better. The first student said that he had been feeling better. The second student said that he was feeling better. The first student said that he had been feeling better. The second student said that he was feeling better. The first student said that he had been feeling better. The second student said that he was feeling better. The first student said that he had been feeling better. The second student said that he was feeling better. The first student said that he had"}
{"answers": "Ann recommends #Person1# to do the well-being in the air program on the way to New York. Ann introduces the program in detail and #Person1# seems interested in it.", "prediction_text": "The well-being program is a good choice for people who want to avoid jet lag. It is a light diet and a lot of water. The exercises are not very demanding."}
{"answers": "#Person1# doesn't know how to use an ATM. #Person2# teaches #Person1#.", "prediction_text": "This is a great way to teach a new skill to a person who is new to the country. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how"}
{"answers": "Harry tells #Person2# that the bag was overpriced in a street market. #Person2# suggests that Harry should bargain with the seller and consult the price tags in the supermarket first.", "prediction_text": "The Chinese people are very good at bargaining. They are also very good at haggling. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of"}
{"answers": "#Person2# helps #Person1# draw blood for testing white blood cell count.", "prediction_text": "The doctor sent the patient to have his blood drawn. The patient was asked to roll up his left sleeve. The doctor wanted to check the white blood cell count. The patient was asked what the test was for. The patient was asked if the blood test hurt. The patient was asked if the blood test was painful. The patient was asked if the blood test was uncomfortable. The patient was asked if the blood test was painful. The patient was asked if the blood test was painful. The patient was asked if the blood test was painful. The patient was asked if the blood test was painful. The patient was asked if the blood test was painful. The patient was asked if the blood test was painful. The patient was asked if the blood test was"}
{"answers": "#Person2# tells #Person1# a parking structure for parking and where it is.", "prediction_text": "The speaker is asking the second person to find a parking spot for him. The second person is asking the first person if he needs a spot on campus. The first person is asking the second person if the parking structure is full. The second person is asking the first person if it is full now. The first person is asking the second person if it is full now. The second person is asking the first person if it is full now. The first person is asking the second person if it is full now. The second person is asking the first person if it is full now. The first person is asking the second person if it is full now. The second person is asking the first person if it is full now. The first person is asking the"}
{"answers": "#Person2# left #Person2#'s backpack and wallet in a taxi and failed to contact the driver because #Person2# didn't have the number. #Person1# lends 50 dollars to #Person1# and will drive #Person2# home.", "prediction_text": "A man lost his wallet and his backpack. He called the taxi driver to ask for the receipt. The driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver"}
{"answers": "Tom suggests going swimming but Jane refuses beacuse of her paper due. After discussion, Jane finnally agrees to have dinner with him and then go to study together in the library.", "prediction_text": "Jane and Tom are having a conversation about their studies. Jane is worried about her upcoming test, and Tom is worried about his upcoming paper. They decide to go swimming at the Student Center, but Tom has a paper due on Friday, and he can't stay very long. They decide to go to the Grill for dinner, and Tom will stay until Jane is ready to go home."}
{"answers": "#Person2# introduces EDD's popular services to #Person1#. #Person2# tells #Person1# that EDD provided computers, job information, workshops, and job fairs.", "prediction_text": "The EDD has a number of services to help you find work."}
{"answers": "#Person1# is interviewing #Person2#. #Person1# thinks #Person2# answers #Person1#'s questions beautifully, and is the person they want.", "prediction_text": "#Person1#: I am a very confident person, and I am very good at what I do. I am very interested in learning everything I can about the job. #Person2#: I am very interested in learning everything I can about the job. I am very confident in my work. I am very good at what I do. I am very interested in learning everything I can about the job. I am very confident in my work. I am very good at what I do. I am very interested in learning everything I can about the job. I am very confident in my work. I am very good at what I do. I am very interested in learning everything I can about the job. I am very confident in my work. I"}
{"answers": "#Person1# shows #Person2# some sandalwoods, #Person2# likes it and buys some.", "prediction_text": "A man and a woman are talking about a sandalwood fan. The man says that he wants to buy a real one. The woman says that she has some. The man says that he wants to buy two small ones and a big one. The woman says that she has some. The man says that he wants to buy a real one. The woman says that she has some. The man says that he wants to buy a real one. The woman says that she has some. The man says that he wants to buy a real one. The woman says that she has some. The man says that he wants to buy a real one. The woman says that she has some. The man says that he wants to buy a real one."}
{"answers": "Brandon is signing up for a free website to improve writing skills but it requires personal information. #Person1# thinks the website cannot be trusted but Brandon doesn't believe so. Therefore, #Person1# shuts down Brandon's computer to stop him.", "prediction_text": "#Person1#: Hey, Brandon. What are you doing?"}
{"answers": "#Person1# praises #Person2#'s spacious courtyard and asks about why corn ears are hanging on the tree. #Person2# explains.", "prediction_text": "The two people are talking about the house. The first person says that the house is spacious and the second person says that the house is beautiful. The first person says that the corn ears are hanging on the tree branch. The second person says that the corn ears are hanging on the tree branch. The first person says that the corn ears are hanging on the tree branch. The second person says that the corn ears are hanging on the tree branch. The first person says that the corn ears are hanging on the tree branch. The second person says that the corn ears are hanging on the tree branch. The first person says that the corn ears are hanging on the tree branch. The second person says that the corn ears are hanging on the tree branch. The"}
{"answers": "Greg Sonders calls Mary to ask whether Mary is interested in sports and tells Mary to wait for final admission decision later.", "prediction_text": "The speaker is a college admissions officer. He is talking to a student who has just applied to Brown College. The student is interested in playing college sports. The admissions officer asks the student if he would be interested in playing college sports. The student says that he would be interested. The admissions officer asks the student if he plays any other sports. The student says that he also plays volleyball. The admissions officer asks the student if he has any other interests. The student says that he also plays basketball. The admissions officer asks the student if he has any other interests. The student says that he also plays volleyball. The admissions officer asks the student if he has any other interests. The student says that he also plays basketball. The admissions officer asks the student"}
{"answers": "#Person1# wants to have a farm and gets away from working 9 to 5 in an office. #Person2#'d visit #Person1#.", "prediction_text": "#Person1#: I'd love to have my own farm."}
{"answers": "Andrew's elder sister is surprised by Andrew's putting on so much weight and figures out Andrew has probably got scammed by a junk mail. She offers Andrew systematic suggestions on living healthy and losing weight.", "prediction_text": "Andrew is a big guy, and he's been on a diet for a while. He's been on the Wafu Diet, and he's lost a lot of weight. He's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on"}
{"answers": "The taxi driver takes #Person1# to the Friendship Hotel quickly.", "prediction_text": "A taxi driver and a passenger are chatting. The passenger asks the driver to take him to a certain place. The driver says that he can't do it because he has to pay the meter. The passenger says that he can pay the meter. The driver says that he can't do it because he has to pay the meter. The passenger says that he can pay the meter. The driver says that he can't do it because he has to pay the meter. The passenger says that he can pay the meter. The driver says that he can't do it because he has to pay the meter. The passenger says that he can pay the meter. The driver says that he can't do it because he has to pay the meter. The passenger says"}
{"answers": "#Person1# pays the meal and tips #Person2# but #Person2# doesn't take it.", "prediction_text": "A man and a woman are sitting at a table in a restaurant. The man says, \"How much should I pay?\" The woman says, \"Let me see. A roasted duck is $ 25. A plate of pork is $ 6. 50. A bowl of beef is $ 7. Two bottles of beer are $ 4. That's a total of $ 42. 50.\" The man says, \"Here is $ 50. Keep the change, please.\" The woman says, \"Oh, sorry sir. We take no tips here. Thank you all the same.\""}
{"answers": "#Person2# plans to have a trip in Hebei but #Person1# says there are sandstorms in there.", "prediction_text": "The report said that the number of people with respiratory tract infections tended to rise after sandstorms. The sand gets into people's noses and throats and creates irritation."}
{"answers": "#Person1# and #Person2# are talking about the weather in Beijing. #Person2# tells #Person1# it's cold in the evening around this time.", "prediction_text": "The weather in Beijing is very different in the summer and the winter. The summer is hot and dry, while the winter is cold and wet. The weather in spring is warm and short. The weather in the evening is cool."}
{"answers": "#Person1# feels bored at home and asks Jim go for a beer. Jim refuses and suggests going to the gym and meeting friends.", "prediction_text": "The two friends had a good time at the gym. They played pingpong and danced. They also met some of their friends."}
{"answers": "#Person1# evaluates #Person2#'s ability and personality by asking #Person2# 's strengths, weaknesses and career planning. #Person1# finds #Person2# is a suitable candidate.", "prediction_text": "#Person1#: I am a very confident person, and I am very good at what I do. I am very interested in learning everything I can about the job. #Person2#: I am very interested in learning everything I can about the job. I am very confident in my work. I am very good at what I do. I am very interested in learning everything I can about the job. I am very confident in my work. I am very good at what I do. I am very interested in learning everything I can about the job. I am very confident in my work. I am very good at what I do. I am very interested in learning everything I can about the job. I am very confident in my work. I"}
{"answers": "#Person1# wants to take a date to the best restaurant, #Person2# suggests the Gramercy Tavern and is calling to reserve a table for him.", "prediction_text": "A man and a woman are on a date. The man is a hotel guest, and the woman is a guest at the hotel restaurant. The man is trying to decide what to order for dinner. The woman is trying to decide what to order for dinner. The man is trying to decide what to order for dinner. The woman is trying to decide what to order for dinner. The man is trying to decide what to order for dinner. The woman is trying to decide what to order for dinner. The man is trying to decide what to order for dinner. The woman is trying to decide what to order for dinner. The man is trying to decide what to order for dinner. The woman is trying to decide what to order for dinner. The man"}
{"answers": "Harry and his wife may go abroad during the holidays. However, they might not go anywhere because his wife always worries too much.", "prediction_text": "The two friends are discussing their plans for the holidays. The first person says that he is going to Egypt, but his wife wants to go to Egypt, too. The second person says that he is going to Egypt, but he is not sure. The first person says that he is going to Egypt, but he is not sure. The second person says that he is going to Egypt, but he is not sure. The first person says that he is going to Egypt, but he is not sure. The second person says that he is going to Egypt, but he is not sure. The first person says that he is going to Egypt, but he is not sure. The second person says that he is going to Egypt, but he is not"}
{"answers": "#Person1# delivers a parcel for Mr. Lee. He happens to have something to express.", "prediction_text": "A man and a woman are talking to each other. The man is looking for his parcel. The woman is looking for her signature."}
{"answers": "#Person1# and #Person2# are looking at jobs. #Person2# is interested in the media industry.", "prediction_text": "#Person1# and #Person2# are two people who are considering different career paths. #Person1# is interested in working in the media, while #Person2# is interested in working with computers. #Person1# is interested in working in the media, but he thinks it would be stressful. #Person2# is interested in working with computers, but he thinks it would be exciting. #Person1# is interested in working in the media, but he thinks it would be fun. #Person2# is interested in working with computers, but he thinks it would be exciting. #Person1# is interested in working in the media, but he thinks it would be fun. #Person2# is interested in working with"}
{"answers": "#Person1# and #Person2# are talking about the heavy storm last night, and #Person1#'s positive. #Person2# thinks the weather is terrible. #Person1# thinks they should not complain.", "prediction_text": "The weather is a topic of conversation in this dialogue. The speaker is a person who has had a bad storm. The other person is a person who has had a good storm. The speaker is a person who has had a bad storm. The other person is a person who has had a good storm. The speaker is a person who has had a bad storm. The other person is a person who has had a good storm. The speaker is a person who has had a bad storm. The other person is a person who has had a good storm. The speaker is a person who has had a bad storm. The other person is a person who has had a good storm. The speaker is a person who has had a bad storm. The other"}
{"answers": "#Person2# is checking out and asks #Person1# for the bill. #Person1# gives #Person2# a wrong bill at first then corrects it.", "prediction_text": "The hotel staff is very helpful. They are very polite and friendly. They are very professional. They are very efficient. They are very good at their job. They are very good at their work. They are very good at their job. They are very good at their work. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their"}
{"answers": "#Person1# will pays for the overweight luggage.", "prediction_text": "The customer is charged for the excess luggage. The customer is given a fragile label. The customer is asked to attach the label to the luggage and put the luggage in the designated area."}
{"answers": "Mr. Faber books a double room for 3 nights from July 20th at York Hotel.", "prediction_text": "A man calls a hotel to book a room for three nights. The hotel asks for his name, and he gives it. The hotel asks for his arrival date, and he gives it. The hotel asks for his room type, and he gives it. The hotel asks for the number of people in his party, and he gives it. The hotel asks for the number of nights he will be staying, and he gives it. The hotel asks for the amount he will be paying, and he gives it. The hotel asks for his name, and he gives it. The hotel asks for his arrival date, and he gives it. The hotel asks for his room type, and he gives it. The hotel asks for the number of people in his"}
{"answers": "#Person1# stops #Person2# from taking photos in the museum.", "prediction_text": "The museum is a place where people can learn about the history of the country. The museum is not a place where people can take pictures. The museum is a place where people can take pictures."}
{"answers": "#Person2# calls Pizza House to deliver a thin crust seafood pizza to holyrood 9A.", "prediction_text": "A man and a woman are at a pizza place. The man orders a large pizza with pepperoni, olives, and extra cheese. The woman orders a large pizza with seafood. The man is very happy with his pizza. The woman is very happy with her pizza. The man is very happy with his pizza. The woman is very happy with her pizza. The man is very happy with his pizza. The woman is very happy with her pizza. The man is very happy with his pizza. The woman is very happy with her pizza. The man is very happy with his pizza. The woman is very happy with her pizza. The man is very happy with his pizza. The woman is very happy with her pizza. The man is very happy"}
{"answers": "#Person1# wants some online information about the Normandy Landing, #Person2# teaches #Person1# how to google it.", "prediction_text": "The dialogue above is a typical example of a dialogue between a person who wants to find information and a person who can help him or her do an internet search. The person who wants to find information is called the person who is asking for help. The person who can help is called the person who is giving the help. The person who is asking for help is called the person who is asking. The person who is giving the help is called the person who is giving. The person who is asking for help is called the person who is asking. The person who is giving the help is called the person who is giving. The person who is asking for help is called the person who is asking. The person who is giving the help is called the person"}
{"answers": "#Person2# let in someone and #Person1# thinks it was #Person2#'s fault.", "prediction_text": "A man is in a room with a woman. He is not sure if she is a friend or a foe. He is not sure if she is a friend or a foe. He is not sure if she is a friend or a foe. He is not sure if she is a friend or a foe. He is not sure if she is a friend or a foe. He is not sure if she is a friend or a foe. He is not sure if she is a friend or a foe. He is not sure if she is a friend or a foe. He is not sure if she is a friend or a foe. He is not sure if she is a friend or a foe. He is not sure if she is a friend or"}
{"answers": "#Person1# and #Person2# talk about Mirella's dressing style in the office which is different from others. The management decided to give Mirella formal clothes directly to warn her.", "prediction_text": "The two people in the office are discussing the new dress code that was introduced by the management. The first person says that it is strange that the rest of the people in the office are allowed to wear more casual clothes, while Mirella is not. The second person says that it is not as bad as it seems, because Mirella is allowed to wear more casual clothes. The first person says that he heard that Mirella found a new dress on her desk this morning. The second person says that the dress is a sign that the management is trying to tell Mirella that her new style of dressing down is not acceptable."}
{"answers": "#Person2# wants to borrow romance videos for #Person2#'s daughter and asks the detailed cost and tenancy term. #Person1# reminds of the overdue fee.", "prediction_text": "A man and a woman are at a video store. The man asks the woman if she has a membership card. The woman says no. The man asks if she needs one to rent videos. The woman says no. The man asks how much the movies are. The woman says $3.50 for new releases and $2 for all other movies. The man says okay. The woman says okay. The man says they have a five dollar deal where you can rent any five movies for $5.00. The woman says okay. The man says they have to be returned on Thursday by ten o'clock PM. The woman says okay. The man says there is an overdue fee equal to the rental fee of the video. The woman says okay"}
{"answers": "Tony got caught cheating and feels ashamed. Steven tells him to learn from it and study hard.", "prediction_text": "A student is caught cheating and feels ashamed. He feels that his father will kill him if he fails. He feels that he should study hard. He feels that he should learn from his mistakes."}
{"answers": "#Person1# is surprised at the Olympic Stadium'volume, capacity and interior setting to #Person1#.", "prediction_text": "The Olympic stadium is so big that it is hard to imagine."}
{"answers": "#Person1# congratulates Mr. Stuart on winning the city marathon.", "prediction_text": "The dialogue above is a conversation between two people. The first person (Person 1) congratulates the second person (Person 2) on winning the city marathon. The second person (Person 2) is very happy and excited about the win. The first person (Person 1) is very proud of the second person (Person 2) and says that they are all very proud of him. The second person (Person 2) is very happy and excited about the win. The first person (Person 1) says that they watched the race with their children and that they all screamed with excitement. The second person (Person 2) says that it was a big game and that they all watched the race with their children."}
{"answers": "#Person1#, a reporter, takes #Person2# to a forum. #Person2# is bored and tries to find a pastime.", "prediction_text": "The Daily News is a newspaper that is published in New York City. It is a paper that is known for its coverage of the city's social and political life. The paper is also known for its coverage of the city's sports teams. The paper is also known for its coverage of the city's crime and corruption. The paper is also known for its coverage of the city's politics. The paper is also known for its coverage of the city's economy. The paper is also known for its coverage of the city's environment. The paper is also known for its coverage of the city's education. The paper is also known for its coverage of the city's health and safety. The paper is also known for its coverage of the city's culture."}
{"answers": "Harry tells #Person1# he was almost hit by a car and he will be more careful next time.", "prediction_text": "A man and a woman are talking about a terrible accident that happened to them. The man is pale and the woman is pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and"}
{"answers": "#Person1# helps #Person2# order one hard-boiled egg with dark toast and orange juice to follow.", "prediction_text": "A man and a woman are having breakfast together. The man asks the woman how she would like her eggs. The woman replies that she would like them hard-boiled. The man asks the woman if she would like her toast light or dark. The woman replies that she would like her toast dark. The man asks the woman if she would like her eggs and toast later. The woman replies that she would like them later. The man asks the woman if she would like anything else. The woman replies that she would like a boiled egg with toast and orange juice. The man asks the woman if she would like her eggs and toast later. The woman replies that she would like them later. The man asks the woman if she would like anything else."}
{"answers": "Jack invites Charlie to play video games after he picks up his dad. Charlie agrees.", "prediction_text": "#Person1# and #Person2# are two friends who are going to play a new video game together. #Person1# is excited about the game and wants to show it to #Person2#."}
{"answers": "#Person1# is tired and will take a nap and #Person2# gets the dinner ready.", "prediction_text": "A man and a woman are talking about their plans for the day. The man says he is going to take a nap. The woman says she is going to take a nap. The man says he is going to take a nap. The woman says she is going to take a nap. The man says he is going to take a nap. The woman says she is going to take a nap. The man says he is going to take a nap. The woman says she is going to take a nap. The man says he is going to take a nap. The woman says she is going to take a nap. The man says he is going to take a nap. The woman says she is going to take a nap. The man says he"}
{"answers": "#Person2# is looking at the map. #Person1# finds geography interesting and wants to know more about it.", "prediction_text": "The two people are talking about the geography of the world. The first person is interested in the geography of the world and is looking at a map. The second person is interested in the geography of the world and is looking at the map. The first person is surprised that there are no countries marked on the map. The second person is surprised that most of the world is covered with water. The first person is surprised that the oceans and landscapes all influence our climate. The second person is surprised that the oceans and landscapes all influence our climate. The first person is surprised that the oceans and landscapes all influence our climate. The second person is surprised that the oceans and landscapes all influence our climate. The first person is surprised that the oceans and landscapes all influence"}
{"answers": "#Person2# shares an umbrella with #Person1# when it's rainy.", "prediction_text": "A man and a woman are walking together. The man says, \"Excuse me, Sir, do you know what time it is?\" The woman says, \"Oh, it's almost eleven twenty.\" The man says, \"Thank you.\" The woman says, \"You're welcome.\" The man says, \"The rain is quite heavy, isn't it?\" The woman says, \"Yeah. I was in a hurry and I forgot my umbrella today.\" The man says, \"I was in a hurry and I forgot my umbrella today.\" The woman says, \"I have one. We could share it. Which way are you going?\" The man says, \"Oh, that's very kind of you. I am going to the Garden Hotel"}
{"answers": "Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week.", "prediction_text": "The two men are talking about their jobs. The first man is a bank employee and the second is a student. The first man is twenty-seven years old and the second is twenty-seven years old. The first man has a B. A. in Economics and the second has a B. A. in Economics. The first man has worked in a bank for the last five years and the second has worked in a bank for the last five years. The first man's salary is $ 500 a week and the second man's salary is $ 500 a week. The first man is twenty-seven years old and the second man is twenty-seven years old. The first man is a bank employee and the second man is a student. The first man"}
{"answers": "#Person2# wants some traditional Chinese arts and crafts. #Person1# shows her sandalwood fan and she buys some.", "prediction_text": "A man and a woman are talking about a sandalwood fan. The man says that he wants to buy a real one. The woman says that she has some. The man says that he wants to buy two small ones and a big one. The woman says that she has some. The man says that he wants to buy a real one. The woman says that she has some. The man says that he wants to buy a real one. The woman says that she has some. The man says that he wants to buy a real one. The woman says that she has some. The man says that he wants to buy a real one. The woman says that she has some. The man says that he wants to buy a real one."}
{"answers": "#Person1# introduces Henry to Pete. The three persons talk about their previous experiences and life and decide to go to a bar after dinner.", "prediction_text": "The three friends met for dinner and then went to a Karaoke bar. They had a great time singing and dancing."}
{"answers": "#Person1# is surprised to see Andrew put on so much weight but Andrew tells #Person1# that to lose weight, he signs up for a Wafu Diet online for $490. #Person1# thinks he's getting scammed and suggests that he should take more exercise, eat smaller portions, eat a well-balanced breakfast, cut off fast food and sugar and eat fresh fruits and vegetables", "prediction_text": "Andrew is a big guy, and he's been on a diet for a while. He's been on the Wafu Diet, and he's lost a lot of weight. He's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on the diet for a while, and he's been on"}
{"answers": "#Person2# will give #Person1# a ride after work.", "prediction_text": "A man and a woman meet at a bus stop. They talk about their work and their lives. They agree to meet at five."}
{"answers": "#Person1# and #Person2# will go to Burger Queen for lunch and eat cheese burgers and French fries. They will have to wait for good food.", "prediction_text": "The two friends are going to Burger Queen for lunch. They are both hungry. One of them is a cheese burger lover, while the other one is a milkshake lover. They are both very excited to try the cheese burger and the milkshake. They are both very hungry, so they are going to wait in line to order their food. They are both very excited to try the cheese burger and the milkshake. They are both very hungry, so they are going to wait in line to order their food."}
{"answers": "#Person1# is asking #Person2# about the places that #Person2# has stayed or lived.", "prediction_text": "#Person1#: I was born in Chicago as a matter of fact."}
{"answers": "#Person2# helps Mr. Murray get a library card and reminds him of library rules.", "prediction_text": "The library card application process is a two-step process. The first step is to fill out the application form. The second step is to fill out the application form and pay the fee. The library card application process is a two-step process. The first step is to fill out the application form. The second step is to fill out the application form and pay the fee. The library card application process is a two-step process. The first step is to fill out the application form. The second step is to fill out the application form and pay the fee. The library card application process is a two-step process. The first step is to fill out the application form. The second step is to fill out the application form and pay the fee. The library"}
{"answers": "#Person2# finds #Person2# being mischarged. #Person1# corrects the bill and #Person2# pays for it.", "prediction_text": "The hotel staff is very helpful. They are very polite and friendly. They are very professional. They are very efficient. They are very good at their job. They are very good at their work. They are very good at their job. They are very good at their work. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their"}
{"answers": "#Person2# has worked overtime with minimum wage and little bonus. Although #Person2# are not very confident, #Person1# hopes #Person2# could quit that job and find another one.", "prediction_text": "#Person1#: is working overtime again. #Person2#: I know, but I don't have a choice, my boss has complete control over our work schedules. #Person1#: Well, I hope they're paying you the big bucks for all this work... #Person2#: No, not exactly, I'm working for minimum wage. And they don't give extra pay for overtime. Overtime is our own time. #Person1#: What?! Isn't there a law against that, I think there is some legislation that requires employers to give their employees a fair rate of wages. You should know your rights! If you are putting in so many hours, they should give you some kind of compensation. #Person2"}
{"answers": "#Person1# congratulates Tom for achieving the Nobel Prize.", "prediction_text": "A man and a woman are having a conversation. The man says, \"I've got good news for you.\" The woman says, \"What is it?\" The man says, \"Haven't you heard that your novel has won The Nobel Prize?\" The woman says, \"Really? I can't believe it. It's like a dream come true. I never expected that I would win The Nobel Prize!\" The man says, \"You did a good job. I'm extremely proud of you.\" The woman says, \"Thanks for the compliment.\" The man says, \"You certainly deserve it. Let's celebrate!\""}
{"answers": "Sally reads the letter from Tom to #Person1#. The letter invites them to visit Tom.", "prediction_text": "Tom is a man who is very fond of his wife. He is also a man who is very fond of his daughter. He is a man who is very fond of his son. He is a man who is very fond of his mother. He is a man who is very fond of his father. He is a man who is very fond of his brother. He is a man who is very fond of his sister. He is a man who is very fond of his wife's mother. He is a man who is very fond of his wife's mother's mother. He is a man who is very fond of his wife's mother's mother's mother. He is a man who is very fond of his wife's mother's mother's mother"}
{"answers": "#Person2# needs to change clothes to enter the conference and #Person1# offers #Person2# the convenience by putting the conference off.", "prediction_text": "The dialogue above is a typical example of a situation where a person is asked to wear a jacket and a tie in a restaurant. The person is not allowed to borrow a jacket or a tie from others. The person is asked to return the jacket and tie to the hotel. The person is asked to put off the time of the conference. The person is asked to offer the convenience of the conference to the person who is the chairman. The person is asked to be back at a certain time. The person is asked to be back sooner. The person is asked to be back at a certain time. The person is asked to be back sooner. The person is asked to be back at a certain time. The person is asked to be back sooner."}
{"answers": "Stephanie has a headache but doesn't see a doctor because of the report due and George is willing to help with the report and suggests she ask the board for more time", "prediction_text": "George is a young man who works in a bank. He is a good friend of Stephanie, a young woman who works in the same bank. Stephanie is a bit of a tomboy and is not very good at making friends. George is a bit of a loner and is not very good at making friends. Stephanie and George are both in their early twenties. They are both single and are looking for a partner. They are both looking for a partner who is not a friend. They are both looking for a partner who is not a friend. They are both looking for a partner who is not a friend. They are both looking for a partner who is not a friend. They are both looking for a partner who is not a friend. They"}
{"answers": "Jim suggests #Person1# go to the gym to meet friends instead of drinking beers after dinner.", "prediction_text": "The two friends had a good time at the gym. They played pingpong and danced. They also met some of their friends."}
{"answers": "#Person2# is from America and is picking up presents for families in a duty-free shop. #Person1# recommends some for #Person2#.", "prediction_text": "A man and a woman are shopping in a duty-free shop. The man is buying some presents for his children and wife. The woman is buying a perfume. The man asks the shopkeeper if he can buy some sneakers for his children. The shopkeeper says that the total amount of the purchases cannot be over $ 300. The man says that he has forgotten about the wife's perfume. The shopkeeper says that the perfume is very expensive in America. The man says that he can buy it in the duty-free shop. The shopkeeper says that the perfume is very expensive in America. The man says that he can buy it in the duty-free shop. The shopkeeper says that the perfume is very expensive in America. The man says that"}
{"answers": "#Person1# is interviewing Vet and asking her about the ways she deals with stress as a young mother, feelings of having a baby, plans for the future, her favorite quote, and advice for teens.", "prediction_text": "The two teens talked about their lives and their future. They talked about their goals and dreams, and how they were going to achieve them. They talked about their plans for the future, and how they were going to make it happen. They talked about their favorite quotes, and how they were going to live their lives to the fullest. They talked about their advice for teens, and how they were going to live their lives to the fullest. They talked about their goals and dreams, and how they were going to achieve them. They talked about their plans for the future, and how they were going to make it happen. They talked about their favorite quotes, and how they were going to live their lives to the fullest. They talked about their advice for"}
{"answers": "#Person1# calls to inform Ballam that Ballam was going to be hired, and agrees to raise the starting salary after discussion.", "prediction_text": "The salary of a computer engineer is 3, 000 yuan a month. The starting salary is 4, 000 yuan a month. The lowest salary is 4, 000 yuan a month. The salary is raised to 5, 000 yuan a month after six months. The salary is 5, 000 yuan a month. The salary is 5, 000 yuan a month. The salary is 5, 000 yuan a month. The salary is 5, 000 yuan a month. The salary is 5, 000 yuan a month. The salary is 5, 000 yuan a month. The salary is 5, 000 yuan a month. The salary is 5,"}
{"answers": "#Person1#'s new suit cost $150. #Person2# doesn't think it's a good bargain.", "prediction_text": "#Person1#: What do you think of my new suit?"}
{"answers": "Tom complains about his unhappy lunch experience in the fast-food restaurant while Catherine enjoyed her home-made meal. Catherin doesn't like fast food and says most Americans avoid it too.", "prediction_text": "The two people had a conversation about fast food. The first person said that he didn't like fast food because it was unhealthy. The second person said that he didn't like fast food because it was popular in America."}
{"answers": "#Person2# had been in the charity organization for 2 months and the social activities influenced #Person2#'s way of thinking.", "prediction_text": "#Person1#: I think it is very important to have a good social life. #Person2#: I think it is very important to have a good social life."}
{"answers": "Nick has never used washing machines before, and he turns to a girl for help. Nick appreciates Alice's help and tells her his mother used to do washing for him. Alice will help him be more independent.", "prediction_text": "A young man, Nick, is trying to learn how to use the washing machines in a new apartment complex. He is a little confused about how to use them. He asks his mother, Alice, for help. She tells him that he can buy soap from a vending machine. He buys some soap and tries to use the machines. He is not sure how to use them. He asks his mother for help. She tells him that he can't use so much soap. He asks her how old she is. She tells him that she is nineteen. He asks her how she can live nineteen years without ever washing clothes. She tells him that she was a child when her mother did all the washing. He asks her how she is going to survive"}
{"answers": "#Person1# and #Person2# are drinking while discussing what to do if they won the Pools. #Person1# wants to go around the world while #Person2# wants to buy a big house.", "prediction_text": "The two men are having a drink together. One of them has won the Pools, and the other has not. The first man says that he would go round the world if he won the Pools, and the second man says that he would not. The first man says that he would buy a big house with a garden for his wife and children, and the second man says that he would not. The first man says that he would not work any more if he had a lot of money, and the second man says that he would not. The first man says that he would go round the world, and the second man says that he would not. The first man says that he would not be happy if he got a rise, and"}
{"answers": "#Person1# asks #Person2# how well #Person2# knows about the company, #Person2#'s ideal department, and current salary. #Person2# also asks #Person1# about the company's benefits and insurance.", "prediction_text": "The interviewer asked the applicant about his current job, his salary, fringe benefits, and insurance. The applicant said he would like to work in sales department. The interviewer asked the applicant how much he would be paid if he worked in sales department. The applicant said he would be paid 2, 000 yuan per month plus some allowances and bonus. The interviewer asked the applicant how much he would be paid if he worked in sales department. The applicant said he would be paid 2, 500 yuan per month. The interviewer asked the applicant how much he would be paid if he worked in sales department. The applicant said he would be paid 2, 000 yuan per month plus some allowances and bonus. The interviewer asked the applicant how much"}
{"answers": "#Person1# will go to London and invites #Person2# to come along. But they have disagreements on how they get there.", "prediction_text": "The two people are arguing about which mode of transport is more convenient. The first person prefers the train, but the second person prefers the car. The first person says that the train is more expensive, but the second person says that the train is quicker. The first person says that the bus is more expensive, but the second person says that the bus is quicker. The first person says that the car is more convenient, but the second person says that the car is more convenient. The first person says that the train is more convenient, but the second person says that the train is more convenient. The first person says that the bus is more convenient, but the second person says that the bus is more convenient. The first person says that the car is"}
{"answers": "#Person2# is holding a baby shower for #Person1# and there are many presents for #Person1#. #Person1#'s water broke.", "prediction_text": "A baby shower is a great way to celebrate a new baby. It's a great way to get to know the new parents and to get some gifts for the baby."}
{"answers": "#Person2# shows #Person1# the way to the seventy seventh.", "prediction_text": "A man and a woman are walking in a city. The man is tired and wants to take a bus. The woman is tired and wants to take a bus. They are both strangers in the city. The man asks the woman for directions. The woman tells him the bus number. The man thanks her and walks away. The woman is happy that she helped the man."}
{"answers": "#Person1# shows Jack a picture of #Person1#'s new puppy.", "prediction_text": "Jack and his new puppy are having a conversation. Jack is showing off his new puppy. He says that his puppy is a lot of fun, but he is also a lot of trouble. Jack is trying to show off his puppy to his friend, but he is not sure if his friend will like it."}
{"answers": "#Person1# inquires Tom about his successful business experience and his own green life and how to learn from mistakes.", "prediction_text": "Tom and his company are doing a great job of being green. They are using recycled materials, and they are making their own products. They are also working with other companies to make their products."}
{"answers": "#Person1# and #Person2# are talking about the heavy rain last night and the weather in Beijing.", "prediction_text": "The weather in Beijing is very different in the summer and the winter. The summer is hot and dry, while the winter is cold and wet. The weather in spring is warm and short. The weather in the evening is cool."}
{"answers": "#Person2# explains how the city grew into a large place by selling coal and absorbing villages, and introduced old buildings to #Person1#.", "prediction_text": "The city of Manchester is a historical place. It has many buildings that are more than 200 years old. The city has a castle and a pub."}
{"answers": "#Person2# asks #Person1# to bring #Person1#'s husband to talk about #Person1#'s test results.", "prediction_text": "#Person1# and #Person2# are having a conversation. #Person1# is worried about something. #Person2# is trying to help. #Person1# is trying to get a clear picture of what is going on. #Person2# is trying to help #Person1# understand. #Person1# is trying to get a clear picture of what is going on. #Person2# is trying to help #Person1# understand. #Person1# is trying to get a clear picture of what is going on. #Person2# is trying to help #Person1# understand. #Person1# is trying to get a clear picture of what is going on. #Person2# is trying to help"}
{"answers": "#Person1# and #Person2# talks about Mirella dressed casually after she came back from Silicon Valley. Managements put white shirts on her desk to warn her.", "prediction_text": "The two people in the office are discussing the new dress code that was introduced by the management. The first person says that it is strange that the rest of the people in the office are allowed to wear more casual clothes, while Mirella is not. The second person says that it is not as bad as it seems, because Mirella is allowed to wear more casual clothes. The first person says that he heard that Mirella found a new dress on her desk this morning. The second person says that the dress is a sign that the management is trying to tell Mirella that her new style of dressing down is not acceptable."}
{"answers": "#Person1# encourages Lucy to sing but Lucy just wants to listen.", "prediction_text": "#Person1#: OK, Lucy. It's your turn. Are you ready?"}
{"answers": "Mr. Lee offers to give Mrs. Word a lift home on a terrible night.", "prediction_text": "A man and a woman are walking home from a party. The man is carrying a large umbrella. The woman is carrying a small umbrella. The man says, \"Could you hold my umbrella when I get my keys out, please?\" The woman says, \"Of course. It's a terrible night, isn't it?\" The man says, \"Dreadful. There, the door is open now.\" The woman says, \"Thank you.\""}
{"answers": "#Person2# is surprised to hear from #Person1# that Mary got married.", "prediction_text": "Mary got married. She married Jerrod. She's going to live in Paris."}
{"answers": "#Person1# will take the leather shoes.", "prediction_text": "#Person1# wants a pair of enameled leather shoes in size 30. #Person2# offers a pair of shoes that are a little tight. #Person1# says that he will take the shoes. #Person2# says that the shoes will stretch a bit with use."}
{"answers": "#Person2# tells Tenants Advocacy Resource Center about the problem with #Person2#'s house owner. #Person2# tells #Person1# it always takes #Person2#'s house owner a long time to repair and they can't agree on the repair costs.", "prediction_text": "The tenant is having a problem with the house owner. The tenant is having a problem with the house owner. The tenant is having a problem with the house owner. The tenant is having a problem with the house owner. The tenant is having a problem with the house owner. The tenant is having a problem with the house owner. The tenant is having a problem with the house owner. The tenant is having a problem with the house owner. The tenant is having a problem with the house owner. The tenant is having a problem with the house owner. The tenant is having a problem with the house owner. The tenant is having a problem with the house owner. The tenant is having a problem with the house owner. The tenant is having a problem with"}
{"answers": "#Person1# is visiting #Person2#'s spacious house. #Person1# finds it interesting that corn ears are hanging on the tree branch.", "prediction_text": "The two people are talking about the house. The first person says that the house is spacious and the second person says that the house is beautiful. The first person says that the corn ears are hanging on the tree branch. The second person says that the corn ears are hanging on the tree branch. The first person says that the corn ears are hanging on the tree branch. The second person says that the corn ears are hanging on the tree branch. The first person says that the corn ears are hanging on the tree branch. The second person says that the corn ears are hanging on the tree branch. The first person says that the corn ears are hanging on the tree branch. The second person says that the corn ears are hanging on the tree branch. The"}
{"answers": "#Person1# buys some nice clothes by credit card with #Person2#'s assistance.", "prediction_text": "The customer is not always right."}
{"answers": "#Person2# tells #Person1# the bus route to get to Sons.", "prediction_text": "#Person1#: Is there a bus that'll go all the way to Sons from PHS?"}
{"answers": "#Person1# and #Person2# go to Burger Queen for lunch, but there's a long line and they have to wait. #Person2# is going to order French fries that #Person1# recommends.", "prediction_text": "The two friends are going to Burger Queen for lunch. They are both hungry. One of them is a cheese burger lover, while the other one is a milkshake lover. They are both very excited to try the cheese burger and the milkshake. They are both very hungry, so they are going to wait in line to order their food. They are both very excited to try the cheese burger and the milkshake. They are both very hungry, so they are going to wait in line to order their food."}
{"answers": "#Person2# suggests that #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "A mother and daughter are discussing how to put up a curtain. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold"}
{"answers": "#Person1# has a bad lifestyle. #Person2# kindly advises #Person1# to be positive.", "prediction_text": "The two people are talking about their problems. The first person says that he can't sleep well every night, and the second person says that he often feels tired. The first person says that he drinks a lot of wine, and the second person says that he should exercise every morning. The first person says that he often feels the shadow of death in front of him, and the second person says that he should take his advice. The first person says that he is young and will make great contribution to the world, and the second person says that he should take his advice."}
{"answers": "#Person2# tells #Person1# that #Person2# is bored and explains the reasons, so #Person1# suggests #Person2# read some old newspapers and be quiet.", "prediction_text": "The Daily News is a newspaper that is published in New York City. It is a paper that is known for its coverage of the city's social and political life. The paper is also known for its coverage of the city's sports teams. The paper is also known for its coverage of the city's crime and corruption. The paper is also known for its coverage of the city's politics. The paper is also known for its coverage of the city's economy. The paper is also known for its coverage of the city's environment. The paper is also known for its coverage of the city's education. The paper is also known for its coverage of the city's health and safety. The paper is also known for its coverage of the city's culture."}
{"answers": "#Person1# borrows 200 yuan from Fred to pay the mortgage of a computer and will pay back when having money.", "prediction_text": "The two people are in a relationship. The first person is a student. The second person is a teacher. The first person is in a bad financial situation. The second person is in a good financial situation. The first person is in a relationship with the second person. The first person is in a relationship with the second person. The first person is in a relationship with the second person. The first person is in a relationship with the second person. The first person is in a relationship with the second person. The first person is in a relationship with the second person. The first person is in a relationship with the second person. The first person is in a relationship with the second person. The first person is in a relationship with the second person."}
{"answers": "#Person2# tells #Person1# that the company not only raised a lot of money sponsoring a marathon for the American Cancer Society but also established a good image out of this fundraiser event.", "prediction_text": "The fundraiser was a success for both the American Cancer Society and the company. The company was able to raise a lot of money for a good cause, and the American Cancer Society was able to get a lot of exposure for their cause."}
{"answers": "#Person2# suggests Harry consult the price tags in the supermarket first and then negotiate for a fair deal after Harry bought a bag overpriced and got a terrible feeling.", "prediction_text": "The Chinese people are very good at bargaining. They are also very good at haggling. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of"}
{"answers": "#Person1# teaches #Person2# how to use the training machine and suggests starting working small reps if #Person2# wants a good physique.", "prediction_text": "The person who is new to the gym is confused and lost. The trainer is trying to help the person by giving them some pointers and advice. The trainer is also trying to help the person by giving them some tips on how to use the machine. The trainer is also trying to help the person by giving them some advice on how to work out. The trainer is also trying to help the person by giving them some tips on how to stretch before working out. The trainer is also trying to help the person by giving them some tips on how to work out with less weight. The trainer is also trying to help the person by giving them some tips on how to work out with less weight. The trainer is also trying to help the person by giving them"}
{"answers": "Simon tells #Person1# that he is on a scheme called phased retirement during which he had a six-month break from work, and after that he could apply for projects of the company he used to work for but manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "Simon is a retired employee who has been working on a phased retirement scheme. He has been able to work on a project for the company he used to work for, and he has been able to decide when he wants to work. He has been able to use his contacts from work to get things done. He has been able to use his skills to get work done. He has been able to use his time to do what he wants to do. He has been able to use his time to do what he wants to do. He has been able to use his time to do what he wants to do. He has been able to use his time to do what he wants to do. He has been able to use his time to do what he"}
{"answers": "#Person2# agrees to help #Person1#'s friend, a new emigrant who had never been to school, to learn English.", "prediction_text": "A man volunteers his friend to teach him how to read. The man is a new immigrant from South America and has never been to school. The man's friend is 78 years old and has never been to school either. The man's friend is a great teacher and the man is very grateful. The man's friend is a great cook and the man is very grateful. The man's friend is adorable and the man is very grateful. The man's friend is a great cook and the man is very grateful. The man's friend is a great teacher and the man is very grateful. The man's friend is a great cook and the man is very grateful. The man's friend is a great teacher and the man is very grateful. The man"}
{"answers": "#Person2# introduces the Olympic Stadium's finishing time, capacity and interior setting to #Person1#.", "prediction_text": "The Olympic stadium is so big that it is hard to imagine."}
{"answers": "#Person1# and Ben discuss what evening classes they shall get and come into an agreement to have an Indian cooking course for their interest.", "prediction_text": "The two people in the conversation are talking about evening classes. The first person is interested in flower arranging, the second in photography, the third in Indian cooking, and the fourth in web design. The first person is interested in the evening classes because he likes to watch baseball, the second is interested in the evening classes because he likes to watch Indian movies, the third is interested in the evening classes because he likes to cook Indian food, and the fourth is interested in the evening classes because he likes to design websites. The first person is interested in the evening classes because he likes to watch baseball, the second is interested in the evening classes because he likes to watch Indian movies, the third is interested in the evening classes because he likes to cook Indian food"}
{"answers": "#Person2# introduces the Ford Focus to #Person1# who wants to buy a new car. #Person2# describes every detail of the car and #Person1# decides to have a test drive.", "prediction_text": "The two people had a great conversation about the Ford Focus. The car was very affordable and had a lot of features. The car was also very fuel efficient. The two people were able to get the car they wanted and were able to get it for a great price."}
{"answers": "#Person2# suggests #Person1# get a costume of a Canadian Mountie for a dress party. They will go to the shopping center for that at ten o'clock on Saturday.", "prediction_text": "#Person1#: Are you busy next Saturday?"}
{"answers": "In the workshop, #Person2# offer #Person1# some suggestions on how to perform well in interviews.", "prediction_text": "The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the"}
{"answers": "#Person1# and #Person2# decide to go to Carrots where can meet everyone's requirements with others tomorrow evening.", "prediction_text": "The two friends are discussing the various restaurants they have been to. They have been to the Steakhouse, the Carrots, the Pizza experience, and the Kingfisher. They have not been to the Carrots on Thursday, and they have not been to the Steakhouse on Thursday. They have been to the Kingfisher on Thursday. They have been to the Pizza experience on Wednesday. They have been to the Carrots on Wednesday. They have been to the Steakhouse on Wednesday. They have been to the Kingfisher on Wednesday. They have been to the Pizza experience on Wednesday. They have been to the Carrots on Wednesday. They have been to the Steakhouse on Wednesday. They have been to the Kingfisher on Wednesday"}
{"answers": "#Person2# tells the trips and sports activities of the holiday camp to #Person1#. The kids enjoyed camp and would like to go next year.", "prediction_text": "The children enjoyed the holiday camp. They had a great time and made new friends."}
{"answers": "#Person2#'s friend is visiting #Person2#. #Person2# invites him to give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "A Ph.D. student at the University of Edinburgh is going to give a speech at my lab. He is going to talk about multimodal text generation. He will also talk about discourse modeling and video summarization. I am going to invite him to my lab."}
{"answers": "Tom asks Sara to help to take care of his son when he takes his daughter to the hospital.", "prediction_text": "A man and a woman are talking on the phone. The man says, \"I'm sorry to bother you at supper time.\" The woman says, \"Not at all.\" The man says, \"My little girl Maria has a high fever. We're taking her to hospital in a short time.\" The woman says, \"I'm sorry to hear that. Is there anything I can do for you?\" The man says, \"Do you mind taking care of my son Ken? We can't take him along.\" The woman says, \"OK. Can I bring him to my house?\" The man says, \"Thank you. But he hasn't finished his dinner yet.\" The woman says, \"No problem. He can have dinner with us,"}
{"answers": "#Person2# tells #Person1# that the company raised a lot of money to contribute to cancer research, and the marathon event was also a great deal for our company", "prediction_text": "The fundraiser was a success for both the American Cancer Society and the company. The company was able to raise a lot of money for a good cause, and the American Cancer Society was able to get a lot of exposure for their cause."}
{"answers": "#Person2# suggests that #Person1# should follow the doctor's instruction.", "prediction_text": "#Person1# is a 45 year old male who has been suffering from back pain for the past 3 years. He has been taking a painkiller prescribed by his doctor, but it has not helped. #Person2# is a 35 year old female who has been suffering from back pain for the past 2 years. She has been taking a painkiller prescribed by her doctor, but it has not helped. #Person1# and #Person2# are both in their mid 40s and are both married with children. They are both in good health and have no other medical conditions. #Person1# and #Person2# are both employed and have stable incomes. #Person1# and #Person2# are"}
{"answers": "#Person1# is going to New York for the first time. #Person2# suggests #Person1# use a personal tour guide service to make #Person1#'s trip plan.", "prediction_text": "A friend in New York is a personal tour guide service that helps you create a perfect trip for you according to your budget."}
{"answers": "#Person1# and #Person2# change ideas on Barry and Paul, and then talk about their own personalities.", "prediction_text": "The two friends, #Person1# and #Person2#, are talking about their friends. #Person1# says that he likes his brother Paul, but #Person2# disagrees. #Person1# says that he likes his brother Paul, but #Person2# disagrees. #Person1# says that he likes his brother Paul, but #Person2# disagrees. #Person1# says that he likes his brother Paul, but #Person2# disagrees. #Person1# says that he likes his brother Paul, but #Person2# disagrees. #Person1# says that he likes his brother Paul, but #Person2# disagrees. #Person1# says that he likes his brother Paul, but #Person2#"}
{"answers": "#Person1# is a tourist from America and has a chat with #Person2#.", "prediction_text": "A man and a woman are having a conversation. The man is from Los Angeles, a port city of the West part. The woman is from New York, a city in the East part. The man is American. The woman is American. The man is from Los Angeles. The woman is from New York. The man is from Los Angeles. The woman is from New York. The man is from Los Angeles. The woman is from New York. The man is from Los Angeles. The woman is from New York. The man is from Los Angeles. The woman is from New York. The man is from Los Angeles. The woman is from New York. The man is from Los Angeles. The woman is from New York. The man is"}
{"answers": "#Person1# compliments Brian on his English and asks Brian about his life in the U.S.", "prediction_text": "#Person1#: Brian, do you know how to speak English?"}
{"answers": "#Person2# is surprised that Ruojia's married. #Person1# and #Person2# will go to her party and give their presents to wish her a happy marriage.", "prediction_text": "The two friends are talking about Ruojia's marriage. #Person1# says that Ruojia has got married. #Person2# asks him who the party is. #Person1# says that Ruojia has got married. #Person2# says that he didn't receive the email. #Person1# says that he saw the news from Ruojia's twitter. #Person2# says that he didn't receive the email. #Person1# says that he will bring a pair of wineglasses and a card to wish her happy marriage. #Person2# says that he will buy a tea set."}
{"answers": "James helped Mrs. Thomas with housework since she has broken her legs, and he wanted to save money for a bike.", "prediction_text": "A man and a woman are talking about their jobs. The man is a gardener and the woman is a housekeeper. The man is cleaning up the yard and the woman is cleaning the house. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the"}
{"answers": "#Person1# and #Person2# feel scared when heard Sarah insists on marrying Dick.", "prediction_text": "The two friends, #Person1# and #Person2#, are discussing the marriage of their friend, #Person3#. #Person1# says that #Person3# is a very good girl, but #Person2# says that she is a very bad girl. #Person1# says that #Person3# is a very good girl, but #Person2# says that she is a very bad girl. #Person1# says that #Person3# is a very good girl, but #Person2# says that she is a very bad girl. #Person1# says that #Person3# is a very good girl, but #Person2# says that she is a very bad girl. #Person1# says"}
{"answers": "#Person1# and #Person2# hold different opinions towards people with different facial expressions.", "prediction_text": "#Person1#: I am fed up with those who keep smiling to me. #Person2#: I am fed up with those who keep a straight face in front of me."}
{"answers": "#Person1# and #Person2# argue to repay for a meal but no one succeeds. They finally decide to go dutch.", "prediction_text": "The two people are in a restaurant."}
{"answers": "#Person1# thinks the eyelash curler is very dangerous, but #Person2# doesn't think so.", "prediction_text": "The two people in this dialogue are arguing about the use of eyelash curlers. The first person, #Person1#, says that the eyelash curler is a form of torture, while the second person, #Person2#, says that it is just a simple way to curl the eyelashes. The two people are arguing about the use of the eyelash curler, and the first person says that it is a form of torture. The second person says that it is just a simple way to curl the eyelashes. The two people are arguing about the use of the eyelash curler, and the first person says that it is a form of torture. The second person says that it is just a simple way to curl the eyelashes"}
{"answers": "Jack checks in at #Person1#'s hotel.", "prediction_text": "The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with"}
{"answers": "Ann suggests #Person1# take well-being in the air programme including drinking mineral water and exercising so that #Person1# could get over jet lag.", "prediction_text": "The well-being program is a good choice for people who want to avoid jet lag. It is a light diet and a lot of water. The exercises are not very demanding."}
{"answers": "#Person2# is curling eyelashes. #Person1# feels dangerous, but #Person2# doesn't think so.", "prediction_text": "The two people in this dialogue are arguing about the use of eyelash curlers. The first person, #Person1#, says that the eyelash curler is a form of torture, while the second person, #Person2#, says that it is just a simple way to curl the eyelashes. The two people are arguing about the use of the eyelash curler, and the first person says that it is a form of torture. The second person says that it is just a simple way to curl the eyelashes. The two people are arguing about the use of the eyelash curler, and the first person says that it is a form of torture. The second person says that it is just a simple way to curl the eyelashes"}
{"answers": "#Person1# and #Person2# have a discussion about what they usually do to relieve anger.", "prediction_text": "#Person1#: I usually calm down first and think about the reason that caused my anger. #Person2#: That's smart. What do you usually do to relieve your anger? #Person1#: Something like listening to music is a good idea. #Person2#: Oh, what kind of music do you listen to? #Person1#: Classic. Something that could calm your mind. #Person2#: I think that might work for me as well. #Person1#: Like what? #Person2#: Working out is another good way to relieve anger. #Person1#: Like what? #Person2#: Jogging, yoga, or even just taking a walk."}
{"answers": "#Person1# and #Person2# are looking for work and will think about the electrician apprentice program.", "prediction_text": "The two people are having a conversation. The first person is looking for work, and the second person is looking for a job. The first person is looking for anything that will pay the mortgage, and the second person is looking for a job that will pay the mortgage. The first person has gone out on interviews, but has not heard back from them. The second person has seen the posting for the electrician apprentice program, and is interested in it."}
{"answers": "Mr. Polly wants to get a break from work and he asks #Person1# to buy a drink for him, but #Person1# refuses.", "prediction_text": "A man is talking to a woman. The man is a salesman. The woman is a customer. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink."}
{"answers": "Jane invites Peter to join her travel to Xi'an. Peter asks the duration and cost and is interested. Then they discuss their ideal places for a holiday if they had enough money.", "prediction_text": "Peter and Jane are thinking of going to Xi'an in the summer. They are both interested in going, but Peter is not sure how much money he has. Jane is not sure how much it will cost. They are both happy that Monica is not going. Peter is not sure where he would like to go for a holiday. Jane is not sure where she would like to go."}
{"answers": "#Person1# invites #Person2# to watch a movie together that night. #Person2# wants to see nothing but a war movie.", "prediction_text": "The two people in the conversation are discussing what they would like to see at the movies. The first person says that he would like to see a horror movie, but the second person says that he doesn't like horror movies. The first person says that he would like to see a detective movie, but the second person says that he doesn't like detective movies. The first person says that he would like to see a comedy, but the second person says that he doesn't like comedies. The first person says that he would like to see a love story, but the second person says that he doesn't like love stories. The first person says that he can't imagine that a girl like the second person would want to see a war movie, but"}
{"answers": "Hong tells #Person1# to buy a local SIM card to make a cheap phone call to the UK.", "prediction_text": "The two people had a conversation about the cost of calling the UK."}
{"answers": "#Person1#'s flight is delayed. #Person2# can't tell further information about the delay because of the changeable weather.", "prediction_text": "The dialogue is about a flight delay. The speaker is asking the person at the counter for the flight number. The person at the counter is telling the speaker that the flight has been delayed due to heavy rain. The speaker is asking the person at the counter for the delay time. The person at the counter is telling the speaker that the delay time is unknown at present. The speaker is asking the person at the counter for the weather forecast. The person at the counter is telling the speaker that the weather forecast is not available at present. The speaker is asking the person at the counter for the change in weather. The person at the counter is telling the speaker that the change in weather is not available at present. The speaker is asking the person at the"}
{"answers": "#Person1# and #Person2# are talking about taking care of their puppies and the time of vet appointment.", "prediction_text": "The two people in the conversation are talking about a dog that they have. The first person says that they have fed the dog today, and the second person says that they have fed the dog today. The first person says that they have given the dog a bath, and the second person says that they have given the dog a bath. The first person says that they have a vet appointment with the dog, and the second person says that they have a vet appointment with the dog. The first person says that they have to be there at eleven in the morning, and the second person says that they have to be there at eleven in the morning. The first person says that they have to remember to give the dog a bath, and the second person says"}
{"answers": "#Person1# makes an appointment with Jane on next Monday afternoon around three to kick around some ideas.", "prediction_text": "A man and a woman meet for the first time. They talk about their lives and their plans for the future. They agree to meet again next week."}
{"answers": "#Person2# draws #Person1#'s blood to check white blood cell count.", "prediction_text": "The doctor sent the patient to have his blood drawn. The patient was asked to roll up his left sleeve. The doctor wanted to check the white blood cell count. The patient was asked what the test was for. The patient was asked if the blood test hurt. The patient was asked if the blood test was painful. The patient was asked if the blood test was uncomfortable. The patient was asked if the blood test was painful. The patient was asked if the blood test was painful. The patient was asked if the blood test was painful. The patient was asked if the blood test was painful. The patient was asked if the blood test was painful. The patient was asked if the blood test was painful. The patient was asked if the blood test was"}
{"answers": "Weather's nice. #Person1# suggests having lunch outside and hopes to go to the beach to get some sun.", "prediction_text": "The weather is nice today, so we should go outside and have our lunch. The weather is nice today, so we should go outside and have our lunch."}
{"answers": "#Person1# is not ready for marriage. #Person2# reminds him of what he said a month ago and what will happen if he leaves Amy. After listening to #Person2#, #Person1# is ready for marriage.", "prediction_text": "This is a story about a man who is in love with a woman and wants to marry her. He is afraid to do it because he is afraid of what will happen if he does. He is also afraid of what will happen if he doesn't. He is also afraid of what will happen if he does. He is also afraid of what will happen if he does. He is also afraid of what will happen if he does. He is also afraid of what will happen if he does. He is also afraid of what will happen if he does. He is also afraid of what will happen if he does. He is also afraid of what will happen if he does. He is also afraid of what will happen if he does. He is also"}
{"answers": "#Person1# asks Mike about his sister.", "prediction_text": "Mike and his sister have a conversation about their appearance. Mike says that his sister is tall and pretty, while his sister says that she is not as clever as Mike."}
{"answers": "Jack bought a new car and invites Daisy to hop in and take a ride. Daisy thinks it's a good feeling.", "prediction_text": "Jack and Daisy are driving their new car on the highway. They are having a great time. Suddenly, a car comes out of nowhere and hits them. Jack and Daisy are injured. They are taken to the hospital. The police are called. The police are looking for the driver of the car. The police are looking for the driver of the car. The police are looking for the driver of the car. The police are looking for the driver of the car. The police are looking for the driver of the car. The police are looking for the driver of the car. The police are looking for the driver of the car. The police are looking for the driver of the car. The police are looking for the driver of the car. The police"}
{"answers": "#Person1# teaches #Person2#, a newbie, to use a machine. #Person2# doesn't want to be ripped and thus works with less weight than #Person1#.", "prediction_text": "The person who is new to the gym is confused and lost. The trainer is trying to help the person by giving them some pointers and advice. The trainer is also trying to help the person by giving them some tips on how to use the machine. The trainer is also trying to help the person by giving them some advice on how to work out. The trainer is also trying to help the person by giving them some tips on how to stretch before working out. The trainer is also trying to help the person by giving them some tips on how to work out with less weight. The trainer is also trying to help the person by giving them some tips on how to work out with less weight. The trainer is also trying to help the person by giving them"}
{"answers": "#Person1# asks #Person2# about past experiences of living in different cities around the world.", "prediction_text": "#Person1#: I was born in Chicago as a matter of fact."}
{"answers": "#Person1# helps #Person2# find the things that #Person2# wants. #Person1# finds it comfortable working in the office.", "prediction_text": "#Person1#: We're having a department meeting at 10 o'clock, ok? #Person2#: That's fine... I need to pick up some stationary-you know, a stapler, scissors, files, who should I see about that? #Person1#: See Julie, the receptionist. She knows where all that stuff is kept. You might like a calendar for you desk. She can give you one of those too. #Person2#: Thanks. I need to make a few photocopies. #Person1#: The photocopier is near my office. Come on, I'll show you where it is. #Person2#: Do you enjoy working in this office? #Person1#: Yes, I do"}
{"answers": "Jack invites Charlie to play a new video game and Charlie is interested.", "prediction_text": "#Person1# and #Person2# are two friends who are going to play a new video game together. #Person1# is excited about the game and wants to show it to #Person2#."}
{"answers": "Tom Wilson pays the bill for his room and meal cost by credit card.", "prediction_text": "The hotel charges a fixed rate for each room, and the bill is calculated by adding the room rate and the meals. The hotel also offers a credit card payment option. The customer can pay by credit card and sign the bill."}
{"answers": "#Person1# wants to know about the Normandy Landing. #Person2# teaches #Person1# to search and suggests reading a history book.", "prediction_text": "The dialogue above is a typical example of a dialogue between a person who wants to find information and a person who can help him or her do an internet search. The person who wants to find information is called the person who is asking for help. The person who can help is called the person who is giving the help. The person who is asking for help is called the person who is asking. The person who is giving the help is called the person who is giving. The person who is asking for help is called the person who is asking. The person who is giving the help is called the person who is giving. The person who is asking for help is called the person who is asking. The person who is giving the help is called the person"}
{"answers": "Laura does exercise for her health and good looking. #Person1# talks about regular exercise. They may do exercise together one day.", "prediction_text": "The two people are talking about their exercise habits. #Person1# says that he does not exercise regularly, but he does not like to go to the gym. #Person2# says that he does not exercise regularly either, but he does like to go to the gym. #Person1# says that he does not like to go to the gym because he is lazy. #Person2# says that he does not like to go to the gym because he is not interested in it. #Person1# says that he does not like to go to the gym because he does not like to exercise. #Person2# says that he does not like to go to the gym because he does not like to exercise. #Person1# says"}
{"answers": "Susan wants to find the memo. John has no idea where it is.", "prediction_text": "Susan Miller is a secretary in a large corporation. She is a very good secretary, but she is also a very good friend. She is also a very good friend of John, who is a very good friend of #Person1#. John is a very good friend of #Person2#."}
{"answers": "Martin tells Miss Eliza about his exams and his part-time job. Martin appreciates Mister Lam and the students' welfare club.", "prediction_text": "The students welfare club helped Martin to get a part-time job."}
{"answers": "#Person2# orders a turkey sandwich with cheese and a beef with vegetable soup at Danny's.", "prediction_text": "A man and a woman are sitting at a table in a restaurant. The man orders a turkey sandwich and a Diet Coke. The woman orders a beef with vegetables and a Diet Coke. The man asks the waitress if the soup of the day is beef with vegetables. The waitress says it is. The man asks if the soup is good. The waitress says it is. The man asks if the soup is hot. The waitress says it is. The man asks if the soup is cold. The waitress says it is. The man asks if the soup is hot and cold. The waitress says it is. The man asks if the soup is hot and cold and if it is good. The waitress says it is. The man asks if the soup is"}
{"answers": "Benjamin hasn't finished his project report so #Person1# helps him. #Person1# tells him about the content and format of a report, and agrees to teach him how to work Microsoft Words.", "prediction_text": "The report is a kind of document that is required to be written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students"}
{"answers": "#Person2# can speak three languages and do general desk work in English.", "prediction_text": "#Person1#: I can speak English and French. #Person2#: I can speak English and French. #Person1#: You mean you can read and write both of them? #Person2#: No, I can only carry on a simple conversation in French. #Person1#: Can you read and write English well? #Person2#: I can speak the language better than I read and write. #Person1#: Do you speak the language well enough to communicate with people of English-speaking countries? #Person2#: I think so. I used to be an interpreter. #Person1#: Do you think your English is good enough to do desk work? #Person2#: The amount of English I know enables me to do general desk"}
{"answers": "#Person1# refuses to stay by #Person2#'s side and won't return.", "prediction_text": "The two characters are in a conversation. The first character is a man who is leaving his wife and children. The second character is a man who is staying with his wife and children. The first character says that he must go. The second character says that he will stay. The first character says that he has spoken his mind and can go anywhere now. The second character says that he asks the first character to pass through life at his side. The first character says that he will never again come to the second character's side. The second character says that he will stay with his wife and children. The first character says that he will never again come to the second character's side. The second character says that he will stay with his wife and children"}
{"answers": "#Person1# wants to go around the world while #Person2# wants to buy a big house, if win the Pools. But they come down to the earth finally.", "prediction_text": "The two men are having a drink together. One of them has won the Pools, and the other has not. The first man says that he would go round the world if he won the Pools, and the second man says that he would not. The first man says that he would buy a big house with a garden for his wife and children, and the second man says that he would not. The first man says that he would not work any more if he had a lot of money, and the second man says that he would not. The first man says that he would go round the world, and the second man says that he would not. The first man says that he would not be happy if he got a rise, and"}
{"answers": "#Person2# answers #Person1#'s questions about getting special discount coupons and how to use them.", "prediction_text": "The supermarket has a special discount coupon for sugar. The customer can buy 3 bags of sugar and get 3 coupons. The customer can use the coupons to buy goods in the supermarket. The customer can use the coupons within one year."}
{"answers": "#Person1# borrows $50 from #Person2# and will pay back once #Person1# gets a job.", "prediction_text": "A man and a woman are talking about their jobs. The man says he has a job, but he doesn't like it. The woman says she has a job, but she doesn't like it. The man says he is looking for a job, but he doesn't know where to look. The woman says she is looking for a job, but she doesn't know where to look. The man says he is looking for a job, but he doesn't know where to look. The woman says she is looking for a job, but she doesn't know where to look. The man says he is looking for a job, but he doesn't know where to look. The woman says she is looking for a job, but she doesn't"}
{"answers": "#Person1# bought a new suit with $ 150 and #Person2# thinks it is too expensive.", "prediction_text": "#Person1#: What do you think of my new suit?"}
{"answers": "#Person2# lost #Person2#'s backpack, wallet, and money in a taxi. #Person1# lends #Person2# some money and will take #Person2# home.", "prediction_text": "A man lost his wallet and his backpack. He called the taxi driver to ask for the receipt. The driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver"}
{"answers": "Mark did't come in because of a hangover. Jill calls him and tells him news about Bill's newborn daugther. They decide to celebrate it tomorrow.", "prediction_text": "A man is on the phone with his wife. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the"}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "The actors are given a scene to read, and then they are asked to improvise a scene with their partner. The scene is then filmed, and the actors are asked to watch the film and give their feedback. The actors are then asked to try out the scene with their partner, and then the scene is filmed again. The actors are then asked to watch the film again and give their feedback. The actors are then asked to try out the scene with their partner, and then the scene is filmed again. The actors are then asked to watch the film again and give their feedback. The actors are then asked to try out the scene with their partner, and then the scene is filmed again. The actors are then asked to watch the film again and"}
{"answers": "#Person1# will have a business trip to New York. #Person2# suggests #Person1# visit the UN building, Broadway, Columbia University, and New York University.", "prediction_text": "#Person1#: I'm going to New York next week to sign a business contract. #Person2#: You should definitely see the UN building and from there, you could walk over to Broadway and see a movie or drama. #Person1#: How about New York's universities? I'm especially interested in learning about the schools there and the courses they offer. #Person2#: Columbia University and New York University are two of the best schools in the city. I'm sure they offer excellent courses. #Person1#: Do you have a map of the city? #Person2#: Yes, I have one right here in fact. I can give you this one, but I'd also suggest that you stop at the visitor's office near the"}
{"answers": "#Person1# asks #Person2# about the company's surroundings, location, and staff restaurant.", "prediction_text": "#Person1#: How long does it take to get to downtown from here?"}
{"answers": "Julie got food poisoning last Sunday and Mr. Jones cares about her health and study.", "prediction_text": "The two students were talking about their illnesses. The first student said that he had been feeling ill for a week. The second student said that he had been feeling ill for a few days. The first student said that he had been feeling better. The second student said that he was feeling better. The first student said that he had been feeling better. The second student said that he was feeling better. The first student said that he had been feeling better. The second student said that he was feeling better. The first student said that he had been feeling better. The second student said that he was feeling better. The first student said that he had been feeling better. The second student said that he was feeling better. The first student said that he had"}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "The actors are given a scene to read and then they are given a chance to try it out. The actors are then given a chance to try it out again and then they are given a chance to try it out again and then they are given a chance to try it out again and then they are given a chance to try it out again and then they are given a chance to try it out again and then they are given a chance to try it out again and then they are given a chance to try it out again and then they are given a chance to try it out again and then they are given a chance to try it out again and then they are given a chance to try it out again and then they are given a chance to try it"}
{"answers": "#Person1# helps #Person2# to choose a new phone.", "prediction_text": "The customer is trying to buy a mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to make video calls. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy a mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy a mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy a mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy"}
{"answers": "#Person1# buys a ticket under #Person2#'s guidance.", "prediction_text": "The person who is supposed to pay for the ticket is the one who is supposed to pay for the ticket. The person who is supposed to pay for the ticket is the one who is supposed to pay for the ticket. The person who is supposed to pay for the ticket is the one who is supposed to pay for the ticket. The person who is supposed to pay for the ticket is the one who is supposed to pay for the ticket. The person who is supposed to pay for the ticket is the one who is supposed to pay for the ticket. The person who is supposed to pay for the ticket is the one who is supposed to pay for the ticket. The person who is supposed to pay for the ticket is the one who is supposed to pay"}
{"answers": "#Person2# talks about #Person2#'s interest in collecting stamps and the first postage stamps with #Person1#. It gives #Person2# much pleasure. They all agree that stamps collecting gives much pleasure than becoming wealthy.", "prediction_text": "#Person1#: When did you become interested in collecting stamps?"}
{"answers": "#Person1# is not satisfied with the steak and #Person2# will change it.", "prediction_text": "A man is served a steak that is too well done. He asks for it to be changed, but the waiter is not happy about it."}
{"answers": "#Person1# advises Mary to try applying for a job online and teaches her how to do it. Mary will try it.", "prediction_text": "The Internet is a very convenient way to job hunt. You can apply for a job on the Internet, and you can also find a job on the Internet."}
{"answers": "#Person1# explains the checking items in #Person2#'s annual physical examination and will do test to look into #Person2#'s breathing.", "prediction_text": "The patient is having trouble breathing, and the doctor is looking for allergies, asthma, and blood tests. The doctor is also looking for cholesterol, blood sugar, and white blood cell count. The patient is looking for a good physical exam."}
{"answers": "#Person1# lost #Person1#'s passport. #Person2# suggests #Person1# fill in the lost property report and inform the embassy of the situation so they can issue a new passport in case it cannot be found.", "prediction_text": "A man is looking for his passport in a department store. He asks a woman if she has seen it. She says she hasn't. He asks her if she has lost it. She says she hasn't. He asks her if she has found it. She says she hasn't. He asks her if she has seen it. She says she hasn't. He asks her if she has lost it. She says she hasn't. He asks her if she has found it. She says she hasn't. He asks her if she has seen it. She says she hasn't. He asks her if she has lost it. She says she hasn't. He asks her if she has found it. She says she hasn't. He asks"}
{"answers": "#Person1# and #Person2# are talking about the history of how #Person2#'s city gets into an industrial centre.", "prediction_text": "The city of Manchester is a historical place. It has many buildings that are more than 200 years old. The city has a castle and a pub."}
{"answers": "#Person1# explains the rules of bowling game to #Person2#.", "prediction_text": "The dialogue is about bowling. The speaker is a person who has never played bowling before. The listener is a person who has played bowling before. The speaker asks the listener to tell him more about bowling. The listener tells the speaker that bowling is a game that has 10 frames. The speaker asks the listener to tell him more about bowling. The listener tells the speaker that bowling is a game that has 10 rounds. The speaker asks the listener to tell him more about bowling. The listener tells the speaker that bowling is a game that has 10 frames. The speaker asks the listener to tell him more about bowling. The listener tells the speaker that bowling is a game that has 10 rounds. The speaker asks the listener to tell him more"}
{"answers": "#Person1# asks #Person2# to help teach #Person1#'s friend English and #Person2# agrees.", "prediction_text": "A man volunteers his friend to teach him how to read. The man is a new immigrant from South America and has never been to school. The man's friend is 78 years old and has never been to school either. The man's friend is a great teacher and the man is very grateful. The man's friend is a great cook and the man is very grateful. The man's friend is adorable and the man is very grateful. The man's friend is a great cook and the man is very grateful. The man's friend is a great teacher and the man is very grateful. The man's friend is a great cook and the man is very grateful. The man's friend is a great teacher and the man is very grateful. The man"}
{"answers": "#Person1# introduces Mark Leach to this week's People You Meet. Mark Leach describes his job responsibilities and gives advice to people coming to Britain.", "prediction_text": "Mark Leach is an information officer at the Britain Business Center in London. He speaks 13 languages and offers tourist information to visitors from overseas."}
{"answers": "Harry is mad because he bought a bag overpriced at a street market in China. #Person2# says it's common and people should know the real worth and then negotiate for a fair deal.", "prediction_text": "The Chinese people are very good at bargaining. They are also very good at haggling. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of losing. They are not afraid of"}
{"answers": "Harry tells #Person1# that he was almost hit by a car and he will be more careful next time.", "prediction_text": "A man and a woman are talking about a terrible accident that happened to them. The man is pale and the woman is pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and"}
{"answers": "Joe disagrees with Tom on Tom's new assistant and thinks her stuck up.", "prediction_text": "A man and a woman meet at a party. The man is impressed with the woman's assistant, but the woman is not impressed with the man's assistant."}
{"answers": "#Person1# and #Person2# talk about their drinking capacity and their drinking habits. They decide to have a drink together tomorrow night.", "prediction_text": "#Person1#: Do you drink much?"}
{"answers": "#Person1# and #Person2# exchanges thoughts on how to deal with their son's bad behavior. #Person2# suggests treating the kid with patience instead of punishment.", "prediction_text": "The child is not a bad child. He is just a child. He is not a bad person. He is just a child. He is not a bad child. He is just a child. He is not a bad person. He is just a child. He is not a bad child. He is just a child. He is not a bad person. He is just a child. He is not a bad child. He is just a child. He is not a bad person. He is just a child. He is not a bad child. He is just a child. He is not a bad person. He is just a child. He is not a bad child. He is just a child. He is not a bad person"}
{"answers": "Gian and Gina introduce themselves to each other. Gian introduces Gina to Robert.", "prediction_text": "The two participants introduce themselves and discuss their work. They also talk about the conference and the work of the service provider."}
{"answers": "#Person2#'d like to rent a silver Toyota Carola and #Person1# helps #Person2# go through procedures.", "prediction_text": "A man and a woman are talking about renting a car. The man says he has never rented a car before. The woman says she has never rented a car before. The man says he doesn't like black or red cars. The woman says she doesn't like black or red cars. The man says he doesn't like black or red cars. The woman says she doesn't like black or red cars. The man says he doesn't like black or red cars. The woman says she doesn't like black or red cars. The man says he doesn't like black or red cars. The woman says she doesn't like black or red cars. The man says he doesn't like black or red cars. The woman says she doesn't like black"}
{"answers": "Mr. Simpson accepts #Person1#'s invitation to lunch on Thursday.", "prediction_text": "A man and a woman meet in a restaurant. The man is free, the woman is not. The man asks the woman if she is free. The woman says she is not. The man says he knows of a place where they can meet. The woman says she will be there at 12:30. The man says he will be there at 12:30. The woman says she will see him then. The man says he will see her then. The woman says she will see him then. The man says he will see her then. The woman says she will see him then. The man says he will see her then. The woman says she will see him then. The man says he will see her then. The woman"}
{"answers": "Simon tells #Person1# that he joined in a phased retirement scheme which offers retired people work positions from the former company, and he can manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "Simon is a retired employee who has been working on a phased retirement scheme. He has been able to work on a project for the company he used to work for, and he has been able to decide when he wants to work. He has been able to use his contacts from work to get things done. He has been able to use his skills to get work done. He has been able to use his time to do what he wants to do. He has been able to use his time to do what he wants to do. He has been able to use his time to do what he wants to do. He has been able to use his time to do what he wants to do. He has been able to use his time to do what he"}
{"answers": "#Person2# tells #Person1# #Person2# plan to save money and #Person1# thinks it is good.", "prediction_text": "#Person1#: What are you working on?"}
{"answers": "#Person1# wants to travel to China, but #Person1#'s mother is not available.", "prediction_text": "A mother and her daughter discuss their plans for a trip to China."}
{"answers": "Ted and #Person2# are discussing the place where they are going to take a holiday.", "prediction_text": "The two people are talking about their holiday plans. #Person1# is going to China, while #Person2# is going to her parents' house. #Person1# says that he doesn't like living with his wife's parents, so he will stay at home. #Person2# says that she will go to China with her husband."}
{"answers": "Mary is tired because of finding jobs in the market. #Person1# suggests Mary apply for jobs on the Internet and says she could place her information on the Job Wanted channel on a recruitment web. Mary will try at once.", "prediction_text": "The Internet is a very convenient way to job hunt. You can apply for a job on the Internet, and you can also find a job on the Internet."}
{"answers": "#Person1# and #Person2# are talking about a couple.", "prediction_text": "A man and a woman meet and fall in love. They are married and have a child. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful"}
{"answers": "Gene is learning to drive and wants to buy a sixteen Honda Accord after passing the test.", "prediction_text": "#Person1#: What are you doing this Saturday, Gene?"}
{"answers": "#Person2#'s country exploits, exports, and imports natural resources. #Person1# thinks the government in #Person2#'s country should invests in infrastructure. #Person2# agrees.", "prediction_text": "The two people are talking about the country's natural resources. The first person says that the country exports a lot of coal, copper, and oil. The second person says that the country is self-sufficient in oil and aluminium, but imports a little oil from Venezuela. The two people agree that the country should invest in long-term projects rather than short-term ones."}
{"answers": "#Person1# has been working for a company for six years, and #Person2# thinks #Person2#'s ready for promotion.", "prediction_text": "#Person1#: OK, Let me just have a look at the information here. You've been with a company for 6 years."}
{"answers": "Steve helps look after Jonny and house-keeping. Mrs. Robinson appreciates it.", "prediction_text": "A man and a woman are talking about their children. The man says that he is worried about his son, who is a bit of a troublemaker. The woman says that she is worried about her daughter, who is a bit of a troublemaker. The man says that he is worried about his daughter, who is a bit of a troublemaker. The woman says that she is worried about her son, who is a bit of a troublemaker. The man says that he is worried about his son, who is a bit of a troublemaker. The woman says that she is worried about her son, who is a bit of a troublemaker. The man says that he is worried about his son, who is a bit of a troublemaker"}
{"answers": "#Person2# tells #Person1# #Person2# has few guiding experiences.", "prediction_text": "#Person1#: I have been guiding for 5 years. I have guided many groups of foreign tourists. I have been guiding for 5 years. I have guided many groups of foreign tourists. I have been guiding for 5 years. I have guided many groups of foreign tourists. I have been guiding for 5 years. I have guided many groups of foreign tourists. I have been guiding for 5 years. I have guided many groups of foreign tourists. I have been guiding for 5 years. I have guided many groups of foreign tourists. I have been guiding for 5 years. I have guided many groups of foreign tourists. I have been guiding for 5 years. I have guided many groups of foreign tourists. I have"}
{"answers": "#Person1# is asking Rebecca about her working experience. She worked for York Herald as an intern and then as a junior local news reporter. After that, she began to work for London Weekly.", "prediction_text": "#Person1#: I see. And you were a journalist for a while?"}
{"answers": "#Person1# is helping #Person2# confirm the price for sending the letter.", "prediction_text": "A man and a woman are talking on the phone. The man says, \"I need some stamps for this letter.\" The woman says, \"What kind of stamps do you want?\" The man says, \"How much do I need for this letter?\" The woman says, \"I must weigh it first. Err... It's five grams over weigh, Do you want to send it as an ordinary or registered letter?\" The man says, \"I must weigh it first. Err... It's five grams over weigh, Do you want to send it as an ordinary or registered letter?\" The woman says, \"I want it registered. How much is it then?\" The man says, \"Registration plus overnight... err... seven dollars in all.\" The"}
{"answers": "#Person1# wants to know the charge at #Person2#'s buffet party.", "prediction_text": "The restaurant is a buffet restaurant. The owner is a person of the party. The owner has three people in the party. The owner wants to know how many people in the party. The owner wants to know the price of the buffet. The owner wants to know the location of the buffet. The owner wants to know the price of the drinks. The owner wants to know the price of the food. The owner wants to know the price of the alcohol. The owner wants to know the price of the cold dishes. The owner wants to know the price of the hot dishes. The owner wants to know the price of the soft drinks. The owner wants to know the price of the alcohol. The owner wants to know the price of the vegetables."}
{"answers": "#Person2# tells Mary about #Person2#'s experience this morning. when #Person2# was delivering milk, a huge dog rushed at #Person2# and #Person2# was knocked into a tree. It reminds Mary of a movie.", "prediction_text": "A man and a woman are talking about a dog that ran after them. The man says that he knocked into a tree, and the dog ran after him. The woman says that she was lucky, because the dog didn't jump over the fence."}
{"answers": "#Person1# helps #Person2# get a ballot card and guides #Person2# the next step.", "prediction_text": "#Person1#: Did you need help with something?"}
{"answers": "Ben is nervous about his new school life. #Person1# is giving him some brief information about the school.", "prediction_text": "The students are getting ready for the first class of the day. They are nervous because they don't know what to expect. They are also worried about how they will get along with their classmates. The teacher tells them to take it easy and to try their best."}
{"answers": "#Person1# teaches #Person2# to cook chicken for #Person2#'s original thought of having frozen pizza is unhealthy.", "prediction_text": "#Person1#: do you have any plans for dinner tonight?"}
{"answers": "Tom invites #Person1# to run in the morning. #Person1# would try to get up and join him.", "prediction_text": "A man and a woman are talking about going for a run. The man says he can't get up early enough to run in the morning, but the woman says she can't get up early enough to run in the morning. The man says he can't get up early enough to run in the morning, but the woman says she can't get up early enough to run in the morning. The man says he can't get up early enough to run in the morning, but the woman says she can't get up early enough to run in the morning. The man says he can't get up early enough to run in the morning, but the woman says she can't get up early enough to run in the morning. The man says he can"}
{"answers": "#Person1# and #Person2# have different ideas on computer games.", "prediction_text": "The dialogue is about the negative effects of computer games. The first person says that he hates computer games because they are violent and disgusting. The second person says that he does not think that computer games are bad. He says that they are good for boys to learn how to use the computer. The first person says that if boys grow up with computer games, they will surely become bad. The second person says that things are not so bad. Boys can learn to use the computer through games."}
{"answers": "#Person1# asks for mom's permission to play card games and promises not to make noise.", "prediction_text": "A mother and her son are playing card games. The mother is worried that her son will make noise. The son promises not to make any noise. The mother is happy that her son is behaving well."}
{"answers": "#Person1# is showing the new clothes to #Person2#, but #Person2# is busy watching the games. They quarrel and get angry.", "prediction_text": "#Person1#: What do you think of this one? #Person2#: Eh, so so. #Person1#: And this one? Too flashy? #Person2#: Nah, not too flashy. #Person1#: Uhg! And this sweater from my aunt? Isn't it hideous? #Person2#: I guess. #Person1#: Are you even listening? I'm trying to have a conversation with you. #Person2#: And I'm trying to watch the game, but you're yapping on about your new clothes! #Person1#: Well I have to decide which gifts to keep and which to exchange for better ones when I go to the Boxing Day sales this afternoon! #Person2#: Well could you"}
{"answers": "#Person1# and #Person2# are both unemployed. #Person2# suggests applying for the electrician program and #Person1# agrees.", "prediction_text": "The two people in this dialogue are having a conversation about what they are doing. The first person is unemployed, and the second person is looking for a job. The first person is not interested in interviews, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The"}
{"answers": "Simon and Cleo argue the effectiveness of demonstrations. Simon thinks they are helpful, but Cleo disagrees and refuses to go to the demonstration of helping stop the spread of nuclear weapons with Simon.", "prediction_text": "The two students are discussing the upcoming demonstration to stop the spread of nuclear weapons. #Person1# is going to the demonstration and #Person2# is not. #Person1# says that he has heard that North Korea has nuclear weapons and that he does not want to be involved in a demonstration that could get him hurt. #Person2# says that he does not want to be involved in a demonstration either because he does not understand the issues and because he does not want to get hurt. #Person1# says that he has heard that demonstrations can change people's minds and that he thinks it is important to stand up for what he believes in. #Person2# says that he does not want to be involved in a demonstration because he"}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. They both decide to have fried bananas and #Person1# will bring bananas back.", "prediction_text": "#Person1#: It's time for desserts! Are you still hungry?"}
{"answers": "Tom Wilson pays for his bill for hotel and food by credit card.", "prediction_text": "The hotel charges a fixed rate for each room, and the bill is calculated by adding the room rate and the meals. The hotel also offers a credit card payment option. The customer can pay by credit card and sign the bill."}
{"answers": "#Person1# and #Person2# are waiting in line for food.", "prediction_text": "The two friends are standing in line at a restaurant. One of them says, \"Let's get the table wares we need first, and then stand in the line.\" The other one says, \"Let's stand in the line now.\" The first one says, \"It smells good. Look at these. They make my mouth watering.\" The second one says, \"Me too.\" The first one says, \"It will be our turn soon.\" The second one says, \"Tell the waiter what you want when it's your turn.\" The first one says, \"OK. I'll have many cream cakes today.\" The second one says, \"I don't like them. I think they are too icky.\" The first one says,"}
{"answers": "#Person1# introduces Henry Johnson, an expat in China to Pete. Mr. Johnson shares with Pete how much he enjoys his time in China, until #Person1# interrupts and tries to finalize the evening plan. The three decide to go have dinner and then to a bar district.", "prediction_text": "The three friends met for dinner and then went to a Karaoke bar. They had a great time singing and dancing."}
{"answers": "#Person1# is preparing for her friends' coming this afternoon. Her son Mike helps her to buy drinks and make coffee.", "prediction_text": "Mike and his friends are going to a party. Mike wants to make coffee for his friends. He asks his mother to buy some fruit juice and cola. His mother asks him if he knows how to make coffee. Mike says that he can make coffee now. His mother asks him when his friends will arrive. Mike says that they will arrive in an hour. His mother asks him to buy some fruit. Mike says that he has some bananas left. His mother asks him to buy some apples. Mike says that he has some apples left. His mother asks him to buy some cola. Mike says that he has some cola left. His mother asks him to buy some fruit juice. Mike says that he has some fruit juice left. His mother asks him"}
{"answers": "#Person1# gets lost and asks #Person2# where the central department stall and the national bank are. #Person2# directs #Person1#.", "prediction_text": "A man and a woman are lost in a city. They are looking for a bank. The man is lost and the woman is looking for a bank. The man is looking for a bank and the woman is looking for a bank. The man is looking for a bank and the woman is looking for a bank. The man is looking for a bank and the woman is looking for a bank. The man is looking for a bank and the woman is looking for a bank. The man is looking for a bank and the woman is looking for a bank. The man is looking for a bank and the woman is looking for a bank. The man is looking for a bank and the woman is looking for a bank. The man is looking for a bank"}
{"answers": "#Person1# warmly welcomes #Person2# and drives #Person2# to the Beijing Hotel. Mr. Wu has arranged a banquet for #Person2# in the evening.", "prediction_text": "The two people are in a car. The driver is talking to the passenger. The passenger is talking to the driver. The driver is talking to the passenger. The passenger is talking to the driver. The driver is talking to the passenger. The passenger is talking to the driver. The driver is talking to the passenger. The passenger is talking to the driver. The driver is talking to the passenger. The passenger is talking to the driver. The driver is talking to the passenger. The passenger is talking to the driver. The driver is talking to the passenger. The passenger is talking to the driver. The driver is talking to the passenger. The passenger is talking to the driver. The driver is talking to the passenger. The passenger is talking to the"}
{"answers": "Fanny had a nightmare last night and her mother consoled her. Andy listens to her experience and thinks she has a good mother.", "prediction_text": "Andy and Fanny are talking about their dreams. Andy had a bad dream and Fanny had a good dream. Andy is worried about getting into the University of Michigan. Fanny is worried about getting into the University of Michigan."}
{"answers": "#Person2# organized a great baby shower for #Person1#. #Person1# receives many gifts. #Person1#'s water broke.", "prediction_text": "A baby shower is a great way to celebrate a new baby. It's a great way to get to know the new parents and to get some gifts for the baby."}
{"answers": "#Person2# tells #Person1# about the fundamental concepts of the accounting process.", "prediction_text": "The first is accounting entity, and the second is going concern. The third is measuring unit. The fourth is accounting period, and the fifth is objectivity."}
{"answers": "#Person1# makes a big mistake which may cause a huge loss and #Person1# may lose this job because of the loss and #Person1#'s dishonest supervisor. #Person2# suggests that #Person1# move in with #Person2# until #Person1# find another job and stop buying all the junk, if #Person1# loses the job.", "prediction_text": "#Person1#: I don't know what I'm going to do! It's going to be impossible to make ends meet if I lose my job!"}
{"answers": "#Person2# thinks that unemployment gives rise to social problems, and #Person1# thinks that it's difficult to handle it efficiently.", "prediction_text": "The government has to face a lot of social problems now. But it is very difficult for any government to handle it efficiently."}
{"answers": "#Person1# and #Person2# are talking about a map of the world. They talk about geographical features and find geographical interesting.", "prediction_text": "The two people are talking about the geography of the world. The first person is interested in the geography of the world and is looking at a map. The second person is interested in the geography of the world and is looking at the map. The first person is surprised that there are no countries marked on the map. The second person is surprised that most of the world is covered with water. The first person is surprised that the oceans and landscapes all influence our climate. The second person is surprised that the oceans and landscapes all influence our climate. The first person is surprised that the oceans and landscapes all influence our climate. The second person is surprised that the oceans and landscapes all influence our climate. The first person is surprised that the oceans and landscapes all influence"}
{"answers": "#Person1# asks #Person2# who saw the robbery some questions. #Person2# agrees to come to the station for more questioning.", "prediction_text": "#Person1#: Did you see the robbery?"}
{"answers": "#Person2# orders some food with #Person3#'s help in a restaurant.", "prediction_text": "The customer is asked to order a drink, a meal, and a side dish. The waiter asks the customer what he would like to drink. The customer says he will have a bottle of water. The waiter asks what he would like to eat. The customer says he will have a tuna fish sandwich and a bowl of vegetable soup. The waiter asks what he would like to drink. The customer says he will have a bottle of water. The waiter asks what he would like to eat. The customer says he will have a tuna fish sandwich and a bowl of vegetable soup. The waiter asks what he would like to drink. The customer says he will have a bottle of water. The waiter asks what he would like to eat. The customer says he"}
{"answers": "#Person2# thanks #Person1# for #Person1#'s help.", "prediction_text": "#Person1#: You're all set now?"}
{"answers": "#Person2# introduces #Person1# a website for personal tour guide service.", "prediction_text": "A friend in New York is a personal tour guide service that helps you create a perfect trip for you according to your budget."}
{"answers": "#Person1# attends Brian's birthday party. Brian thinks #Person1# looks great and charming.", "prediction_text": "A man and a woman are having a party. The man is happy to see his friend, but the woman is not. The man asks the woman to dance with him. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to"}
{"answers": "#Person1# is checking the draft of an agreement and #Person2# will finish the agreement this evening.", "prediction_text": "The agreement was signed."}
{"answers": "#Person1# and Bill talk about Bill's roommate Brain Locker.", "prediction_text": "#Person1#: Bill, how can you hear so happy today?"}
{"answers": "Kalina calls Professor Clark that she needs to take this week off because she had a concussion.", "prediction_text": "A student is injured in a car accident and needs to miss a few days of school. The teacher is very concerned and wants to know how much school the student will miss."}
{"answers": "#Person2# lost the credit card in travelling and is asking the Client Services for help.", "prediction_text": "The client is a tourist in the city and lost his credit card. He is not sure if he lost it in the hotel lobby bar or not. He is not sure when he lost it. He is not sure if he picked it up again. He is not sure if he used it to charge something to his room. He is not sure if he used it to charge something to his room. He is not sure if he used it to charge something to his room. He is not sure if he used it to charge something to his room. He is not sure if he used it to charge something to his room. He is not sure if he used it to charge something to his room. He is not sure if he used it to charge"}
{"answers": "Darlene calls to check the delayed order. Dan tells her the situation. Darlene will talk to Steve.", "prediction_text": "A call to a factory to check on an order of computers has been delayed for two days. The factory is short of hands at the moment. The caller is told to call Steve's phone number to get the order. The caller is told that Steve can't really complain about the delay."}
{"answers": "#Person1# and #Person2# talk about the accounting process.", "prediction_text": "The first is accounting entity, and the second is going concern. The third is measuring unit. The fourth is accounting period, and the fifth is objectivity."}
{"answers": "#Person2# wants to buy a gift for his mother's birthday. After knowing his mother's preference, #Person1# recommends a gold watch.", "prediction_text": "A woman is looking for a gift for her mother's birthday. She asks a salesperson for advice. The salesperson suggests a watch. The woman says she doesn't like jewels. The salesperson suggests a gold watch. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson"}
{"answers": "#Person1# helps #Person2# to order some food.", "prediction_text": "A man and a woman are having a conversation. The man is asking the woman what she would like to eat. The woman says she doesn't know. The man says he doesn't know either. The woman says she doesn't know what she would like to eat. The man says he doesn't know what he would like to eat. The woman says she doesn't know what she would like to eat. The man says he doesn't know what he would like to eat. The woman says she doesn't know what she would like to eat. The man says he doesn't know what he would like to eat. The woman says she doesn't know what she would like to eat. The man says he doesn't know what he would like to"}
{"answers": "#Person2# tells #Person1# the address of the Grand Hotel and #Person1# drives her there.", "prediction_text": "A woman is asked to identify a man who is in the Grand Hotel. She is unable to do so. She is then asked to identify the Grand Hotel. She is unable to do so. She is then asked to identify the 11th Avenue, Park Street. She is unable to do so. She is then asked to identify the Grand Hotel. She is unable to do so. She is then asked to identify the 11th Avenue, Park Street. She is unable to do so. She is then asked to identify the Grand Hotel. She is unable to do so. She is then asked to identify the 11th Avenue, Park Street. She is unable to do so. She is then asked to identify the Grand Hotel. She"}
{"answers": "#Person1# and #Person2# remember the chaotic situation of New York after 9-11, and #Person1#'s uncle ended up dying in the tower.", "prediction_text": "The terrorist attacks on 9-11-2001 were a horrific event that changed the world forever. The attacks were carried out by hijackers who flew two planes into the World Trade Center in New York City and the Pentagon in Washington, D.C. The attacks killed thousands of people and injured many more. The attacks were a wake-up call for the United States and the world. The attacks were a reminder that terrorism is a serious threat to the world and that we must all work together to prevent it."}
{"answers": "#Person1# and #Person2# are at an exciting party. #Person2# has a crush on Janice and #Person1# encourages him to have a try to talk to her.", "prediction_text": "#Person1#: this party rocks! #Person2#: you said it. Good beer, good music, and on top of that, there are so many hot girls here! #Person1#: I'm with you on that. Check out that one over there. I think I'm in love! #Person2#: well, she's alright, but not really my cup of tea. What about the blond with the red dress? #Person1#: oh, that's Janice. She's just new in town. You have a thing for blonds, eh? #Person2#: yeah! She could do anything she wants to me! #Person1#: I bet. Why not go over and talk to her? She's really"}
{"answers": "#Person1# and #Person2# think John's fallen for a girl.", "prediction_text": "A man and a woman meet and fall in love. They are married and have a child. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful businessman and the woman is a housewife. The man is a successful"}
{"answers": "Nathan is going to Chicago for practice and he is confident that he will do a good job and learn from others. Though worried at first, his mom thinks it's a great opportunity for him.", "prediction_text": "#Person1#: Well, you must be happy, Nathan you're almost ready to go to Chicago for your practice. We're sure going to miss you around here."}
{"answers": "#Person1# is introducing the Eiffel Tower to #Person2#.", "prediction_text": "The Eiffel Tower is a famous landmark in Paris, France. It was built in 1889 for the World's Fair. It is 324m (1060ft) high and held the record as the world's tallest structure until 1930."}
{"answers": "#Person1# tells ABC Rent-a-car Company that #Person1# had a car accident. #Person2# will call an ambulance and the police.", "prediction_text": "A man and a woman were driving a car when they ran into a guardrail. The man was seriously injured and the woman was not. The woman called the police and the ambulance. The police and the ambulance arrived and took the man to the hospital. The woman was not injured."}
{"answers": "Matthew and Steve meet after a long time. Steve is looking for a new place to live. Mathew's neighbor is moving out and They might be neighbors again.", "prediction_text": "Matthew and Steve are neighbors in the same apartment building. They have not seen each other in a year. Matthew is looking for a new place to live. Steve is looking for a place to live. They are both looking for a place to live in the same neighborhood. They are both looking for a place to live in the same apartment building. They are both looking for a place to live in the same building. They are both looking for a place to live in the same building. They are both looking for a place to live in the same building. They are both looking for a place to live in the same building. They are both looking for a place to live in the same building. They are both looking for a place to live in the"}
{"answers": "#Person2# thinks the movie industry should be revolutionized to survive the threat from DVD industry.", "prediction_text": "#Person1#: Do you think home video players will replace movie theatres and force them out of the entertainment business?"}
{"answers": "Mr. Brown is interviewing #Person2# and they are talking over #Person2#'s salary.", "prediction_text": "The two people are in a job interview. The first person is a manager and the second is a worker. The manager asks the worker how much he/she is paid. The worker says he/she is paid on a salary. The manager asks how much he/she hopes to get a month. The worker says he/she hopes to get two thousand and five hundred yuan a month. The manager says he/she will pay the worker two thousand and five hundred yuan a month at the start. The worker says he/she can have Saturdays and Sundays off. The manager says he/she will give the worker a paid month holiday every year. The worker says he/she is satisfied with the offer."}
{"answers": "#Person1# is crazy for Trump and voted for him but #Person2# will vote for Biden.", "prediction_text": "The two people are talking about the election. The first person is a Trump supporter, and the second person is a Biden supporter. The first person says that he cannot imagine if Trump were to be our President again. The second person says that he is proud to say that he voted for Trump, and that he will be really happy if he could be re-elected. The first person says that he is not sure about this, and that he has nothing but faith in Trump. The second person says that he has nothing but faith in Trump, and that he is pretty sure that Trump will make America great again. The first person says that he is not sure about this, and that he will vote for Biden anyway. The second person says that he will"}
{"answers": "According to the schedule planned by Brian, #Person1# will be picked up at 6, meet the Managing Director at the airport and attend the conference at noon.", "prediction_text": "The two people are discussing the details of a trip to a factory. The first person is Brian, who is the Managing Director of the company. The second person is the person who is going to meet Brian at the airport. The first person is going to the factory to meet the Managing Director. The second person is going to the factory to meet the Managing Director. The first person is going to the factory to meet the Managing Director. The second person is going to the factory to meet the Managing Director. The first person is going to the factory to meet the Managing Director. The second person is going to the factory to meet the Managing Director. The first person is going to the factory to meet the Managing Director. The second person is going to the"}
{"answers": "#Person2#'s friend is a Ph.D. student at the University of Edinburgh. He is visiting #Person2# and will give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "A Ph.D. student at the University of Edinburgh is going to give a speech at my lab. He is going to talk about multimodal text generation. He will also talk about discourse modeling and video summarization. I am going to invite him to my lab."}
{"answers": "#Person1# is telling #Person2# how to play bowling.", "prediction_text": "The dialogue is about bowling. The speaker is a person who has never played bowling before. The listener is a person who has played bowling before. The speaker asks the listener to tell him more about bowling. The listener tells the speaker that bowling is a game that has 10 frames. The speaker asks the listener to tell him more about bowling. The listener tells the speaker that bowling is a game that has 10 rounds. The speaker asks the listener to tell him more about bowling. The listener tells the speaker that bowling is a game that has 10 frames. The speaker asks the listener to tell him more about bowling. The listener tells the speaker that bowling is a game that has 10 rounds. The speaker asks the listener to tell him more"}
{"answers": "Adam's knee is getting better but #Person1# still suggests that he should not do any running today. Adam decides to watch the films of Michigan after practice and will be back tomorrow for full practice.", "prediction_text": "#Person1#: Adam, how is your knee today? Is it still giving you trouble?"}
{"answers": "#Person1# helps #Person2# collect an Export L /C from Tokyo.", "prediction_text": "A Japanese company has sent a representative to collect a L / C from a British company. The representative is a woman, and the British company is a man. The Japanese company is a large company, and the British company is a small company. The Japanese company is a large company, and the British company is a small company. The Japanese company is a large company, and the British company is a small company. The Japanese company is a large company, and the British company is a small company. The Japanese company is a large company, and the British company is a small company. The Japanese company is a large company, and the British company is a small company. The Japanese company is a large company, and the British company is a small company"}
{"answers": "Leah informs Nathaniel that Ms. Collins won't be available for their appointment and explains the reason. Then Nathaniel decides to speak with someone else.", "prediction_text": "Ms. Collins is not available today. Ms. Fonda is available at 11:30."}
{"answers": "#Person1# and Judy have a discussion on their financial budget and find out ways to save money.", "prediction_text": "The two people are discussing the budget of their trip. The first person is worried about the transportation fee, and the second person is worried about the accommodation. The two people are not in agreement."}
{"answers": "#Person1# introduces the services included in regular car wash package and #Person2# will take it.", "prediction_text": "The customer is satisfied with the service."}
{"answers": "#Person1# sends a package at #Person2#'s and buys some stamps at the other window.", "prediction_text": "#Person1# wants to send a package by first-class mail. #Person2# wants to send it insured. #Person1# wants to get stamps and money orders. #Person2# wants to get stamps only."}
{"answers": "Since #Person2# is very social, #Person1# asks for the communication strategy.", "prediction_text": "The two people are both outgoing and like to be with a lot of friends. They both have a strategy in communicating with colleagues."}
{"answers": "#Person2#'s friend is visiting #Person2# and will give a speech about multimodal text generation. #Person1# thinks it interesting and will attend it.", "prediction_text": "A Ph.D. student at the University of Edinburgh is going to give a speech at my lab. He is going to talk about multimodal text generation. He will also talk about discourse modeling and video summarization. I am going to invite him to my lab."}
{"answers": "#Person2# is watching a programme about Islam's pilgrimage to Mecca. #Person1# has heard that there are often accidents happen during this pilgrimage. They also talk about pilgrimage in other countries.", "prediction_text": "The pilgrims in the story are Muslims who are on a pilgrimage to Mecca. They are going to the holy city to perform the haj. The haj is a pilgrimage to Mecca. The pilgrims are going to Mecca to perform the haj. The pilgrims are going to Mecca to perform the haj. The pilgrims are going to Mecca to perform the haj. The pilgrims are going to Mecca to perform the haj. The pilgrims are going to Mecca to perform the haj. The pilgrims are going to Mecca to perform the haj. The pilgrims are going to Mecca to perform the haj. The pilgrims are going to Mecca to perform the h"}
{"answers": "Sarah wants to move. #Person1# suggests buying a cheaper house and offers information on possible options.", "prediction_text": "#Person1#: Have you ever thought of moving, Sarah?"}
{"answers": "#Person1# serves #Person2# to borrow movies and tells #Person2# the price and when to return them.", "prediction_text": "A man and a woman are at a video store. The man asks the woman if she has a membership card. The woman says no. The man asks if she needs one to rent videos. The woman says no. The man asks how much the movies are. The woman says $3.50 for new releases and $2 for all other movies. The man says okay. The woman says okay. The man says they have a five dollar deal where you can rent any five movies for $5.00. The woman says okay. The man says they have to be returned on Thursday by ten o'clock PM. The woman says okay. The man says there is an overdue fee equal to the rental fee of the video. The woman says okay"}
{"answers": "#Person1# and #Person2# are standing in line to buy food.", "prediction_text": "The two friends are standing in line at a restaurant. One of them says, \"Let's get the table wares we need first, and then stand in the line.\" The other one says, \"Let's stand in the line now.\" The first one says, \"It smells good. Look at these. They make my mouth watering.\" The second one says, \"Me too.\" The first one says, \"It will be our turn soon.\" The second one says, \"Tell the waiter what you want when it's your turn.\" The first one says, \"OK. I'll have many cream cakes today.\" The second one says, \"I don't like them. I think they are too icky.\" The first one says,"}
{"answers": "#Person1# feels sorry for Mr. Wilson's loss caused by #Person1# and assures that such mistakes will not happen again.", "prediction_text": "The two parties have a dialogue about the mistake in the goods. The first party is sorry for the mistake and the second party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The second party is sorry for the mistake and the first party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The first party is sorry for the mistake and the second party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The second party is sorry for the mistake and the first party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The second party is sorry"}
{"answers": "Tony tells Steven that he cheated on the exam and realized his mistake.", "prediction_text": "A student is caught cheating and feels ashamed. He feels that his father will kill him if he fails. He feels that he should study hard. He feels that he should learn from his mistakes."}
{"answers": "#Person1# wants a new bike from #Person2#, but she refuses. #Person1# promises to buy a plane for #Person2#.", "prediction_text": "The two people are talking about their bikes. The first person says that his bike is better than the second person's bike. The second person says that the first person's bike is better than his own. The first person says that he will buy a big car one day. The second person says that he is looking forward to that. The first person says that he will buy a plane for the second person. The second person says that he is looking forward to that. The first person says that he will buy a plane for his mother. The second person says that he is looking forward to that. The first person says that he will buy a plane for his mother. The second person says that he is looking forward to that. The first person says"}
{"answers": "Harry tells #Person1# that he crossed the street while reading the newspaper and almost got hit by a car.", "prediction_text": "A man and a woman are talking about a terrible accident that happened to them. The man is pale and the woman is pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and pale and pale. The man is pale and pale and pale. The woman is pale and"}
{"answers": "#Person2# finds that #Person1# has a fever and says #Person1# should've called in sick earlier.", "prediction_text": "A man and a woman are talking on the phone. The man says he has a headache. The woman says she thinks he has a small fever. The man says he felt dizzy this morning. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick"}
{"answers": "#Person1# and #Person2# go back to work and will meet after work.", "prediction_text": "A man and a woman meet at a bus stop. They talk about their work and their lives. They agree to meet at five."}
{"answers": "Mark's absence was the result of a hangover. Bill's wife had a baby girl last night. Mark and Jill decide to celebrate it tomorrow.", "prediction_text": "A man is on the phone with his wife. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the night before. He is talking about a party that he had the"}
{"answers": "#Person2# leaves the samples to Mr. Grant and will talk to him next week.", "prediction_text": "The two people in this dialogue are trying to decide whether to hire a new employee. The first person, #Person1#, is the one who makes the decision. The second person, #Person2#, is the one who gives the decision. The first person is trying to decide whether to hire the new employee. The second person is trying to decide whether to give the decision to the first person. The first person is trying to decide whether to give the decision to the second person. The second person is trying to decide whether to give the decision to the first person. The first person is trying to decide whether to give the decision to the second person. The second person is trying to decide whether to give the decision to the first person. The first"}
{"answers": "Steve will try to persuade #Person1#'s wife not to divorce #Person1# as #Person1# swears to remain faithful forever.", "prediction_text": "A man is in love with his secretary. He is afraid that his wife will find out about his affair. He is trying to convince his wife to stay with him."}
{"answers": "Ms. Dawson helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "prediction_text": "The new policy restricts all office communications to email correspondence and official memos. The use of Instant Messaging programs by employees during working hours is strictly prohibited. Employees who persist in using Instant Messaging will first receive a warning and be placed on probation. At second offense, the employee will face termination. Any questions regarding this new policy may be directed to department heads."}
{"answers": "#Person2# wants to make a reservation for a round trip flight this month. #Person1# helps #Person2# book the cheapest flight and vegetarian meals.", "prediction_text": "A man and a woman are on a flight to Helsinki, Finland. The man is trying to make a reservation for a flight to Helsinki, but the woman is having trouble. The man asks the woman if she has any questions, and she says she does. The man asks her what questions, and she says she'd like to make a reservation for a flight to Helsinki, Finland. The man asks her if she'd like to make a reservation for a flight to Helsinki, Finland, and she says she'd like to make a reservation for a flight to Helsinki, Finland. The man asks her if she'd like to make a reservation for a flight to Helsinki, Finland, and she says she'd like to make a reservation for a flight to Helsinki"}
{"answers": "#Person2# can develop #Person1#'s roll of film in six hours.", "prediction_text": "The customer is not interested in the time it takes to develop the film. He is interested in the quality of the film. The customer is not interested in the cost of the film. He is interested in the cost of the film. The customer is not interested in the quality of the film. He is interested in the quality of the film. The customer is not interested in the cost of the film. He is interested in the cost of the film. The customer is not interested in the quality of the film. He is interested in the quality of the film. The customer is not interested in the cost of the film. He is interested in the cost of the film. The customer is not interested in the quality of the film. He is interested"}
{"answers": "#Person2# suggests #Person1# ask the policeman the way to Peking University.", "prediction_text": "A man and a woman are walking along a street. The man says, \"Excuse me, could you tell me where Peking University is on this map?\" The woman says, \"Let me see. Here it is.\" The man says, \"Could you tell me how to get there?\" The woman says, \"I'm afraid I can't because I'm a stranger here.\" The man says, \"Oh, thanks all the same.\" The woman says, \"Not at all. You can ask the policeman over there.\""}
{"answers": "#Person2# shows #Person2#'s house and courtyard to #Person1# and introduces the corn ears hunging on the tree.", "prediction_text": "The two people are talking about the house. The first person says that the house is spacious and the second person says that the house is beautiful. The first person says that the corn ears are hanging on the tree branch. The second person says that the corn ears are hanging on the tree branch. The first person says that the corn ears are hanging on the tree branch. The second person says that the corn ears are hanging on the tree branch. The first person says that the corn ears are hanging on the tree branch. The second person says that the corn ears are hanging on the tree branch. The first person says that the corn ears are hanging on the tree branch. The second person says that the corn ears are hanging on the tree branch. The"}
{"answers": "#Person1# and #Person2# order chicken feet and wine.", "prediction_text": "A man and a woman are seated at a table in a restaurant. The man orders a bottle of wine and the woman orders a bottle of beer. The man then asks the woman if she'd like to order a bottle of wine or a bottle of beer. The woman replies that she'd prefer a bottle of wine. The man then asks the woman if she'd like to order a bottle of wine or a bottle of beer. The woman replies that she'd prefer a bottle of beer. The man then asks the woman if she'd like to order a bottle of wine or a bottle of beer. The woman replies that she'd prefer a bottle of beer. The man then asks the woman if she'd like to order a bottle of wine or a"}
{"answers": "#Person1# wants to take a date to a restaurant. #Person2# recommends the Gramercy Tavern.", "prediction_text": "A man and a woman are on a date. The man is a hotel guest, and the woman is a guest at the hotel restaurant. The man is trying to decide what to order for dinner. The woman is trying to decide what to order for dinner. The man is trying to decide what to order for dinner. The woman is trying to decide what to order for dinner. The man is trying to decide what to order for dinner. The woman is trying to decide what to order for dinner. The man is trying to decide what to order for dinner. The woman is trying to decide what to order for dinner. The man is trying to decide what to order for dinner. The woman is trying to decide what to order for dinner. The man"}
{"answers": "Anne didn't enjoy the date with #Person2# last time, she rejects #Person2#'s invitation firmly.", "prediction_text": "A man and a woman meet for the first time. They have a great time. They laugh and talk all night. They have a great time. They laugh and talk all night. They have a great time. They laugh and talk all night. They have a great time. They laugh and talk all night. They have a great time. They laugh and talk all night. They have a great time. They laugh and talk all night. They have a great time. They laugh and talk all night. They have a great time. They laugh and talk all night. They have a great time. They laugh and talk all night. They have a great time. They laugh and talk all night. They have a great time. They laugh and"}
{"answers": "#Person1# helps #Person2# to find a table in the centre and order some food at Beijing Tasty Restaurant.", "prediction_text": "The customer is not always right."}
{"answers": "#Person2# tells #Person1# he falls in love with the girl living under him and she's very popular. #Person2#'s going to call her now and invite her to dinner tonight.", "prediction_text": "A man and a woman meet and start dating. The man is a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman is not. The man is also a bit of a flirt, and the woman"}
{"answers": "Ballam asks for a position with 4,000 yuan starting salary from #Person1#, given that 3,000 yuan cannot support his family.", "prediction_text": "The salary of a computer engineer is 3, 000 yuan a month. The starting salary is 4, 000 yuan a month. The lowest salary is 4, 000 yuan a month. The salary is raised to 5, 000 yuan a month after six months. The salary is 5, 000 yuan a month. The salary is 5, 000 yuan a month. The salary is 5, 000 yuan a month. The salary is 5, 000 yuan a month. The salary is 5, 000 yuan a month. The salary is 5, 000 yuan a month. The salary is 5, 000 yuan a month. The salary is 5,"}
{"answers": "#Person1# is interviewing #Person2# and asking #Person2# some information relating the job.", "prediction_text": "#Person1#: Have you ever done this kind of work before?"}
{"answers": "#Person1# and #Person2# will call an emergency meeting today even #Person2# doesn't think Ken can get back.", "prediction_text": "#Person1# and #Person2# are in a meeting. #Person1# is the person who is in charge of the meeting. #Person2# is the person who is in charge of the meeting. #Person1# is the person who is in charge of the meeting. #Person2# is the person who is in charge of the meeting. #Person1# is the person who is in charge of the meeting. #Person2# is the person who is in charge of the meeting. #Person1# is the person who is in charge of the meeting. #Person2# is the person who is in charge of the meeting. #Person1# is the person who is in charge of the meeting. #"}
{"answers": "#Person2# has worked for a company for 6 years. With much experience and new skills, #Person2# tells #Person1# that #Person2# is ready for a promotion.", "prediction_text": "#Person1#: OK, Let me just have a look at the information here. You've been with a company for 6 years."}
{"answers": "#Person1# and #Person2# talk about popular sports in their country. Rugby is popular in both countries.", "prediction_text": "In this dialogue, #Person1# and #Person2# talk about their favourite sports. #Person1# says that football is popular in his country, but that it is not as popular as it used to be. #Person2# says that football is popular in his country, but that it is not as popular as it used to be. #Person1# says that tennis is popular in his country, but that it is not as popular as it used to be. #Person2# says that tennis is popular in his country, but that it is not as popular as it used to be. #Person1# says that golf is popular in his country, but that it is not as popular as it used to be. #"}
{"answers": "#Person1# and #Person2# are concerned about environmental problems. #Person1# thinks that developing countries care more about economic development than environmental protection. #Person2# would like to join an organization committed to protecting the environment.", "prediction_text": "#Person1#: There are so many environmental problems in the world today. Do you think we can really solve them all or will destroy the world?"}
{"answers": "Emily's never done paycheck in the States before, so she asks Susan questions about it. Susan explains what the number and terms on the paycheck mean. Emily thanks Susan for her help.", "prediction_text": "In this dialogue, the narrator, Susan, is a customer at a local bank. She asks the teller, Emily, about her paycheck. Emily explains that the deductions are for federal and state income taxes, and that the health insurance and 401(K) deductions are for the bank's employee benefit programs. Emily also explains that the deductions for health insurance and the 401(K) are small, and that the bank's net pay is not as high as it would like."}
{"answers": "#Person2# is being interviewed for a music teacher. #Person1# is impressed by #Person2#'s background and knowledge of classical music. And #Person1# will try classical music.", "prediction_text": "The interview was about a music teacher. The teacher said that she likes classical music and that she listens to it while she is cooking. She also said that she listens to classical music to reduce stress. The teacher said that she can find plenty of classical music on the internet and that she can buy it cheaply online. The teacher said that she can listen to different kinds of classical music and that she can choose the one that she likes the most."}
{"answers": "Mr. Sellers instructs Steven to solve the power failure by replacing bad fuses.", "prediction_text": "Steven, the tenant, is a bit confused about the power failure. He asks his landlord, Mr. Sellers, for help. Mr. Sellers explains to Steven that the power failure is caused by a blown fuse. He also tells Steven to check the fuse box in the basement. Steven goes down to the basement and finds the fuse box. He unscrews the burnt fuses and replaces them with the good ones. He calls Mr. Sellers again to check if the power is back on."}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells #Person2# she is pregnant. #Person2# feels happy. They go to the doctor. #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "A man and a woman are having a conversation about their pregnancy. The man is convinced that the baby is his, but the woman is sure that it's not. The man is worried that the woman is lying, but the woman is sure that the man is lying. The man is convinced that the woman is lying, but the woman is sure that the man is lying. The man is convinced that the woman is lying, but the woman is sure that the man is lying. The man is convinced that the woman is lying, but the woman is sure that the man is lying. The man is convinced that the woman is lying, but the woman is sure that the man is lying. The man is convinced that the woman is lying, but the"}
{"answers": "#Person1# helps #Person2# order a hard-boiled egg with dark toast and orange juice to follow.", "prediction_text": "A man and a woman are having breakfast together. The man asks the woman how she would like her eggs. The woman replies that she would like them hard-boiled. The man asks the woman if she would like her toast light or dark. The woman replies that she would like her toast dark. The man asks the woman if she would like her eggs and toast later. The woman replies that she would like them later. The man asks the woman if she would like anything else. The woman replies that she would like a boiled egg with toast and orange juice. The man asks the woman if she would like her eggs and toast later. The woman replies that she would like them later. The man asks the woman if she would like anything else."}
{"answers": "#Person2# fails to hold Mr. Fox's reservation for a mid-size vehicle. Mr. Fox thinks it unreasonable. #Person2# offers Mr. Fox a compact instead and offer him a discount for his costs.", "prediction_text": "The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy. The customer is not happy."}
{"answers": "#Person2# bought a personal stereo at #Person1#'s shop, but #Person2# found there was a scratch and other people could hear noise from it. #Person1# will exchange it for another model if #Person2# can provide the receipt.", "prediction_text": "The customer is not happy with the product. The shopkeeper is not happy with the customer. The shopkeeper is not happy with the product. The customer is not happy with the shopkeeper. The shopkeeper is not happy with the customer. The customer is not happy with the shopkeeper. The shopkeeper is not happy with the customer. The customer is not happy with the shopkeeper. The shopkeeper is not happy with the customer. The customer is not happy with the shopkeeper. The shopkeeper is not happy with the customer. The customer is not happy with the shopkeeper. The shopkeeper is not happy with the customer. The customer is not happy with the shopkeeper. The shopkeeper is not happy with the customer. The"}
{"answers": "The International Student Office calls Nathaniel to inform him that he has to book another appointment with someone else as Ms. Collins is not available. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "Ms. Collins is not available today. Ms. Fonda is available at 11:30."}
{"answers": "#Person1# has been washing dishes for over a week and #Person2# will do the dishes next week.", "prediction_text": "The two people in this dialogue are discussing who should do the dishes. The first person says that they have been doing the dishes for a week, and the second person agrees. The first person says that they have been doing the dishes for a week, and the second person agrees. The first person says that they have been doing the dishes for a week, and the second person agrees. The first person says that they have been doing the dishes for a week, and the second person agrees. The first person says that they have been doing the dishes for a week, and the second person agrees. The first person says that they have been doing the dishes for a week, and the second person agrees. The first person says that they have been doing the"}
{"answers": "Greg Sonders from Brown College calls the applicant Mary to ask whether she is interested in college sports and will make a further decision later.", "prediction_text": "The speaker is a college admissions officer. He is talking to a student who has just applied to Brown College. The student is interested in playing college sports. The admissions officer asks the student if he would be interested in playing college sports. The student says that he would be interested. The admissions officer asks the student if he plays any other sports. The student says that he also plays volleyball. The admissions officer asks the student if he has any other interests. The student says that he also plays basketball. The admissions officer asks the student if he has any other interests. The student says that he also plays volleyball. The admissions officer asks the student if he has any other interests. The student says that he also plays basketball. The admissions officer asks the student"}
{"answers": "#Person2# recommends a personal tour guide service to #Person1#.", "prediction_text": "A friend in New York is a personal tour guide service that helps you create a perfect trip for you according to your budget."}
{"answers": "Karren and Tim say goodbye.", "prediction_text": "Tim and Karren meet for the first time. Tim is a new employee at the company. Karren is a new employee at the company. They are both assigned to the same team. They are both assigned to the same team. They are both assigned to the same team. They are both assigned to the same team. They are both assigned to the same team. They are both assigned to the same team. They are both assigned to the same team. They are both assigned to the same team. They are both assigned to the same team. They are both assigned to the same team. They are both assigned to the same team. They are both assigned to the same team. They are both assigned to the same team. They are both assigned"}
{"answers": "Tim and Karren say farewell words after meeting.", "prediction_text": "Tim and Karren meet for the first time. Tim is a new employee at the company. Karren is a new employee at the company. They are both assigned to the same team. They are both assigned to the same project. They are both assigned to the same department. They are both assigned to the same floor. They are both assigned to the same building. They are both assigned to the same city. They are both assigned to the same state. They are both assigned to the same country. They are both assigned to the same continent. They are both assigned to the same country. They are both assigned to the same continent. They are both assigned to the same country. They are both assigned to the same continent. They are both assigned"}
{"answers": "Benjamin has trouble writing a project report. #Person1# teaches him that a good report involves a final conclusion on his work of this year, experiences in dealing with some emergent events, plan for the next year, and the format.", "prediction_text": "The report is a kind of document that is required to be written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students in the university. It is a kind of report that is written by students"}
{"answers": "#Person1# pays #Person2# by credit card for some clothes.", "prediction_text": "The customer is not always right."}
{"answers": "Judy explains to #Person1# about the budget for a trip and tries to lower the cost by choosing a economic hotel.", "prediction_text": "The two people are discussing the budget of their trip. The first person is worried about the transportation fee, and the second person is worried about the accommodation. The two people are not in agreement."}
{"answers": "#Person1# and #Person2# discuss grandpa's birthday. They decide on where and when to hold the party and what food and gift to prepare.", "prediction_text": "The two people in this dialogue are planning a surprise birthday party for their grandfather. The first person, #Person1#, suggests that they have a party at their house, but the second person, #Person2#, suggests that they have it at a hotel. The first person suggests that they should have a party at their house because it is too cold for a garden party at this time of year. The second person suggests that they should have a party at a hotel because it is too expensive and it is too cold for a garden party at this time of year. The first person suggests that they should have a party on Friday because it is the best day for a party. The second person suggests that they should have a party on Saturday because it is"}
{"answers": "Charlie helps Myrdal to think over where Myrdal lost the wallet.", "prediction_text": "The two people are arguing about a lost purse. The first person says that the purse disappeared, and the second person says that the purse did not disappear. The first person says that the purse disappeared when they left the house, and the second person says that the purse disappeared when they started to get the hotdog. The first person says that they had the purse when they left the house, and the second person says that they had the purse when they started to get the hotdog. The first person says that they had the purse when they left the house, and the second person says that they had the purse when they started to get the hotdog. The first person says that they had the purse when they left the house, and the second person"}
{"answers": "#Person2# tells #Person1# about the family activities on Easter day.", "prediction_text": "A family reunion is a time for family members to get together and catch up on each other's lives. But when the family reunion is held at a restaurant, the family members are not able to get together. The family members are forced to hide the gifts they have brought to the reunion."}
{"answers": "#Person1# makes an appointment with Jane before their meeting.", "prediction_text": "A man and a woman meet for the first time. They talk about their lives and their plans for the future. They agree to meet again next week."}
{"answers": "#Person2# liked Michael's motorcycle but won't buy one.", "prediction_text": "The two friends had a discussion about the safety of motorcycles and bicycles. The first person said that he liked the new motorcycle and tried it. The second person said that he liked the new bicycle and tried it. The first person said that he was afraid of the new motorcycle. The second person said that he was afraid of the new bicycle. The first person said that he would like to buy a motorcycle himself. The second person said that he would like to buy a bicycle himself. The first person said that he was afraid of the new motorcycle. The second person said that he was afraid of the new bicycle. The first person said that he would like to buy a motorcycle himself. The second person said that he would like to buy a bicycle himself."}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells him that she's pregnant. They go to see the doctor. However, #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "A man and a woman are having a conversation about their pregnancy. The man is convinced that the baby is his, but the woman is sure that it's not. The man is worried that the woman is lying, but the woman is sure that the man is lying. The man is convinced that the woman is lying, but the woman is sure that the man is lying. The man is convinced that the woman is lying, but the woman is sure that the man is lying. The man is convinced that the woman is lying, but the woman is sure that the man is lying. The man is convinced that the woman is lying, but the woman is sure that the man is lying. The man is convinced that the woman is lying, but the"}
{"answers": "#Person1# comes over to say hello to Mrs. Todd, and will go shopping afterwards.", "prediction_text": "A man and a woman meet in the garden of a house they have just moved into. They talk about their lives and their plans for the future."}
{"answers": "#Person2# cannot understand abstract art so #Person2# decides to go to an art exhibition of greek and roman sculpture with #Person1# tomorrow.", "prediction_text": "#Person1#: I love going to art exhibitions. I hardly know anything about art or sculpture. #Person2#: I never understand the meaning of those painting. They are too abstract for me. #Person1#: Art isn't for everyone. I'm going to an exhibition tomorrow at the national gallery. It's an exhibition of greek and roman sculpture. #Person2#: I like sculpture, especially that form ancient rome or Greece. What time are you thinking of going? I'd love to go with you. #Person1#: I thought I'd have an early lunch and go immediately afterwards. Does that sound ok to you? Bus 51 goes directly there. #Person2#: That sounds fine. What time shall we meet"}
{"answers": "#Person1# cannot stand rainy days, but #Person2#'s been used to it.", "prediction_text": "The weather is really miserable. It has been raining hard all day long. Yes, it's so cold. Do you think the rain is going to let up soon? Yes, I think so. The rainy season will be over soon. How long does the rainy season last? About two months. I can hardly stand these wet and cold days. It seems that it doesn't bother you very much. I'm used to this kind of days. But I prefer warm weather. Spring will come soon. And we will have some pleasant weather then. I hope so."}
{"answers": "#Person1# helps #Person2# to order a drink and some food.", "prediction_text": "The waiter asked the customer if he wanted anything to drink. The customer said yes, and the waiter asked if he wanted an appetizer. The customer said yes, and the waiter asked if he wanted anything else. The customer said yes, and the waiter asked if he wanted anything else. The customer said yes, and the waiter asked if he wanted anything else. The customer said yes, and the waiter asked if he wanted anything else. The customer said yes, and the waiter asked if he wanted anything else. The customer said yes, and the waiter asked if he wanted anything else. The customer said yes, and the waiter asked if he wanted anything else. The customer said yes, and the waiter asked if he wanted anything else. The customer"}
{"answers": "#Person1# interviews Mr. Kayne why he ran a bicycle shop. He says he loves it to be his own boss. His friends helped him and they could play when there were no customers.", "prediction_text": "Mr. Steven Kayne, who has just taken over and established bicycle shop, tells us what made him want to run his own store. He says he loves racing bikes and fixing them. He knew then that as soon as he had enough money to get his own business going, he'll do it. He had his heart set on it and he didn't let anything stand in his way. When he went down to the bank and got a business loan, he knew he'd love being his own boss. Now his time is his own. He opens the store when he wants and leaves when he wants. He has hired a couple of friends of his who love biking as much as he does. They help him out a few days a week."}
{"answers": "#Person2# bargains with #Person1# on a gold watch.", "prediction_text": "The two people are in a shop. The first person says to the second person, \"I would like to look at some watches.\" The second person says, \"I would like to look at some gold watches.\" The first person says, \"May I show you the gold ones?\" The second person says, \"Pure gold ones, please.\" The first person says, \"OK. Here they are.\" The second person says, \"What is the price for this one?\" The first person says, \"One thousand yuan.\" The second person says, \"That's quite steep. Can you come down a little bit?\" The first person says, \"I'm afraid not. The price is very moderate.\" The second person says, \"It's rather"}
{"answers": "#Person2# tells #Person1# #Person2# enjoyed the weekend organized by #Person1#.", "prediction_text": "#Person1# and #Person2# are two people who have been invited to a party. #Person1# has enjoyed the party very much. #Person2# has enjoyed the party very much too."}
{"answers": "Frank invites Besty to the party to celebrate his big promotion. Besty couldn't wait for the party.", "prediction_text": "This is a great example of how to use the \"I\" and \"you\" forms of the verb \"to be\" in a conversation. The \"I\" form is used to talk about yourself, and the \"you\" form is used to talk about others. The \"I\" form is used to talk about yourself, and the \"you\" form is used to talk about others. The \"I\" form is used to talk about yourself, and the \"you\" form is used to talk about others. The \"I\" form is used to talk about yourself, and the \"you\" form is used to talk about others. The \"I\" form is used to talk about yourself, and the \"you\" form is used to"}
{"answers": "#Person1# is giving #Person2# some advice for upgrading #Person2#'s system, such as adding a painting program and a faster processor.", "prediction_text": "#Person1#: Have you considered upgrading your system?"}
{"answers": "Mr. Lee signs his parcel and has something to express.", "prediction_text": "A man and a woman are talking to each other. The man is looking for his parcel. The woman is looking for her signature."}
{"answers": "#Person1# buys shoes from #Person2#.", "prediction_text": "#Person1# wants a pair of enameled leather shoes in size 30. #Person2# offers a pair of shoes that are a little tight. #Person1# says that he will take the shoes. #Person2# says that the shoes will stretch a bit with use."}
{"answers": "#Person1# wants to go to Peking University. #Person2# suggests asking the policeman.", "prediction_text": "A man and a woman are walking along a street. The man says, \"Excuse me, could you tell me where Peking University is on this map?\" The woman says, \"Let me see. Here it is.\" The man says, \"Could you tell me how to get there?\" The woman says, \"I'm afraid I can't because I'm a stranger here.\" The man says, \"Oh, thanks all the same.\" The woman says, \"Not at all. You can ask the policeman over there.\""}
{"answers": "#Person2# claims #Person2# bought a personal stereo from #Person1#'s shop but the product has two problems that #Person2# is not happy with. #Person1# offers to exchange it for another one, but #Person2# has a problem with the receipt.", "prediction_text": "The customer is not happy with the product. The shopkeeper is not happy with the customer. The shopkeeper is not happy with the product. The customer is not happy with the shopkeeper. The shopkeeper is not happy with the customer. The customer is not happy with the shopkeeper. The shopkeeper is not happy with the customer. The customer is not happy with the shopkeeper. The shopkeeper is not happy with the customer. The customer is not happy with the shopkeeper. The shopkeeper is not happy with the customer. The customer is not happy with the shopkeeper. The shopkeeper is not happy with the customer. The customer is not happy with the shopkeeper. The shopkeeper is not happy with the customer. The"}
{"answers": "Ann interviews Robin at a university radio. Robin talks about the mountain lake cycle tour and their method to avoid roadblock problem.", "prediction_text": "The Martha Bicycle Club is a group of cyclists who cycle from Ottawa to the mountain lake in the summer. The tour is a race, but the cyclists are not racing against each other. They are racing against their own time. The cyclists are divided into groups and start at different times. The first group leaves at seven o'clock in the morning. The cyclists are divided into groups and start at different times. The first group leaves at seven o'clock in the morning. The cyclists are divided into groups and start at different times. The first group leaves at seven o'clock in the morning. The cyclists are divided into groups and start at different times. The first group leaves at seven o'clock in the morning. The cyclists are divided into groups and start at"}
{"answers": "The doctor prescribes some medicine for Mr. Smith and tells him where to get it.", "prediction_text": "A man comes to a doctor's office with a nasty infection. The doctor gives him some antibiotics and a cream to ease the itching and burning. The man goes to the pharmacy and gets a prescription for the antibiotics and the cream. The man goes to the pharmacy and gets a prescription for the antibiotics and the cream. The man goes to the pharmacy and gets a prescription for the antibiotics and the cream. The man goes to the pharmacy and gets a prescription for the antibiotics and the cream. The man goes to the pharmacy and gets a prescription for the antibiotics and the cream. The man goes to the pharmacy and gets a prescription for the antibiotics and the cream. The man goes to the pharmacy and gets a prescription for the antibiotics and the cream. The man"}
{"answers": "#Person2# is ordering lemonade, barbeque wings, and baby-back ribs, and #Person1# serves #Person2# gently.", "prediction_text": "The waiter is trying to get the customer to order more food. The customer is trying to get the waiter to get him more drinks. The customer is trying to get the waiter to get him more food. The customer is trying to get the waiter to get him more drinks. The customer is trying to get the waiter to get him more food. The customer is trying to get the waiter to get him more drinks. The customer is trying to get the waiter to get him more food. The customer is trying to get the waiter to get him more drinks. The customer is trying to get the waiter to get him more food. The customer is trying to get the waiter to get him more drinks. The customer is trying to get the waiter to get him"}
{"answers": "#Person1# inquires Mary about the price of products online and their payments. Mary teaches #Person1# how to shop online.", "prediction_text": "The two people talked about online shopping. The first person said that she often does her shopping online. The second person said that she is used to online shopping. The first person said that she can get the same product at a much lower price. The second person said that she can buy anything online. The first person said that she should open an account at the online bank first. The second person said that there are many things online. The first person said that she can find everything all over the world. The second person said that she can buy anything online."}
{"answers": "#Person2# gives #Person1# a book with English songs.", "prediction_text": "#Person1# and #Person2# are two people who are trying to find English songs. #Person1# is a student who is trying to find English songs for his English class. #Person2# is a librarian who is trying to find English songs for her English class. #Person1# and #Person2# are trying to find English songs for their English class."}
{"answers": "#Person1# has a dance with Brian at Brian's birthday party. Brian thinks #Person1# looks great and is popular.", "prediction_text": "A man and a woman are having a party. The man is happy to see his friend, but the woman is not. The man asks the woman to dance with him. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to"}
{"answers": "Tim's project is about how to lead a greener life. #Person1# consults about his motivation and his findings at school. Tim finds there are many aspects that can be improved for his school.", "prediction_text": "Tim and his family have decided to lead a greener life. They have started to use less paper and to send in their work electronically. They have also decided to have meatless Mondays."}
{"answers": "Ted likes Jenny but is afraid to ask her out. Mike encourages Ted to tell Jenny his feelings.", "prediction_text": "Ted and Ted's friend, Mike, are having a conversation about a girl named Jenny. Ted is afraid to ask her out because he doesn't know how to tell her. Mike tells him to just tell her what he's feeling about her."}
{"answers": "#Person1# and #Person2# are enjoying the weather and may go to the beach this weekend.", "prediction_text": "The weather is nice today, so we should go outside and have our lunch. The weather is nice today, so we should go outside and have our lunch."}
{"answers": "#Person1# takes an interview workshop. #Person2# offer #Person1# some useful tips on getting ready for an interview.", "prediction_text": "The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the"}
{"answers": "Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies.", "prediction_text": "The two friends, Frank and Mary, are talking about their favorite movies. Frank says he watches a lot of movies, and Mary says she watches whatever there is. Frank says he likes art films, and Mary says she likes thrillers. Frank says he goes to the cinema once in a while, and Mary says she rents movies at Movie Salon. Frank says he likes Movie Salon, and Mary says she has a membership there. Frank says he might also sign up for its membership, and Mary says she might, too."}
{"answers": "#Person2# wants a pair of Jordan shoes in size 41.", "prediction_text": "#Person1#: Can I help you? #Person2#: Yes, I want a pair of sport shoes, Jordan. #Person1#: What size do you want? #Person2#: Size 41. #Person1#: Here you are. #Person2#: They fit me. Can you make it a little cheaper? #Person1#: It's already on sale now."}
{"answers": "#Person1# and #Person2# are talking about the founding time and founder of the New York Times, the Washington Post, and the Los Angeles Times were founded.", "prediction_text": "The Washington Post was founded in 1877 by Stilson Hutchens. The New York Times was founded in 1789 by Henry Raymond and George Jones. The Los Angeles Times was founded in 1881 by Nathan Cole Junior and Thomas Guardineer."}
{"answers": "#Person2# introduces the surroundings and information about their company to #Person1#.", "prediction_text": "#Person1#: How long does it take to get to downtown from here?"}
{"answers": "Amy called Jimmy to return his book but Jimmy went to Beihai Park for picnic so Amy didn't reach him. They will meet later.", "prediction_text": "The two people talked about their day. The first person said that he had a lovely day. The second person said that he had a lovely day too. The first person said that he had a lovely day because he had a picnic with his friends. The second person said that he had a lovely day because he had a picnic with his friends. The first person said that he had a lovely day because he had a picnic with his friends. The second person said that he had a lovely day because he had a picnic with his friends. The first person said that he had a lovely day because he had a picnic with his friends. The second person said that he had a lovely day because he had a picnic with his friends. The first person said that"}
{"answers": "#Person2# orders a pizza delivery at Pizza House. Marty promises it will arrive in thirty minutes or it's free.", "prediction_text": "A man and a woman are at a pizza place. The man orders a large pizza with pepperoni, olives, and extra cheese. The woman orders a large pizza with seafood. The man is very happy with his pizza. The woman is very happy with her pizza. The man is very happy with his pizza. The woman is very happy with her pizza. The man is very happy with his pizza. The woman is very happy with her pizza. The man is very happy with his pizza. The woman is very happy with her pizza. The man is very happy with his pizza. The woman is very happy with her pizza. The man is very happy with his pizza. The woman is very happy with her pizza. The man is very happy"}
{"answers": "Tom tells Catherine he had an underdone sandwich for lunch. Catherine doesn't like fast food because she thinks it's not healthy. She explains that two-thirds of Americans may avoid places like McDonald's and KFC.", "prediction_text": "The two people had a conversation about fast food. The first person said that he didn't like fast food because it was unhealthy. The second person said that he didn't like fast food because it was popular in America."}
{"answers": "#Person1# first introduces the 2006 fiscal year marketing plan then analyzes their performance and explains with the sale graph.", "prediction_text": "The sales in 2006 were much better than the previous year. The reason for this is that we had a new marketing plan, which was a good plan. The plan was to appeal to a younger set of consumers, and also to double distribution in overseas markets. We also had a new image, which was to be more youthful and hip. The sales in 2006 were much better than the previous year. The reason for this is that we had a new marketing plan, which was a good plan. The plan was to appeal to a younger set of consumers, and also to double distribution in overseas markets. We also had a new image, which was to be more youthful and hip."}
{"answers": "#Person1# is not satisfied with #Person1#'s steak and asks to change one, and #Person2# agrees.", "prediction_text": "A man is served a steak that is too well done. He asks for it to be changed, but the waiter is not happy about it."}
{"answers": "#Person1# complains to Tony that Christmas has made #Person1# busier.", "prediction_text": "The person who is tired is #Person1# and the person who is sorry is #Person2#. The person who is tired is talking about the fact that he has to work at 8:00 AM and that he is late. The person who is sorry is talking about the fact that the shopping center is very busy and that people are shouting and pushing him around. The person who is tired is talking about the fact that he is used to it and that he is used to selling gifts for kids before Christmas. The person who is sorry is talking about the fact that Christmas doesn't mean much to him now."}
{"answers": "#Person1# interviews #Person2# about the understanding of this company, personal preference, and salary expectation. #Person2# asks about the fringe benefits.", "prediction_text": "The interviewer asked the applicant about his current job, his salary, fringe benefits, and insurance. The applicant said he would like to work in sales department. The interviewer asked the applicant how much he would be paid if he worked in sales department. The applicant said he would be paid 2, 000 yuan per month plus some allowances and bonus. The interviewer asked the applicant how much he would be paid if he worked in sales department. The applicant said he would be paid 2, 500 yuan per month. The interviewer asked the applicant how much he would be paid if he worked in sales department. The applicant said he would be paid 2, 000 yuan per month plus some allowances and bonus. The interviewer asked the applicant how much"}
{"answers": "Mr. Kayne shares with the audience about how he took over and established his bicycle shop as a salesman and how he's running his business now.", "prediction_text": "Mr. Steven Kayne, who has just taken over and established bicycle shop, tells us what made him want to run his own store. He says he loves racing bikes and fixing them. He knew then that as soon as he had enough money to get his own business going, he'll do it. He had his heart set on it and he didn't let anything stand in his way. When he went down to the bank and got a business loan, he knew he'd love being his own boss. Now his time is his own. He opens the store when he wants and leaves when he wants. He has hired a couple of friends of his who love biking as much as he does. They help him out a few days a week."}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay the rent. Ayden is short on cash but still lends $20 to #Person1#. They decide to have dinner at #Person1#'s place.", "prediction_text": "A man and a woman are having a conversation. The man is asking the woman for money. The woman is saying that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money"}
{"answers": "#Person1# gives #Person2# a few suggestions on clothes but none is taken.", "prediction_text": "The two people are talking about a dress. The first person says that the dress is too heavy. The second person says that the dress is suitable for spring. The first person says that they have many kinds of dresses. The second person says that they have a white dress. The first person says that they would like the white dress. The second person says that they would like the white dress. The first person says that they would like the yellow dress. The second person says that they would like the yellow dress. The first person says that they would like the green dress. The second person says that they would like the green dress. The first person says that they would like the yellow dress. The second person says that they would like the yellow dress"}
{"answers": "#Person2# wants to buy a pair of Jordan shoes in size 41.", "prediction_text": "#Person1#: Can I help you? #Person2#: Yes, I want a pair of sport shoes, Jordan. #Person1#: What size do you want? #Person2#: Size 41. #Person1#: Here you are. #Person2#: They fit me. Can you make it a little cheaper? #Person1#: It's already on sale now."}
{"answers": "#Person1# and #Person2# exchange opinions toward the effect of earthquake and how people feel about it.", "prediction_text": "The author of this article is a Chinese journalist. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of the China Daily. He is a reporter of"}
{"answers": "Mr. Murray wants a library card. #Person2# issues one to him after checking his application and driver license.", "prediction_text": "The library card application process is a two-step process. The first step is to fill out the application form. The second step is to fill out the application form and pay the fee. The library card application process is a two-step process. The first step is to fill out the application form. The second step is to fill out the application form and pay the fee. The library card application process is a two-step process. The first step is to fill out the application form. The second step is to fill out the application form and pay the fee. The library card application process is a two-step process. The first step is to fill out the application form. The second step is to fill out the application form and pay the fee. The library"}
{"answers": "Frank explains to Judy the reason why he took a job at the Post office is that the government offers excellent health insurance benefits for its employees.", "prediction_text": "A man and a woman are talking about their jobs. The man says that he has a heavy work schedule, and the woman says that she has excellent health insurance benefits. The man says that he wants to work for the Post Office because it offers excellent health insurance benefits. The woman says that she wants to work for the Post Office because her children can get free medical care. The man says that he knows why he wants to work for the Post Office. The woman says that she knows why she wants to work for the Post Office."}
{"answers": "#Person1# hosts the week's People You Meet, featuring Mark Leach. Mark introduces his work as an information officer in London.", "prediction_text": "Mark Leach is an information officer at the Britain Business Center in London. He speaks 13 languages and offers tourist information to visitors from overseas."}
{"answers": "#Person2# is going to set up #Person2#'s own law office. #Person1# wishes #Person2# good luck.", "prediction_text": "A lawyer and a client discuss the advantages of having an office of one's own."}
{"answers": "The workmen are walking across the wet cement to put up the notice.", "prediction_text": "The two boys are discussing the wet cement on the floor. #Person1# says that he won't touch it, but #Person2# says that he won't either. They then discuss the workmen who left the wet cement without a notice. #Person1# says that the headmaster asked them not to do so, but #Person2# says that they went to their stores to get a notice. #Person1# says that they forgot about the wet cement and are walking across it to put up the notice. #Person2# says that they are not doing so."}
{"answers": "#Person1# asks #Person2# about the instructions for getting and using the discount coupons.", "prediction_text": "The supermarket has a special discount coupon for sugar. The customer can buy 3 bags of sugar and get 3 coupons. The customer can use the coupons to buy goods in the supermarket. The customer can use the coupons within one year."}
{"answers": "Ann interviews Robin about the mountain lake cycle tour. Robin introduces the event and explains its settings of the tour.", "prediction_text": "The Martha Bicycle Club is a group of cyclists who cycle from Ottawa to the mountain lake in the summer. The tour is a race, but the cyclists are not racing against each other. They are racing against their own time. The cyclists are divided into groups and start at different times. The first group leaves at seven o'clock in the morning. The cyclists are divided into groups and start at different times. The first group leaves at seven o'clock in the morning. The cyclists are divided into groups and start at different times. The first group leaves at seven o'clock in the morning. The cyclists are divided into groups and start at different times. The first group leaves at seven o'clock in the morning. The cyclists are divided into groups and start at"}
{"answers": "#Person1# gets lost on the way to Liu Ichang. #Person2# tells #Person1# to take Bus No. 713 because it's far.", "prediction_text": "A man is lost in a city. He asks a woman for directions. She tells him to go to the Olympic Park. He asks her how to get there. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No"}
{"answers": "#Person1# and #Person2# are planning the places of interest they are going to visit in London, such as Nelson's Column, Westminster Abbey, Tower of London, Madame Tussaud's Waxworks Museum, and so on. They are both looking forward to it.", "prediction_text": "The two people are talking about London. The first person says that London is a historic city. The second person says that there is a lot to see in London. The first person says that he wants to see the crown jewels. The second person says that he has seen the crown jewels before. The first person says that he wants to see the famous tower of London. The second person says that he has seen the tower of London before. The first person says that he wants to see the famous castle and prison. The second person says that he has seen the castle and prison before. The first person says that he wants to see the famous waxworks museum. The second person says that he has seen the waxworks museum before. The first person says"}
{"answers": "#Person1# offers some job choices to #Person2#, but #Person2# doesn't like them.", "prediction_text": "#Person1# and #Person2# are two people who are considering different career paths. #Person1# is interested in working in the media, while #Person2# is interested in working with computers. #Person1# is interested in working in the media, but he thinks it would be stressful. #Person2# is interested in working with computers, but he thinks it would be exciting. #Person1# is interested in working in the media, but he thinks it would be fun. #Person2# is interested in working with computers, but he thinks it would be exciting. #Person1# is interested in working in the media, but he thinks it would be fun. #Person2# is interested in working with"}
{"answers": "Tom calls Sara for her help to take care of his son Ken, because he is taking his daughter to the hospital.", "prediction_text": "A man and a woman are talking on the phone. The man says, \"I'm sorry to bother you at supper time.\" The woman says, \"Not at all.\" The man says, \"My little girl Maria has a high fever. We're taking her to hospital in a short time.\" The woman says, \"I'm sorry to hear that. Is there anything I can do for you?\" The man says, \"Do you mind taking care of my son Ken? We can't take him along.\" The woman says, \"OK. Can I bring him to my house?\" The man says, \"Thank you. But he hasn't finished his dinner yet.\" The woman says, \"No problem. He can have dinner with us,"}
{"answers": "#Person1# drives #Person2# from the airport to an inn and they have a casual talk about themselves.", "prediction_text": "A young man and a young woman meet at a Holiday Inn in Los Angeles. They are both on business trips, and they are both from Mexico. They are both 26 years old, and they are both fluent in Spanish. They are both very nice, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are"}
{"answers": "#Person1# has difficulty getting access to the computers in the library to do #Person1#'s assignment.", "prediction_text": "The two people are frustrated with the way they are being treated by the library. They are both looking forward to the day when they can afford to get their own computers."}
{"answers": "#Person1# and #Person2# talk about Abraham Lincoln and his glorious history. They both admire him.", "prediction_text": "Abraham Lincoln was a great man. He was a great president, and he was a great man. He was a great man because he was a great president. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great"}
{"answers": "Sarah is upset and complains to #Person1# about an inefficient meeting and Bob's interruption. #Person1# gives Sarah some suggestions on how to keep her speech short and sweet.", "prediction_text": "The meeting was not very productive because the chair was too strong and did not listen to the other people's ideas. The meeting was not efficient because the chair did not keep things short and sweet. The meeting was not efficient because the chair did not explain the main points. The meeting was not efficient because the chair did not bring up the main points after the meeting. The meeting was not efficient because the chair did not explain the main points to the people who were directly involved. The meeting was not efficient because the chair did not keep things short and sweet. The meeting was not efficient because the chair did not explain the main points during the meeting. The meeting was not efficient because the chair did not bring up the main points after the meeting. The meeting"}
{"answers": "Marquet suggests #Person1# take a good introductory course for non-science majors.", "prediction_text": "#Person1#: Marquet, do you think I should enroll in the science course?"}
{"answers": "#Person2# thinks #Person2#'s meal as perfect and orders dessert and tea to share with friends.", "prediction_text": "The dialogue above is a conversation between two people, #Person1# and #Person2#. The first part of the dialogue is a simple meal order. The second part is a dessert order. The third part is a dessert split order. The fourth part is a dessert order for two people. The fifth part is a dessert order for two people and a coffee order. The sixth part is a dessert order for two people and a tea order. The seventh part is a dessert order for two people and a coffee order. The eighth part is a dessert order for two people and a tea order. The ninth part is a dessert order for two people and a coffee order. The tenth part is a dessert order for two people and a tea order. The"}
{"answers": "#Person2# describes to #Person1# about the relationship with the boss and what their boss strengths lie on.", "prediction_text": "#Person1#: I think he is a very good leader because he is very good at making decisions and he is very good at analyzing information."}
{"answers": "#Person1# can tell #Person2# their organization's decision by phone or e-mail in about one week.", "prediction_text": "The dialogue is a good example of a dialogue that is not a conversation. The dialogue is a conversation that is not a dialogue. The dialogue is a conversation that is not a conversation. The dialogue is a conversation that is not a conversation. The dialogue is a conversation that is not a conversation. The dialogue is a conversation that is not a conversation. The dialogue is a conversation that is not a conversation. The dialogue is a conversation that is not a conversation. The dialogue is a conversation that is not a conversation. The dialogue is a conversation that is not a conversation. The dialogue is a conversation that is not a conversation. The dialogue is a conversation that is not a conversation. The dialogue is a conversation that is not a conversation. The dialogue is"}
{"answers": "Ms. Dawson takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists.", "prediction_text": "The new policy restricts all office communications to email correspondence and official memos. The use of Instant Messaging programs by employees during working hours is strictly prohibited. Employees who persist in using Instant Messaging will first receive a warning and be placed on probation. At second offense, the employee will face termination. Any questions regarding this new policy may be directed to department heads."}
{"answers": "#Person1# is starving and wants to have authentic Chinese cuisine so #Person2# recommends some. #Person1# chooses the Quanjude restaurant as #Person2# wants to try the Beijing roast duck and it's nearby.", "prediction_text": "#Person1#: Oh, I'm starving. It's my first time to China. And I'd like to try some real Chinese cuisine. What would you recommend?"}
{"answers": "#Person1# wants to start #Person1#'s own business, but #Person2# warns #Person1# of the hassle. #Person2# tells #Person1# what is needed in a business plan and #Person1# decides to stick to the old job for now.", "prediction_text": "A business plan is a document that outlines the details of a business idea. It is a written document that includes a description of the business, its goals, and the steps needed to achieve those goals. A business plan is a crucial document for any business, as it provides a roadmap for the business to follow and helps investors understand the business's potential."}
{"answers": "#Person2# is teaching #Person1# how to use an ATM step by step because #Person1# doesn't know how to use.", "prediction_text": "This is a great way to teach a new skill to a person who is new to the country. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how to use a new skill. It is also a great way to teach a person how"}
{"answers": "#Person1# confirms Jack's reservation for a double for three nights. Jack checks in.", "prediction_text": "The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with the service. The customer is not satisfied with"}
{"answers": "#Person2# wants to have his coffee with his dinner and his order for dessert later.", "prediction_text": "The customer service representative is polite and helpful. The customer is satisfied with the service."}
{"answers": "Nathan is going to Chicago for his practice in Chicago Tribune. His mother is worried about him and keeps asking him questions about this internship. Nathan says he'll work with a regular writer to get experience and she's finally at ease.", "prediction_text": "#Person1#: Well, you must be happy, Nathan you're almost ready to go to Chicago for your practice. We're sure going to miss you around here."}
