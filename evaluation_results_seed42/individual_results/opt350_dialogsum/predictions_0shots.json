{"answers": "#Person1# and #Person2# talk about pilgrims around the world, including Muslims' pilgrimage to mecca and Christians' pilgrimage to Canterbury or Vatican. #Person2# thinks faith heals people instead of magical places.", "prediction_text": "#Person1#: I'm going to go to the mosque."}
{"answers": "#Person1# is sending a package with the help of #Person2#.", "prediction_text": "#Person1#: I want to send this package by first-class mail."}
{"answers": "#Person1# invites <PERSON> to picnic this weekend. <PERSON> accepts.", "prediction_text": "I'm a little late for this one, but I'm going to try to get it done before the weekend."}
{"answers": "#Person1# makes drinks for #Person2# at a bar.", "prediction_text": "#Person1#: I'm sorry, but I can't help you."}
{"answers": "<PERSON> and <PERSON> are talking about their favourite subjects.", "prediction_text": "#Person1#: <PERSON> is a very different person from <PERSON>. She is very different from <PERSON>."}
{"answers": "#Person2# tells #Person1# that he falls in love with his neighbour and #Person1# thinks he has some competition and wishes him good luck.", "prediction_text": "#Person1#: I'm not sure if I should be happy or sad."}
{"answers": "Mr. <PERSON> asks #Person1# to help find his train ticket, but #Person1# finds out that Mr. <PERSON> didn't have any proof of purchase. Thus Mr. <PERSON> brings another ticket in the end.", "prediction_text": "#Person1#: I'm sorry, sir. I'm really sorry. But you don't have any proof of purchase. You'll simply have to purchase another ticket."}
{"answers": "#Person1# congratulates #Person2# on #Person2#'s new venture and expresses #Person1#'s willingness to help.", "prediction_text": "#Person1#: I'm going to set up my own law office, aren't you?"}
{"answers": "#Person1# gets a crank call and is angry about it.", "prediction_text": "#Person1#: Hello?"}
{"answers": "#Person1# introduces the weekend driving courses to #Person2#. #Person2# feels satisfied with its settings.", "prediction_text": "#Person1#: I am very happy to be here."}
{"answers": "#Person1# and #Person2# are talking about the most fundamental concepts underlying the accounting process.", "prediction_text": "#Person1#: What is the most fundamental concept underlying the accounting process?"}
{"answers": "#Person1# and #Person2# are talking about the low temperature at night, although spring has come.", "prediction_text": "#Person1#: I think spring is finally here."}
{"answers": "Mark asks Maggie for her notes because Mark's notes aren't good. They decide to be study partners and study together.", "prediction_text": "#Person1#: I'm a little tired."}
{"answers": "Joe doesn't think highly of Tom's new assistant while Tom does.", "prediction_text": "Person 1: I'm a little confused."}
{"answers": "#Person1# and #Person2# are talking about the popular sports in their countries, including football, basketball, tennis, swimming, golf, extreme sports and rugby.", "prediction_text": "#Person1#: I think most people like football. More boys like football than girls. A few girls play it really well. Some people like playing basketball."}
{"answers": "#Person1# thinks #Person2# has chicken pox and warns #Person2# about the possible hazards but #Person2# thinks it will be fine.", "prediction_text": "#Person1#: I'm not sure what's wrong with me. I feel like I'm coming down with something. I think I may be coming down with something."}
{"answers": "#Person1# gives #Person2# a physical check and advises #Person2# to stay in hospital for a shoulder X-ray the next day.", "prediction_text": "#Person1#: I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry. . . . I'm sorry."}
{"answers": "People in #Person2#'s country like football, basketball, tennis, and swimming. People in #Person1#'s country like golf. A few people like extreme sports. Both people like rugby.", "prediction_text": "#Person1#: I think most people like football. More boys like football than girls. A few girls play it really well. Some people like playing basketball."}
{"answers": "Sue doesn't eat cakes and sandwiches at her birthday party to avoid being allergic. Bill is willing to order her some hot soup to make her feel better. They decide to go to the restaurant later.", "prediction_text": "#Person1#: I'm not sure I can eat all of the sandwiches Jill brought."}
{"answers": "#Person2# tells #Person1# #Person2#'s writing experience.", "prediction_text": ": I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer."}
{"answers": "#Person1# forgot to take an umbrella while it's raining. #Person2# shares an umbrella with #Person1#.", "prediction_text": "#Person1#: I am going to the Garden Hotel."}
{"answers": "#Person1# comes to sign an agreement but it isn't ready until evening. So #Person1# is going through the draft.", "prediction_text": "#Person1#: I'm sorry. The agreement hasn't been fully prepared. It will be ready by tomorrow."}
{"answers": "#Person2# instructs #Person1# on how to send a package by mail.", "prediction_text": "#Person1#: I want to send this package by first-class mail."}
{"answers": "#Person2# talks about Katie's evaluation and wants Katie to do better when she doesn't have customers.", "prediction_text": ": #Person1#: I'm sorry, but I'm not going to be able to do this."}
{"answers": "#Person1# tells #Person2# that Ruojia is married and will have a party tonight. #Person2#'s surprised to know that. They will bring their gifts to bless her.", "prediction_text": "#Person1#: I'm going to the party tonight."}
{"answers": "#Person2# would like to go to Australia.", "prediction_text": "#Person1#: I've been to Australia."}
{"answers": "James and Muriel are talking while waiting for Susan, Muriel's associate. They talk about how they spent the holiday with their families.", "prediction_text": "#Person1#: Hello, are you Muriel Douglas?"}
{"answers": "Amy is talking about her first job experience and later job with #Person1#.", "prediction_text": "#Person1#: What was your first job?"}
{"answers": "#Person2# thinks #Person1#'s new suit is not worthwhile.", "prediction_text": "I'm a little disappointed in my new suit. I think it's a little too big and I think it's too tight. I'm not sure if I should get a new suit or not."}
{"answers": "#Person2# tells #Person1# the charge policy at #Person2#'s buffet.", "prediction_text": "#Person1#: I'm a little late for the buffet."}
{"answers": "#Person1# was too busy to reconfirm a fight reservation and needs to buy another ticket.", "prediction_text": "#Person1#: I am sorry. I have been so busy all these days. Well, is there any other ticket available? I want the next one."}
{"answers": "Peter stops watering the garden and will have tea with #Person1#, since it's raining.", "prediction_text": "Peter is a man of few words."}
{"answers": "#Person1# and #Person2# are discussing where to have fun, and they decide to go to the theater tonight.", "prediction_text": "#Person1#: I'm going to the theater tonight."}
{"answers": "#Person2# likes a skirt at #Person1#'s store, but thinks it too expensive.", "prediction_text": "I'm a guy, and I'm looking for a skirt. I'm not sure what to wear, but I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to wear. I'm not sure what to"}
{"answers": "Mike is describing his sister to #Person1#.", "prediction_text": "Mike is a man of few words."}
{"answers": "#Person2# feels bored with the meeting. #Person1# gives #Person2# some suggestions on how to entertain but finally asks #Person2# to keep quiet.", "prediction_text": "#Person1#: I'm not sure what to say. I'm not sure what to do. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say."}
{"answers": "It's #Person1# and #Person2#'s grandpa's birthday next week. #Person1# and #Person2# decide to have a party with jazz, pizza at their house. #Person1# and grandma will make food. #Person2# will get him a book. The party will be on Saturday.", "prediction_text": ": #Person1#: I'm going to have a party. #Person2#: I'm going to have a party. #Person1#: I'm going to have a party. #Person2#: I'm going to have a party. #Person1#: I'm going to have a party. #Person2#: I'm going to have a party. #Person1#: I'm going to have a party. #Person2#: I'm going to have a party. #Person1#: I'm going to have a party. #Person2#: I'm going to have a party. #Person1#: I'm going to have a party. #Person2#: I'm"}
{"answers": "#Person1# asks #Person2# for the way to Peak Tram and writes it down.", "prediction_text": "#Person1#: I'm sorry."}
{"answers": "#Person1# is withdrawing money from an ATM. But the ATM wrongly transfers #Person1#'s money to the World Wildlife Foundation. It drives #Person1# crazy.", "prediction_text": "#Person1#: I'm sorry, I'm not a very good person."}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. Then they want to have fried bananas and #Person1# goes out to buy some.", "prediction_text": "#Person1#: I'm going to try the yogurt."}
{"answers": "#Person1# and #Person2# talk about what kind of movies to watch and they decided to watch a war movie.", "prediction_text": "#Person1#: I'm not sure if I'm ready to go to the movies tonight."}
{"answers": "#Person1# and #Person2# feel happy because #Person1# is pregnant.", "prediction_text": "#Person1#: I'm going to be a father."}
{"answers": "#Person1# pushes #Person2# to pay for the dormitory soon, but #Person2# is thinking about living off campus. They analyze the advantages and disadvantages of living on and off campus.", "prediction_text": "#Person1#: You should have seen the line at the housing office. It took me an hour to make my dormitory deposit for next year. Have you made yours yet?"}
{"answers": "Johnson shows #Person1# the correct way to use weights machine and tells #Person1# using a card to keep track of exercise intensity. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "#Person1#: I'm ready."}
{"answers": "#Person2# has been working overtime and only gets minimum wage. #Person1# suggests #Person2# either asking for more compensation or quitting, but #Person2# refuses in fear of not being able to find another job.", "prediction_text": "#Person1#: I'm sorry, but I don't think you're qualified for this job."}
{"answers": "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks Ms. Dawson to send out a memo to all employees by the afternoon.", "prediction_text": "#Person1#: Ms. Dawson, I need you to take a dictation for me."}
{"answers": "#Person1# interviews Tom about his experience to start a company selling green products, how to live a mildly green life and to learn from mistakes.", "prediction_text": "#Person1#: What is your favorite thing about your job?"}
{"answers": "#Person1# and #Person2# talk about the weather and #Person2# invites #Person1# to have a picnic.", "prediction_text": "#Person1#: I'm glad summer is over. I like autumn best."}
{"answers": "#Person1# helps #Person2# to return a book and check out a video in the library.", "prediction_text": "#Person1#: I would like to return this book."}
{"answers": "#Person2# describes how the working days are like, including the working hours and working contents, to #Person1#.", "prediction_text": "I'm a software engineer. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work in a cubicle. I work"}
{"answers": "#Person1# and #Person2# are shocked about Sarah's love for Dick.", "prediction_text": "I was in a very bad mood."}
{"answers": "Gian and Gina introduce themselves to each other and Gian introduces the friend Robert to Gina.", "prediction_text": "#Person1#: I'm a journalist, I write articles for magazines. I'm here at this conference to research for an article on internet service providers."}
{"answers": "#Person1# asks Tom for his opinion on second-hand goods and Tom suggested #Person1# being careful.", "prediction_text": "#Person1#: I'm thinking of buying a second-hand computer for I'm short of money now."}
{"answers": "#Person1# and #Person2# go to the nightclub to dance and #Person1# prefers fast dances to slow dances.", "prediction_text": "#Person1#: I'm glad you like it. The band is called 'Four and One'. They are especially good at playing rock and roll."}
{"answers": "#Person2# will change a steak for #Person1# as that one doesn't satisfy #Person1#.", "prediction_text": "#Person1#: I'm sorry, sir."}
{"answers": "#Person1# and #Person2# both get laid off and they want to find a job.", "prediction_text": "#Person1#: I'm a little nervous about this."}
{"answers": "#Person1# begs Steven's to persuade #Person1#'s wife not to divorce him, and Steven agrees.", "prediction_text": "I am a man who has been married for ten years."}
{"answers": "#Person1# thinks it's strange of Mirella wearing casually in the office. #Person2# thinks she was influenced by the casual atmosphere in California and tells #Person1# management decided to give her a new white dress to warn her.", "prediction_text": "#Person1#: I think that Mirella is a bit of a weirdo. She's not a very good person. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good"}
{"answers": "#Person1# and #Person2# are talking about what gifts to buy for their families from the Olympic souvenir store after the volleyball match.", "prediction_text": "I'm going to go to the Olympic souvenir store and buy some souvenirs."}
{"answers": "#Person1# asks about the things that took up #Person2#.", "prediction_text": "I'm a guy who's been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in"}
{"answers": "Tom suggests going swimming, but Jane refuses. They decide to meet at the Grill to eat and then go to study together in the library.", "prediction_text": "I'm a little late for my test, but I'm still here."}
{"answers": "Mom asks May to help to prepare for the picnic and May agrees.", "prediction_text": "#Person1#: I'm sorry, but I have to go."}
{"answers": "Lucy likes English and P.E. best, but Lin Fang's favorite is Chinese and Science.", "prediction_text": "#Person1#: Nancy is a very different person from Lin Fang. She is very different from Lin Fang."}
{"answers": "Sarah tells #Person1# that she can't afford a new house. #Person1# says #Person1#'s sister-in-law bought a cheap house and suggests Sarah should buy a house far from the city center.", "prediction_text": "#Person1#: I'm not sure if I should move or not."}
{"answers": "#Person1# invites Peter to have tea, but Peter is watering the garden. Then it rains, Peter can get in and have tea with #Person1#.", "prediction_text": "Peter is a man of few words."}
{"answers": "#Person1# wants to pick up a ticket at #Person2#'s and pays by card.", "prediction_text": "I'm going to pick up my ticket."}
{"answers": "Ms. Ross and Mr. Fisher start the conversation with greetings. Then they discuss the preparation for a presentation. Ms. Ross confirms the details of the presentation, including schedule, food service. Ms. Ross is concerned that Mr. Fisher's sent too many invitations than the room capacity.", "prediction_text": "#Person1#: I'm sorry, I'm not sure what you mean."}
{"answers": "Mr. Faber calls #Person1# to book a double room for 3 nights at York Hotel.", "prediction_text": "The hotel is a little too small for me."}
{"answers": "#Person2# is making a shopping budget to save the rest of the money. #Person1# thinks it's a good idea.", "prediction_text": "#Person1#: What are you working on?"}
{"answers": "#Person1# makes requires about #Person2#'s English standard.", "prediction_text": "#Person1#: I am a very good student. I have a good English."}
{"answers": "#Person2# tells Mary about #Person2#'s terrible experience that a dog rushed at #Person2# and barked loudly when #Person2# was delivering milk.", "prediction_text": "I was in the middle of a conversation with a friend when I heard a loud noise."}
{"answers": "#Person1# speculates the signature on the book is valuable, but #Person2# thinks it is somewhat impractical.", "prediction_text": "A young woman is walking through a bookstore in a small town. She is wearing a black dress and a black hat. She is carrying a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book. She is reading a book"}
{"answers": "Emily has her first paycheck in the States and there are a few things she doesn't understand. Susan explains the federal and state deductions to her. Emily thinks it might be the same in the UK but she just never paid much attention before.", "prediction_text": "I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom"}
{"answers": "Catherine and Tom talk about American fast-food culture inspired by a movie, and they think Americans need to find a way to make the most of their fast foods.", "prediction_text": "#Person1#: I think fast food is a lifestyle that craves convenience. People just don't care whether it is healthy or not."}
{"answers": "#Person1# teaches #Person2# the rules and terms of bowling.", "prediction_text": "#Person1#: I'm a little confused."}
{"answers": "#Person1# and #Person2# talk about the difficulty of not having a personal computer.", "prediction_text": ": I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm"}
{"answers": "#Person2# wants to know about requesting a loan. #Person1# introduces the policy of loan interest and credit assessment. #Person2# then leaves as he has a terrible credit score.", "prediction_text": "#Person1#: I have a bad credit score."}
{"answers": "#Person1# and Bob share their last weekend's activities. And they decide to play a game this weekend.", "prediction_text": "#Person1#: I'm a little nervous about the weekend."}
{"answers": "Steven calls Mr. Sellers and asks him what he should do to deal with a power failure.", "prediction_text": "#Person1#: I'm sorry, but I can't find the circuit box."}
{"answers": "Steven tries to do the repairment under Mr. Seller's instruction to solve power failure.", "prediction_text": "#Person1#: I'm sorry, but I can't find the circuit box."}
{"answers": "Fanny had a nightmare because of the pressure from school applications. Her mother and Andy both comfort her.", "prediction_text": "#Person1#: I am going to the University of Michigan."}
{"answers": "#Person1# asks #Person2#'s opinion on new clothes, but #Person2# only focuses on the game and feels annoyed. They are both angry and argue.", "prediction_text": "I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if"}
{"answers": "#Person1# and Mr. Simpson will have lunch together.", "prediction_text": "Mr. Simpson is a man of few words."}
{"answers": "James helps Mrs. Thomas to do chores because James wanted to save money and Mrs. Thomas is incapable.", "prediction_text": ": I'm a little late to the party, but I'm glad you're here."}
{"answers": "Steven wants to stay up to go over the household budget when #Person1# is going to sleep.", "prediction_text": "#Person1#: I'm going to bed. I'm tired."}
{"answers": "#Person2# cannot stand the noise near her room and #Person1# promises to change her room tomorrow as there is no room available today.", "prediction_text": "#Person1#: I'm sorry to hear that. This room is at the end of the corridor. It's possible that the noise is heard early in the morning when everything is quiet."}
{"answers": "Jack tells #Person1# that business communication is his favorite last year and #Person1# will check it.", "prediction_text": "#Person1#: I think I would say my favorite class was my business communication class."}
{"answers": "Laura comes across #Person1# on the way to the gym, and they stop to talk about exercise habits. Unlike Laura, #Person1# prefers other exercises over going to the gym.", "prediction_text": "#Person1#: I'm going to the gym. I've got to stay in shape, you know."}
{"answers": "#Person1# and #Person2# have a chat about the current rainy season which #Person1# can hardly endure while #Person2# has already been used to.", "prediction_text": "#Person1#: I'm not sure if I like this weather."}
{"answers": "#Person2# has a girlfriend in Thailand. They know each other on the Internet but never meet. #Person1# is surprised but congratulates #Person2#.", "prediction_text": "I'm going to Thailand. I'm going to meet my girlfriend. I'm going to spend my winter vacation."}
{"answers": "#Person1# suggests having beers after dinner. Jim refuses. They decide to go to the gym to meet their friends.", "prediction_text": "#Person1#: I think we should go for a walk."}
{"answers": "#Person1# will send #Person1#'s son an important certificate by certified mail. And #Person2# suggests #Person1# send a watch by registered mail.", "prediction_text": "#Person1#: I want to make sure my son receives this letter. It has an important certificate in it."}
{"answers": "#Person1# asks #Person2# how to use the library. #Person2# offers specific directions.", "prediction_text": "#Person1#: I'm sorry, but I can't find the book you're looking for."}
{"answers": "#Person2# offers #Person1# two ways to send the package and #Person1# chooses the cheaper one.", "prediction_text": "#Person1#: I want to mail this package to Korea."}
{"answers": "#Person1# and #Person2# talk about the weather and how to keep warm.", "prediction_text": "#Person1#: I think spring is finally here."}
{"answers": "#Person2# wants to inquire about a loan for studying abroad. #Person1# explains the policy and age limitations to #Person2#. #Person2# will decide later.", "prediction_text": "#Person1#: I'm a student studying abroad in the UK. I'm currently studying at the University of Oxford. I'm currently in my second year of my degree. I'm currently studying at the University of Oxford. I'm currently studying at the University of Oxford. I'm currently studying at the University of Oxford. I'm currently studying at the University of Oxford. I'm currently studying at the University of Oxford. I'm currently studying at the University of Oxford. I'm currently studying at the University of Oxford. I'm currently studying at the University of Oxford. I'm currently studying at the University of Oxford. I'm currently studying at the University of Oxford. I'm currently studying at the University of Oxford. I'm currently"}
{"answers": "#Person2# appreciates for #Person1# 's kind help.", "prediction_text": "#Person1#: I'm sorry."}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay for the house rent but Ayden lends $20 because of the lack of money. They decide to have dinner at #Person1#'s place.", "prediction_text": "#Person1#: Hey Ayden, I was wondering if you could do me a favor."}
{"answers": "#Person1# agrees with #Person2#'s doctor that #Person2# should diet.", "prediction_text": "#Person1#: I have been eating too much red meat."}
{"answers": "#Person2# has traditional Greek yogurt, which #Person1# thinks rather plain. #Person1# has an Italian tiramisu, which #Person2# thinks delicious. #Person1# goes and gets both of them a fried banana.", "prediction_text": "#Person1#: I'm going to try the yogurt."}
{"answers": "#Person2# has the bottom bunk by winning the coin flip with #Person1#. They decide to use #Person1#'s stereos because it's better. #Person1# has the desk by the window for it's fair. #Person1# will go out for food and come back to unpack after #Person2# has done.", "prediction_text": "Person 1: I'm a little bit of a jerk. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not a good person. I'm not"}
{"answers": "Bill tells Jim that Dick's been sick.", "prediction_text": "#Person1#: I'm sorry, Bill."}
{"answers": "#Person1# works as a toy salesperson and feels so tired recently because Christmas is coming, and everyone's shopping for presents.", "prediction_text": ": #Person1#: I'm sorry, but I have to go."}
{"answers": "#Person1# might make a peanut butter and jelly sandwich to eat.", "prediction_text": "#Person1#: I'm hungry."}
{"answers": "#Person1# prefers sending the package by post to by first class after being told the price and the duration by #Person2#.", "prediction_text": "#Person1#: I want to mail this package to Korea."}
{"answers": "Kathy complains about the noise and #Person2# explains it's because it's the year of the 17-year bird calls. Then they discuss where the birds live.", "prediction_text": "#Person1#: I'm not sure what to do. I'm not sure what to do."}
{"answers": "#Person1# is taking off to restart a business. #Person1# expresses appreciation to #Person2# and asks #Person2# to take care of Pamela.", "prediction_text": "#Person1#: I am going to be a good friend to Pamela."}
{"answers": "#Person2# at first thinks #Person1#'s behaviour cruel but finally joins #Person1#.", "prediction_text": "#Person1#: I'm not sure I like the way you look at me."}
{"answers": "#Person1# has no jobs but is unwilling to go home. #Person2# lends $50 to #Person1#.", "prediction_text": "#Person1#: I'm going to have to work on my father's farm."}
{"answers": "#Person2# has applied for a student visa in the states and tells #Person1# about the rules and the required documents of applying for a USA visa and why they are so strict.", "prediction_text": "#Person1#: I'm very happy to be able to go to the United States. I'm very happy to be able to study in the United States."}
{"answers": "Mr. Steven Kayne is invited to share his experience of establishing a bicycle shop by a business program.", "prediction_text": "#Person1#: Thank you, Mr. Kayne. We wish you success in your new business."}
{"answers": "#Person2# tells #Person1# that #Person2#'s house was robbed.", "prediction_text": "I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure"}
{"answers": "#Person2# admires Abraham Lincoln for his perseverance, courage and humility.", "prediction_text": "#Person1#: I think of him as a man of sound character."}
{"answers": "Lisa found that her husband was cheating on her. She asked him to tell her the truth or end the relationship that she would divorce him. #Person1# agrees with what she did.", "prediction_text": "#Person1#: I'm sorry, Lisa. I'm not sure what you're talking about."}
{"answers": "#Person1#'s flight has been delayed because of heavy rain and #Person2# suggests #Person1# listen to the announcement for further information.", "prediction_text": "#Person1#: I'm sorry, I'm late."}
{"answers": "#Person1# is warning #Person2# not to do that or #Person2# will get into trouble, but #Person2# doesn't care.", "prediction_text": "#Person1#: I'm going to get into a lot of trouble if I do that."}
{"answers": "#Person2# is going to travel around Europe and will buy #Person1# a souvenir.", "prediction_text": "#Person1#: I'm going to go to the UK."}
{"answers": "#Person1# is afraid that eyelash curler may hurt #Person2#'s eyes.", "prediction_text": "I'm not sure if this is a good idea."}
{"answers": "#Person2# borrows some money from #Person1# because #Person2#'s backpack and wallet were lost and cannot take it back. #Person1# will drive #Person2# home.", "prediction_text": "I'm not sure if this is a good idea, but I'm going to try it."}
{"answers": "#Person1# shares the Alex's bachelor party plan with #Person2# and promises nothing will be too much. #Person2# doubts that and will also organize a bachelorette party.", "prediction_text": "I'm a little late to the party, but I'm going to tell you about my friend Julie."}
{"answers": "Leah informs Nathaniel that Ms. Collins will not be able to call him back until next week, so he needs to book an appointment with someone else from the International Student Office. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "#Person1#: I'm sorry about that. I had hoped she would have called me back yesterday."}
{"answers": "#Person1# and David's father want to have a quiet Christmas at a country hotel, but David thinks it is boring.", "prediction_text": "#Person1#: I'm going to the party."}
{"answers": "#Person1# wants to try real Chinese cuisine. #Person2# recommends several ones. #Person1# decides to go to Quanjude restaurant.", "prediction_text": "#Person1#: I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry."}
{"answers": "Rebecca tells #Person1# about her working experiences. Rebecca first worked for the York Herald after her graduation, and then for the London Weekly.", "prediction_text": "#Person1#: I think you're right. I was lucky to have found a job in the local paper. I was really keen on getting some experience in the journalistic world, and this seemed like a good first step."}
{"answers": "#Person1# is a new member who wants to use the machines. Johnson teaches #Person1# to get a card to keep track of the progress and record the suitable weight for each machine. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "#Person1#: I'm ready."}
{"answers": "Frank got a new job and is telling Judy not only the heavy schedule but also the benefits of this job.", "prediction_text": "#Person1#: I am a very good cook."}
{"answers": "#Person1# and #Person2# are appreciating lanterns. #Person2# explains the customs of the Lantern Festival and Chinese characters on the lanterns to #Person1#.", "prediction_text": "#Person1#: I'm really glad that you're here. I'm sure that you'll enjoy the lantern festival."}
{"answers": "Bill made a cake for Sue's birthday, but Sue is on a diet to avoid allergy. Bill offers to buy her something healthy to eat, but Sue is enjoying her party. So they will go afterwards.", "prediction_text": "#Person1#: I'm not sure I can eat all of the sandwiches Jill brought."}
{"answers": "#Person1# asks the exact time when the big fire broke out. #Person2# remembers that it was about 10 o'clock.", "prediction_text": "The fire broke out at the house of a man who was in the process of moving out."}
{"answers": "#Person1# and #Person2# are scheduling to talk about the sales projections for next year and they decide to meet next Tuesday.", "prediction_text": "#Person1#: I'm sorry, I'm not sure what you're talking about."}
{"answers": "Muriel Douglas and James send greetings and talk about their holiday before the meeting. Douglas stayed in L.A., while James went to Tahoe.", "prediction_text": "#Person1#: Hello, are you Muriel Douglas?"}
{"answers": "#Person1# takes a taxi to the Friendship Hotel for something important.", "prediction_text": "#Person1#: Taxi!"}
{"answers": "#Person2# tells #Person1# #Person1# can park at a parking structure and show #Person1# the direction.", "prediction_text": "I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure"}
{"answers": "#Person1# calls #Person2#'s restaurant to ask about the food and opening hours.", "prediction_text": "#Person1#: I'm sorry, but I'm not sure I understand."}
{"answers": "Mr. Jones asks Julie the reasons Julie got absent for class the whole week and finds out Julie got food poisoning.", "prediction_text": "#Person1#: I'm sorry to hear that."}
{"answers": "Ann recommends #Person1# to do the well-being in the air program on the way to New York. Ann introduces the program in detail and #Person1# seems interested in it.", "prediction_text": "#Person1#: You are a very good person."}
{"answers": "#Person1# doesn't know how to use an ATM. #Person2# teaches #Person1#.", "prediction_text": "#Person1#: I need to use the ATM."}
{"answers": "Harry tells #Person2# that the bag was overpriced in a street market. #Person2# suggests that Harry should bargain with the seller and consult the price tags in the supermarket first.", "prediction_text": "#Person1#: I'm not sure what you're talking about. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen"}
{"answers": "#Person2# helps #Person1# draw blood for testing white blood cell count.", "prediction_text": "#Person1#: I am going to have to have a blood transfusion."}
{"answers": "#Person2# tells #Person1# a parking structure for parking and where it is.", "prediction_text": "I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure what to do. I'm not sure"}
{"answers": "#Person2# left #Person2#'s backpack and wallet in a taxi and failed to contact the driver because #Person2# didn't have the number. #Person1# lends 50 dollars to #Person1# and will drive #Person2# home.", "prediction_text": "I'm not sure if this is a good idea, but I'm going to try it."}
{"answers": "Tom suggests going swimming but Jane refuses beacuse of her paper due. After discussion, Jane finnally agrees to have dinner with him and then go to study together in the library.", "prediction_text": "I'm a little late for my test, but I'm still here."}
{"answers": "#Person2# introduces EDD's popular services to #Person1#. #Person2# tells #Person1# that EDD provided computers, job information, workshops, and job fairs.", "prediction_text": "#Person1#: I have a job offer from a company that I am interested in."}
{"answers": "#Person1# is interviewing #Person2#. #Person1# thinks #Person2# answers #Person1#'s questions beautifully, and is the person they want.", "prediction_text": "#Person1#: I am a very good candidate for the position."}
{"answers": "#Person1# shows #Person2# some sandalwoods, #Person2# likes it and buys some.", "prediction_text": "#Person1#: I'm sorry, Madam."}
{"answers": "Brandon is signing up for a free website to improve writing skills but it requires personal information. #Person1# thinks the website cannot be trusted but Brandon doesn't believe so. Therefore, #Person1# shuts down Brandon's computer to stop him.", "prediction_text": "#Person1#: I'm not going to tell you what to do."}
{"answers": "#Person1# praises #Person2#'s spacious courtyard and asks about why corn ears are hanging on the tree. #Person2# explains.", "prediction_text": "#Person1#: I'm very happy to see you."}
{"answers": "Greg Sonders calls Mary to ask whether Mary is interested in sports and tells Mary to wait for final admission decision later.", "prediction_text": "#Person1#: I'm a senior at Brown College."}
{"answers": "#Person1# wants to have a farm and gets away from working 9 to 5 in an office. #Person2#'d visit #Person1#.", "prediction_text": "#Person1#: I'd like to have my own farm."}
{"answers": "Andrew's elder sister is surprised by Andrew's putting on so much weight and figures out Andrew has probably got scammed by a junk mail. She offers Andrew systematic suggestions on living healthy and losing weight.", "prediction_text": "Andrew is a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit"}
{"answers": "The taxi driver takes #Person1# to the Friendship Hotel quickly.", "prediction_text": "#Person1#: Taxi!"}
{"answers": "#Person1# pays the meal and tips #Person2# but #Person2# doesn't take it.", "prediction_text": "#Person1#: I'm sorry sir, but I have to ask you to leave."}
{"answers": "#Person2# plans to have a trip in Hebei but #Person1# says there are sandstorms in there.", "prediction_text": "#Person1#: I think Hebes is a good place."}
{"answers": "#Person1# and #Person2# are talking about the weather in Beijing. #Person2# tells #Person1# it's cold in the evening around this time.", "prediction_text": "#Person1#: I'm going to go to bed."}
{"answers": "#Person1# feels bored at home and asks Jim go for a beer. Jim refuses and suggests going to the gym and meeting friends.", "prediction_text": "#Person1#: I think we should go for a walk."}
{"answers": "#Person1# evaluates #Person2#'s ability and personality by asking #Person2# 's strengths, weaknesses and career planning. #Person1# finds #Person2# is a suitable candidate.", "prediction_text": "#Person1#: I am a very good candidate for the position."}
{"answers": "#Person1# wants to take a date to the best restaurant, #Person2# suggests the Gramercy Tavern and is calling to reserve a table for him.", "prediction_text": "#Person1#: I'm going to the Gramercy Tavern."}
{"answers": "Harry and his wife may go abroad during the holidays. However, they might not go anywhere because his wife always worries too much.", "prediction_text": "#Person1#: I'm sure you will enjoy yourself."}
{"answers": "#Person1# delivers a parcel for Mr. Lee. He happens to have something to express.", "prediction_text": "I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure"}
{"answers": "#Person1# and #Person2# are looking at jobs. #Person2# is interested in the media industry.", "prediction_text": "#Person1#: I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer"}
{"answers": "#Person1# and #Person2# are talking about the heavy storm last night, and #Person1#'s positive. #Person2# thinks the weather is terrible. #Person1# thinks they should not complain.", "prediction_text": "#Person1#: I'm not sure what to say. I'm not sure what to do."}
{"answers": "#Person2# is checking out and asks #Person1# for the bill. #Person1# gives #Person2# a wrong bill at first then corrects it.", "prediction_text": "#Person1#: I'm sorry. I didn't take any laundry service during my stay here. I think you have added someone else's."}
{"answers": "#Person1# will pays for the overweight luggage.", "prediction_text": "#Person1#: You have to pay the excess luggage charge, please."}
{"answers": "Mr. Faber books a double room for 3 nights from July 20th at York Hotel.", "prediction_text": "The hotel is a little too small for me."}
{"answers": "#Person1# stops #Person2# from taking photos in the museum.", "prediction_text": "#Person1#: I am not a good photographer."}
{"answers": "#Person2# calls Pizza House to deliver a thin crust seafood pizza to holyrood 9A.", "prediction_text": "#Person1#: I'm sorry, I'm not sure what you're asking."}
{"answers": "#Person1# wants some online information about the Normandy Landing, #Person2# teaches #Person1# how to google it.", "prediction_text": "I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if"}
{"answers": "#Person2# let in someone and #Person1# thinks it was #Person2#'s fault.", "prediction_text": ": #Person1#: You're a good person, but you're not a good person."}
{"answers": "#Person1# and #Person2# talk about Mirella's dressing style in the office which is different from others. The management decided to give Mirella formal clothes directly to warn her.", "prediction_text": "#Person1#: I think that Mirella is a bit of a weirdo. She's not a very good person. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good"}
{"answers": "#Person2# wants to borrow romance videos for #Person2#'s daughter and asks the detailed cost and tenancy term. #Person1# reminds of the overdue fee.", "prediction_text": "#Person1#: I'm sorry."}
{"answers": "Tony got caught cheating and feels ashamed. Steven tells him to learn from it and study hard.", "prediction_text": "#Person1#: I'm sorry. I didn't mean to hurt you."}
{"answers": "#Person1# is surprised at the Olympic Stadium'volume, capacity and interior setting to #Person1#.", "prediction_text": "#Person1#: I am so happy to see you."}
{"answers": "#Person1# congratulates Mr. Stuart on winning the city marathon.", "prediction_text": "I am a proud mother of two beautiful children."}
{"answers": "#Person1#, a reporter, takes #Person2# to a forum. #Person2# is bored and tries to find a pastime.", "prediction_text": "#Person1#: I'm not sure what to say. I'm not sure what to do. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say."}
{"answers": "Harry tells #Person1# he was almost hit by a car and he will be more careful next time.", "prediction_text": "I was walking home from school when I saw a man walking down the street."}
{"answers": "#Person1# helps #Person2# order one hard-boiled egg with dark toast and orange juice to follow.", "prediction_text": "#Person1#: I'm sorry, but I have to go."}
{"answers": "Jack invites Charlie to play video games after he picks up his dad. Charlie agrees.", "prediction_text": "#Person1#: I'm Charlie. I'm a little bit of a nerd. I like to play video games. I like to watch movies. I like to read. I like to watch TV. I like to go to the movies. I like to go to the gym. I like to go to the gym. I like to go to the movies. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to"}
{"answers": "#Person1# is tired and will take a nap and #Person2# gets the dinner ready.", "prediction_text": "#Person1#: I'm going to take a nap."}
{"answers": "#Person2# is looking at the map. #Person1# finds geography interesting and wants to know more about it.", "prediction_text": "#Person1#: I'm looking at this map of the world. I'm preparing for a geography class."}
{"answers": "#Person2# shares an umbrella with #Person1# when it's rainy.", "prediction_text": "#Person1#: I am going to the Garden Hotel."}
{"answers": "Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week.", "prediction_text": "#Person1#: What is your favorite color?"}
{"answers": "#Person2# wants some traditional Chinese arts and crafts. #Person1# shows her sandalwood fan and she buys some.", "prediction_text": "#Person1#: I'm sorry, Madam."}
{"answers": "#Person1# introduces Henry to Pete. The three persons talk about their previous experiences and life and decide to go to a bar after dinner.", "prediction_text": "Person1: I'm a Chinese expat living in Beijing. I'm a bit of a geek and I love to read. I'm also a bit of a nerd and I love to watch movies. I'm also a bit of a nerd and I love to play video games. I'm also a bit of a nerd and I love to watch movies. I'm also a bit of a nerd and I love to watch movies. I'm also a bit of a nerd and I love to watch movies. I'm also a bit of a nerd and I love to watch movies. I'm also a bit of a nerd and I love to watch movies. I'm also a bit of a nerd and I love to watch movies. I"}
{"answers": "#Person1# is surprised to see Andrew put on so much weight but Andrew tells #Person1# that to lose weight, he signs up for a Wafu Diet online for $490. #Person1# thinks he's getting scammed and suggests that he should take more exercise, eat smaller portions, eat a well-balanced breakfast, cut off fast food and sugar and eat fresh fruits and vegetables", "prediction_text": "Andrew is a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit of a jerk. He's a little bit"}
{"answers": "#Person2# will give #Person1# a ride after work.", "prediction_text": "Person 1: I'm going to be late for work."}
{"answers": "#Person1# and #Person2# will go to Burger Queen for lunch and eat cheese burgers and French fries. They will have to wait for good food.", "prediction_text": "#Person1#: I am hungry. I like their milkshake. They're very creamy and tasty."}
{"answers": "#Person1# is asking #Person2# about the places that #Person2# has stayed or lived.", "prediction_text": "#Person1#: You were born in Chicago?"}
{"answers": "#Person2# helps Mr. Murray get a library card and reminds him of library rules.", "prediction_text": "I have a question. I have a question."}
{"answers": "#Person2# finds #Person2# being mischarged. #Person1# corrects the bill and #Person2# pays for it.", "prediction_text": "#Person1#: I'm sorry. I didn't take any laundry service during my stay here. I think you have added someone else's."}
{"answers": "#Person2# has worked overtime with minimum wage and little bonus. Although #Person2# are not very confident, #Person1# hopes #Person2# could quit that job and find another one.", "prediction_text": "#Person1#: I'm sorry, but I don't think you're qualified for this job."}
{"answers": "#Person1# congratulates Tom for achieving the Nobel Prize.", "prediction_text": "The world is a strange place."}
{"answers": "Sally reads the letter from Tom to #Person1#. The letter invites them to visit Tom.", "prediction_text": "I am a man who has been married for over twenty years. I have two children, a son and a daughter. I have been married for over twenty years. I have been married for over twenty years. I have been married for over twenty years. I have been married for over twenty years. I have been married for over twenty years. I have been married for over twenty years. I have been married for over twenty years. I have been married for over twenty years. I have been married for over twenty years. I have been married for over twenty years. I have been married for over twenty years. I have been married for over twenty years. I have been married for over twenty years. I have been married for over twenty"}
{"answers": "#Person2# needs to change clothes to enter the conference and #Person1# offers #Person2# the convenience by putting the conference off.", "prediction_text": "#Person1#: I am sorry. I have to return to the hotel and fetch my jacket and tie."}
{"answers": "Stephanie has a headache but doesn't see a doctor because of the report due and George is willing to help with the report and suggests she ask the board for more time", "prediction_text": "#Person1#: I'm sorry, but I have to go."}
{"answers": "Jim suggests #Person1# go to the gym to meet friends instead of drinking beers after dinner.", "prediction_text": "#Person1#: I think we should go for a walk."}
{"answers": "#Person2# is from America and is picking up presents for families in a duty-free shop. #Person1# recommends some for #Person2#.", "prediction_text": "#Person1#: I'm picking out some presents for my children and wife."}
{"answers": "#Person1# is interviewing Vet and asking her about the ways she deals with stress as a young mother, feelings of having a baby, plans for the future, her favorite quote, and advice for teens.", "prediction_text": "#Person1#: What is your favorite quote?"}
{"answers": "#Person1# calls to inform Ballam that Ballam was going to be hired, and agrees to raise the starting salary after discussion.", "prediction_text": "I am a computer engineer."}
{"answers": "#Person1#'s new suit cost $150. #Person2# doesn't think it's a good bargain.", "prediction_text": "I'm a little disappointed in my new suit. I think it's a little too big and I think it's too tight. I'm not sure if I should get a new suit or not."}
{"answers": "Tom complains about his unhappy lunch experience in the fast-food restaurant while Catherine enjoyed her home-made meal. Catherin doesn't like fast food and says most Americans avoid it too.", "prediction_text": "#Person1#: I'm not sure I agree with you."}
{"answers": "#Person2# had been in the charity organization for 2 months and the social activities influenced #Person2#'s way of thinking.", "prediction_text": "#Person1#: What is your favorite thing about being a person?"}
{"answers": "Nick has never used washing machines before, and he turns to a girl for help. Nick appreciates Alice's help and tells her his mother used to do washing for him. Alice will help him be more independent.", "prediction_text": "#Person1#: I'm Alice. I'm nineteen. I'm a student at the University of Taiwan. I'm a student at the University of Taiwan. I'm a student at the University of Taiwan. I'm a student at the University of Taiwan. I'm a student at the University of Taiwan. I'm a student at the University of Taiwan. I'm a student at the University of Taiwan. I'm a student at the University of Taiwan. I'm a student at the University of Taiwan. I'm a student at the University of Taiwan. I'm a student at the University of Taiwan. I'm a student at the University of Taiwan. I'm a student at the University of Taiwan. I'm a student at the"}
{"answers": "#Person1# and #Person2# are drinking while discussing what to do if they won the Pools. #Person1# wants to go around the world while #Person2# wants to buy a big house.", "prediction_text": "Person 1: I'd like to go round the world."}
{"answers": "#Person1# asks #Person2# how well #Person2# knows about the company, #Person2#'s ideal department, and current salary. #Person2# also asks #Person1# about the company's benefits and insurance.", "prediction_text": "#Person1#: I'm very happy to work with you."}
{"answers": "#Person1# will go to London and invites #Person2# to come along. But they have disagreements on how they get there.", "prediction_text": "#Person1#: I have to go up to London for a couple of days next week. Would you like to come?"}
{"answers": "#Person2# is holding a baby shower for #Person1# and there are many presents for #Person1#. #Person1#'s water broke.", "prediction_text": "#Person1#: I'm so excited to be a mom!"}
{"answers": "#Person2# shows #Person1# the way to the seventy seventh.", "prediction_text": "#Person1#: I am a stranger here."}
{"answers": "#Person1# shows Jack a picture of #Person1#'s new puppy.", "prediction_text": "Person 1: I'm not sure what to do with this picture."}
{"answers": "#Person1# inquires Tom about his successful business experience and his own green life and how to learn from mistakes.", "prediction_text": "#Person1#: What is your favorite thing about your job?"}
{"answers": "#Person1# and #Person2# are talking about the heavy rain last night and the weather in Beijing.", "prediction_text": "#Person1#: I'm going to go to bed."}
{"answers": "#Person2# explains how the city grew into a large place by selling coal and absorbing villages, and introduced old buildings to #Person1#.", "prediction_text": "I'm not sure if this is a good idea. I'm not sure if this is a good idea."}
{"answers": "#Person2# asks #Person1# to bring #Person1#'s husband to talk about #Person1#'s test results.", "prediction_text": "Person1: I have a question."}
{"answers": "#Person1# and #Person2# talks about Mirella dressed casually after she came back from Silicon Valley. Managements put white shirts on her desk to warn her.", "prediction_text": "#Person1#: I think that Mirella is a bit of a weirdo. She's not a very good person. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good at her job. She's not very good"}
{"answers": "#Person1# encourages Lucy to sing but Lucy just wants to listen.", "prediction_text": "#Person1#: I'm sorry, but I can't sing."}
{"answers": "Mr. Lee offers to give Mrs. Word a lift home on a terrible night.", "prediction_text": "The first time I met Mr. Lee, he was a young man who was in his early twenties."}
{"answers": "#Person2# is surprised to hear from #Person1# that Mary got married.", "prediction_text": "#Person1#: She's going to live in Paris."}
{"answers": "#Person1# will take the leather shoes.", "prediction_text": "#Person1#: I want a pair of enameled leather shoes in size 30."}
{"answers": "#Person2# tells Tenants Advocacy Resource Center about the problem with #Person2#'s house owner. #Person2# tells #Person1# it always takes #Person2#'s house owner a long time to repair and they can't agree on the repair costs.", "prediction_text": "#Person1#: I'm sorry. I don't know what to do."}
{"answers": "#Person1# is visiting #Person2#'s spacious house. #Person1# finds it interesting that corn ears are hanging on the tree branch.", "prediction_text": "#Person1#: I'm very happy to see you."}
{"answers": "#Person1# buys some nice clothes by credit card with #Person2#'s assistance.", "prediction_text": "#Person1#: I'm sorry, but I'm not interested."}
{"answers": "#Person2# tells #Person1# the bus route to get to Sons.", "prediction_text": "#Person1#: I'm going to have to take a bus to Sons."}
{"answers": "#Person1# and #Person2# go to Burger Queen for lunch, but there's a long line and they have to wait. #Person2# is going to order French fries that #Person1# recommends.", "prediction_text": "#Person1#: I am hungry. I like their milkshake. They're very creamy and tasty."}
{"answers": "#Person2# suggests that #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "I'm a guy who's been in the military for a while. I've been in the military for a while. I've been in the military for a while. I've been in the military for a while. I've been in the military for a while. I've been in the military for a while. I've been in the military for a while. I've been in the military for a while. I've been in the military for a while. I've been in the military for a while. I've been in the military for a while. I've been in the military for a while. I've been in the military for a while. I've been in the military for a while. I've been in"}
{"answers": "#Person1# has a bad lifestyle. #Person2# kindly advises #Person1# to be positive.", "prediction_text": "#Person1#: I'm a very happy person."}
{"answers": "#Person2# tells #Person1# that #Person2# is bored and explains the reasons, so #Person1# suggests #Person2# read some old newspapers and be quiet.", "prediction_text": "#Person1#: I'm not sure what to say. I'm not sure what to do. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say. I'm not sure what to say."}
{"answers": "#Person1# borrows 200 yuan from Fred to pay the mortgage of a computer and will pay back when having money.", "prediction_text": "I have a new computer, and I am going to use it to make money. I have a new phone, and I am going to use it to make money. I have a new computer, and I am going to use it to make money. I have a new phone, and I am going to use it to make money. I have a new computer, and I am going to use it to make money. I have a new computer, and I am going to use it to make money. I have a new computer, and I am going to use it to make money. I have a new computer, and I am going to use it to make money. I have a new computer, and I am going to use"}
{"answers": "#Person2# tells #Person1# that the company not only raised a lot of money sponsoring a marathon for the American Cancer Society but also established a good image out of this fundraiser event.", "prediction_text": "#Person1#: I think that the event was a great success. We were able to raise a lot of money for the American Cancer Society. We were able to sponsor a marathon for the American Cancer Society. We were able to raise a lot of money to contribute to cancer research."}
{"answers": "#Person2# suggests Harry consult the price tags in the supermarket first and then negotiate for a fair deal after Harry bought a bag overpriced and got a terrible feeling.", "prediction_text": "#Person1#: I'm not sure what you're talking about. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen"}
{"answers": "#Person1# teaches #Person2# how to use the training machine and suggests starting working small reps if #Person2# wants a good physique.", "prediction_text": "#Person1#: I'm not sure what you're talking about. I'm not a beginner. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything"}
{"answers": "Simon tells #Person1# that he is on a scheme called phased retirement during which he had a six-month break from work, and after that he could apply for projects of the company he used to work for but manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": ": #Person1#: What is your plan for the future?"}
{"answers": "#Person2# agrees to help #Person1#'s friend, a new emigrant who had never been to school, to learn English.", "prediction_text": "I'm a teacher."}
{"answers": "#Person2# introduces the Olympic Stadium's finishing time, capacity and interior setting to #Person1#.", "prediction_text": "#Person1#: I am so happy to see you."}
{"answers": "#Person1# and Ben discuss what evening classes they shall get and come into an agreement to have an Indian cooking course for their interest.", "prediction_text": "#Person1#: I'm not sure I can afford it."}
{"answers": "#Person2# introduces the Ford Focus to #Person1# who wants to buy a new car. #Person2# describes every detail of the car and #Person1# decides to have a test drive.", "prediction_text": "#Person1#: I am looking for a new car. I have this old Ford Pinto that I would like to trade in."}
{"answers": "#Person2# suggests #Person1# get a costume of a Canadian Mountie for a dress party. They will go to the shopping center for that at ten o'clock on Saturday.", "prediction_text": "#Person1#: I'm going to the shopping center."}
{"answers": "In the workshop, #Person2# offer #Person1# some suggestions on how to perform well in interviews.", "prediction_text": "#Person1#: I think that you should be honest with your answers."}
{"answers": "#Person1# and #Person2# decide to go to Carrots where can meet everyone's requirements with others tomorrow evening.", "prediction_text": "#Person1#: I'm not sure what to do with myself."}
{"answers": "#Person2# tells the trips and sports activities of the holiday camp to #Person1#. The kids enjoyed camp and would like to go next year.", "prediction_text": "#Person1#: Did you have any fun?"}
{"answers": "#Person2#'s friend is visiting #Person2#. #Person2# invites him to give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "#Person1#: I'm going to give a talk at my lab."}
{"answers": "Tom asks Sara to help to take care of his son when he takes his daughter to the hospital.", "prediction_text": "#Person1#: I'm sorry to hear that. Is there anything I can do for you?"}
{"answers": "#Person2# tells #Person1# that the company raised a lot of money to contribute to cancer research, and the marathon event was also a great deal for our company", "prediction_text": "#Person1#: I think that the event was a great success. We were able to raise a lot of money for the American Cancer Society. We were able to sponsor a marathon for the American Cancer Society. We were able to raise a lot of money to contribute to cancer research."}
{"answers": "#Person2# suggests that #Person1# should follow the doctor's instruction.", "prediction_text": ": I'm not sure if I should be happy or sad."}
{"answers": "#Person1# is going to New York for the first time. #Person2# suggests #Person1# use a personal tour guide service to make #Person1#'s trip plan.", "prediction_text": "#Person1#: I'm going to New York for the first time, but I don't have a tour guide. Can you give me any suggestions?"}
{"answers": "#Person1# and #Person2# change ideas on Barry and Paul, and then talk about their own personalities.", "prediction_text": "#Person1#: I'm not sure I like you."}
{"answers": "#Person1# is a tourist from America and has a chat with #Person2#.", "prediction_text": "#Person1#: I'm sorry, I'm not English."}
{"answers": "#Person1# compliments Brian on his English and asks Brian about his life in the U.S.", "prediction_text": "#Person1#: You're a good person."}
{"answers": "#Person2# is surprised that Ruojia's married. #Person1# and #Person2# will go to her party and give their presents to wish her a happy marriage.", "prediction_text": "#Person1#: I'm going to the party tonight."}
{"answers": "James helped Mrs. Thomas with housework since she has broken her legs, and he wanted to save money for a bike.", "prediction_text": ": I'm a little late to the party, but I'm glad you're here."}
{"answers": "#Person1# and #Person2# feel scared when heard Sarah insists on marrying Dick.", "prediction_text": "I was in a very bad mood."}
{"answers": "#Person1# and #Person2# hold different opinions towards people with different facial expressions.", "prediction_text": "I am a guy who is fed up with the people who keep smiling to me. I am fed up with the people who keep smiling to me. I am fed up with the people who keep smiling to me. I am fed up with the people who keep smiling to me. I am fed up with the people who keep smiling to me. I am fed up with the people who keep smiling to me. I am fed up with the people who keep smiling to me. I am fed up with the people who keep smiling to me. I am fed up with the people who keep smiling to me. I am fed up with the people who keep smiling to me. I am fed up with the people who keep smiling to me. I"}
{"answers": "#Person1# and #Person2# argue to repay for a meal but no one succeeds. They finally decide to go dutch.", "prediction_text": "I'm a bit of a noob, but I'm pretty good at this. I'm not sure if I'm good enough to be a waiter, but I'm pretty good at being a waiter."}
{"answers": "#Person1# thinks the eyelash curler is very dangerous, but #Person2# doesn't think so.", "prediction_text": "I'm not sure if this is a good idea."}
{"answers": "Jack checks in at #Person1#'s hotel.", "prediction_text": "#Person1#: I'm Jack."}
{"answers": "Ann suggests #Person1# take well-being in the air programme including drinking mineral water and exercising so that #Person1# could get over jet lag.", "prediction_text": "#Person1#: You are a very good person."}
{"answers": "#Person2# is curling eyelashes. #Person1# feels dangerous, but #Person2# doesn't think so.", "prediction_text": "I'm not sure if this is a good idea."}
{"answers": "#Person1# and #Person2# have a discussion about what they usually do to relieve anger.", "prediction_text": "#Person1#: I'm not sure what you mean by \"working out.\""}
{"answers": "#Person1# and #Person2# are looking for work and will think about the electrician apprentice program.", "prediction_text": "#Person1#: I'm a little nervous about this."}
{"answers": "Mr. Polly wants to get a break from work and he asks #Person1# to buy a drink for him, but #Person1# refuses.", "prediction_text": "#Person1#: I'm sorry, Mr. Polly."}
{"answers": "Jane invites Peter to join her travel to Xi'an. Peter asks the duration and cost and is interested. Then they discuss their ideal places for a holiday if they had enough money.", "prediction_text": "#Person1#: I'm going to Xi'an in the summer."}
{"answers": "#Person1# invites #Person2# to watch a movie together that night. #Person2# wants to see nothing but a war movie.", "prediction_text": "#Person1#: I'm not sure if I'm ready to go to the movies tonight."}
{"answers": "Hong tells #Person1# to buy a local SIM card to make a cheap phone call to the UK.", "prediction_text": "#Person1#: I really need to call back to the UK but I can't afford to pay for the roaming charges."}
{"answers": "#Person1#'s flight is delayed. #Person2# can't tell further information about the delay because of the changeable weather.", "prediction_text": "#Person1#: I'm sorry, I'm late."}
{"answers": "#Person1# and #Person2# are talking about taking care of their puppies and the time of vet appointment.", "prediction_text": "#Person1#: I'm sorry, but I have to go."}
{"answers": "#Person1# makes an appointment with Jane on next Monday afternoon around three to kick around some ideas.", "prediction_text": "#Person1#: I'm not sure I can do this."}
{"answers": "#Person2# draws #Person1#'s blood to check white blood cell count.", "prediction_text": "#Person1#: I am going to have to have a blood transfusion."}
{"answers": "Weather's nice. #Person1# suggests having lunch outside and hopes to go to the beach to get some sun.", "prediction_text": "#Person1#: I'm glad you're enjoying the weather."}
{"answers": "#Person1# is not ready for marriage. #Person2# reminds him of what he said a month ago and what will happen if he leaves Amy. After listening to #Person2#, #Person1# is ready for marriage.", "prediction_text": "I'm not sure if I should be happy or sad. I'm not sure if I should be angry or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be angry or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be angry or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be angry or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure"}
{"answers": "#Person1# asks Mike about his sister.", "prediction_text": "Mike is a man of few words."}
{"answers": "Jack bought a new car and invites Daisy to hop in and take a ride. Daisy thinks it's a good feeling.", "prediction_text": "#Person1#: I'm going to be a good boy."}
{"answers": "#Person1# teaches #Person2#, a newbie, to use a machine. #Person2# doesn't want to be ripped and thus works with less weight than #Person1#.", "prediction_text": "#Person1#: I'm not sure what you're talking about. I'm not a beginner. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything. I'm not a beginner at anything"}
{"answers": "#Person1# asks #Person2# about past experiences of living in different cities around the world.", "prediction_text": "#Person1#: You were born in Chicago?"}
{"answers": "#Person1# helps #Person2# find the things that #Person2# wants. #Person1# finds it comfortable working in the office.", "prediction_text": "#Person1#: I'm a lawyer."}
{"answers": "Jack invites Charlie to play a new video game and Charlie is interested.", "prediction_text": "#Person1#: I'm Charlie. I'm a little bit of a nerd. I like to play video games. I like to watch movies. I like to read. I like to watch TV. I like to go to the movies. I like to go to the gym. I like to go to the gym. I like to go to the movies. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to go to the gym. I like to"}
{"answers": "Tom Wilson pays the bill for his room and meal cost by credit card.", "prediction_text": "I'm not sure if this is a good idea."}
{"answers": "#Person1# wants to know about the Normandy Landing. #Person2# teaches #Person1# to search and suggests reading a history book.", "prediction_text": "I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if"}
{"answers": "Laura does exercise for her health and good looking. #Person1# talks about regular exercise. They may do exercise together one day.", "prediction_text": "#Person1#: I'm going to the gym. I've got to stay in shape, you know."}
{"answers": "Susan wants to find the memo. John has no idea where it is.", "prediction_text": "#Person1#: I'm sorry. I have no idea. I haven't seen that for ages."}
{"answers": "Martin tells Miss Eliza about his exams and his part-time job. Martin appreciates Mister Lam and the students' welfare club.", "prediction_text": "#Person1#: I'm very happy to see you again."}
{"answers": "#Person2# orders a turkey sandwich with cheese and a beef with vegetable soup at Danny's.", "prediction_text": "#Person1#: I'm sorry, I'm not sure what you're asking."}
{"answers": "Benjamin hasn't finished his project report so #Person1# helps him. #Person1# tells him about the content and format of a report, and agrees to teach him how to work Microsoft Words.", "prediction_text": "#Person1#: I'm sorry, I'm not sure what you mean by \"mat for it.\""}
{"answers": "#Person2# can speak three languages and do general desk work in English.", "prediction_text": "#Person1#: You can read and write both of them."}
{"answers": "#Person1# refuses to stay by #Person2#'s side and won't return.", "prediction_text": "I am a man of the world."}
{"answers": "#Person1# wants to go around the world while #Person2# wants to buy a big house, if win the Pools. But they come down to the earth finally.", "prediction_text": "Person 1: I'd like to go round the world."}
{"answers": "#Person2# answers #Person1#'s questions about getting special discount coupons and how to use them.", "prediction_text": "#Person1#: I will buy the sugar and the sugar will be free."}
{"answers": "#Person1# borrows $50 from #Person2# and will pay back once #Person1# gets a job.", "prediction_text": "#Person1#: I'm going to have to work on my father's farm."}
{"answers": "#Person1# bought a new suit with $ 150 and #Person2# thinks it is too expensive.", "prediction_text": "I'm a little disappointed in my new suit. I think it's a little too big and I think it's too tight. I'm not sure if I should get a new suit or not."}
{"answers": "#Person2# lost #Person2#'s backpack, wallet, and money in a taxi. #Person1# lends #Person2# some money and will take #Person2# home.", "prediction_text": "I'm not sure if this is a good idea, but I'm going to try it."}
{"answers": "Mark did't come in because of a hangover. Jill calls him and tells him news about Bill's newborn daugther. They decide to celebrate it tomorrow.", "prediction_text": "#Person1#: Hello."}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "#Person1#: I'm not sure what to do."}
{"answers": "#Person1# will have a business trip to New York. #Person2# suggests #Person1# visit the UN building, Broadway, Columbia University, and New York University.", "prediction_text": "#Person1#: I'm going to New York to sign a business contract. What would you suggest I see while I'm there?"}
{"answers": "#Person1# asks #Person2# about the company's surroundings, location, and staff restaurant.", "prediction_text": "#Person1#: We have a lot of people working here."}
{"answers": "Julie got food poisoning last Sunday and Mr. Jones cares about her health and study.", "prediction_text": "#Person1#: I'm sorry to hear that."}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "#Person1#: I'm not sure what to do. I'm not sure what to say. I'm not sure what to do."}
{"answers": "#Person1# helps #Person2# to choose a new phone.", "prediction_text": "#Person1#: I'd like to buy a new mobile phone please."}
{"answers": "#Person1# buys a ticket under #Person2#'s guidance.", "prediction_text": "I'm going to pick up my ticket."}
{"answers": "#Person2# talks about #Person2#'s interest in collecting stamps and the first postage stamps with #Person1#. It gives #Person2# much pleasure. They all agree that stamps collecting gives much pleasure than becoming wealthy.", "prediction_text": "#Person1#: I have a very good feeling about this."}
{"answers": "#Person1# is not satisfied with the steak and #Person2# will change it.", "prediction_text": "#Person1#: I'm sorry, sir."}
{"answers": "#Person1# advises Mary to try applying for a job online and teaches her how to do it. Mary will try it.", "prediction_text": "#Person1#: I have never tried that way."}
{"answers": "#Person1# explains the checking items in #Person2#'s annual physical examination and will do test to look into #Person2#'s breathing.", "prediction_text": "I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a doctor. I am a"}
{"answers": "#Person1# lost #Person1#'s passport. #Person2# suggests #Person1# fill in the lost property report and inform the embassy of the situation so they can issue a new passport in case it cannot be found.", "prediction_text": "#Person1#: I have a passport."}
{"answers": "#Person1# and #Person2# are talking about the history of how #Person2#'s city gets into an industrial centre.", "prediction_text": "I'm not sure if this is a good idea. I'm not sure if this is a good idea."}
{"answers": "#Person1# explains the rules of bowling game to #Person2#.", "prediction_text": "#Person1#: I'm a little confused."}
{"answers": "#Person1# asks #Person2# to help teach #Person1#'s friend English and #Person2# agrees.", "prediction_text": "I'm a teacher."}
{"answers": "#Person1# introduces Mark Leach to this week's People You Meet. Mark Leach describes his job responsibilities and gives advice to people coming to Britain.", "prediction_text": "#Person1#: I'm Mark Leach. I'm an information officer at the Britain Business Center. Here, we offer a tourist information service to mainly visitors from overseas. And each year, we have about 500,000 people come to the office."}
{"answers": "Harry is mad because he bought a bag overpriced at a street market in China. #Person2# says it's common and people should know the real worth and then negotiate for a fair deal.", "prediction_text": "#Person1#: I'm not sure what you're talking about. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen a street market in China. I've never seen"}
{"answers": "Harry tells #Person1# that he was almost hit by a car and he will be more careful next time.", "prediction_text": "I was walking home from school when I saw a man walking down the street."}
{"answers": "Joe disagrees with Tom on Tom's new assistant and thinks her stuck up.", "prediction_text": "Person 1: I'm a little confused."}
{"answers": "#Person1# and #Person2# talk about their drinking capacity and their drinking habits. They decide to have a drink together tomorrow night.", "prediction_text": ": I'm a little drunk."}
{"answers": "#Person1# and #Person2# exchanges thoughts on how to deal with their son's bad behavior. #Person2# suggests treating the kid with patience instead of punishment.", "prediction_text": "#Person1#: I don't want you to be worried, but our son has some bad habits now. He says painful words everyday."}
{"answers": "Gian and Gina introduce themselves to each other. Gian introduces Gina to Robert.", "prediction_text": "#Person1#: I'm a journalist, I write articles for magazines. I'm here at this conference to research for an article on internet service providers."}
{"answers": "#Person2#'d like to rent a silver Toyota Carola and #Person1# helps #Person2# go through procedures.", "prediction_text": "#Person1#: I'm sorry, I'm not sure what you're asking."}
{"answers": "Mr. Simpson accepts #Person1#'s invitation to lunch on Thursday.", "prediction_text": "Mr. Simpson is a man of few words."}
{"answers": "Simon tells #Person1# that he joined in a phased retirement scheme which offers retired people work positions from the former company, and he can manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": ": #Person1#: What is your plan for the future?"}
{"answers": "#Person2# tells #Person1# #Person2# plan to save money and #Person1# thinks it is good.", "prediction_text": "#Person1#: What are you working on?"}
{"answers": "#Person1# wants to travel to China, but #Person1#'s mother is not available.", "prediction_text": "I'm a young man who has been living in China for a while."}
{"answers": "Ted and #Person2# are discussing the place where they are going to take a holiday.", "prediction_text": "#Person1#: I'm going to spend a few weeks in China. My husband and I want to see around the country."}
{"answers": "Mary is tired because of finding jobs in the market. #Person1# suggests Mary apply for jobs on the Internet and says she could place her information on the Job Wanted channel on a recruitment web. Mary will try at once.", "prediction_text": "#Person1#: I have never tried that way."}
{"answers": "#Person1# and #Person2# are talking about a couple.", "prediction_text": "I'm a guy."}
{"answers": "Gene is learning to drive and wants to buy a sixteen Honda Accord after passing the test.", "prediction_text": "#Person1#: I'm going to take my driving test tomorrow."}
{"answers": "#Person2#'s country exploits, exports, and imports natural resources. #Person1# thinks the government in #Person2#'s country should invests in infrastructure. #Person2# agrees.", "prediction_text": "#Person1#: I think that the government should invest in our infrastructure."}
{"answers": "#Person1# has been working for a company for six years, and #Person2# thinks #Person2#'s ready for promotion.", "prediction_text": "#Person1#: You're a good person."}
{"answers": "Steve helps look after Jonny and house-keeping. Mrs. Robinson appreciates it.", "prediction_text": "#Person1#: I'm sorry, but I have to go."}
{"answers": "#Person2# tells #Person1# #Person2# has few guiding experiences.", "prediction_text": "#Person1#: What is the most important thing you have learned from your experience?"}
{"answers": "#Person1# is asking Rebecca about her working experience. She worked for York Herald as an intern and then as a junior local news reporter. After that, she began to work for London Weekly.", "prediction_text": "#Person1#: I think you're right. I was lucky to have found a job in the local paper. I was really keen on getting some experience in the journalistic world, and this seemed like a good first step."}
{"answers": "#Person1# is helping #Person2# confirm the price for sending the letter.", "prediction_text": "#Person1#: I need some stamps for this letter."}
{"answers": "#Person1# wants to know the charge at #Person2#'s buffet party.", "prediction_text": "#Person1#: I'm a little late for the buffet."}
{"answers": "#Person2# tells Mary about #Person2#'s experience this morning. when #Person2# was delivering milk, a huge dog rushed at #Person2# and #Person2# was knocked into a tree. It reminds Mary of a movie.", "prediction_text": "I was in the middle of a conversation with a friend when I heard a loud noise."}
{"answers": "#Person1# helps #Person2# get a ballot card and guides #Person2# the next step.", "prediction_text": "#Person1#: I'm a Democrat."}
{"answers": "Ben is nervous about his new school life. #Person1# is giving him some brief information about the school.", "prediction_text": "#Person1#: I'm not sure what to expect from this class. I'm not sure what to expect from this class."}
{"answers": "#Person1# teaches #Person2# to cook chicken for #Person2#'s original thought of having frozen pizza is unhealthy.", "prediction_text": "#Person1#: I'm useless at cooking!"}
{"answers": "Tom invites #Person1# to run in the morning. #Person1# would try to get up and join him.", "prediction_text": "I'm a little late for my run, but I'm going to try to get up early."}
{"answers": "#Person1# and #Person2# have different ideas on computer games.", "prediction_text": "I have a friend who is a computer geek. He is a very good programmer. He is also a very good gamer. He is also a very good programmer. He is also a very good gamer. He is also a very good programmer. He is also a very good programmer. He is also a very good programmer. He is also a very good programmer. He is also a very good programmer. He is also a very good programmer. He is also a very good programmer. He is also a very good programmer. He is also a very good programmer. He is also a very good programmer. He is also a very good programmer. He is also a very good programmer. He is also a very good programmer. He is also a"}
{"answers": "#Person1# asks for mom's permission to play card games and promises not to make noise.", "prediction_text": "#Person1#: I'm not going to make any noise."}
{"answers": "#Person1# is showing the new clothes to #Person2#, but #Person2# is busy watching the games. They quarrel and get angry.", "prediction_text": "I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if this is a good idea. I'm not sure if"}
{"answers": "#Person1# and #Person2# are both unemployed. #Person2# suggests applying for the electrician program and #Person1# agrees.", "prediction_text": "#Person1#: I am a very good student."}
{"answers": "Simon and Cleo argue the effectiveness of demonstrations. Simon thinks they are helpful, but Cleo disagrees and refuses to go to the demonstration of helping stop the spread of nuclear weapons with Simon.", "prediction_text": "#Person1#: I am not going to the demonstration."}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. They both decide to have fried bananas and #Person1# will bring bananas back.", "prediction_text": "#Person1#: I'm going to try the yogurt."}
{"answers": "Tom Wilson pays for his bill for hotel and food by credit card.", "prediction_text": "I'm not sure if this is a good idea."}
{"answers": "#Person1# and #Person2# are waiting in line for food.", "prediction_text": "#Person1#: I'm a little bit of a bitch."}
{"answers": "#Person1# introduces Henry Johnson, an expat in China to Pete. Mr. Johnson shares with Pete how much he enjoys his time in China, until #Person1# interrupts and tries to finalize the evening plan. The three decide to go have dinner and then to a bar district.", "prediction_text": "Person1: I'm a Chinese expat living in Beijing. I'm a bit of a geek and I love to read. I'm also a bit of a nerd and I love to watch movies. I'm also a bit of a nerd and I love to play video games. I'm also a bit of a nerd and I love to watch movies. I'm also a bit of a nerd and I love to watch movies. I'm also a bit of a nerd and I love to watch movies. I'm also a bit of a nerd and I love to watch movies. I'm also a bit of a nerd and I love to watch movies. I'm also a bit of a nerd and I love to watch movies. I"}
{"answers": "#Person1# is preparing for her friends' coming this afternoon. Her son Mike helps her to buy drinks and make coffee.", "prediction_text": "#Person1#: I'm going to have to go to the bathroom."}
{"answers": "#Person1# gets lost and asks #Person2# where the central department stall and the national bank are. #Person2# directs #Person1#.", "prediction_text": "#Person1#: I'm a stranger here and lost my way."}
{"answers": "#Person1# warmly welcomes #Person2# and drives #Person2# to the Beijing Hotel. Mr. Wu has arranged a banquet for #Person2# in the evening.", "prediction_text": "#Person1#: I'm sorry, but I have to go."}
{"answers": "Fanny had a nightmare last night and her mother consoled her. Andy listens to her experience and thinks she has a good mother.", "prediction_text": "#Person1#: I am going to the University of Michigan."}
{"answers": "#Person2# organized a great baby shower for #Person1#. #Person1# receives many gifts. #Person1#'s water broke.", "prediction_text": "#Person1#: I'm so excited to be a mom!"}
{"answers": "#Person2# tells #Person1# about the fundamental concepts of the accounting process.", "prediction_text": "#Person1#: What is the most fundamental concept underlying the accounting process?"}
{"answers": "#Person1# makes a big mistake which may cause a huge loss and #Person1# may lose this job because of the loss and #Person1#'s dishonest supervisor. #Person2# suggests that #Person1# move in with #Person2# until #Person1# find another job and stop buying all the junk, if #Person1# loses the job.", "prediction_text": "I'm going to lose my job. I'm going to lose my apartment. I'm going to lose my job. I'm going to lose my job. I'm going to lose my apartment. I'm going to lose my job. I'm going to lose my apartment. I'm going to lose my apartment. I'm going to lose my apartment. I'm going to lose my apartment. I'm going to lose my apartment. I'm going to lose my apartment. I'm going to lose my apartment. I'm going to lose my apartment. I'm going to lose my apartment. I'm going to lose my apartment. I'm going to lose my apartment. I'm going to lose my apartment. I'm going to"}
{"answers": "#Person2# thinks that unemployment gives rise to social problems, and #Person1# thinks that it's difficult to handle it efficiently.", "prediction_text": "#Person1#: The government has to face a lot of social problems now."}
{"answers": "#Person1# and #Person2# are talking about a map of the world. They talk about geographical features and find geographical interesting.", "prediction_text": "#Person1#: I'm looking at this map of the world. I'm preparing for a geography class."}
{"answers": "#Person1# asks #Person2# who saw the robbery some questions. #Person2# agrees to come to the station for more questioning.", "prediction_text": "#Person1#: Did you see the robbery?"}
{"answers": "#Person2# orders some food with #Person3#'s help in a restaurant.", "prediction_text": "The restaurant is a small, but very nice, restaurant. The owner is a very nice man. He is very nice to the staff. He is very nice to the customers. He is very nice to the staff. He is very nice to the customers. He is very nice to the staff. He is very nice to the customers. He is very nice to the staff. He is very nice to the customers. He is very nice to the staff. He is very nice to the staff. He is very nice to the staff. He is very nice to the staff. He is very nice to the staff. He is very nice to the staff. He is very nice to the staff. He is very nice to the staff."}
{"answers": "#Person2# thanks #Person1# for #Person1#'s help.", "prediction_text": "#Person1#: I'm sorry."}
{"answers": "#Person2# introduces #Person1# a website for personal tour guide service.", "prediction_text": "#Person1#: I'm going to New York for the first time, but I don't have a tour guide. Can you give me any suggestions?"}
{"answers": "#Person1# attends Brian's birthday party. Brian thinks #Person1# looks great and charming.", "prediction_text": "Brian is a very nice guy."}
{"answers": "#Person1# is checking the draft of an agreement and #Person2# will finish the agreement this evening.", "prediction_text": "#Person1#: I'm sorry. The agreement hasn't been fully prepared. It will be ready by tomorrow."}
{"answers": "#Person1# and Bill talk about Bill's roommate Brain Locker.", "prediction_text": "I'm a bit of a weirdo. I like to think I'm a bit of a weirdo. I like to think I'm a bit of a weirdo. I like to think I'm a bit of a weirdo. I like to think I'm a bit of a weirdo. I like to think I'm a bit of a weirdo. I like to think I'm a bit of a weirdo. I like to think I'm a bit of a weirdo. I like to think I'm a bit of a weirdo. I like to think I'm a bit of a weirdo. I like to think I'm a bit of a weirdo. I like to think I'm a bit of"}
{"answers": "Kalina calls Professor Clark that she needs to take this week off because she had a concussion.", "prediction_text": "I am a student at the University of California, Berkeley."}
{"answers": "#Person2# lost the credit card in travelling and is asking the Client Services for help.", "prediction_text": "#Person1#: I'm a tourist in this city and unfortunately I've lost my card!"}
{"answers": "Darlene calls to check the delayed order. Dan tells her the situation. Darlene will talk to Steve.", "prediction_text": "#Person1#: I'm calling to check on that order of 100 computers were the tenth of September. However, it has been delayed for 2 days."}
{"answers": "#Person1# and #Person2# talk about the accounting process.", "prediction_text": "#Person1#: What is the most fundamental concept underlying the accounting process?"}
{"answers": "#Person2# wants to buy a gift for his mother's birthday. After knowing his mother's preference, #Person1# recommends a gold watch.", "prediction_text": "#Person1#: I'm sorry, sir. I'm not sure what you mean."}
{"answers": "#Person1# helps #Person2# to order some food.", "prediction_text": "#Person1#: I'm a man of few words."}
{"answers": "#Person2# tells #Person1# the address of the Grand Hotel and #Person1# drives her there.", "prediction_text": "#Person1#: I'm sorry, ma'am."}
{"answers": "#Person1# and #Person2# remember the chaotic situation of New York after 9-11, and #Person1#'s uncle ended up dying in the tower.", "prediction_text": "#Person1#: I was in my apartment in Beijing. Where were you?"}
{"answers": "#Person1# and #Person2# are at an exciting party. #Person2# has a crush on Janice and #Person1# encourages him to have a try to talk to her.", "prediction_text": "#Person1#: I'm with you on that. Check out that one over there. I think I'm in love!"}
{"answers": "#Person1# and #Person2# think John's fallen for a girl.", "prediction_text": "I'm a guy."}
{"answers": "Nathan is going to Chicago for practice and he is confident that he will do a good job and learn from others. Though worried at first, his mom thinks it's a great opportunity for him.", "prediction_text": "#Person1#: I'm glad you're back. I'm sure you'll be a great addition to the team."}
{"answers": "#Person1# is introducing the Eiffel Tower to #Person2#.", "prediction_text": "#Person1#: I have heard the name of the tower."}
{"answers": "#Person1# tells ABC Rent-a-car Company that #Person1# had a car accident. #Person2# will call an ambulance and the police.", "prediction_text": "#Person1#: I'm sorry, but I can't help you."}
{"answers": "Matthew and Steve meet after a long time. Steve is looking for a new place to live. Mathew's neighbor is moving out and They might be neighbors again.", "prediction_text": "#Person1#: I'm sorry, but I can't find a place to live."}
{"answers": "#Person2# thinks the movie industry should be revolutionized to survive the threat from DVD industry.", "prediction_text": "#Person1#: I think home video players will replace movie theatres and force them out of the entertainment business."}
{"answers": "Mr. Brown is interviewing #Person2# and they are talking over #Person2#'s salary.", "prediction_text": "#Person1#: I'm a very good student."}
{"answers": "#Person1# is crazy for Trump and voted for him but #Person2# will vote for Biden.", "prediction_text": "#Person1#: I am proud to say that I voted for Trump."}
{"answers": "According to the schedule planned by Brian, #Person1# will be picked up at 6, meet the Managing Director at the airport and attend the conference at noon.", "prediction_text": "#Person1#: I'm going to have to make some changes to the schedule."}
{"answers": "#Person2#'s friend is a Ph.D. student at the University of Edinburgh. He is visiting #Person2# and will give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "#Person1#: I'm going to give a talk at my lab."}
{"answers": "#Person1# is telling #Person2# how to play bowling.", "prediction_text": "#Person1#: I'm a little confused."}
{"answers": "Adam's knee is getting better but #Person1# still suggests that he should not do any running today. Adam decides to watch the films of Michigan after practice and will be back tomorrow for full practice.", "prediction_text": "#Person1#: I'm going to go ahead and say that Michigan is a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are a very good team. They are"}
{"answers": "#Person1# helps #Person2# collect an Export L /C from Tokyo.", "prediction_text": "#Person1#: Hello, Madam. What can we do for you today?"}
{"answers": "Leah informs Nathaniel that Ms. Collins won't be available for their appointment and explains the reason. Then Nathaniel decides to speak with someone else.", "prediction_text": "#Person1#: I'm sorry about that. I had hoped she would have called me back yesterday."}
{"answers": "#Person1# and Judy have a discussion on their financial budget and find out ways to save money.", "prediction_text": ": #Person1#: I'm afraid that the hotel will deteriorate."}
{"answers": "#Person1# introduces the services included in regular car wash package and #Person2# will take it.", "prediction_text": "#Person1#: I want to get my car washed."}
{"answers": "#Person1# sends a package at #Person2#'s and buys some stamps at the other window.", "prediction_text": "#Person1#: I want to send this package by first-class mail."}
{"answers": "Since #Person2# is very social, #Person1# asks for the communication strategy.", "prediction_text": "#Person1#: Are you a social person?"}
{"answers": "#Person2#'s friend is visiting #Person2# and will give a speech about multimodal text generation. #Person1# thinks it interesting and will attend it.", "prediction_text": "#Person1#: I'm going to give a talk at my lab."}
{"answers": "#Person2# is watching a programme about Islam's pilgrimage to Mecca. #Person1# has heard that there are often accidents happen during this pilgrimage. They also talk about pilgrimage in other countries.", "prediction_text": "#Person1#: I think that the pilgrims are very lucky to be able to go on a pilgrimage."}
{"answers": "Sarah wants to move. #Person1# suggests buying a cheaper house and offers information on possible options.", "prediction_text": "#Person1#: I'm not sure if I should move or not."}
{"answers": "#Person1# serves #Person2# to borrow movies and tells #Person2# the price and when to return them.", "prediction_text": "#Person1#: I'm sorry."}
{"answers": "#Person1# and #Person2# are standing in line to buy food.", "prediction_text": "#Person1#: I'm a little bit of a bitch."}
{"answers": "#Person1# feels sorry for Mr. Wilson's loss caused by #Person1# and assures that such mistakes will not happen again.", "prediction_text": "The first problem is supposed to be solved after the investigation. About the second problem, I admit it's our fault, so we will exchange all merchandise that falls short of our sample."}
{"answers": "Tony tells Steven that he cheated on the exam and realized his mistake.", "prediction_text": "#Person1#: I'm sorry. I didn't mean to hurt you."}
{"answers": "#Person1# wants a new bike from #Person2#, but she refuses. #Person1# promises to buy a plane for #Person2#.", "prediction_text": "The first person to buy a car is the first person to buy a car."}
{"answers": "Harry tells #Person1# that he crossed the street while reading the newspaper and almost got hit by a car.", "prediction_text": "I was walking home from school when I saw a man walking down the street."}
{"answers": "#Person2# finds that #Person1# has a fever and says #Person1# should've called in sick earlier.", "prediction_text": "#Person1#: I'm sorry."}
{"answers": "#Person1# and #Person2# go back to work and will meet after work.", "prediction_text": "Person 1: I'm going to be late for work."}
{"answers": "Mark's absence was the result of a hangover. Bill's wife had a baby girl last night. Mark and Jill decide to celebrate it tomorrow.", "prediction_text": "#Person1#: Hello."}
{"answers": "#Person2# leaves the samples to Mr. Grant and will talk to him next week.", "prediction_text": "I'm a little late to the party, but I'm glad I got to see these samples."}
{"answers": "Steve will try to persuade #Person1#'s wife not to divorce #Person1# as #Person1# swears to remain faithful forever.", "prediction_text": "I am a man who has been married for ten years."}
{"answers": "Ms. Dawson helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "prediction_text": "#Person1#: Ms. Dawson, I need you to take a dictation for me."}
{"answers": "#Person2# wants to make a reservation for a round trip flight this month. #Person1# helps #Person2# book the cheapest flight and vegetarian meals.", "prediction_text": "#Person1#: Okay."}
{"answers": "#Person2# can develop #Person1#'s roll of film in six hours.", "prediction_text": "#Person1#: I am a man of my word."}
{"answers": "#Person2# suggests #Person1# ask the policeman the way to Peking University.", "prediction_text": "#Person1#: I ' m afraid I can ' t because I ' m a stranger here."}
{"answers": "#Person2# shows #Person2#'s house and courtyard to #Person1# and introduces the corn ears hunging on the tree.", "prediction_text": "#Person1#: I'm very happy to see you."}
{"answers": "#Person1# and #Person2# order chicken feet and wine.", "prediction_text": "#Person1#: I'm sorry, but I'm not sure I can make it."}
{"answers": "#Person1# wants to take a date to a restaurant. #Person2# recommends the Gramercy Tavern.", "prediction_text": "#Person1#: I'm going to the Gramercy Tavern."}
{"answers": "Anne didn't enjoy the date with #Person2# last time, she rejects #Person2#'s invitation firmly.", "prediction_text": "#Person1#: I'm sorry, I'm very tired. I'm going to bed early. I didn't have good time, and I don't really want to see you again."}
{"answers": "#Person1# helps #Person2# to find a table in the centre and order some food at Beijing Tasty Restaurant.", "prediction_text": "#Person1#: I'm sorry, I'm late."}
{"answers": "#Person2# tells #Person1# he falls in love with the girl living under him and she's very popular. #Person2#'s going to call her now and invite her to dinner tonight.", "prediction_text": "#Person1#: I'm not sure if I should be happy or sad."}
{"answers": "Ballam asks for a position with 4,000 yuan starting salary from #Person1#, given that 3,000 yuan cannot support his family.", "prediction_text": "I am a computer engineer."}
{"answers": "#Person1# is interviewing #Person2# and asking #Person2# some information relating the job.", "prediction_text": "#Person1#: I'm a very good listener."}
{"answers": "#Person1# and #Person2# will call an emergency meeting today even #Person2# doesn't think Ken can get back.", "prediction_text": "#Person1#: I ' m going to call Ken."}
{"answers": "#Person2# has worked for a company for 6 years. With much experience and new skills, #Person2# tells #Person1# that #Person2# is ready for a promotion.", "prediction_text": "#Person1#: You're a good person."}
{"answers": "#Person1# and #Person2# talk about popular sports in their country. Rugby is popular in both countries.", "prediction_text": "#Person1#: I think most people like football. More boys like football than girls. A few girls play it really well. Some people like playing basketball."}
{"answers": "#Person1# and #Person2# are concerned about environmental problems. #Person1# thinks that developing countries care more about economic development than environmental protection. #Person2# would like to join an organization committed to protecting the environment.", "prediction_text": "#Person1#: I think we can solve the problem of air pollution before we destroy the atmosphere. There's lots of clean, modern technology, but companies in developed countries say it's expensive. Developing countries put more emphasis on economic development than on environmental protection."}
{"answers": "Emily's never done paycheck in the States before, so she asks Susan questions about it. Susan explains what the number and terms on the paycheck mean. Emily thanks Susan for her help.", "prediction_text": "I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom with a 2 year old. I'm a single mom"}
{"answers": "#Person2# is being interviewed for a music teacher. #Person1# is impressed by #Person2#'s background and knowledge of classical music. And #Person1# will try classical music.", "prediction_text": "#Person1#: I'm a music teacher. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a music teacher who loves music. I'm a"}
{"answers": "Mr. Sellers instructs Steven to solve the power failure by replacing bad fuses.", "prediction_text": "#Person1#: I'm sorry, but I can't find the circuit box."}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells #Person2# she is pregnant. #Person2# feels happy. They go to the doctor. #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "Person 1: I'm sorry, honey. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry"}
{"answers": "#Person1# helps #Person2# order a hard-boiled egg with dark toast and orange juice to follow.", "prediction_text": "#Person1#: I'm sorry, but I have to go."}
{"answers": "#Person2# fails to hold Mr. Fox's reservation for a mid-size vehicle. Mr. Fox thinks it unreasonable. #Person2# offers Mr. Fox a compact instead and offer him a discount for his costs.", "prediction_text": "#Person1#: I'm sorry, Mr. Fox. We have no mid-size available at the moment."}
{"answers": "#Person2# bought a personal stereo at #Person1#'s shop, but #Person2# found there was a scratch and other people could hear noise from it. #Person1# will exchange it for another model if #Person2# can provide the receipt.", "prediction_text": ": #Person1#: Yes."}
{"answers": "The International Student Office calls Nathaniel to inform him that he has to book another appointment with someone else as Ms. Collins is not available. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "#Person1#: I'm sorry about that. I had hoped she would have called me back yesterday."}
{"answers": "#Person1# has been washing dishes for over a week and #Person2# will do the dishes next week.", "prediction_text": ": I have been washing the dishes for over a week now."}
{"answers": "Greg Sonders from Brown College calls the applicant Mary to ask whether she is interested in college sports and will make a further decision later.", "prediction_text": "#Person1#: I'm a senior at Brown College."}
{"answers": "#Person2# recommends a personal tour guide service to #Person1#.", "prediction_text": "#Person1#: I'm going to New York for the first time, but I don't have a tour guide. Can you give me any suggestions?"}
{"answers": "Karren and Tim say goodbye.", "prediction_text": "#Person1#: I'm sorry, Tim. I'm not sure I can do this."}
{"answers": "Tim and Karren say farewell words after meeting.", "prediction_text": "I'm a little late for my meeting, but I'm still here."}
{"answers": "Benjamin has trouble writing a project report. #Person1# teaches him that a good report involves a final conclusion on his work of this year, experiences in dealing with some emergent events, plan for the next year, and the format.", "prediction_text": "#Person1#: I'm sorry, I'm not sure what you mean by \"mat for it.\""}
{"answers": "#Person1# pays #Person2# by credit card for some clothes.", "prediction_text": "#Person1#: I'm sorry, but I'm not interested."}
{"answers": "Judy explains to #Person1# about the budget for a trip and tries to lower the cost by choosing a economic hotel.", "prediction_text": ": #Person1#: I'm afraid that the hotel will deteriorate."}
{"answers": "#Person1# and #Person2# discuss grandpa's birthday. They decide on where and when to hold the party and what food and gift to prepare.", "prediction_text": ": #Person1#: I'm going to have a party. #Person2#: I'm going to have a party. #Person1#: I'm going to have a party. #Person2#: I'm going to have a party. #Person1#: I'm going to have a party. #Person2#: I'm going to have a party. #Person1#: I'm going to have a party. #Person2#: I'm going to have a party. #Person1#: I'm going to have a party. #Person2#: I'm going to have a party. #Person1#: I'm going to have a party. #Person2#: I'm"}
{"answers": "Charlie helps Myrdal to think over where Myrdal lost the wallet.", "prediction_text": "I was in the middle of a conversation with a friend when I heard a loud bang. I looked up and saw Charlie, who was sitting on the couch, staring at me. I looked back and saw Charlie, who was still staring at me, staring at Charlie. I looked back and saw Charlie, who was still staring at me, staring at Charlie. I looked back and saw Charlie, who was still staring at me, staring at Charlie. I looked back and saw Charlie, who was still staring at me, staring at Charlie. I looked back and saw Charlie, who was still staring at me, staring at Charlie. I looked back and saw Charlie, who was still staring at me, staring at Charlie. I looked back and saw"}
{"answers": "#Person2# tells #Person1# about the family activities on Easter day.", "prediction_text": "The family is in the middle of a big family reunion."}
{"answers": "#Person1# makes an appointment with Jane before their meeting.", "prediction_text": "#Person1#: I'm not sure I can do this."}
{"answers": "#Person2# liked Michael's motorcycle but won't buy one.", "prediction_text": "#Person1#: I'm not afraid of you."}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells him that she's pregnant. They go to see the doctor. However, #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "Person 1: I'm sorry, honey. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry. I'm so sorry"}
{"answers": "#Person1# comes over to say hello to Mrs. Todd, and will go shopping afterwards.", "prediction_text": "I'm a little late to the party, but I'm glad to see you."}
{"answers": "#Person2# cannot understand abstract art so #Person2# decides to go to an art exhibition of greek and roman sculpture with #Person1# tomorrow.", "prediction_text": "#Person1#: I'm an art aficionado. I like going to an art exhibition once or twice a year. I hardly know anything about art or sculpture. You are a true art lover, aren't you?"}
{"answers": "#Person1# cannot stand rainy days, but #Person2#'s been used to it.", "prediction_text": "#Person1#: I'm not sure if I like this weather."}
{"answers": "#Person1# helps #Person2# to order a drink and some food.", "prediction_text": "Person 1: I'm sorry, I'm not sure what I'm supposed to be doing here."}
{"answers": "#Person1# interviews Mr. Kayne why he ran a bicycle shop. He says he loves it to be his own boss. His friends helped him and they could play when there were no customers.", "prediction_text": "#Person1#: Thank you, Mr. Kayne. We wish you success in your new business."}
{"answers": "#Person2# bargains with #Person1# on a gold watch.", "prediction_text": "#Person1#: I'm sorry."}
{"answers": "#Person2# tells #Person1# #Person2# enjoyed the weekend organized by #Person1#.", "prediction_text": "#Person1#: I'm sorry to hear that. I hope you'll come and stay again next time you're in Changsha."}
{"answers": "Frank invites Besty to the party to celebrate his big promotion. Besty couldn't wait for the party.", "prediction_text": "#Person1#: I'm so excited to be promoted to the position of VP of Marketing. I'm so excited to be able to help my friends and family. I'm so excited to be able to help my family. I'm so excited to be able to help my friends and family. I'm so excited to be able to help my friends and family. I'm so excited to be able to help my friends and family. I'm so excited to be able to help my friends and family. I'm so excited to be able to help my friends and family. I'm so excited to be able to help my friends and family. I'm so excited to be able to help my friends and family. I'm so excited to"}
{"answers": "#Person1# is giving #Person2# some advice for upgrading #Person2#'s system, such as adding a painting program and a faster processor.", "prediction_text": "#Person1#: You could upgrade your system."}
{"answers": "Mr. Lee signs his parcel and has something to express.", "prediction_text": "I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure if I should be happy or sad. I'm not sure"}
{"answers": "#Person1# buys shoes from #Person2#.", "prediction_text": "#Person1#: I want a pair of enameled leather shoes in size 30."}
{"answers": "#Person1# wants to go to Peking University. #Person2# suggests asking the policeman.", "prediction_text": "#Person1#: I ' m afraid I can ' t because I ' m a stranger here."}
{"answers": "#Person2# claims #Person2# bought a personal stereo from #Person1#'s shop but the product has two problems that #Person2# is not happy with. #Person1# offers to exchange it for another one, but #Person2# has a problem with the receipt.", "prediction_text": ": #Person1#: Yes."}
{"answers": "Ann interviews Robin at a university radio. Robin talks about the mountain lake cycle tour and their method to avoid roadblock problem.", "prediction_text": "#Person1#: Robin, what is the best way to get to the start line?"}
{"answers": "The doctor prescribes some medicine for Mr. Smith and tells him where to get it.", "prediction_text": "I was in the hospital for a week."}
{"answers": "#Person2# is ordering lemonade, barbeque wings, and baby-back ribs, and #Person1# serves #Person2# gently.", "prediction_text": "#Person1#: I'm sorry, I'm not sure what you're asking."}
{"answers": "#Person1# inquires Mary about the price of products online and their payments. Mary teaches #Person1# how to shop online.", "prediction_text": "#Person1#: I'm a woman, and I'm not afraid to ask for help."}
{"answers": "#Person2# gives #Person1# a book with English songs.", "prediction_text": "#Person1#: I'm sorry, I'm not sure what you mean."}
{"answers": "#Person1# has a dance with Brian at Brian's birthday party. Brian thinks #Person1# looks great and is popular.", "prediction_text": "Brian is a very nice guy."}
{"answers": "Tim's project is about how to lead a greener life. #Person1# consults about his motivation and his findings at school. Tim finds there are many aspects that can be improved for his school.", "prediction_text": ": #Person1#: Tim, you're going to talk about your project and how to lead a greener life. Why did you choose that subject?"}
{"answers": "Ted likes Jenny but is afraid to ask her out. Mike encourages Ted to tell Jenny his feelings.", "prediction_text": "#Person1#: I'm not sure I can do it."}
{"answers": "#Person1# and #Person2# are enjoying the weather and may go to the beach this weekend.", "prediction_text": "#Person1#: I'm glad you're enjoying the weather."}
{"answers": "#Person1# takes an interview workshop. #Person2# offer #Person1# some useful tips on getting ready for an interview.", "prediction_text": "#Person1#: I think that you should be honest with your answers."}
{"answers": "Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies.", "prediction_text": "#Person1#: I like to watch movies."}
{"answers": "#Person2# wants a pair of Jordan shoes in size 41.", "prediction_text": "#Person1#: I'm sorry, but I can't afford to buy shoes."}
{"answers": "#Person1# and #Person2# are talking about the founding time and founder of the New York Times, the Washington Post, and the Los Angeles Times were founded.", "prediction_text": "#Person1#: What is the difference between a newspaper and a newspaper?"}
{"answers": "#Person2# introduces the surroundings and information about their company to #Person1#.", "prediction_text": "#Person1#: We have a lot of people working here."}
{"answers": "Amy called Jimmy to return his book but Jimmy went to Beihai Park for picnic so Amy didn't reach him. They will meet later.", "prediction_text": "#Person1#: I'm sorry, I didn't pick up."}
{"answers": "#Person2# orders a pizza delivery at Pizza House. Marty promises it will arrive in thirty minutes or it's free.", "prediction_text": "#Person1#: I'm sorry, I'm not sure what you're asking."}
{"answers": "Tom tells Catherine he had an underdone sandwich for lunch. Catherine doesn't like fast food because she thinks it's not healthy. She explains that two-thirds of Americans may avoid places like McDonald's and KFC.", "prediction_text": "#Person1#: I'm not sure I agree with you."}
{"answers": "#Person1# first introduces the 2006 fiscal year marketing plan then analyzes their performance and explains with the sale graph.", "prediction_text": "#Person1#: So, we set a goal to double distribution in overseas markets. Now, when looking at the data to evaluate whether or not we made our goals, there are three things to consider. First, the original condition of the market, second, our marketing numbers from the previous year, and third, our final sales figures for this year. Now I want to describe for you the second and third parts. If you look at the overhead, you'll see a graph. The blue line represents our sales from the year 2005, the red line is the sales in 2006. As you can see, our sales in 2005 were quite slow to start off with, but managed to make decent performance in the last part of the year."}
{"answers": "#Person1# is not satisfied with #Person1#'s steak and asks to change one, and #Person2# agrees.", "prediction_text": "#Person1#: I'm sorry, sir."}
{"answers": "#Person1# complains to Tony that Christmas has made #Person1# busier.", "prediction_text": ": #Person1#: I'm sorry, but I have to go."}
{"answers": "#Person1# interviews #Person2# about the understanding of this company, personal preference, and salary expectation. #Person2# asks about the fringe benefits.", "prediction_text": "#Person1#: I'm very happy to work with you."}
{"answers": "Mr. Kayne shares with the audience about how he took over and established his bicycle shop as a salesman and how he's running his business now.", "prediction_text": "#Person1#: Thank you, Mr. Kayne. We wish you success in your new business."}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay the rent. Ayden is short on cash but still lends $20 to #Person1#. They decide to have dinner at #Person1#'s place.", "prediction_text": "#Person1#: Hey Ayden, I was wondering if you could do me a favor."}
{"answers": "#Person1# gives #Person2# a few suggestions on clothes but none is taken.", "prediction_text": "#Person1#: I like the color of the dress."}
{"answers": "#Person2# wants to buy a pair of Jordan shoes in size 41.", "prediction_text": "#Person1#: I'm sorry, but I can't afford to buy shoes."}
{"answers": "#Person1# and #Person2# exchange opinions toward the effect of earthquake and how people feel about it.", "prediction_text": "I'm not sure if this is a good idea."}
{"answers": "Mr. Murray wants a library card. #Person2# issues one to him after checking his application and driver license.", "prediction_text": "I have a question. I have a question."}
{"answers": "Frank explains to Judy the reason why he took a job at the Post office is that the government offers excellent health insurance benefits for its employees.", "prediction_text": "#Person1#: I am a very good cook."}
{"answers": "#Person1# hosts the week's People You Meet, featuring Mark Leach. Mark introduces his work as an information officer in London.", "prediction_text": "#Person1#: I'm Mark Leach. I'm an information officer at the Britain Business Center. Here, we offer a tourist information service to mainly visitors from overseas. And each year, we have about 500,000 people come to the office."}
{"answers": "#Person2# is going to set up #Person2#'s own law office. #Person1# wishes #Person2# good luck.", "prediction_text": "#Person1#: I'm going to set up my own law office, aren't you?"}
{"answers": "The workmen are walking across the wet cement to put up the notice.", "prediction_text": "The headmaster is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a man of few words. He is a"}
{"answers": "#Person1# asks #Person2# about the instructions for getting and using the discount coupons.", "prediction_text": "#Person1#: I will buy the sugar and the sugar will be free."}
{"answers": "Ann interviews Robin about the mountain lake cycle tour. Robin introduces the event and explains its settings of the tour.", "prediction_text": "#Person1#: Robin, what is the best way to get to the start line?"}
{"answers": "#Person1# gets lost on the way to Liu Ichang. #Person2# tells #Person1# to take Bus No. 713 because it's far.", "prediction_text": "#Person1#: I'm sorry, I'm lost. Can you show me where I am in this map?"}
{"answers": "#Person1# and #Person2# are planning the places of interest they are going to visit in London, such as Nelson's Column, Westminster Abbey, Tower of London, Madame Tussaud's Waxworks Museum, and so on. They are both looking forward to it.", "prediction_text": "#Person1#: London is such a historic city! There's history everywhere you look. There's nelson's column, built as a monument to one of the Britain's great admirals and his important victory. He won the battle of"}
{"answers": "#Person1# offers some job choices to #Person2#, but #Person2# doesn't like them.", "prediction_text": "#Person1#: I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer. I'm a writer"}
{"answers": "Tom calls Sara for her help to take care of his son Ken, because he is taking his daughter to the hospital.", "prediction_text": "#Person1#: I'm sorry to hear that. Is there anything I can do for you?"}
{"answers": "#Person1# drives #Person2# from the airport to an inn and they have a casual talk about themselves.", "prediction_text": "#Person1#: I am a very good student."}
{"answers": "#Person1# has difficulty getting access to the computers in the library to do #Person1#'s assignment.", "prediction_text": ": I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm frustrated. I'm"}
{"answers": "#Person1# and #Person2# talk about Abraham Lincoln and his glorious history. They both admire him.", "prediction_text": "#Person1#: I think of him as a man of sound character."}
{"answers": "Sarah is upset and complains to #Person1# about an inefficient meeting and Bob's interruption. #Person1# gives Sarah some suggestions on how to keep her speech short and sweet.", "prediction_text": "#Person1#: I'm sorry, I'm not sure what you're talking about."}
{"answers": "Marquet suggests #Person1# take a good introductory course for non-science majors.", "prediction_text": "I'm not sure if this is a good idea. I'm not sure if I should take the science course."}
{"answers": "#Person2# thinks #Person2#'s meal as perfect and orders dessert and tea to share with friends.", "prediction_text": "I'm not sure if this is a good idea."}
{"answers": "#Person2# describes to #Person1# about the relationship with the boss and what their boss strengths lie on.", "prediction_text": "#Person1#: I think he is a very good person."}
{"answers": "#Person1# can tell #Person2# their organization's decision by phone or e-mail in about one week.", "prediction_text": "#Person1#: I am a member of the American Society of Civil Engineers. I am a member of the American Society of Civil Engineers. I am a member of the American Society of Civil Engineers. I am a member of the American Society of Civil Engineers. I am a member of the American Society of Civil Engineers. I am a member of the American Society of Civil Engineers. I am a member of the American Society of Civil Engineers. I am a member of the American Society of Civil Engineers. I am a member of the American Society of Civil Engineers. I am a member of the American Society of Civil Engineers. I am a member of the American Society of Civil Engineers. I am a member of the American Society of Civil Engineers."}
{"answers": "Ms. Dawson takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists.", "prediction_text": "#Person1#: Ms. Dawson, I need you to take a dictation for me."}
{"answers": "#Person1# is starving and wants to have authentic Chinese cuisine so #Person2# recommends some. #Person1# chooses the Quanjude restaurant as #Person2# wants to try the Beijing roast duck and it's nearby.", "prediction_text": "#Person1#: I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry. I'm hungry."}
{"answers": "#Person1# wants to start #Person1#'s own business, but #Person2# warns #Person1# of the hassle. #Person2# tells #Person1# what is needed in a business plan and #Person1# decides to stick to the old job for now.", "prediction_text": "#Person1#: I am going to start a company!"}
{"answers": "#Person2# is teaching #Person1# how to use an ATM step by step because #Person1# doesn't know how to use.", "prediction_text": "#Person1#: I need to use the ATM."}
{"answers": "#Person1# confirms Jack's reservation for a double for three nights. Jack checks in.", "prediction_text": "#Person1#: I'm Jack."}
{"answers": "#Person2# wants to have his coffee with his dinner and his order for dessert later.", "prediction_text": "I'm a guy who's been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in the business for a while. I've been in"}
{"answers": "Nathan is going to Chicago for his practice in Chicago Tribune. His mother is worried about him and keeps asking him questions about this internship. Nathan says he'll work with a regular writer to get experience and she's finally at ease.", "prediction_text": "#Person1#: I'm glad you're back. I'm sure you'll be a great addition to the team."}
