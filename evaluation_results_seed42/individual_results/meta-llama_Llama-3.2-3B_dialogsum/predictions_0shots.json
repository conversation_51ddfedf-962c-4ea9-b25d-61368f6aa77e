{"answers": "#Person1# and #Person2# talk about pilgrims around the world, including Muslims' pilgrimage to mecca and Christians' pilgrimage to Canterbury or Vatican. #Person2# thinks faith heals people instead of magical places.", "prediction_text": "Dialogue: #Person1#: Hi! What are you watching?"}
{"answers": "#Person1# is sending a package with the help of #Person2#.", "prediction_text": "The post office is a place where you can send letters, packages, and money orders. You can also get stamps and airmail."}
{"answers": "#Person1# invites <PERSON> to picnic this weekend. <PERSON> accepts.", "prediction_text": "<PERSON> and her friend are going to have a picnic."}
{"answers": "#Person1# makes drinks for #Person2# at a bar.", "prediction_text": "The dialogue is about a conversation between a waiter and a customer in a bar. The customer is not sure what to order, so the waiter suggests a Compari, which is a popular Italian aperitif. The customer asks if the bar has a signature drink, and the waiter recommends a Singer, which is a non-alcoholic cocktail. The customer tries the Singer and enjoys it. The dialogue ends with the customer thanking the waiter and saying that he will enjoy himself."}
{"answers": "<PERSON> and <PERSON> are talking about their favourite subjects.", "prediction_text": "The dialogue is about two students, <PERSON> and <PERSON>. They are talking about their favorite subjects. <PERSON> says that English is his favorite subject. <PERSON> says that she doesn't like English because it is difficult for her. She says that <PERSON> also doesn't like science. <PERSON> <PERSON> says that he doesn't like math. <PERSON> says that she is not sure which subject she likes the most. She says that she likes Chinese and science. <PERSON> <PERSON> says that he likes PE."}
{"answers": "#Person2# tells #Person1# that he falls in love with his neighbour and #Person1# thinks he has some competition and wishes him good luck.", "prediction_text": "A dialogue between two people who are in love with the same girl."}
{"answers": "Mr. Smith asks #Person1# to help find his train ticket, but #Person1# finds out that Mr. Smith didn't have any proof of purchase. Thus Mr. Smith brings another ticket in the end.", "prediction_text": "A man is trying to buy a ticket to shanghai, but he has lost his wallet. He asks the ticket seller for help, but she is unable to help him. He then asks her to let him travel on a hard seat, but she refuses. He then asks her to let him travel on a soft sleeper, but he is unable to pay for it. He then asks her to let him travel on a hard seat, but she refuses. He then asks her to let him travel on a soft sleeper, but he is unable to pay for it. He then asks her to let him travel on a hard seat, but she refuses. He then asks her to let him travel on a soft sleeper, but he is unable to pay for"}
{"answers": "#Person1# congratulates #Person2# on #Person2#'s new venture and expresses #Person1#'s willingness to help.", "prediction_text": "Dialogue: #Person1#: You're going to set up your own law office, aren't you?"}
{"answers": "#Person1# gets a crank call and is angry about it.", "prediction_text": "A man is being harassed by a woman who keeps calling him. He tells her to stop calling him, and threatens to call the police if she doesn't."}
{"answers": "#Person1# introduces the weekend driving courses to #Person2#. #Person2# feels satisfied with its settings.", "prediction_text": "The dialogue is about a person who wants to know about the driving courses. The person is interested in the short full time courses during the summer. The person is free only at weekends. The person is interested in the weekend courses. The person wants to know about the coaches. The person wants to know about the hours of training. The person wants to know about the number of people sharing a training car. The person is interested in the 2 coaches for each car. The person is interested in the 3 hours of training in the morning and 2 hours of training in the afternoon. The person is interested in the 6:00 PM."}
{"answers": "#Person1# and #Person2# are talking about the most fundamental concepts underlying the accounting process.", "prediction_text": "The first is accounting entity, and the second is going concern. The third is measuring unit. The fourth is accounting period, and the fifth is objectivity."}
{"answers": "#Person1# and #Person2# are talking about the low temperature at night, although spring has come.", "prediction_text": "I'm not sure if this is a good idea or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should"}
{"answers": "Mark asks Maggie for her notes because Mark's notes aren't good. They decide to be study partners and study together.", "prediction_text": "Dialogue: #Person1#: Maggie, can I borrow your notes for history? I'll return them tomorrow."}
{"answers": "Joe doesn't think highly of Tom's new assistant while Tom does.", "prediction_text": "Dialogue: #Person1#: Hi Joe. You met my new assistant, right?"}
{"answers": "#Person1# and #Person2# are talking about the popular sports in their countries, including football, basketball, tennis, swimming, golf, extreme sports and rugby.", "prediction_text": "The dialogue is about sports. The first person asks the second person about the sports that are popular in his country. The second person says that football is the most popular sport. He also says that more boys than girls play football. He says that a few girls play football well. He says that some people like playing basketball. He says that more and more people like tennis. He says that fewer people play table tennis than before. He says that many people like swimming. He says that swimming is fun and keeps you fit. He says that extreme sports are only for a small minority of people. He says that several people from his university enjoy extreme sports. He says that most people just watch extreme sports. He says that there are plenty of golf courses"}
{"answers": "#Person1# thinks #Person2# has chicken pox and warns #Person2# about the possible hazards but #Person2# thinks it will be fine.", "prediction_text": "A person with chicken pox is contagious and can spread the disease to others."}
{"answers": "#Person1# gives #Person2# a physical check and advises #Person2# to stay in hospital for a shoulder X-ray the next day.", "prediction_text": "A doctor examines a patient's shoulder."}
{"answers": "People in #Person2#'s country like football, basketball, tennis, and swimming. People in #Person1#'s country like golf. A few people like extreme sports. Both people like rugby.", "prediction_text": "The dialogue is about sports. The first person asks the second person about the sports that are popular in his country. The second person says that football is the most popular sport. He also says that more boys than girls play football. He says that a few girls play football well. He says that some people like playing basketball. He says that more and more people like tennis. He says that fewer people play table tennis than before. He says that many people like swimming. He says that swimming is fun and keeps you fit. He says that extreme sports are only for a small minority of people. He says that several people from his university enjoy extreme sports. He says that most people just watch extreme sports. He says that there are plenty of golf courses"}
{"answers": "Sue doesn't eat cakes and sandwiches at her birthday party to avoid being allergic. Bill is willing to order her some hot soup to make her feel better. They decide to go to the restaurant later.", "prediction_text": "Dialogue: #Person1#: Why don't you have some of my cake, Sue? I made it just for this party."}
{"answers": "#Person2# tells #Person1# #Person2#'s writing experience.", "prediction_text": "The dialogue is between a newspaper editor and a potential employee. The editor is trying to find out why the employee is interested in the job. The employee is trying to find out more about the job."}
{"answers": "#Person1# forgot to take an umbrella while it's raining. #Person2# shares an umbrella with #Person1#.", "prediction_text": "The dialogue is between a man and a woman. The man asks the woman what time it is. The woman tells him that it is almost eleven twenty. The man thanks her. The woman asks the man if the rain is heavy. The man says that it is. The woman offers to share her umbrella with the man. The man says that he is going to the Garden Hotel. The woman says that she is going there, too. The man says that they can walk together. The woman says that they can. The man says that they can."}
{"answers": "#Person1# comes to sign an agreement but it isn't ready until evening. So #Person1# is going through the draft.", "prediction_text": "The dialogue is about a business deal. The first person is a businessman who is here to sign the agreement. The second person is the person who is responsible for the agreement. The first person asks the second person to speed up the agreement and let them have it today. The second person says that he will try his best. He then gives the first person a draft of the agreement. The first person says that it contains basically all they have agreed upon. The second person asks about the terms concerning packing. The first person says that he doesn't think so. The second person says that if the first person totally agrees, he will type the agreement this evening and have it duplicated for signatures. The first person says that's fine."}
{"answers": "#Person2# instructs #Person1# on how to send a package by mail.", "prediction_text": "The post office is a place where you can send letters, packages, and money orders. You can also get stamps and airmail."}
{"answers": "#Person2# talks about Katie's evaluation and wants Katie to do better when she doesn't have customers.", "prediction_text": "The manager is trying to improve the employee's performance. The employee is trying to improve. The manager is trying to help the employee improve. The employee is trying to improve. The manager is trying to help the employee improve. The employee is trying to improve. The manager is trying to help the employee improve. The employee is trying to improve. The manager is trying to help the employee improve. The employee is trying to improve. The manager is trying to help the employee improve. The employee is trying to improve. The manager is trying to help the employee improve. The employee is trying to improve. The manager is trying to help the employee improve. The employee is trying to improve. The manager is trying to help the employee improve. The employee is"}
{"answers": "#Person1# tells #Person2# that Ruojia is married and will have a party tonight. #Person2#'s surprised to know that. They will bring their gifts to bless her.", "prediction_text": "Dialogue: #Person1#: Would you like to go to the party tonight?"}
{"answers": "#Person2# would like to go to Australia.", "prediction_text": "The speaker asks the listener if they have been to Australia. The listener says that they haven't. The speaker asks if the listener would like to go there. The listener says that they would like to go there. The listener says that they would like to see the Great Barrier Reef."}
{"answers": "James and Muriel are talking while waiting for Susan, Muriel's associate. They talk about how they spent the holiday with their families.", "prediction_text": "This dialogue is a sample of a conversation between a salesperson and a potential client. The salesperson is trying to get the client to agree to a meeting to discuss a new account. The client is a little hesitant at first but eventually agrees to meet. The salesperson offers the client something to drink while they wait for the other person to arrive. The client says no thanks and then asks the salesperson about their holiday. The salesperson says that they had a nice holiday and then asks the client about theirs. The client says that they stayed in L. A. and it was sunny the entire weekend. They spent most of the time at home but they did go see King Kong on Christmas day. The salesperson asks how they liked it and"}
{"answers": "Amy is talking about her first job experience and later job with #Person1#.", "prediction_text": "The dialogue is about a person who worked as a secretary and then as a personnel manager."}
{"answers": "#Person2# thinks #Person1#'s new suit is not worthwhile.", "prediction_text": "The dialogue is about a man who is trying to sell his new suit to a friend. The friend is not interested in buying it because he thinks it is too expensive."}
{"answers": "#Person2# tells #Person1# the charge policy at #Person2#'s buffet.", "prediction_text": "The dialogue is about a restaurant. The first person is the waiter and the second person is a customer. The customer asks the waiter how much the buffet costs and where he can get the food. The waiter tells him that the buffet costs thirty yuan for each adult and twenty yuan for each kid. He also tells the customer that the cold dishes are on one side and the hot dishes are on the other side. The customer asks if he needs to pay extra for drinks. The waiter tells him that he doesn\u2019t need to pay extra for soft drinks but he needs to pay ten yuan for each alcohol order."}
{"answers": "#Person1# was too busy to reconfirm a fight reservation and needs to buy another ticket.", "prediction_text": "The dialogue is about a person who has booked a ticket and is trying to pick it up. The person at the counter is not willing to give the ticket to the person because he has not reconfirmed the ticket. The person is trying to get another ticket."}
{"answers": "Peter stops watering the garden and will have tea with #Person1#, since it's raining.", "prediction_text": "Dialogue: #Person1#: Can't you come in and have tea now, Peter?"}
{"answers": "#Person1# and #Person2# are discussing where to have fun, and they decide to go to the theater tonight.", "prediction_text": "The speaker is asking the listener if he or she is enjoying his or her trip to New Orleans. The listener says that he or she is enjoying the trip. The speaker asks if the listener would like to do something tonight. The listener says that he or she would love to. The speaker asks if the listener has been to a jazz club yet. The listener says that he or she has already been to several clubs. The speaker asks if the listener would like to go on a riverboat tour. The listener says that he or she has already gone twice this week. The speaker asks what the listener would like to do. The listener says that he or she would like to go to the theater. The speaker says that he or she knows of a"}
{"answers": "#Person2# likes a skirt at #Person1#'s store, but thinks it too expensive.", "prediction_text": "The dialogue is about a customer who is looking for a skirt. The salesperson suggests a skirt that is in fashion. The customer tries on the skirt and likes it. The customer asks how much it costs. The salesperson says that it costs 400 dollars. The customer is surprised and says that the price is ridiculous."}
{"answers": "Mike is describing his sister to #Person1#.", "prediction_text": "The speaker is talking about his sister. He asks his friend what she looks like. The friend says she is tall and pretty. The speaker asks if she is like him. The friend says she is friendly and easy-going. The speaker asks if she is as clever as he is. The friend says she is not as clever as he is. The speaker says she has a big head."}
{"answers": "#Person2# feels bored with the meeting. #Person1# gives #Person2# some suggestions on how to entertain but finally asks #Person2# to keep quiet.", "prediction_text": "A man and a woman are at a charity event. The man is bored and the woman is trying to find something to do."}
{"answers": "It's #Person1# and #Person2#'s grandpa's birthday next week. #Person1# and #Person2# decide to have a party with jazz, pizza at their house. #Person1# and grandma will make food. #Person2# will get him a book. The party will be on Saturday.", "prediction_text": "The dialogue is about a surprise party for grandpa. The two people are discussing the details of the party. The first person wants to have a party at home, but the second person thinks that it is too cold to have a party outside. The first person suggests a hotel, but the second person thinks that it is too expensive. The first person suggests that they should have a party at home, but the second person thinks that it is too cold to have a party outside. The first person suggests a hotel, but the second person thinks that it is too expensive. The first person suggests that they should have a party at home, but the second person thinks that it is too cold to have a party outside. The first person suggests a hotel,"}
{"answers": "#Person1# asks #Person2# for the way to Peak Tram and writes it down.", "prediction_text": "This dialogue is a conversation between a tourist and a local resident. The tourist asks for directions to the Peak Tram, and the local resident gives him detailed instructions. The tourist then asks for a pencil to write down the directions, and the local resident gives him one."}
{"answers": "#Person1# is withdrawing money from an ATM. But the ATM wrongly transfers #Person1#'s money to the World Wildlife Foundation. It drives #Person1# crazy.", "prediction_text": "A man is trying to withdraw money from the ATM, but the machine is not cooperating. The man gets angry and starts yelling at the machine, but the machine continues to ignore him. The man eventually gives up and leaves, but not before the machine locks the doors and seals the exits. The man is left stranded and has to wait for the authorities to arrive."}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. Then they want to have fried bananas and #Person1# goes out to buy some.", "prediction_text": "The dialogue is about two people who are eating dessert at a restaurant. The first person is trying a traditional Greek yogurt, while the second person is trying an Italian tiramisu. The first person offers the second person a bite of his tiramisu, and the second person tries it and says that it is the best tiramisu he has ever had. The first person then offers the second person a fried banana, and the second person accepts."}
{"answers": "#Person1# and #Person2# talk about what kind of movies to watch and they decided to watch a war movie.", "prediction_text": "Dialogue: #Person1#: Would you like to go to the movies tonight?"}
{"answers": "#Person1# and #Person2# feel happy because #Person1# is pregnant.", "prediction_text": "The dialogue is about a couple who are going to have a baby. The man is happy and excited about the news, but the woman is surprised and shocked. The man tries to comfort her and tell her that she is going to be a good mother. The woman is happy and excited about the news, but she is also nervous and scared. The man tries to reassure her that everything will be okay."}
{"answers": "#Person1# pushes #Person2# to pay for the dormitory soon, but #Person2# is thinking about living off campus. They analyze the advantages and disadvantages of living on and off campus.", "prediction_text": "Dialogue: #Person1#: You should have seen the line at the housing office. It took me an hour to make my dormitory deposit for next year. Have you made yours yet? #Person2#: No, I'm not sure I'm going to. #Person1#: There's not much time left. The deadline's May 1. That is just two weeks from now. Are you short of cash? #Person2#: No, I'm Okay. #Person1#: You'd better hurry up if you want a dorm room next September. There aren't enough rooms for every one, and first year students have priority. #Person2#: Well, I've been thinking about living off campus. #Person1#: Have you"}
{"answers": "Johnson shows #Person1# the correct way to use weights machine and tells #Person1# using a card to keep track of exercise intensity. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "The dialogue is about a new member of a gym. The narrator is a trainer who helps the new member to get started. The narrator explains how to use the machines and how to keep track of the weight used. The narrator also explains the importance of not pushing oneself too hard."}
{"answers": "#Person2# has been working overtime and only gets minimum wage. #Person1# suggests #Person2# either asking for more compensation or quitting, but #Person2# refuses in fear of not being able to find another job.", "prediction_text": "The dialogue is about a person who is working overtime without any compensation. The person is working for minimum wage and is not getting any extra pay for the overtime. The person is not getting any incentive for working so many hours. The person is afraid that if he quits, he won't be able to find another job."}
{"answers": "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks Ms. Dawson to send out a memo to all employees by the afternoon.", "prediction_text": "A boss gives a memo to his secretary to type up. The secretary asks a question about the memo, and the boss gives a curt answer. The secretary then types up the memo and distributes it to all employees."}
{"answers": "#Person1# interviews Tom about his experience to start a company selling green products, how to live a mildly green life and to learn from mistakes.", "prediction_text": "The dialogue is about a successful businessman who is now a manager of a company selling green products. He talks about his family and how they left Budapest in 1986 and settled in Canada. He also talks about his friends' parents who were highly successful businessmen. He then talks about how he started a web-designed company and learned a lot from that experience. He also talks about how he raised 18 million dollars from about 50 people. He then talks about how he owns 6% of the company and all employees get their shares. He also talks about how he is only mildly green. He then talks about how he is a huge biker and how he biked cross Canada. He then talks about how he has never done anything wasteful"}
{"answers": "#Person1# and #Person2# talk about the weather and #Person2# invites #Person1# to have a picnic.", "prediction_text": "The dialogue is about the weather. The two people are talking about the weather and the weather forecast."}
{"answers": "#Person1# helps #Person2# to return a book and check out a video in the library.", "prediction_text": "The librarian is helping a customer return a book and check out a video."}
{"answers": "#Person2# describes how the working days are like, including the working hours and working contents, to #Person1#.", "prediction_text": "The dialogue is about a person's job. The speaker is asking questions about the person's work. The person is answering the questions. The speaker is asking about the person's work day. The person is answering the questions. The speaker is asking about the person's job. The person is answering the questions. The speaker is asking about the person's job. The person is answering the questions. The speaker is asking about the person's job. The person is answering the questions. The speaker is asking about the person's job. The person is answering the questions. The speaker is asking about the person's job. The person is answering the questions. The speaker is asking about the person's job. The person is answering the questions. The speaker is"}
{"answers": "#Person1# and #Person2# are shocked about Sarah's love for Dick.", "prediction_text": "When Sarah told #Person1# that she would marry no man but Dick, #Person1# was scared. #Person2# was scared too, but he did not tell #Person1# his feeling."}
{"answers": "Gian and Gina introduce themselves to each other and Gian introduces the friend Robert to Gina.", "prediction_text": "Dialogue: #Person1#: Can I introduce myself? I'm Gian Luca Donatelli. I'm from Spain. #Person2#: I'm Gina. I'm from Finland. #Person1#: And who do you work for? #Person2#: I don't work for a company. I'm self-employed. I am a journalist, I write articles for magazines. I'm here at this conference to research for an article on internet service providers. #Person1#: That's interesting, a friend of mine works for an Italian service provider. Can I introduce you to him? #Person2#: Yes, of course, that would be nice. #Person1#: Robert, can you come here for a minute? This is Gina."}
{"answers": "#Person1# asks Tom for his opinion on second-hand goods and Tom suggested #Person1# being careful.", "prediction_text": "The dialogue is about the second-hand goods. The speaker is considering buying a second-hand computer. The speaker thinks that the second-hand goods are just as good as the new ones. The speaker thinks that the second-hand goods are just as good as the new ones. The speaker thinks that the second-hand goods are just as good as the new ones. The speaker thinks that the second-hand goods are just as good as the new ones. The speaker thinks that the second-hand goods are just as good as the new ones. The speaker thinks that the second-hand goods are just as good as the new ones. The speaker thinks that the second-hand goods are just as good as the new ones. The speaker thinks that the second-hand goods are just as good"}
{"answers": "#Person1# and #Person2# go to the nightclub to dance and #Person1# prefers fast dances to slow dances.", "prediction_text": "Dialogue: #Person1#: How is night life in Beijing?"}
{"answers": "#Person2# will change a steak for #Person1# as that one doesn't satisfy #Person1#.", "prediction_text": "Dialogue: #Person1#: Excuse me."}
{"answers": "#Person1# and #Person2# both get laid off and they want to find a job.", "prediction_text": "This is a dialogue between two people who are looking for work. The first person is looking for any job that will pay the mortgage, while the second person is looking for an electrician apprenticeship. The first person is looking for a job, while the second person is looking for an apprenticeship. The first person is looking for a job, while the second person is looking for an apprenticeship. The first person is looking for a job, while the second person is looking for an apprenticeship. The first person is looking for a job, while the second person is looking for an apprenticeship. The first person is looking for a job, while the second person is looking for an apprenticeship. The first person is looking for a job, while"}
{"answers": "#Person1# begs Steven's to persuade #Person1#'s wife not to divorce him, and Steven agrees.", "prediction_text": "Dialogue: #Person1#: Steven, I need badly your help. #Person2#: What's the matter? #Person1#: My wife has found that I have an affair with my secretary, and now she is going to divorce me. #Person2#: How could you cheat on your wife? You have been married for ten years. #Person1#: Yes, I know I'm wrong. But I swear that the affair lasts only for two months. And I still love my wife. I couldn't live without her. #Person2#: I will try my best to persuade her to reconsider the divorce. But are you sure that from now on you will be faithful to her forever? #Person1#: Yes, I swear."}
{"answers": "#Person1# thinks it's strange of Mirella wearing casually in the office. #Person2# thinks she was influenced by the casual atmosphere in California and tells #Person1# management decided to give her a new white dress to warn her.", "prediction_text": "The speaker is complaining about the way Mirella dresses at work. She is wearing jeans and sweatshirts, which is not acceptable in the office. The speaker thinks that Mirella is trying to make herself at home in the office. The speaker thinks that Mirella is influenced by the way people dress in California. The speaker thinks that Mirella's new style of dressing down is not quite acceptable."}
{"answers": "#Person1# and #Person2# are talking about what gifts to buy for their families from the Olympic souvenir store after the volleyball match.", "prediction_text": "Dialogue: #Person1#: what shall we do after the volleyball match? #Person2#: how about going to the Olympic souvenir store to have a look? I remember there is one in the gym. #Person1#: that's a good idea. I was thinking of buying some gifts for my family. #Person2#: me, too. What did you have in mind? #Person1#: I haven't deceided yet, but I think the Olympic Mascots are a must. #Person2#: I agree. They're very lovely, especially 'Huan Huan'. #Person1#: is that the red one? That is my favorite, too. Do you know how much they are? #Person2#: the five dolls"}
{"answers": "#Person1# asks about the things that took up #Person2#.", "prediction_text": "This dialogue is a simple example of how to use the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in the \"dialogue\" feature in"}
{"answers": "Tom suggests going swimming, but Jane refuses. They decide to meet at the Grill to eat and then go to study together in the library.", "prediction_text": "Jane and Tom are friends who want to spend time together. They have a disagreement about how to spend their time."}
{"answers": "Mom asks May to help to prepare for the picnic and May agrees.", "prediction_text": "Dialogue: #Person1#: May, do you mind helping me prepare for the picnic? #Person2#: Sure. Have you checked the weather report? #Person1#: Yes. It says it will be sunny all day. No sign of rain at all. This is your father's favorite sausage. Sandwiches for you and Daniel. #Person2#: No, thanks Mom. I'd like some toast and chicken wings. #Person1#: Okay. Please take some fruit salad and crackers for me. #Person2#: Done. Oh, don't forget to take napkins disposable plates, cups and picnic blanket. #Person1#: All set. May, can you help me take all these things to the living room? #Person2"}
{"answers": "Lucy likes English and P.E. best, but Lin Fang's favorite is Chinese and Science.", "prediction_text": "The dialogue is about two students, Lin Fang and Lucy. They are talking about their favorite subjects. Lin Fang says that English is his favorite subject. Lucy says that she doesn't like English because it is difficult for her. She says that Nancy also doesn't like science. Lin Fang says that he doesn't like math. Lucy says that she is not sure which subject she likes the most. She says that she likes Chinese and science. Lin Fang says that he likes PE."}
{"answers": "Sarah tells #Person1# that she can't afford a new house. #Person1# says #Person1#'s sister-in-law bought a cheap house and suggests Sarah should buy a house far from the city center.", "prediction_text": "The speaker is talking to a friend about a house that is for sale. The friend is interested in buying it but is worried about the location. The speaker tells her that the house is in a good location and that the neighbors are friendly."}
{"answers": "#Person1# invites Peter to have tea, but Peter is watering the garden. Then it rains, Peter can get in and have tea with #Person1#.", "prediction_text": "Dialogue: #Person1#: Can't you come in and have tea now, Peter?"}
{"answers": "#Person1# wants to pick up a ticket at #Person2#'s and pays by card.", "prediction_text": "The dialogue is about a person who is coming to pick up his ticket. The dialogue is between a person who is working at the ticket counter and the person who is coming to pick up his ticket. The dialogue is about the ticket price and the payment method."}
{"answers": "Ms. Ross and Mr. Fisher start the conversation with greetings. Then they discuss the preparation for a presentation. Ms. Ross confirms the details of the presentation, including schedule, food service. Ms. Ross is concerned that Mr. Fisher's sent too many invitations than the room capacity.", "prediction_text": "Dialogue: #Person1#: Ah, good morning, Ms. Ross, do come in."}
{"answers": "Mr. Faber calls #Person1# to book a double room for 3 nights at York Hotel.", "prediction_text": "A man calls a hotel to book a room for three nights."}
{"answers": "#Person2# is making a shopping budget to save the rest of the money. #Person1# thinks it's a good idea.", "prediction_text": "The dialogue is about a person who is making a budget to save money. The person is trying to save money for bills. The person is also trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is"}
{"answers": "#Person1# makes requires about #Person2#'s English standard.", "prediction_text": "The interviewer is interested in the candidate\u2019s English proficiency. The candidate is asked about his/her English education and other languages he/she speaks."}
{"answers": "#Person2# tells Mary about #Person2#'s terrible experience that a dog rushed at #Person2# and barked loudly when #Person2# was delivering milk.", "prediction_text": "A man was delivering milk to a house when he saw a note on the door. He jumped over the fence and went towards the door. Suddenly, a huge dog rushed at him. He started running as fast as he could, but he didn't see that the big branch of the tree near the garden gate. He knocked into it. The dog didn't jump over the fence, but stayed in the garden barking loudly."}
{"answers": "#Person1# speculates the signature on the book is valuable, but #Person2# thinks it is somewhat impractical.", "prediction_text": "The two people in this dialogue are in a secondhand bookstore. They are looking at a book. The first person says that he likes to look at old books. The second person says that some of the books are not so old. The first person says that he is interested in nineteenth-century plays. The second person says that he is not interested in them. The first person says that he is going to buy the book and see if he can find a name that looks like the name of a famous person. The second person says that he thinks that the story is a better buy."}
{"answers": "Emily has her first paycheck in the States and there are a few things she doesn't understand. Susan explains the federal and state deductions to her. Emily thinks it might be the same in the UK but she just never paid much attention before.", "prediction_text": "This dialogue is about a new employee who is confused about the deductions on his paycheck. The dialogue is between the employee and his new boss, who explains the deductions to him. The dialogue is a good example of how to explain complex financial concepts to someone who is unfamiliar with them."}
{"answers": "Catherine and Tom talk about American fast-food culture inspired by a movie, and they think Americans need to find a way to make the most of their fast foods.", "prediction_text": "Dialogue: #Person1#: Catherine, have you ever seen the movie Fast Food Nation?"}
{"answers": "#Person1# teaches #Person2# the rules and terms of bowling.", "prediction_text": "The dialogue is about bowling. The first person is a teacher and the second person is a student. The teacher explains the rules of bowling to the student. The student asks the teacher to explain the rules again. The teacher explains the rules again. The student asks the teacher to play with him. The teacher agrees to play with the student."}
{"answers": "#Person1# and #Person2# talk about the difficulty of not having a personal computer.", "prediction_text": "The speaker is frustrated because he is unable to use the computers in the library. The other person understands the speaker's frustration and is looking forward to the day when he can afford to get his own computer."}
{"answers": "#Person2# wants to know about requesting a loan. #Person1# introduces the policy of loan interest and credit assessment. #Person2# then leaves as he has a terrible credit score.", "prediction_text": "This is a dialogue between a customer and a bank employee. The customer is interested in a loan but the bank employee informs him that he has a bad credit score and that he will not be able to get the loan."}
{"answers": "#Person1# and Bob share their last weekend's activities. And they decide to play a game this weekend.", "prediction_text": "The dialogue is about a person who is a tennis player. He is asked what he did last weekend. He says he stayed at home and watched TV in the morning and went shopping in the afternoon. He also played tennis on Sunday. He says he won the game. The other person asks if he can play with him sometime this weekend. He says he can."}
{"answers": "Steven calls Mr. Sellers and asks him what he should do to deal with a power failure.", "prediction_text": "Dialogue: #Person1#: Mr. Sellers? It's Steven speaking, your tenant. We've just suffered a power failure. What should I do now?"}
{"answers": "Steven tries to do the repairment under Mr. Seller's instruction to solve power failure.", "prediction_text": "Dialogue: #Person1#: Mr. Sellers? It's Steven speaking, your tenant. We've just suffered a power failure. What should I do now?"}
{"answers": "Fanny had a nightmare because of the pressure from school applications. Her mother and Andy both comfort her.", "prediction_text": "The dialogue is about a student who had a bad dream. He was chased by ghosts in the graveyard. His mother woke him up. He was embarrassed. He was afraid to fall back asleep. He was worrying about getting into the University of Michigan."}
{"answers": "#Person1# asks #Person2#'s opinion on new clothes, but #Person2# only focuses on the game and feels annoyed. They are both angry and argue.", "prediction_text": "This is a dialogue between two people who are having a conversation about a game. The first person is trying to talk about their new clothes, but the second person is trying to watch the game. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person"}
{"answers": "#Person1# and Mr. Simpson will have lunch together.", "prediction_text": "The dialogue is about a meeting between two people. The first person is Mr. Simpson and the second person is Mr. Smith. The first person asks the second person if he is free for lunch. The second person says that he is free on Thursday. The first person says that he knows of a place that the second person will like and he has to be there. The second person says that he will be there at 12:30. The first person says that he will be there at 12:30. The second person says that he will see the first person."}
{"answers": "James helps Mrs. Thomas to do chores because James wanted to save money and Mrs. Thomas is incapable.", "prediction_text": "The dialogue is between a man and a woman. The man is James and the woman is Mrs Thomas. James is helping Mrs Thomas with her housework. Mrs Thomas is very grateful for James's help. She asks him to take her dog for a walk. James agrees to do so. Mrs Thomas is very happy that James is helping her. She says that she is very grateful to him. She also says that she is very happy that James is helping her. She says that she is very happy that James is helping her. She says that she is very happy that James is helping her. She says that she is very happy that James is helping her. She says that she is very happy that James is helping her. She says that she is"}
{"answers": "Steven wants to stay up to go over the household budget when #Person1# is going to sleep.", "prediction_text": "The dialogue is between a husband and wife. The husband is going to bed and the wife is going to stay up a while. The husband is tired and wants to go to bed. The wife is not tired and wants to finish the household budget. The husband is not interested in the budget and wants the wife to go to bed. The wife is not interested in going to bed and wants to finish the budget. The husband is tired and wants to go to bed. The wife is not tired and wants to finish the budget. The husband is tired and wants to go to bed. The wife is not tired and wants to finish the budget. The husband is tired and wants to go to bed. The wife is not tired and wants to finish the"}
{"answers": "#Person2# cannot stand the noise near her room and #Person1# promises to change her room tomorrow as there is no room available today.", "prediction_text": "Dialogue: #Person1#: Good morning. What can I do for you?"}
{"answers": "Jack tells #Person1# that business communication is his favorite last year and #Person1# will check it.", "prediction_text": "The dialogue is about a conversation between two people. The first person asks the second person how his classes went. The second person says that they were not too bad. The first person asks if the second person liked his poli-sci class. The second person says that it was not his favorite, but it was good. The first person asks what his favorite class was. The second person says that he took a business communication class last year and it was terrific. The first person says that he never took that class and that he will check it out."}
{"answers": "Laura comes across #Person1# on the way to the gym, and they stop to talk about exercise habits. Unlike Laura, #Person1# prefers other exercises over going to the gym.", "prediction_text": "The dialogue is about exercise. The two people are talking in the street. The first person is a woman, and the second is a man. The woman is going to the gym, and the man is going for a walk. The woman asks the man why he doesn't exercise more. The man says he doesn't have time, and the woman says that she does a lot of exercise for both health and looks. The man says he doesn't have time, but the woman says that she does a lot of exercise for both health and looks. The man says he doesn't have time, but the woman says that she does a lot of exercise for both health and looks. The man says he doesn't have time, but the woman says that"}
{"answers": "#Person1# and #Person2# have a chat about the current rainy season which #Person1# can hardly endure while #Person2# has already been used to.", "prediction_text": "The weather is really miserable. It has been raining hard all day long. Yes, it's so cold. Do you think the rain is going to let up soon? Yes, I think so. The rainy season will be over soon. How long does the rainy season last? About two months. I can hardly stand these wet and cold days. It seems that it doesn't bother you very much. I'm used to this kind of days. But I prefer warm weather. Spring will come soon. And we will have some pleasant weather then. I hope so."}
{"answers": "#Person2# has a girlfriend in Thailand. They know each other on the Internet but never meet. #Person1# is surprised but congratulates #Person2#.", "prediction_text": "The dialogue is about a man who is going to meet his girlfriend in Thailand. He is surprised to hear that the girl is his girlfriend because he has never met her before. The man is also surprised that they are planning to get married so soon. The man says that the Internet is making interaction much faster."}
{"answers": "#Person1# suggests having beers after dinner. Jim refuses. They decide to go to the gym to meet their friends.", "prediction_text": "The dialogue is about two friends who are trying to decide what to do after dinner. They are trying to decide whether to go for a few beers or to go to the gym. The first friend suggests going for a few beers, but the second friend says that it is not good for their fitness. The first friend says that it will help them to relax, but the second friend says that it will just make them fat and act silly. The first friend says that he doesn't feel like sitting at home, but the second friend suggests that they go to the gym where they can play singsong and meet some of their friends. The first friend says that he hears that Mary and Sally often go to the gym to play pingpong, and the second"}
{"answers": "#Person1# will send #Person1#'s son an important certificate by certified mail. And #Person2# suggests #Person1# send a watch by registered mail.", "prediction_text": "The speaker wants to send a package to his son. He asks for advice on how to do it. The speaker wants to send a package to his son. He asks for advice on how to do it."}
{"answers": "#Person1# asks #Person2# how to use the library. #Person2# offers specific directions.", "prediction_text": "The dialogue is about the library. It is a place where people can borrow books and read journals or magazines. The library has two books at a time. The books can be kept for one month. If you can't return the books in time, you'll be fined. But if you renew them, you can keep them longer. The dialogue is about the library. It is a place where people can borrow books and read journals or magazines. The library has two books at a time. The books can be kept for one month. If you can't return the books in time, you'll be fined. But if you renew them, you can keep them longer."}
{"answers": "#Person2# offers #Person1# two ways to send the package and #Person1# chooses the cheaper one.", "prediction_text": "The dialogue is about a person who wants to send a package to Korea. The person is told that it will cost 20 pounds to send it by first class mail, but there is a cheaper way. The person decides to send it by package post, which will take 10 days longer than first class mail."}
{"answers": "#Person1# and #Person2# talk about the weather and how to keep warm.", "prediction_text": "I'm not sure if this is a good idea or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should"}
{"answers": "#Person2# wants to inquire about a loan for studying abroad. #Person1# explains the policy and age limitations to #Person2#. #Person2# will decide later.", "prediction_text": "The above dialogue is a sample of a conversation between a customer and a bank representative. The customer is interested in a personal loan for studying abroad. The bank representative provides information about the loan and its eligibility criteria. The customer has some questions about the loan and the representative answers them. The customer expresses interest in the loan and plans to discuss it with their parents. The representative thanks the customer for their interest and provides contact details for further assistance."}
{"answers": "#Person2# appreciates for #Person1# 's kind help.", "prediction_text": "Dialogue: #Person1#: You're all set now?"}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay for the house rent but Ayden lends $20 because of the lack of money. They decide to have dinner at #Person1#'s place.", "prediction_text": "This is a dialogue between two people, #Person1# and #Person2#. #Person1# is asking #Person2# for a favor, and #Person2# is trying to help #Person1# out. #Person1# is in a tight spot, and #Person2# is trying to help him out. #Person2# is also in a tight spot, and he is trying to help #Person1# out. #Person1# is grateful for #Person2#'s help, and he promises to pay #Person2# back on Monday. #Person2# is happy to help #Person1#, and he agrees to come over for dinner at #Person1#'s place. #Person1#"}
{"answers": "#Person1# agrees with #Person2#'s doctor that #Person2# should diet.", "prediction_text": "The dialogue is about a person who has been eating too much and has been told by the doctor to cut down on red meat. The person is advised to put himself on a diet."}
{"answers": "#Person2# has traditional Greek yogurt, which #Person1# thinks rather plain. #Person1# has an Italian tiramisu, which #Person2# thinks delicious. #Person1# goes and gets both of them a fried banana.", "prediction_text": "The dialogue is about two people who are eating dessert at a restaurant. The first person is trying a traditional Greek yogurt, while the second person is trying an Italian tiramisu. The first person offers the second person a bite of his tiramisu, and the second person tries it and says that it is the best tiramisu he has ever had. The first person then offers the second person a fried banana, and the second person accepts."}
{"answers": "#Person2# has the bottom bunk by winning the coin flip with #Person1#. They decide to use #Person1#'s stereos because it's better. #Person1# has the desk by the window for it's fair. #Person1# will go out for food and come back to unpack after #Person2# has done.", "prediction_text": "Dialogue: #Person1#: Alright, we need to have a plan here. First of all, which bed do you want? #Person2#: Well, everybody wants the bottom bunk. Why don't we flip a coin for it? #Person1#: Alright. Do you have a coin? #Person2#: Yes. Here's a quarter. I flip it, you call it in the air. #Person1#: Heads. #Person2#: Sorry, it's tails. You lose. #Person1#: Oh, well. So you get the bottom bunk. What about our stereos? It looks like we both brought our stereos. Probably we only have room for one. #Person2#: Your stereo is better than mine"}
{"answers": "Bill tells Jim that Dick's been sick.", "prediction_text": "The dialogue is about a person who is sick and is recovering. The speaker is asking about the sick person and the other person is answering the question. The speaker is asking about the sick person's health and the other person is answering the question. The speaker is asking about the sick person's health and the other person is answering the question. The speaker is asking about the sick person's health and the other person is answering the question. The speaker is asking about the sick person's health and the other person is answering the question. The speaker is asking about the sick person's health and the other person is answering the question. The speaker is asking about the sick person's health and the other person is answering the question. The speaker is asking about the"}
{"answers": "#Person1# works as a toy salesperson and feels so tired recently because Christmas is coming, and everyone's shopping for presents.", "prediction_text": "Dialogue: #Person1#: Hi Tony, I feel so tired."}
{"answers": "#Person1# might make a peanut butter and jelly sandwich to eat.", "prediction_text": "Dialogue: #Person1#: I'm hungry."}
{"answers": "#Person1# prefers sending the package by post to by first class after being told the price and the duration by #Person2#.", "prediction_text": "The dialogue is about a person who wants to send a package to Korea. The person is told that it will cost 20 pounds to send it by first class mail, but there is a cheaper way. The person decides to send it by package post, which will take 10 days longer than first class mail."}
{"answers": "Kathy complains about the noise and #Person2# explains it's because it's the year of the 17-year bird calls. Then they discuss where the birds live.", "prediction_text": "The birds are singing in the trees."}
{"answers": "#Person1# is taking off to restart a business. #Person1# expresses appreciation to #Person2# and asks #Person2# to take care of Pamela.", "prediction_text": "Dialogue: #Person1#: Where is Pamela?"}
{"answers": "#Person2# at first thinks #Person1#'s behaviour cruel but finally joins #Person1#.", "prediction_text": "A man and a woman are having a conversation. The man is trying to convince the woman to let him use her as a human shield."}
{"answers": "#Person1# has no jobs but is unwilling to go home. #Person2# lends $50 to #Person1#.", "prediction_text": "The dialogue is about a person who is looking for a job. He goes to the employment office every day. He doesn't like his father's farm. He wants to find a job he likes. He needs a little luck."}
{"answers": "#Person2# has applied for a student visa in the states and tells #Person1# about the rules and the required documents of applying for a USA visa and why they are so strict.", "prediction_text": "Dialogue: #Person1#: Have you applied for you visa to go to study in the united states yet?"}
{"answers": "Mr. Steven Kayne is invited to share his experience of establishing a bicycle shop by a business program.", "prediction_text": "The speaker is talking about a new bicycle shop that has just opened. The speaker asks the owner a few questions about his business. The owner says that he loves biking and fixing bikes. He also says that he is his own boss and that he can open and close the store whenever he wants. The speaker asks the owner if he has hired any employees to work with him. The owner says that he has hired a couple of friends who love biking as much as he does. The speaker thanks the owner and wishes him success in his new business."}
{"answers": "#Person2# tells #Person1# that #Person2#'s house was robbed.", "prediction_text": "The dialogue is about a person who is sad because his house was robbed. He is sad because he lost all his furniture. The person who is sad has called the police. He has to wait for the police to come."}
{"answers": "#Person2# admires Abraham Lincoln for his perseverance, courage and humility.", "prediction_text": "Dialogue: #Person1#: Who stands out in your mind as a man or woman of sound character? #Person2#: If I think of famous people, I think of Abraham Lincoln. #Person1#: He's the US president, who walked five miles just to give a lady her change, isn't he? #Person2#: That's the one. He also was famous for never giving up on his goals. #Person1#: That's right. He ran for office quite a few times before he was finally elected. #Person2#: And I also admire him for his courage in fighting for equal rights. #Person1#: He had great vision, didn't he? #Person2#: And humility. I would have liked to"}
{"answers": "Lisa found that her husband was cheating on her. She asked him to tell her the truth or end the relationship that she would divorce him. #Person1# agrees with what she did.", "prediction_text": "A woman finds out that her husband is cheating on her. She confronts him and he admits to a small indiscretion. She tells him that she will divorce him if he doesn't end the relationship with the other woman."}
{"answers": "#Person1#'s flight has been delayed because of heavy rain and #Person2# suggests #Person1# listen to the announcement for further information.", "prediction_text": "Dialogue: #Person1#: Excuse me, Miss."}
{"answers": "#Person1# is warning #Person2# not to do that or #Person2# will get into trouble, but #Person2# doesn't care.", "prediction_text": "Dialogue: #Person1#: You're going to get into a lot of trouble if you do that."}
{"answers": "#Person2# is going to travel around Europe and will buy #Person1# a souvenir.", "prediction_text": "Dialogue: #Person1#: Have you decided what you're going to do during your holiday?"}
{"answers": "#Person1# is afraid that eyelash curler may hurt #Person2#'s eyes.", "prediction_text": "A person is doing something to their eyelashes, and another person is criticizing them for it."}
{"answers": "#Person2# borrows some money from #Person1# because #Person2#'s backpack and wallet were lost and cannot take it back. #Person1# will drive #Person2# home.", "prediction_text": "A man lost his wallet and asks for money from a stranger. The stranger offers to help him."}
{"answers": "#Person1# shares the Alex's bachelor party plan with #Person2# and promises nothing will be too much. #Person2# doubts that and will also organize a bachelorette party.", "prediction_text": "Dialogue: #Person1#: Hi honey! You'll never guess what! My friends Julie and Alex are getting married!"}
{"answers": "Leah informs Nathaniel that Ms. Collins will not be able to call him back until next week, so he needs to book an appointment with someone else from the International Student Office. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "The International Student Office is calling to book an appointment with someone else today."}
{"answers": "#Person1# and David's father want to have a quiet Christmas at a country hotel, but David thinks it is boring.", "prediction_text": "Dialogue: #Person1#: Look out of the window David, there is ice on the lake. Actually their size all over the lake."}
{"answers": "#Person1# wants to try real Chinese cuisine. #Person2# recommends several ones. #Person1# decides to go to Quanjude restaurant.", "prediction_text": "Dialogue: #Person1#: Oh, I'm starving. It's my first time to China. And I'd like to try some real Chinese cuisine. What would you recommend?"}
{"answers": "Rebecca tells #Person1# about her working experiences. Rebecca first worked for the York Herald after her graduation, and then for the London Weekly.", "prediction_text": "This dialogue is a conversation between a journalist and a former colleague. The journalist is asking the former colleague about her career path. The former colleague explains that she started her career as a junior reporter for a local newspaper, and then moved on to a national newspaper. The dialogue highlights the importance of experience and networking in the field of journalism."}
{"answers": "#Person1# is a new member who wants to use the machines. Johnson teaches #Person1# to get a card to keep track of the progress and record the suitable weight for each machine. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "The dialogue is about a new member of a gym. The narrator is a trainer who helps the new member to get started. The narrator explains how to use the machines and how to keep track of the weight used. The narrator also explains the importance of not pushing oneself too hard."}
{"answers": "Frank got a new job and is telling Judy not only the heavy schedule but also the benefits of this job.", "prediction_text": "The dialogue is about a man who is working for the Post Office. He is very happy with his job because he can get free medical care for his family."}
{"answers": "#Person1# and #Person2# are appreciating lanterns. #Person2# explains the customs of the Lantern Festival and Chinese characters on the lanterns to #Person1#.", "prediction_text": "The Lantern Festival is a traditional Chinese festival. It is held on the 15th day of the first lunar month. People enjoy the lanterns and the gala performances. The Lantern Festival is also called the Festival of Lights."}
{"answers": "Bill made a cake for Sue's birthday, but Sue is on a diet to avoid allergy. Bill offers to buy her something healthy to eat, but Sue is enjoying her party. So they will go afterwards.", "prediction_text": "Dialogue: #Person1#: Why don't you have some of my cake, Sue? I made it just for this party."}
{"answers": "#Person1# asks the exact time when the big fire broke out. #Person2# remembers that it was about 10 o'clock.", "prediction_text": "The speaker is asking the other person to tell him the exact time when the fire broke out. The other person says that he doesn't remember the exact time, but he remembers that it was about 10 o'clock."}
{"answers": "#Person1# and #Person2# are scheduling to talk about the sales projections for next year and they decide to meet next Tuesday.", "prediction_text": "The speaker is trying to set up a meeting with the other person. The other person is busy, but the speaker is able to find a time that works for both of them."}
{"answers": "Muriel Douglas and James send greetings and talk about their holiday before the meeting. Douglas stayed in L.A., while James went to Tahoe.", "prediction_text": "This dialogue is a sample of a conversation between a salesperson and a potential client. The salesperson is trying to get the client to agree to a meeting to discuss a new account. The client is a little hesitant at first but eventually agrees to meet. The salesperson offers the client something to drink while they wait for the other person to arrive. The client says no thanks and then asks the salesperson about their holiday. The salesperson says that they had a nice holiday and then asks the client about theirs. The client says that they stayed in L. A. and it was sunny the entire weekend. They spent most of the time at home but they did go see King Kong on Christmas day. The salesperson asks how they liked it and"}
{"answers": "#Person1# takes a taxi to the Friendship Hotel for something important.", "prediction_text": "Dialogue: #Person1#: Taxi!"}
{"answers": "#Person2# tells #Person1# #Person1# can park at a parking structure and show #Person1# the direction.", "prediction_text": "This dialogue is a conversation between a student and a parking attendant. The student is looking for a parking space on campus, and the attendant gives him directions to the parking structure. The student asks if the parking structure is full, and the attendant says that he is not sure."}
{"answers": "#Person1# calls #Person2#'s restaurant to ask about the food and opening hours.", "prediction_text": "The dialogue is between a customer and a waiter in a Japanese restaurant. The customer asks about the food and the hours of operation. The waiter gives the information and the customer makes a reservation."}
{"answers": "Mr. Jones asks Julie the reasons Julie got absent for class the whole week and finds out Julie got food poisoning.", "prediction_text": "The dialogue is about a student who has been ill. He tells his teacher that he has been ill since last Sunday. The teacher asks him what was the problem. The student says that he went to a nice restaurant for lunch with a few of his classmates. He says that the meal was great. He says that he got some fried chicken to eat from a street store. He says that he started feeling sick after he got home. The teacher asks him if he thinks that is what caused his illness. The student says that he must have been. He says that no one else who ate the lunch got sick. He says that he is feeling a little better now. The teacher asks him if he is able to keep up with all his lessons."}
{"answers": "Ann recommends #Person1# to do the well-being in the air program on the way to New York. Ann introduces the program in detail and #Person1# seems interested in it.", "prediction_text": "The dialogue is about a woman who has just returned from a trip to the United States. She says that she didn't have any jet lag because she did the well-being program on the plane. The man asks her how she did it. She tells him that she didn't drink any alcohol or coffee, she didn't eat any meat or rich food, she drank a lot of water and fruit juice, and she did some of the exercises in the program. The man asks her how many passengers did the exercises and how much champagne they drank. The woman says that there weren't many passengers and that there was a lot of champagne. The man says that it's a difficult choice between mineral water and exercises or champagne and jet lag."}
{"answers": "#Person1# doesn't know how to use an ATM. #Person2# teaches #Person1#.", "prediction_text": "This dialogue is a conversation between two people. The first person, #Person1#, needs to use the ATM, but they don't know how. The second person, #Person2#, helps them figure it out. The first person asks what they have to do, and the second person explains the steps. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to"}
{"answers": "Harry tells #Person2# that the bag was overpriced in a street market. #Person2# suggests that Harry should bargain with the seller and consult the price tags in the supermarket first.", "prediction_text": "The dialogue is about the bargaining process in China. The speaker #Person1# is a foreigner who is not used to the bargaining culture. He is told by his Chinese colleagues that he was overcharged. The speaker #Person2# advises him to consult the price tags in the supermarket first."}
{"answers": "#Person2# helps #Person1# draw blood for testing white blood cell count.", "prediction_text": "The doctor sent me over here to have my blood drawn. Certainly, please have a seat and roll up your left sleeve. What is this test for? Well, today your doctor wants us to check your white blood cell count. What does that tell him? Well, if it is elevated, you might have an infection. Will the blood test hurt? It is only a little pin stick. I am going to put a tourniquet on your arm to puff your veins up. Ouch! There we go! I got the blood that I needed."}
{"answers": "#Person2# tells #Person1# a parking structure for parking and where it is.", "prediction_text": "This dialogue is a conversation between a student and a parking attendant. The student is looking for a parking space on campus, and the attendant gives him directions to the parking structure. The student asks if the parking structure is full, and the attendant says that he is not sure."}
{"answers": "#Person2# left #Person2#'s backpack and wallet in a taxi and failed to contact the driver because #Person2# didn't have the number. #Person1# lends 50 dollars to #Person1# and will drive #Person2# home.", "prediction_text": "A man lost his wallet and asks for money from a stranger. The stranger offers to help him."}
{"answers": "Tom suggests going swimming but Jane refuses beacuse of her paper due. After discussion, Jane finnally agrees to have dinner with him and then go to study together in the library.", "prediction_text": "Jane and Tom are friends who want to spend time together. They have a disagreement about how to spend their time."}
{"answers": "#Person2# introduces EDD's popular services to #Person1#. #Person2# tells #Person1# that EDD provided computers, job information, workshops, and job fairs.", "prediction_text": "This dialogue is a conversation between a job seeker and a representative of the Employment Development Department (EDD). The dialogue is designed to help job seekers understand the services that are available to them at the EDD."}
{"answers": "#Person1# is interviewing #Person2#. #Person1# thinks #Person2# answers #Person1#'s questions beautifully, and is the person they want.", "prediction_text": "This is a sample of a job interview. The interviewer is asking the candidate questions to determine if they are a good fit for the job. The candidate is asked to describe their strengths and weaknesses, and to describe their future goals. The interviewer is also trying to determine if the candidate is a good match for the company's culture."}
{"answers": "#Person1# shows #Person2# some sandalwoods, #Person2# likes it and buys some.", "prediction_text": "The dialogue is about a customer who wants to buy some traditional Chinese arts and crafts. The shop assistant helps the customer to choose the right ones."}
{"answers": "Brandon is signing up for a free website to improve writing skills but it requires personal information. #Person1# thinks the website cannot be trusted but Brandon doesn't believe so. Therefore, #Person1# shuts down Brandon's computer to stop him.", "prediction_text": "This dialogue is about a person who is trying to sign up for a free Web site. The other person is trying to convince him that it is a legitimate site. The first person is suspicious of the site and the other person is trying to convince him that it is legitimate."}
{"answers": "#Person1# praises #Person2#'s spacious courtyard and asks about why corn ears are hanging on the tree. #Person2# explains.", "prediction_text": "The dialogue is about a house. The first person is a real estate agent, and the second person is the owner of the house. The real estate agent wants to show the house to the owner. The owner shows the courtyard to the real estate agent. The real estate agent is interested in the corn ears hanging on the tree branch. The owner tells the real estate agent why he hangs the corn ears in the tree. The real estate agent thinks the owner's idea is very good."}
{"answers": "Greg Sonders calls Mary to ask whether Mary is interested in sports and tells Mary to wait for final admission decision later.", "prediction_text": "The speaker is trying to get in touch with Mary, who is a student at Brown College. The speaker is Greg Sonders, and he is calling to ask Mary if she would be interested in playing college sports. Mary is interested in playing sports, and she tells the speaker that she holds her school's all time record for points scored in a game. The speaker is impressed and tells Mary that they will let her know their decision soon."}
{"answers": "#Person1# wants to have a farm and gets away from working 9 to 5 in an office. #Person2#'d visit #Person1#.", "prediction_text": "The dialogue is about a person who wants to become a farmer. The person is not sure if it is a good idea. The person's uncle owns a farm and the person has spent some time working with him. The person wants to get away from the hustle and bustle of the city."}
{"answers": "Andrew's elder sister is surprised by Andrew's putting on so much weight and figures out Andrew has probably got scammed by a junk mail. She offers Andrew systematic suggestions on living healthy and losing weight.", "prediction_text": "Dialogue: #Person1#: Hey, Andrew! An...? Andrew."}
{"answers": "The taxi driver takes #Person1# to the Friendship Hotel quickly.", "prediction_text": "Dialogue: #Person1#: Taxi!"}
{"answers": "#Person1# pays the meal and tips #Person2# but #Person2# doesn't take it.", "prediction_text": "A dialogue between a customer and a waiter in a restaurant."}
{"answers": "#Person2# plans to have a trip in Hebei but #Person1# says there are sandstorms in there.", "prediction_text": "The dialogue is about the sandstorms in Hebei, a province in northern China. The speaker says that the sandstorms are very strong and can cause respiratory tract infections. The other speaker says that the sandstorms are a problem for everyone."}
{"answers": "#Person1# and #Person2# are talking about the weather in Beijing. #Person2# tells #Person1# it's cold in the evening around this time.", "prediction_text": "Dialogue: #Person1#: Well, what a nice day!"}
{"answers": "#Person1# feels bored at home and asks Jim go for a beer. Jim refuses and suggests going to the gym and meeting friends.", "prediction_text": "The dialogue is about two friends who are trying to decide what to do after dinner. They are trying to decide whether to go for a few beers or to go to the gym. The first friend suggests going for a few beers, but the second friend says that it is not good for their fitness. The first friend says that it will help them to relax, but the second friend says that it will just make them fat and act silly. The first friend says that he doesn't feel like sitting at home, but the second friend suggests that they go to the gym where they can play singsong and meet some of their friends. The first friend says that he hears that Mary and Sally often go to the gym to play pingpong, and the second"}
{"answers": "#Person1# evaluates #Person2#'s ability and personality by asking #Person2# 's strengths, weaknesses and career planning. #Person1# finds #Person2# is a suitable candidate.", "prediction_text": "This is a sample of a job interview. The interviewer is asking the candidate questions to determine if they are a good fit for the job. The candidate is asked to describe their strengths and weaknesses, and to describe their future goals. The interviewer is also trying to determine if the candidate is a good match for the company's culture."}
{"answers": "#Person1# wants to take a date to the best restaurant, #Person2# suggests the Gramercy Tavern and is calling to reserve a table for him.", "prediction_text": "The dialogue is between a hotel concierge and a guest. The guest is taking a date to a restaurant. The concierge is trying to help the guest choose a restaurant."}
{"answers": "Harry and his wife may go abroad during the holidays. However, they might not go anywhere because his wife always worries too much.", "prediction_text": "Dialogue: #Person1#: Where are you going to spend your holidays this year, Harry?"}
{"answers": "#Person1# delivers a parcel for Mr. Lee. He happens to have something to express.", "prediction_text": "A man is looking for Mr. Lee. He is surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking"}
{"answers": "#Person1# and #Person2# are looking at jobs. #Person2# is interested in the media industry.", "prediction_text": "The speaker is looking for a job. The other person suggests that he or she could work in the media. The speaker is not interested in that. The other person suggests working with computers. The speaker is interested in that."}
{"answers": "#Person1# and #Person2# are talking about the heavy storm last night, and #Person1#'s positive. #Person2# thinks the weather is terrible. #Person1# thinks they should not complain.", "prediction_text": "The dialogue is about the weather. The speaker is complaining about the weather."}
{"answers": "#Person2# is checking out and asks #Person1# for the bill. #Person1# gives #Person2# a wrong bill at first then corrects it.", "prediction_text": "The dialogue is about a hotel bill. The first person is a hotel receptionist and the second person is a guest. The guest is checking out and wants to know the bill. The receptionist gives him the bill and the guest finds that there is a mistake. The receptionist corrects the mistake and the guest leaves."}
{"answers": "#Person1# will pays for the overweight luggage.", "prediction_text": "The dialogue is about a customer who has to pay the excess luggage charge. The customer asks the staff for a fragile label. The staff gives the customer a fragile label and tells the customer to attach the label to the luggage and put the luggage in the designated place."}
{"answers": "Mr. Faber books a double room for 3 nights from July 20th at York Hotel.", "prediction_text": "A man calls a hotel to book a room for three nights."}
{"answers": "#Person1# stops #Person2# from taking photos in the museum.", "prediction_text": "The museum is a place where you can see many kinds of things. You are not allowed to take pictures in the museum. If you take pictures, you will be asked to give your camera to the museum staff. The museum staff will keep your camera and return it later. You can buy slides and picture postcards at the souvenir shop near the exit."}
{"answers": "#Person2# calls Pizza House to deliver a thin crust seafood pizza to holyrood 9A.", "prediction_text": "A dialogue between a customer and a pizza house."}
{"answers": "#Person1# wants some online information about the Normandy Landing, #Person2# teaches #Person1# how to google it.", "prediction_text": "This dialogue is about how to do an internet search."}
{"answers": "#Person2# let in someone and #Person1# thinks it was #Person2#'s fault.", "prediction_text": "A man is talking to his wife about a man who has been coming to the door asking for gas and electricity."}
{"answers": "#Person1# and #Person2# talk about Mirella's dressing style in the office which is different from others. The management decided to give Mirella formal clothes directly to warn her.", "prediction_text": "The speaker is complaining about the way Mirella dresses at work. She is wearing jeans and sweatshirts, which is not acceptable in the office. The speaker thinks that Mirella is trying to make herself at home in the office. The speaker thinks that Mirella is influenced by the way people dress in California. The speaker thinks that Mirella's new style of dressing down is not quite acceptable."}
{"answers": "#Person2# wants to borrow romance videos for #Person2#'s daughter and asks the detailed cost and tenancy term. #Person1# reminds of the overdue fee.", "prediction_text": "Dialogue between a customer and a video store clerk."}
{"answers": "Tony got caught cheating and feels ashamed. Steven tells him to learn from it and study hard.", "prediction_text": "Dialogue: #Person1#: Hi, Tony. You look unhappy. What's wrong? #Person2#: Oh, Steven, I made a big mistake. #Person1#: What happened? #Person2#: I really wish I hadn't done it. #Person1#: What on earth are you talking about? #Person2#: I got caught cheating. I feel so ashamed. The teacher saw me and told me I failed. #Person1#: What were you thinking? #Person2#: You know my father. If I fail, he'll kill me. I have to do well. #Person1#: But what you should do is study hard. #Person2#: I know... I know... it's"}
{"answers": "#Person1# is surprised at the Olympic Stadium'volume, capacity and interior setting to #Person1#.", "prediction_text": "The dialogue is about the Olympic park. The first person is a foreign visitor. The second person is a guide. The first person is surprised by the size of the Olympic park. The second person tells him that the whole stadium is to be finished this June. The first person asks how many seats are there in the stand. The second person tells him that there are 5000 seats in total. The first person is surprised by the size of the Olympic park. The second person tells him that the whole stadium is to be finished this June. The first person asks how many seats are there in the stand. The second person tells him that there are 5000 seats in total. The first person is surprised by the size of the Olympic park."}
{"answers": "#Person1# congratulates Mr. Stuart on winning the city marathon.", "prediction_text": "The dialogue is about a person who won a marathon. The dialogue is between two people. The first person congratulates the winner. The winner is very happy. The first person says that they watched the race with their children. The winner says that it was an exciting moment."}
{"answers": "#Person1#, a reporter, takes #Person2# to a forum. #Person2# is bored and tries to find a pastime.", "prediction_text": "A man and a woman are at a charity event. The man is bored and the woman is trying to find something to do."}
{"answers": "Harry tells #Person1# he was almost hit by a car and he will be more careful next time.", "prediction_text": "A man is almost hit by a car and has a bad experience."}
{"answers": "#Person1# helps #Person2# order one hard-boiled egg with dark toast and orange juice to follow.", "prediction_text": "A dialogue between a waiter and a customer in a restaurant."}
{"answers": "Jack invites Charlie to play video games after he picks up his dad. Charlie agrees.", "prediction_text": "The speaker is inviting a friend to come over to play a new video game. The speaker is excited about the game and wants the friend to come over and play it with him. The friend is interested in the game and wants to play it, but he has to finish his homework first. The speaker is happy that the friend is interested in the game and is looking forward to playing it with him."}
{"answers": "#Person1# is tired and will take a nap and #Person2# gets the dinner ready.", "prediction_text": "A man and a woman are talking on the phone. The man is going to take a nap, but the woman suggests that he unplug the phone. The man says that's a good idea, and the woman asks if he wants her to wake him in an hour. The man says no, thanks, and the woman says she'll start dinner at 6:00. The man says he thinks he'll be awake by then, and the woman says he might even dream about dinner. The man says he doesn't think he's going to dream about anything, and the woman says have a nice nap."}
{"answers": "#Person2# is looking at the map. #Person1# finds geography interesting and wants to know more about it.", "prediction_text": "The dialogue is about a person who is looking at a map of the world. The person is surprised that most of the world is covered with water. The person is also surprised that most countries are tiny compared to the oceans. The person is also surprised that millions of years of volcanic activity have created mountains thousands of metres tall. The person is also surprised that rivers have created deep valleys and canyons like the grand canyon. The person is also surprised that oceans and landscapes all influence our climate. The person is interested in learning more about the map and geography."}
{"answers": "#Person2# shares an umbrella with #Person1# when it's rainy.", "prediction_text": "The dialogue is between a man and a woman. The man asks the woman what time it is. The woman tells him that it is almost eleven twenty. The man thanks her. The woman asks the man if the rain is heavy. The man says that it is. The woman offers to share her umbrella with the man. The man says that he is going to the Garden Hotel. The woman says that she is going there, too. The man says that they can walk together. The woman says that they can. The man says that they can."}
{"answers": "Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week.", "prediction_text": "The dialogue is about a job interview. The first person is the interviewer and the second person is the interviewee. The interviewer asks the interviewee about his education, age, and job history. The interviewee answers the questions."}
{"answers": "#Person2# wants some traditional Chinese arts and crafts. #Person1# shows her sandalwood fan and she buys some.", "prediction_text": "The dialogue is about a customer who wants to buy some traditional Chinese arts and crafts. The shop assistant helps the customer to choose the right ones."}
{"answers": "#Person1# introduces Henry to Pete. The three persons talk about their previous experiences and life and decide to go to a bar after dinner.", "prediction_text": "Dialogue: #Person1#: Good evening, Pete. I trust you had a nice siesta. #Person2#: Yes. It was just what the doctor ordered. #Person1#: I'd like you to meet Henry Johnson. Mr. Johnson has been working at our Beijing office for over a year now. I think you two are from the same city. #Person2#: Hey there, Henry. Call me Pete. A New Yorker, eh? #Person3#: Born and raised in Brooklyn. Good to meet you, Pete. #Person2#: Brooklyn... that's a great neighborhood. I'm originally from Queens but moved down to Manhattan a while back. So... you're an expat in China.."}
{"answers": "#Person1# is surprised to see Andrew put on so much weight but Andrew tells #Person1# that to lose weight, he signs up for a Wafu Diet online for $490. #Person1# thinks he's getting scammed and suggests that he should take more exercise, eat smaller portions, eat a well-balanced breakfast, cut off fast food and sugar and eat fresh fruits and vegetables", "prediction_text": "Dialogue: #Person1#: Hey, Andrew! An...? Andrew."}
{"answers": "#Person2# will give #Person1# a ride after work.", "prediction_text": "The dialogue between two people at work."}
{"answers": "#Person1# and #Person2# will go to Burger Queen for lunch and eat cheese burgers and French fries. They will have to wait for good food.", "prediction_text": "The dialogue is about two friends who are going to have lunch at Burger Queen. They are talking about the food they are going to order. They are also talking about the food they have tried before."}
{"answers": "#Person1# is asking #Person2# about the places that #Person2# has stayed or lived.", "prediction_text": "The speaker asks the other person about his or her background. The other person answers that he or she was born in Chicago and grew up in the suburbs. The speaker asks about college, and the other person says that he or she went to Europe instead. The speaker asks when the other person arrived in Europe, and the other person says that it was in 1995. The speaker asks how long the other person stayed in Europe, and the other person says that he or she stayed for almost two years. The speaker asks if it was very interesting, and the other person says that it was."}
{"answers": "#Person2# helps Mr. Murray get a library card and reminds him of library rules.", "prediction_text": "A customer is applying for a library card. The librarian asks for the customer's driver's license and application. The customer is told about the rules and limitations of the library card. The customer is thanked for joining the library."}
{"answers": "#Person2# finds #Person2# being mischarged. #Person1# corrects the bill and #Person2# pays for it.", "prediction_text": "The dialogue is about a hotel bill. The first person is a hotel receptionist and the second person is a guest. The guest is checking out and wants to know the bill. The receptionist gives him the bill and the guest finds that there is a mistake. The receptionist corrects the mistake and the guest leaves."}
{"answers": "#Person2# has worked overtime with minimum wage and little bonus. Although #Person2# are not very confident, #Person1# hopes #Person2# could quit that job and find another one.", "prediction_text": "The dialogue is about a person who is working overtime without any compensation. The person is working for minimum wage and is not getting any extra pay for the overtime. The person is not getting any incentive for working so many hours. The person is afraid that if he quits, he won't be able to find another job."}
{"answers": "#Person1# congratulates Tom for achieving the Nobel Prize.", "prediction_text": "The dialogue is about a person who wins the Nobel Prize. The person is very happy and proud of himself. He thanks the other person for the compliment."}
{"answers": "Sally reads the letter from Tom to #Person1#. The letter invites them to visit Tom.", "prediction_text": "The letter is from Tom to Sally and John. It is a short note in reply to their letter. Tom is happy to hear that they will be in town in January. He will pick them up at the station and they can have dinner together in town. Tom's number is 7807842."}
{"answers": "#Person2# needs to change clothes to enter the conference and #Person1# offers #Person2# the convenience by putting the conference off.", "prediction_text": "Dialogue: #Person1#: Well, Sir, we have asked all our guests to wear a jacket and a tie in our restaurant."}
{"answers": "Stephanie has a headache but doesn't see a doctor because of the report due and George is willing to help with the report and suggests she ask the board for more time", "prediction_text": "Dialogue: #Person1#: You look pale, Stephanie. What's wrong?"}
{"answers": "Jim suggests #Person1# go to the gym to meet friends instead of drinking beers after dinner.", "prediction_text": "The dialogue is about two friends who are trying to decide what to do after dinner. They are trying to decide whether to go for a few beers or to go to the gym. The first friend suggests going for a few beers, but the second friend says that it is not good for their fitness. The first friend says that it will help them to relax, but the second friend says that it will just make them fat and act silly. The first friend says that he doesn't feel like sitting at home, but the second friend suggests that they go to the gym where they can play singsong and meet some of their friends. The first friend says that he hears that Mary and Sally often go to the gym to play pingpong, and the second"}
{"answers": "#Person2# is from America and is picking up presents for families in a duty-free shop. #Person1# recommends some for #Person2#.", "prediction_text": "Dialogue: #Person1#: Can I help you, sir? #Person2#: Yes, please. I'm picking out some presents for my children and wife. #Person1#: What kind of presents do you want? #Person2#: Actually, I don't know exactly. Do you have anything recommended? #Person1#: OK. How about some very cool sneakers for your children? Anyway, it won't cost you much. It's only around $ 59. 95 for each pair. It might be cheaper than that in America. #Person2#: Hey, How did you know I came from America? #Person1#: Just a hunch, sir. #Person2#: How much again? #Person1#: $"}
{"answers": "#Person1# is interviewing Vet and asking her about the ways she deals with stress as a young mother, feelings of having a baby, plans for the future, her favorite quote, and advice for teens.", "prediction_text": "This dialogue is between a teen mother and her counselor. The teen mother is going through a lot of stress and depression because of her baby. She is also having a hard time with her baby's father. The counselor gives her advice on how to deal with her stress and depression. The teen mother also talks about her future goals and dreams."}
{"answers": "#Person1# calls to inform Ballam that Ballam was going to be hired, and agrees to raise the starting salary after discussion.", "prediction_text": "The dialogue is about a job interview. The man is looking for a computer engineer. The woman is the one he is interviewing. The man offers her a job and they discuss the salary."}
{"answers": "#Person1#'s new suit cost $150. #Person2# doesn't think it's a good bargain.", "prediction_text": "The dialogue is about a man who is trying to sell his new suit to a friend. The friend is not interested in buying it because he thinks it is too expensive."}
{"answers": "Tom complains about his unhappy lunch experience in the fast-food restaurant while Catherine enjoyed her home-made meal. Catherin doesn't like fast food and says most Americans avoid it too.", "prediction_text": "Dialogue: #Person1#: Hey, Tom, did you enjoy your lunch?"}
{"answers": "#Person2# had been in the charity organization for 2 months and the social activities influenced #Person2#'s way of thinking.", "prediction_text": "The speaker is a person who has worked in a charity organization for 2 months. He has learned a lot from that experience, including love, sympathy, equality, and so on. He thinks that those social activities have direct impact on his way of thinking. He is not easy to be edgy."}
{"answers": "Nick has never used washing machines before, and he turns to a girl for help. Nick appreciates Alice's help and tells her his mother used to do washing for him. Alice will help him be more independent.", "prediction_text": "Dialogue: #Person1#: Excuse me."}
{"answers": "#Person1# and #Person2# are drinking while discussing what to do if they won the Pools. #Person1# wants to go around the world while #Person2# wants to buy a big house.", "prediction_text": "Dialogue: #Person1#: If I won the Pools, I'd go round the world. #Person2#: Would you? I wouldn't. #Person1#: What would you do? #Person2#: Oh, I don't know. I'd buy a big house with a garden for my wife and kids, I suppose. But it's difficult to imagine having a lot of money. #Person1#: One thing's certain. If I had a lot of money, I wouldn't work any more. #Person2#: Wouldn't you? What would you do with all that spare time? #Person1#: As I said, I'd go round the world. #Person2#: What would you do after that? #"}
{"answers": "#Person1# asks #Person2# how well #Person2# knows about the company, #Person2#'s ideal department, and current salary. #Person2# also asks #Person1# about the company's benefits and insurance.", "prediction_text": "Dialogue: #Person1#: What do you know about our company?"}
{"answers": "#Person1# will go to London and invites #Person2# to come along. But they have disagreements on how they get there.", "prediction_text": "Dialogue: #Person1#: I have to go up to London for a couple of days next week. Would you like to come?"}
{"answers": "#Person2# is holding a baby shower for #Person1# and there are many presents for #Person1#. #Person1#'s water broke.", "prediction_text": "A baby shower is a party held for a woman who is expecting a baby. The party is usually held a few weeks before the baby is due. The guests bring gifts for the baby."}
{"answers": "#Person2# shows #Person1# the way to the seventy seventh.", "prediction_text": "Dialogue: #Person1#: Excuse me, can you do me a favor?"}
{"answers": "#Person1# shows Jack a picture of #Person1#'s new puppy.", "prediction_text": "This is a dialogue between two people. The first person is Jack, and the second person is Person 2. Jack wants to show Person 2 a picture of his new puppy. Person 2 is surprised that Jack is showing off a picture of a pet, not a baby. Jack explains that the puppy is just as much trouble as a baby, but a lot of fun. Person 2 agrees that the puppy is cute, and asks how old he is."}
{"answers": "#Person1# inquires Tom about his successful business experience and his own green life and how to learn from mistakes.", "prediction_text": "The dialogue is about a successful businessman who is now a manager of a company selling green products. He talks about his family and how they left Budapest in 1986 and settled in Canada. He also talks about his friends' parents who were highly successful businessmen. He then talks about how he started a web-designed company and learned a lot from that experience. He also talks about how he raised 18 million dollars from about 50 people. He then talks about how he owns 6% of the company and all employees get their shares. He also talks about how he is only mildly green. He then talks about how he is a huge biker and how he biked cross Canada. He then talks about how he has never done anything wasteful"}
{"answers": "#Person1# and #Person2# are talking about the heavy rain last night and the weather in Beijing.", "prediction_text": "Dialogue: #Person1#: Well, what a nice day!"}
{"answers": "#Person2# explains how the city grew into a large place by selling coal and absorbing villages, and introduced old buildings to #Person1#.", "prediction_text": "The dialogue is about the history of a city. The speaker asks the listener about the history of the city and the listener answers. The speaker then asks about the old buildings in the city and the listener answers. The speaker then summarizes the conversation."}
{"answers": "#Person2# asks #Person1# to bring #Person1#'s husband to talk about #Person1#'s test results.", "prediction_text": "The dialogue is between a patient and a doctor. The patient is asking for his test results and the doctor is trying to reassure him that everything is fine. The patient is suspicious and the doctor is trying to calm him down. The dialogue is a good example of how to handle a difficult situation with a patient."}
{"answers": "#Person1# and #Person2# talks about Mirella dressed casually after she came back from Silicon Valley. Managements put white shirts on her desk to warn her.", "prediction_text": "The speaker is complaining about the way Mirella dresses at work. She is wearing jeans and sweatshirts, which is not acceptable in the office. The speaker thinks that Mirella is trying to make herself at home in the office. The speaker thinks that Mirella is influenced by the way people dress in California. The speaker thinks that Mirella's new style of dressing down is not quite acceptable."}
{"answers": "#Person1# encourages Lucy to sing but Lucy just wants to listen.", "prediction_text": "The speaker is talking to a friend about a party they attended. The friend is complaining that she didn't get to sing any songs. The speaker suggests that she sing a song for a friend who was at the party."}
{"answers": "Mr. Lee offers to give Mrs. Word a lift home on a terrible night.", "prediction_text": "The dialogue is between a man and a woman. The man is going to give the woman a lift home. The woman is very grateful to the man for his kindness. The man asks the woman to hold his umbrella while he gets his keys out. The woman agrees to do so. The man opens the door and the woman thanks him for his help."}
{"answers": "#Person2# is surprised to hear from #Person1# that Mary got married.", "prediction_text": "The speaker is talking about a friend who got married. The speaker is surprised that the friend got married so quickly. The speaker is surprised that the friend got married to a man who lives in Paris. The speaker is surprised that the friend is going to live in Paris. The speaker is surprised that the friend is going to live in Paris with her husband. The speaker is surprised that the friend is going to live in Paris with her husband. The speaker is surprised that the friend is going to live in Paris with her husband. The speaker is surprised that the friend is going to live in Paris with her husband. The speaker is surprised that the friend is going to live in Paris with her husband. The speaker is surprised that the friend is going to live"}
{"answers": "#Person1# will take the leather shoes.", "prediction_text": "The customer is always right."}
{"answers": "#Person2# tells Tenants Advocacy Resource Center about the problem with #Person2#'s house owner. #Person2# tells #Person1# it always takes #Person2#'s house owner a long time to repair and they can't agree on the repair costs.", "prediction_text": "This is a sample of a conversation between a tenant and a housing counselor. The tenant is complaining about the house owner's failure to make repairs. The housing counselor is trying to help the tenant understand the landlord's rights and responsibilities."}
{"answers": "#Person1# is visiting #Person2#'s spacious house. #Person1# finds it interesting that corn ears are hanging on the tree branch.", "prediction_text": "The dialogue is about a house. The first person is a real estate agent, and the second person is the owner of the house. The real estate agent wants to show the house to the owner. The owner shows the courtyard to the real estate agent. The real estate agent is interested in the corn ears hanging on the tree branch. The owner tells the real estate agent why he hangs the corn ears in the tree. The real estate agent thinks the owner's idea is very good."}
{"answers": "#Person1# buys some nice clothes by credit card with #Person2#'s assistance.", "prediction_text": "The speaker is in a store and is trying to buy something. The speaker is trying to pay for the item with a credit card. The speaker is trying to sign the credit card slip. The speaker is trying to pay for the item with a credit card. The speaker is trying to sign the credit card slip. The speaker is trying to pay for the item with a credit card. The speaker is trying to sign the credit card slip. The speaker is trying to pay for the item with a credit card. The speaker is trying to sign the credit card slip. The speaker is trying to pay for the item with a credit card. The speaker is trying to sign the credit card slip. The speaker is trying to pay for the item with a credit"}
{"answers": "#Person2# tells #Person1# the bus route to get to Sons.", "prediction_text": "A conversation between two people about how to get to Sons from PHS."}
{"answers": "#Person1# and #Person2# go to Burger Queen for lunch, but there's a long line and they have to wait. #Person2# is going to order French fries that #Person1# recommends.", "prediction_text": "The dialogue is about two friends who are going to have lunch at Burger Queen. They are talking about the food they are going to order. They are also talking about the food they have tried before."}
{"answers": "#Person2# suggests that #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "This is a dialogue between two people. The first person wants to put up a curtain, but the second person offers to help. The first person says that he wants to hold the ladder, and the second person agrees to put up the curtain. The first person thanks the second person for helping."}
{"answers": "#Person1# has a bad lifestyle. #Person2# kindly advises #Person1# to be positive.", "prediction_text": "Dialogue: #Person1#: I don't know how to adjust my life. Would you give me a piece of advice?"}
{"answers": "#Person2# tells #Person1# that #Person2# is bored and explains the reasons, so #Person1# suggests #Person2# read some old newspapers and be quiet.", "prediction_text": "A man and a woman are at a charity event. The man is bored and the woman is trying to find something to do."}
{"answers": "#Person1# borrows 200 yuan from Fred to pay the mortgage of a computer and will pay back when having money.", "prediction_text": "Dialogue: #Person1#: Awful! My cell phone is out of service again. Fred, can you lend me some money? #Person2#: How much do you want? #Person1#: 200 yuan. I bought a new computer online, so I have to pay the mortgage. #Person2#: I am so sorry to hear that. Is that enough? #Person1#: It's so nice of you. You really make my day. #Person2#: Forget it! We seemingly need to gather money little by little. #Person1#: What you said is absolutely right. I will pay you back as soon as I get back on my feet. #Person2#: No rush. You can give it back whenever you"}
{"answers": "#Person2# tells #Person1# that the company not only raised a lot of money sponsoring a marathon for the American Cancer Society but also established a good image out of this fundraiser event.", "prediction_text": "The fundraiser events went really well. We were able to partner with a few of our competitors, all in fun of course, to sponsor a marathon for the American Cancer Society. All of the people who participated it had a great time, and we were able to raise a lot of money to contribute to cancer research."}
{"answers": "#Person2# suggests Harry consult the price tags in the supermarket first and then negotiate for a fair deal after Harry bought a bag overpriced and got a terrible feeling.", "prediction_text": "The dialogue is about the bargaining process in China. The speaker #Person1# is a foreigner who is not used to the bargaining culture. He is told by his Chinese colleagues that he was overcharged. The speaker #Person2# advises him to consult the price tags in the supermarket first."}
{"answers": "#Person1# teaches #Person2# how to use the training machine and suggests starting working small reps if #Person2# wants a good physique.", "prediction_text": "A dialogue between a fitness trainer and a newbie. The trainer gives the newbie pointers on how to use the machine and how to work out. The newbie is impressed by the trainer's physique and asks how he got so ripped. The trainer explains that he doesn't want to be ripped and that he just wants a good physique with weights and cardio. The newbie is surprised by the weight the trainer is lifting and asks if it's really that much. The trainer assures the newbie that it's not that much and demonstrates how to use the machine. The newbie is impressed by the trainer's physique and asks how he got so ripped. The trainer explains that he doesn't want to be ripped and that he just wants a good physique with weights and cardio. The"}
{"answers": "Simon tells #Person1# that he is on a scheme called phased retirement during which he had a six-month break from work, and after that he could apply for projects of the company he used to work for but manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "This dialogue is about a retired person who is working part-time. The retired person is happy with the arrangement, and the company is happy with the arrangement, too."}
{"answers": "#Person2# agrees to help #Person1#'s friend, a new emigrant who had never been to school, to learn English.", "prediction_text": "A man is teaching his friend to read."}
{"answers": "#Person2# introduces the Olympic Stadium's finishing time, capacity and interior setting to #Person1#.", "prediction_text": "The dialogue is about the Olympic park. The first person is a foreign visitor. The second person is a guide. The first person is surprised by the size of the Olympic park. The second person tells him that the whole stadium is to be finished this June. The first person asks how many seats are there in the stand. The second person tells him that there are 5000 seats in total. The first person is surprised by the size of the Olympic park. The second person tells him that the whole stadium is to be finished this June. The first person asks how many seats are there in the stand. The second person tells him that there are 5000 seats in total. The first person is surprised by the size of the Olympic park."}
{"answers": "#Person1# and Ben discuss what evening classes they shall get and come into an agreement to have an Indian cooking course for their interest.", "prediction_text": "The dialogue is about two friends who are looking for evening classes. They are looking for something interesting and not too expensive."}
{"answers": "#Person2# introduces the Ford Focus to #Person1# who wants to buy a new car. #Person2# describes every detail of the car and #Person1# decides to have a test drive.", "prediction_text": "The dialogue is about a man who is looking for a new car. He is looking for a car that is fuel efficient and has a powerful engine. The dialogue is between a salesman and a customer. The salesman is trying to convince the customer to buy the car. The dialogue is very informal and the salesman is very friendly. The customer is very interested in the car and is very excited about buying it. The dialogue is very short and to the point. It is a good example of a dialogue between a salesman and a customer."}
{"answers": "#Person2# suggests #Person1# get a costume of a Canadian Mountie for a dress party. They will go to the shopping center for that at ten o'clock on Saturday.", "prediction_text": "The dialogue is about a person who needs to get a costume for a fancy dress party. The person asks a friend to come shopping with her. The friend agrees to come shopping with her. The friend suggests a costume for the party. The person agrees to the costume. The person asks the friend where to get the costume. The friend tells the person where to get the costume. The person thanks the friend."}
{"answers": "In the workshop, #Person2# offer #Person1# some suggestions on how to perform well in interviews.", "prediction_text": "The dialogue above is a conversation between two people who are taking a workshop to prepare for an interview. The first person is excited to be taking the workshop, and the second person is happy to have the opportunity to help the first person prepare for the interview. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second"}
{"answers": "#Person1# and #Person2# decide to go to Carrots where can meet everyone's requirements with others tomorrow evening.", "prediction_text": "A dialogue between two people who are trying to decide where to go for dinner."}
{"answers": "#Person2# tells the trips and sports activities of the holiday camp to #Person1#. The kids enjoyed camp and would like to go next year.", "prediction_text": "The speaker is talking about a holiday camp for children. The speaker is a teacher who went to the camp with the children. The speaker says that some of the younger children were unhappy because they were homesick. But by the end of the holiday, they were having a great time. The speaker says that the children enjoyed the trips to the castle and the barbeque."}
{"answers": "#Person2#'s friend is visiting #Person2#. #Person2# invites him to give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "The dialogue between two people. The first person is happy because his friend just dropped in from the United Kingdom. The second person is happy because he decided to put his friend up while he's here. They will have fun and the second person will invite his friend to give a speech at his lab. The first person is interested in the topic of the speech. The second person says that the topic is about multimodal text generation. The first person is interested in the topic and wants to go to the speech. The second person leaves a place for the first person."}
{"answers": "Tom asks Sara to help to take care of his son when he takes his daughter to the hospital.", "prediction_text": "Dialogue: #Person1#: Hi, is that Sara?"}
{"answers": "#Person2# tells #Person1# that the company raised a lot of money to contribute to cancer research, and the marathon event was also a great deal for our company", "prediction_text": "The fundraiser events went really well. We were able to partner with a few of our competitors, all in fun of course, to sponsor a marathon for the American Cancer Society. All of the people who participated it had a great time, and we were able to raise a lot of money to contribute to cancer research."}
{"answers": "#Person2# suggests that #Person1# should follow the doctor's instruction.", "prediction_text": "The patient is complaining of back pain. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient is not taking the medication as prescribed. The patient"}
{"answers": "#Person1# is going to New York for the first time. #Person2# suggests #Person1# use a personal tour guide service to make #Person1#'s trip plan.", "prediction_text": "A dialogue between two people about a tour guide service."}
{"answers": "#Person1# and #Person2# change ideas on Barry and Paul, and then talk about their own personalities.", "prediction_text": "The dialogue is about two people who are friends. They talk about their personalities and their plans for the future."}
{"answers": "#Person1# is a tourist from America and has a chat with #Person2#.", "prediction_text": "The dialogue is about a tourist who is visiting a city in China. The dialogue is between a tourist and a waiter in a restaurant. The dialogue is about the tourist's nationality, the tourist's stay in the city, and the tourist's drink."}
{"answers": "#Person1# compliments Brian on his English and asks Brian about his life in the U.S.", "prediction_text": "The dialogue is about a man who is learning English. He asks the man where he learned English. The man says he learned in college. The man says he speaks really well. The man says he has been in the U. S. for 3 weeks. The man says his wife just got here yesterday. The man says he has never been to California. The man says he has been to Las Vegas once on a business trip."}
{"answers": "#Person2# is surprised that Ruojia's married. #Person1# and #Person2# will go to her party and give their presents to wish her a happy marriage.", "prediction_text": "Dialogue: #Person1#: Would you like to go to the party tonight?"}
{"answers": "James helped Mrs. Thomas with housework since she has broken her legs, and he wanted to save money for a bike.", "prediction_text": "The dialogue is between a man and a woman. The man is James and the woman is Mrs Thomas. James is helping Mrs Thomas with her housework. Mrs Thomas is very grateful for James's help. She asks him to take her dog for a walk. James agrees to do so. Mrs Thomas is very happy that James is helping her. She says that she is very grateful to him. She also says that she is very happy that James is helping her. She says that she is very happy that James is helping her. She says that she is very happy that James is helping her. She says that she is very happy that James is helping her. She says that she is very happy that James is helping her. She says that she is"}
{"answers": "#Person1# and #Person2# feel scared when heard Sarah insists on marrying Dick.", "prediction_text": "When Sarah told #Person1# that she would marry no man but Dick, #Person1# was scared. #Person2# was scared too, but he did not tell #Person1# his feeling."}
{"answers": "#Person1# and #Person2# hold different opinions towards people with different facial expressions.", "prediction_text": "The dialogue is about the importance of smiling."}
{"answers": "#Person1# and #Person2# argue to repay for a meal but no one succeeds. They finally decide to go dutch.", "prediction_text": "The dialogue is about two people who are going to have dinner together. The first person is going to pay for the meal, but the second person wants to pay for it. The first person says that it's not a big deal, but the second person insists that it's his turn. The first person says that it's not a big deal, but the second person insists that it's his turn. The first person says that it's not a big deal, but the second person insists that it's his turn. The first person says that it's not a big deal, but the second person insists that it's his turn. The first person says that it's not a big deal, but the second person insists that it's his turn."}
{"answers": "#Person1# thinks the eyelash curler is very dangerous, but #Person2# doesn't think so.", "prediction_text": "A person is doing something to their eyelashes, and another person is criticizing them for it."}
{"answers": "Jack checks in at #Person1#'s hotel.", "prediction_text": "The dialogue is about a hotel reservation. The first person is a hotel receptionist and the second person is a guest. The guest asks for a reservation and the receptionist asks for his name. The guest gives his name and asks if the reservation is correct. The receptionist confirms that the reservation is correct and asks the guest to fill up the forms."}
{"answers": "Ann suggests #Person1# take well-being in the air programme including drinking mineral water and exercising so that #Person1# could get over jet lag.", "prediction_text": "The dialogue is about a woman who has just returned from a trip to the United States. She says that she didn't have any jet lag because she did the well-being program on the plane. The man asks her how she did it. She tells him that she didn't drink any alcohol or coffee, she didn't eat any meat or rich food, she drank a lot of water and fruit juice, and she did some of the exercises in the program. The man asks her how many passengers did the exercises and how much champagne they drank. The woman says that there weren't many passengers and that there was a lot of champagne. The man says that it's a difficult choice between mineral water and exercises or champagne and jet lag."}
{"answers": "#Person2# is curling eyelashes. #Person1# feels dangerous, but #Person2# doesn't think so.", "prediction_text": "A person is doing something to their eyelashes, and another person is criticizing them for it."}
{"answers": "#Person1# and #Person2# have a discussion about what they usually do to relieve anger.", "prediction_text": "Dialogue between two people about how to relieve anger."}
{"answers": "#Person1# and #Person2# are looking for work and will think about the electrician apprentice program.", "prediction_text": "This is a dialogue between two people who are looking for work. The first person is looking for any job that will pay the mortgage, while the second person is looking for an electrician apprenticeship. The first person is looking for a job, while the second person is looking for an apprenticeship. The first person is looking for a job, while the second person is looking for an apprenticeship. The first person is looking for a job, while the second person is looking for an apprenticeship. The first person is looking for a job, while the second person is looking for an apprenticeship. The first person is looking for a job, while the second person is looking for an apprenticeship. The first person is looking for a job, while"}
{"answers": "Mr. Polly wants to get a break from work and he asks #Person1# to buy a drink for him, but #Person1# refuses.", "prediction_text": "Dialogue: #Person1#: What's wrong with you, Mr. Polly?"}
{"answers": "Jane invites Peter to join her travel to Xi'an. Peter asks the duration and cost and is interested. Then they discuss their ideal places for a holiday if they had enough money.", "prediction_text": "Dialogue: #Person1#: I don't know if you've heard of it, Peter, but some of us are thinking of going to Xi'an in the summer. I don't know if you'd like to come with us."}
{"answers": "#Person1# invites #Person2# to watch a movie together that night. #Person2# wants to see nothing but a war movie.", "prediction_text": "Dialogue: #Person1#: Would you like to go to the movies tonight?"}
{"answers": "Hong tells #Person1# to buy a local SIM card to make a cheap phone call to the UK.", "prediction_text": "A conversation between a Chinese person and a foreigner about how to use a local SIM card in China."}
{"answers": "#Person1#'s flight is delayed. #Person2# can't tell further information about the delay because of the changeable weather.", "prediction_text": "Dialogue: #Person1#: Excuse me, Miss."}
{"answers": "#Person1# and #Person2# are talking about taking care of their puppies and the time of vet appointment.", "prediction_text": "Dialogue: #Person1#: Have you given the puppies food yet?"}
{"answers": "#Person1# makes an appointment with Jane on next Monday afternoon around three to kick around some ideas.", "prediction_text": "The speaker is planning to meet with another person to discuss ideas. The speaker wants to meet on Monday afternoon, around three o'clock. The listener agrees to the time and date."}
{"answers": "#Person2# draws #Person1#'s blood to check white blood cell count.", "prediction_text": "The doctor sent me over here to have my blood drawn. Certainly, please have a seat and roll up your left sleeve. What is this test for? Well, today your doctor wants us to check your white blood cell count. What does that tell him? Well, if it is elevated, you might have an infection. Will the blood test hurt? It is only a little pin stick. I am going to put a tourniquet on your arm to puff your veins up. Ouch! There we go! I got the blood that I needed."}
{"answers": "Weather's nice. #Person1# suggests having lunch outside and hopes to go to the beach to get some sun.", "prediction_text": "Dialogue: #Person1#: It's a lovely day, isn't it?"}
{"answers": "#Person1# is not ready for marriage. #Person2# reminds him of what he said a month ago and what will happen if he leaves Amy. After listening to #Person2#, #Person1# is ready for marriage.", "prediction_text": "A man is freaking out because he is about to get married. His friend tries to calm him down and tells him that he is just getting cold feet. The man realizes that he is being crazy and is ready to get married."}
{"answers": "#Person1# asks Mike about his sister.", "prediction_text": "The speaker is talking about his sister. He asks his friend what she looks like. The friend says she is tall and pretty. The speaker asks if she is like him. The friend says she is friendly and easy-going. The speaker asks if she is as clever as he is. The friend says she is not as clever as he is. The speaker says she has a big head."}
{"answers": "Jack bought a new car and invites Daisy to hop in and take a ride. Daisy thinks it's a good feeling.", "prediction_text": "Jack is a car salesman. He is driving his new car. He is talking to Daisy, a customer. Daisy is impressed with Jack's new car."}
{"answers": "#Person1# teaches #Person2#, a newbie, to use a machine. #Person2# doesn't want to be ripped and thus works with less weight than #Person1#.", "prediction_text": "A dialogue between a fitness trainer and a newbie. The trainer gives the newbie pointers on how to use the machine and how to work out. The newbie is impressed by the trainer's physique and asks how he got so ripped. The trainer explains that he doesn't want to be ripped and that he just wants a good physique with weights and cardio. The newbie is surprised by the weight the trainer is lifting and asks if it's really that much. The trainer assures the newbie that it's not that much and demonstrates how to use the machine. The newbie is impressed by the trainer's physique and asks how he got so ripped. The trainer explains that he doesn't want to be ripped and that he just wants a good physique with weights and cardio. The"}
{"answers": "#Person1# asks #Person2# about past experiences of living in different cities around the world.", "prediction_text": "The speaker asks the other person about his or her background. The other person answers that he or she was born in Chicago and grew up in the suburbs. The speaker asks about college, and the other person says that he or she went to Europe instead. The speaker asks when the other person arrived in Europe, and the other person says that it was in 1995. The speaker asks how long the other person stayed in Europe, and the other person says that he or she stayed for almost two years. The speaker asks if it was very interesting, and the other person says that it was."}
{"answers": "#Person1# helps #Person2# find the things that #Person2# wants. #Person1# finds it comfortable working in the office.", "prediction_text": "Dialogue: #Person1#: We're having a department meeting at 10 o'clock, ok?"}
{"answers": "Jack invites Charlie to play a new video game and Charlie is interested.", "prediction_text": "The speaker is inviting a friend to come over to play a new video game. The speaker is excited about the game and wants the friend to come over and play it with him. The friend is interested in the game and wants to play it, but he has to finish his homework first. The speaker is happy that the friend is interested in the game and is looking forward to playing it with him."}
{"answers": "Tom Wilson pays the bill for his room and meal cost by credit card.", "prediction_text": "The dialogue is about a hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist who asks the guest for his name and room number. Then the guest says that he has not used any hotel services. The receptionist asks him if he has used any services. The guest says that he has not used any services. The receptionist asks him if he has used any services. The guest says that he has not used any services. The receptionist asks him if he has used any services. The guest says that he has not used any services. The receptionist asks him if he has used any services. The guest says that he has not used any services. The receptionist asks him if"}
{"answers": "#Person1# wants to know about the Normandy Landing. #Person2# teaches #Person1# to search and suggests reading a history book.", "prediction_text": "This dialogue is about how to do an internet search."}
{"answers": "Laura does exercise for her health and good looking. #Person1# talks about regular exercise. They may do exercise together one day.", "prediction_text": "The dialogue is about exercise. The two people are talking in the street. The first person is a woman, and the second is a man. The woman is going to the gym, and the man is going for a walk. The woman asks the man why he doesn't exercise more. The man says he doesn't have time, and the woman says that she does a lot of exercise for both health and looks. The man says he doesn't have time, but the woman says that she does a lot of exercise for both health and looks. The man says he doesn't have time, but the woman says that she does a lot of exercise for both health and looks. The man says he doesn't have time, but the woman says that"}
{"answers": "Susan wants to find the memo. John has no idea where it is.", "prediction_text": "A person is looking for a memo. The other person doesn't know where it is."}
{"answers": "Martin tells Miss Eliza about his exams and his part-time job. Martin appreciates Mister Lam and the students' welfare club.", "prediction_text": "The dialogue is about a student who is preparing for the coming exams. He is working hard to get good marks. He is also working at weekends to get some pocket money. He is very grateful to Mister Lamb for helping him with his problem. He has no pocket money. He couldn't go out with his friends because he didn't have the money for the bus fee. But he is OK now, with a part-time job. He thinks the students welfare club really helped him."}
{"answers": "#Person2# orders a turkey sandwich with cheese and a beef with vegetable soup at Danny's.", "prediction_text": "The dialogue is about a customer who orders a turkey sandwich with cheese and a medium drink. The customer also wants to have some other things to go with the sandwich for lunch. The dialogue is between a customer and a waiter. The customer asks the waiter about the soup of the day and the waiter tells him that they have beef with vegetables or chicken with rice. The customer then orders the beef with vegetables and a medium drink. The customer pays the waiter and the dialogue ends."}
{"answers": "Benjamin hasn't finished his project report so #Person1# helps him. #Person1# tells him about the content and format of a report, and agrees to teach him how to work Microsoft Words.", "prediction_text": "Dialogue: #Person1#: Hey, Benjamin, have you finished your report on the project?"}
{"answers": "#Person2# can speak three languages and do general desk work in English.", "prediction_text": "The dialogue is about a Chinese man who speaks English and French. He can speak English well but not read and write it. He can speak French well but not read and write it. He used to be an interpreter. He thinks his English is good enough to do general desk work."}
{"answers": "#Person1# refuses to stay by #Person2#'s side and won't return.", "prediction_text": "A man is about to leave his lover, but she begs him to stay. He refuses, and she swears to keep her word."}
{"answers": "#Person1# wants to go around the world while #Person2# wants to buy a big house, if win the Pools. But they come down to the earth finally.", "prediction_text": "Dialogue: #Person1#: If I won the Pools, I'd go round the world. #Person2#: Would you? I wouldn't. #Person1#: What would you do? #Person2#: Oh, I don't know. I'd buy a big house with a garden for my wife and kids, I suppose. But it's difficult to imagine having a lot of money. #Person1#: One thing's certain. If I had a lot of money, I wouldn't work any more. #Person2#: Wouldn't you? What would you do with all that spare time? #Person1#: As I said, I'd go round the world. #Person2#: What would you do after that? #"}
{"answers": "#Person2# answers #Person1#'s questions about getting special discount coupons and how to use them.", "prediction_text": "The dialogue is about a customer who wants to buy goods in a supermarket. The customer asks the cashier for special discount coupons. The cashier tells the customer that he can get more coupons if he buys more goods. The customer asks how much discount he can get if he uses the coupons to buy goods next time. The cashier tells the customer that he can get 10 pence off if he uses the coupons. The customer asks if he can buy everything in the supermarket by the coupons. The cashier tells the customer that he needs to take advantage of the coupons within their expiry date. The customer asks how long he can keep the coupons. The cashier tells the customer that the coupons can be used at least one year. The customer asks how many bags of"}
{"answers": "#Person1# borrows $50 from #Person2# and will pay back once #Person1# gets a job.", "prediction_text": "The dialogue is about a person who is looking for a job. He goes to the employment office every day. He doesn't like his father's farm. He wants to find a job he likes. He needs a little luck."}
{"answers": "#Person1# bought a new suit with $ 150 and #Person2# thinks it is too expensive.", "prediction_text": "The dialogue is about a man who is trying to sell his new suit to a friend. The friend is not interested in buying it because he thinks it is too expensive."}
{"answers": "#Person2# lost #Person2#'s backpack, wallet, and money in a taxi. #Person1# lends #Person2# some money and will take #Person2# home.", "prediction_text": "A man lost his wallet and asks for money from a stranger. The stranger offers to help him."}
{"answers": "Mark did't come in because of a hangover. Jill calls him and tells him news about Bill's newborn daugther. They decide to celebrate it tomorrow.", "prediction_text": "Dialogue: #Person1#: Hello."}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "Dialogue between two actors, one of whom is the director."}
{"answers": "#Person1# will have a business trip to New York. #Person2# suggests #Person1# visit the UN building, Broadway, Columbia University, and New York University.", "prediction_text": "The dialogue is about a person who is going to New York to sign a business contract. The person asks the other person for suggestions on what to see while he is there. The other person suggests that he should see the UN building and from there, he could walk over to Broadway and see a movie or drama. The person asks about the universities in New York and the other person suggests that he should visit Columbia University and New York University. The person asks for a map of the city and the other person gives him a map and suggests that he should stop at the visitor's office near the train station."}
{"answers": "#Person1# asks #Person2# about the company's surroundings, location, and staff restaurant.", "prediction_text": "Dialogue: #Person1#: How long does it take to get to downtown from here?"}
{"answers": "Julie got food poisoning last Sunday and Mr. Jones cares about her health and study.", "prediction_text": "The dialogue is about a student who has been ill. He tells his teacher that he has been ill since last Sunday. The teacher asks him what was the problem. The student says that he went to a nice restaurant for lunch with a few of his classmates. He says that the meal was great. He says that he got some fried chicken to eat from a street store. He says that he started feeling sick after he got home. The teacher asks him if he thinks that is what caused his illness. The student says that he must have been. He says that no one else who ate the lunch got sick. He says that he is feeling a little better now. The teacher asks him if he is able to keep up with all his lessons."}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "Dialogue between two actors who are working on a scene."}
{"answers": "#Person1# helps #Person2# to choose a new phone.", "prediction_text": "This dialogue is about buying a mobile phone. The customer wants a phone with camera and MP3 player and the ability to make video calls."}
{"answers": "#Person1# buys a ticket under #Person2#'s guidance.", "prediction_text": "The dialogue is about a person who is coming to pick up his ticket. The dialogue is between a person who is working at the ticket counter and the person who is coming to pick up his ticket. The dialogue is about the ticket price and the payment method."}
{"answers": "#Person2# talks about #Person2#'s interest in collecting stamps and the first postage stamps with #Person1#. It gives #Person2# much pleasure. They all agree that stamps collecting gives much pleasure than becoming wealthy.", "prediction_text": "Dialogue: #Person1#: When did you become interested in collecting stamps?"}
{"answers": "#Person1# is not satisfied with the steak and #Person2# will change it.", "prediction_text": "Dialogue: #Person1#: Excuse me."}
{"answers": "#Person1# advises Mary to try applying for a job online and teaches her how to do it. Mary will try it.", "prediction_text": "Mary is tired because she goes to the personnel market every day. She has never tried to apply for a job on the Internet. She doesn't know how to apply for a job on the Internet. She doesn't know which employer is hiring. She doesn't know if it is safe to job hunt on the Internet. She will give it a try at once."}
{"answers": "#Person1# explains the checking items in #Person2#'s annual physical examination and will do test to look into #Person2#'s breathing.", "prediction_text": "The patient is here for an annual physical, and the doctor is performing a basic physical exam. The patient is concerned about his breathing, and the doctor offers to do an allergy test and an asthma test. The patient asks about the blood test, and the doctor explains what he is looking for. The patient is confident that the tests will go well because he has been taking good care of himself."}
{"answers": "#Person1# lost #Person1#'s passport. #Person2# suggests #Person1# fill in the lost property report and inform the embassy of the situation so they can issue a new passport in case it cannot be found.", "prediction_text": "A dialogue between a customer and a store employee."}
{"answers": "#Person1# and #Person2# are talking about the history of how #Person2#'s city gets into an industrial centre.", "prediction_text": "The dialogue is about the history of a city. The speaker asks the listener about the history of the city and the listener answers. The speaker then asks about the old buildings in the city and the listener answers. The speaker then summarizes the conversation."}
{"answers": "#Person1# explains the rules of bowling game to #Person2#.", "prediction_text": "The dialogue is about bowling. The first person is a teacher and the second person is a student. The teacher explains the rules of bowling to the student. The student asks the teacher to explain the rules again. The teacher explains the rules again. The student asks the teacher to play with him. The teacher agrees to play with the student."}
{"answers": "#Person1# asks #Person2# to help teach #Person1#'s friend English and #Person2# agrees.", "prediction_text": "A man is teaching his friend to read."}
{"answers": "#Person1# introduces Mark Leach to this week's People You Meet. Mark Leach describes his job responsibilities and gives advice to people coming to Britain.", "prediction_text": "This dialogue is about a tourist information officer in London."}
{"answers": "Harry is mad because he bought a bag overpriced at a street market in China. #Person2# says it's common and people should know the real worth and then negotiate for a fair deal.", "prediction_text": "The dialogue is about the bargaining process in China. The speaker #Person1# is a foreigner who is not used to the bargaining culture. He is told by his Chinese colleagues that he was overcharged. The speaker #Person2# advises him to consult the price tags in the supermarket first."}
{"answers": "Harry tells #Person1# that he was almost hit by a car and he will be more careful next time.", "prediction_text": "A man is almost hit by a car and has a bad experience."}
{"answers": "Joe disagrees with Tom on Tom's new assistant and thinks her stuck up.", "prediction_text": "Dialogue: #Person1#: Hi Joe. You met my new assistant, right?"}
{"answers": "#Person1# and #Person2# talk about their drinking capacity and their drinking habits. They decide to have a drink together tomorrow night.", "prediction_text": "The dialogue is between two people who are talking about drinking. The first person is a light weight and the second person is a heavy drinker. The first person is trying to build up a tolerance to drinking. The second person is trying to convince the first person to drink more."}
{"answers": "#Person1# and #Person2# exchanges thoughts on how to deal with their son's bad behavior. #Person2# suggests treating the kid with patience instead of punishment.", "prediction_text": "The mother is worried about her son's bad habits. The father suggests that she should treat him with patience, affection, and respect. He also suggests that she should motivate him to cooperate by rewarding his good behavior."}
{"answers": "Gian and Gina introduce themselves to each other. Gian introduces Gina to Robert.", "prediction_text": "Dialogue: #Person1#: Can I introduce myself? I'm Gian Luca Donatelli. I'm from Spain. #Person2#: I'm Gina. I'm from Finland. #Person1#: And who do you work for? #Person2#: I don't work for a company. I'm self-employed. I am a journalist, I write articles for magazines. I'm here at this conference to research for an article on internet service providers. #Person1#: That's interesting, a friend of mine works for an Italian service provider. Can I introduce you to him? #Person2#: Yes, of course, that would be nice. #Person1#: Robert, can you come here for a minute? This is Gina."}
{"answers": "#Person2#'d like to rent a silver Toyota Carola and #Person1# helps #Person2# go through procedures.", "prediction_text": "The dialogue is about renting a car. The customer wants to rent a Toyota Carola for 3 days. The customer has never rented a car before. The customer wants to choose the color of the car. The customer shows his ID card and signs the application form. The customer asks for the keys to the car. The customer asks for the time of return. The customer thanks the employee and drives safely."}
{"answers": "Mr. Simpson accepts #Person1#'s invitation to lunch on Thursday.", "prediction_text": "The dialogue is about a meeting between two people. The first person is Mr. Simpson and the second person is Mr. Smith. The first person asks the second person if he is free for lunch. The second person says that he is free on Thursday. The first person says that he knows of a place that the second person will like and he has to be there. The second person says that he will be there at 12:30. The first person says that he will be there at 12:30. The second person says that he will see the first person."}
{"answers": "Simon tells #Person1# that he joined in a phased retirement scheme which offers retired people work positions from the former company, and he can manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "This dialogue is about a retired person who is working part-time. The retired person is happy with the arrangement, and the company is happy with the arrangement, too."}
{"answers": "#Person2# tells #Person1# #Person2# plan to save money and #Person1# thinks it is good.", "prediction_text": "The dialogue is about a person who is making a budget to save money. The person is trying to save money for bills. The person is also trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is trying to save money for other things. The person is"}
{"answers": "#Person1# wants to travel to China, but #Person1#'s mother is not available.", "prediction_text": "Dialogue: #Person1#: I want to go to china for sight-seeing. What do you think of it, Mum?"}
{"answers": "Ted and #Person2# are discussing the place where they are going to take a holiday.", "prediction_text": "Dialogue: #Person1#: Ted, where are you going for your holiday this year?"}
{"answers": "Mary is tired because of finding jobs in the market. #Person1# suggests Mary apply for jobs on the Internet and says she could place her information on the Job Wanted channel on a recruitment web. Mary will try at once.", "prediction_text": "Mary is tired because she goes to the personnel market every day. She has never tried to apply for a job on the Internet. She doesn't know how to apply for a job on the Internet. She doesn't know which employer is hiring. She doesn't know if it is safe to job hunt on the Internet. She will give it a try at once."}
{"answers": "#Person1# and #Person2# are talking about a couple.", "prediction_text": "A man and a woman are talking about a man's dating habits."}
{"answers": "Gene is learning to drive and wants to buy a sixteen Honda Accord after passing the test.", "prediction_text": "The speaker is talking to a friend about a friend who is taking a driving test. The speaker is surprised that the friend has never had a driver's license. The friend explains that he has always lived in Boston and that it is easy to get around on public transportation. The speaker congratulates the friend on his new job in Los Angeles. The friend explains that he is taking a driving test because he will need a car to live in Los Angeles. The speaker asks if the friend is planning to buy a car once he passes his test. The friend says that he has already picked out the car he wants. The speaker asks what kind of car the friend wants. The friend says that he wants a Honda Accord. The speaker says that the Accord is"}
{"answers": "#Person2#'s country exploits, exports, and imports natural resources. #Person1# thinks the government in #Person2#'s country should invests in infrastructure. #Person2# agrees.", "prediction_text": "The speaker is from a country that exports natural resources. The speaker says that the country exports coal and copper. The speaker says that the country imports oil, iron and aluminium. The speaker says that the country has recently discovered deposits of precious stone. The speaker says that the government is keen to exploit the country's natural resources to get money to improve the country. The speaker says that the government should invest in long-term projects rather than short-term ones."}
{"answers": "#Person1# has been working for a company for six years, and #Person2# thinks #Person2#'s ready for promotion.", "prediction_text": "The dialogue is about a man who is thinking about leaving his job. He is talking to a friend who is a manager in the same company. The man is 28 years old and has been with the company for 6 years. He is thinking about leaving because he feels that he is not developing as much as he used to. He is also working longer hours and earning more money. The man's friend asks him if he is happy with the company and if he is developing new skills. The man says that he has done 2 management training courses, so he thinks he is ready for promotion. He also says that he doesn't travel much because he has to look after his mother."}
{"answers": "Steve helps look after Jonny and house-keeping. Mrs. Robinson appreciates it.", "prediction_text": "A man is cleaning the kitchen of a house. A woman comes in and asks him if he helped Johnny with the dishes. He says he did. She asks if he helped with the garbage, too. He says he did. She says she has to go, but thanks him for his help."}
{"answers": "#Person2# tells #Person1# #Person2# has few guiding experiences.", "prediction_text": "The dialogue is about a person who has experience in guiding. The dialogue is about a person who has experience in guiding. The dialogue is about a person who has experience in guiding. The dialogue is about a person who has experience in guiding. The dialogue is about a person who has experience in guiding. The dialogue is about a person who has experience in guiding. The dialogue is about a person who has experience in guiding. The dialogue is about a person who has experience in guiding. The dialogue is about a person who has experience in guiding. The dialogue is about a person who has experience in guiding. The dialogue is about a person who has experience in guiding. The dialogue is about a person who has experience in guiding. The dialogue is about a person"}
{"answers": "#Person1# is asking Rebecca about her working experience. She worked for York Herald as an intern and then as a junior local news reporter. After that, she began to work for London Weekly.", "prediction_text": "This dialogue is a conversation between a journalist and a former colleague. The journalist is asking the former colleague about her career path. The former colleague explains that she started her career as a junior reporter for a local newspaper, and then moved on to a national newspaper. The dialogue highlights the importance of experience and networking in the field of journalism."}
{"answers": "#Person1# is helping #Person2# confirm the price for sending the letter.", "prediction_text": "The dialogue is about a person who needs some stamps for a letter. The dialogue is about the person who needs some stamps for a letter. The dialogue is about the person who needs some stamps for a letter. The dialogue is about the person who needs some stamps for a letter. The dialogue is about the person who needs some stamps for a letter. The dialogue is about the person who needs some stamps for a letter. The dialogue is about the person who needs some stamps for a letter. The dialogue is about the person who needs some stamps for a letter. The dialogue is about the person who needs some stamps for a letter. The dialogue is about the person who needs some stamps for a letter. The dialogue is about the person who needs some stamps"}
{"answers": "#Person1# wants to know the charge at #Person2#'s buffet party.", "prediction_text": "The dialogue is about a restaurant. The first person is the waiter and the second person is a customer. The customer asks the waiter how much the buffet costs and where he can get the food. The waiter tells him that the buffet costs thirty yuan for each adult and twenty yuan for each kid. He also tells the customer that the cold dishes are on one side and the hot dishes are on the other side. The customer asks if he needs to pay extra for drinks. The waiter tells him that he doesn\u2019t need to pay extra for soft drinks but he needs to pay ten yuan for each alcohol order."}
{"answers": "#Person2# tells Mary about #Person2#'s experience this morning. when #Person2# was delivering milk, a huge dog rushed at #Person2# and #Person2# was knocked into a tree. It reminds Mary of a movie.", "prediction_text": "A man was delivering milk to a house when he saw a note on the door. He jumped over the fence and went towards the door. Suddenly, a huge dog rushed at him. He started running as fast as he could, but he didn't see that the big branch of the tree near the garden gate. He knocked into it. The dog didn't jump over the fence, but stayed in the garden barking loudly."}
{"answers": "#Person1# helps #Person2# get a ballot card and guides #Person2# the next step.", "prediction_text": "A voter is confused about where to get a ballot. A poll worker helps the voter and explains how to vote."}
{"answers": "Ben is nervous about his new school life. #Person1# is giving him some brief information about the school.", "prediction_text": "Dialogue: #Person1#: Hello, Ben. You're getting ready for tomorrow's lessons, aren't you?"}
{"answers": "#Person1# teaches #Person2# to cook chicken for #Person2#'s original thought of having frozen pizza is unhealthy.", "prediction_text": "The dialogue is about a couple who are trying to eat healthy food. The man is trying to persuade the woman to eat healthy food instead of frozen pizza. The woman is not very keen on cooking, but the man is trying to teach her how to cook healthy food. The dialogue is about the process of cooking a spicy chicken curry."}
{"answers": "Tom invites #Person1# to run in the morning. #Person1# would try to get up and join him.", "prediction_text": "Dialogue: #Person1#: Hey, Tom, what to go for a run?"}
{"answers": "#Person1# and #Person2# have different ideas on computer games.", "prediction_text": "The speaker is against computer games. He thinks that they are too violent and that they will make boys bad. The other person disagrees. He thinks that computer games are not so bad and that they can teach boys how to use the computer."}
{"answers": "#Person1# asks for mom's permission to play card games and promises not to make noise.", "prediction_text": "Dialogue: #Person1#: Mom, may I play the card games for a while?"}
{"answers": "#Person1# is showing the new clothes to #Person2#, but #Person2# is busy watching the games. They quarrel and get angry.", "prediction_text": "This is a dialogue between two people who are having a conversation about a game. The first person is trying to talk about their new clothes, but the second person is trying to watch the game. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person"}
{"answers": "#Person1# and #Person2# are both unemployed. #Person2# suggests applying for the electrician program and #Person1# agrees.", "prediction_text": "The dialogue is about two people who are out of a job and are looking for a new one. They are talking about the job opportunities that are available to them."}
{"answers": "Simon and Cleo argue the effectiveness of demonstrations. Simon thinks they are helpful, but Cleo disagrees and refuses to go to the demonstration of helping stop the spread of nuclear weapons with Simon.", "prediction_text": "Dialogue: #Person1#: Are you going to the demonstration to help stop the spread of nuclear weapons tomorrow, Cleo?"}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. They both decide to have fried bananas and #Person1# will bring bananas back.", "prediction_text": "The dialogue is about two people who are eating dessert at a restaurant. The first person is trying a traditional Greek yogurt, while the second person is trying an Italian tiramisu. The first person offers the second person a bite of his tiramisu, and the second person tries it and says that it is the best tiramisu he has ever had. The first person then offers the second person a fried banana, and the second person accepts."}
{"answers": "Tom Wilson pays for his bill for hotel and food by credit card.", "prediction_text": "The dialogue is about a hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist who asks the guest for his name and room number. Then the guest says that he has not used any hotel services. The receptionist asks him if he has used any services. The guest says that he has not used any services. The receptionist asks him if he has used any services. The guest says that he has not used any services. The receptionist asks him if he has used any services. The guest says that he has not used any services. The receptionist asks him if he has used any services. The guest says that he has not used any services. The receptionist asks him if"}
{"answers": "#Person1# and #Person2# are waiting in line for food.", "prediction_text": "The dialogue is about a person who is going to a restaurant with his friend. They are going to order food and drinks."}
{"answers": "#Person1# introduces Henry Johnson, an expat in China to Pete. Mr. Johnson shares with Pete how much he enjoys his time in China, until #Person1# interrupts and tries to finalize the evening plan. The three decide to go have dinner and then to a bar district.", "prediction_text": "Dialogue: #Person1#: Good evening, Pete. I trust you had a nice siesta. #Person2#: Yes. It was just what the doctor ordered. #Person1#: I'd like you to meet Henry Johnson. Mr. Johnson has been working at our Beijing office for over a year now. I think you two are from the same city. #Person2#: Hey there, Henry. Call me Pete. A New Yorker, eh? #Person3#: Born and raised in Brooklyn. Good to meet you, Pete. #Person2#: Brooklyn... that's a great neighborhood. I'm originally from Queens but moved down to Manhattan a while back. So... you're an expat in China.."}
{"answers": "#Person1# is preparing for her friends' coming this afternoon. Her son Mike helps her to buy drinks and make coffee.", "prediction_text": "Dialogue: #Person1#: Mike, come here, please."}
{"answers": "#Person1# gets lost and asks #Person2# where the central department stall and the national bank are. #Person2# directs #Person1#.", "prediction_text": "A dialogue between a stranger and a local."}
{"answers": "#Person1# warmly welcomes #Person2# and drives #Person2# to the Beijing Hotel. Mr. Wu has arranged a banquet for #Person2# in the evening.", "prediction_text": "The dialogue is about a businessman who is going to meet a foreigner. The businessman is very kind to the foreigner and he helps the foreigner to get into the car. The foreigner is very grateful to the businessman. The businessman tells the foreigner that he will take the foreigner to the hotel. The foreigner is very happy to hear this. The businessman also tells the foreigner that he will invite the foreigner to a banquet in the evening. The foreigner is very happy to hear this. The businessman also tells the foreigner that he will arrange some sightseeing for the foreigner. The foreigner is very happy to hear this."}
{"answers": "Fanny had a nightmare last night and her mother consoled her. Andy listens to her experience and thinks she has a good mother.", "prediction_text": "The dialogue is about a student who had a bad dream. He was chased by ghosts in the graveyard. His mother woke him up. He was embarrassed. He was afraid to fall back asleep. He was worrying about getting into the University of Michigan."}
{"answers": "#Person2# organized a great baby shower for #Person1#. #Person1# receives many gifts. #Person1#'s water broke.", "prediction_text": "A baby shower is a party held for a woman who is expecting a baby. The party is usually held a few weeks before the baby is due. The guests bring gifts for the baby."}
{"answers": "#Person2# tells #Person1# about the fundamental concepts of the accounting process.", "prediction_text": "The first is accounting entity, and the second is going concern. The third is measuring unit. The fourth is accounting period, and the fifth is objectivity."}
{"answers": "#Person1# makes a big mistake which may cause a huge loss and #Person1# may lose this job because of the loss and #Person1#'s dishonest supervisor. #Person2# suggests that #Person1# move in with #Person2# until #Person1# find another job and stop buying all the junk, if #Person1# loses the job.", "prediction_text": "#Person1#: I don't know what I'm going to do! It's going to be impossible to make ends meet if I lose my job!"}
{"answers": "#Person2# thinks that unemployment gives rise to social problems, and #Person1# thinks that it's difficult to handle it efficiently.", "prediction_text": "The dialogue is about the problems faced by the government."}
{"answers": "#Person1# and #Person2# are talking about a map of the world. They talk about geographical features and find geographical interesting.", "prediction_text": "The dialogue is about a person who is looking at a map of the world. The person is surprised that most of the world is covered with water. The person is also surprised that most countries are tiny compared to the oceans. The person is also surprised that millions of years of volcanic activity have created mountains thousands of metres tall. The person is also surprised that rivers have created deep valleys and canyons like the grand canyon. The person is also surprised that oceans and landscapes all influence our climate. The person is interested in learning more about the map and geography."}
{"answers": "#Person1# asks #Person2# who saw the robbery some questions. #Person2# agrees to come to the station for more questioning.", "prediction_text": "The speaker is asking the other person if they saw a robbery. The other person says that they did. The speaker asks if they saw everything. The other person says that they were in the bank at the time. The speaker asks what they saw. The other person says that they saw the guy come in with a gun. The speaker asks if they got a good look at his face. The other person says that he was wearing a mask. The speaker asks if anyone else was with him. The other person says that he came in alone. The speaker asks if they can come to the station for more questioning. The other person says that that will be fine."}
{"answers": "#Person2# orders some food with #Person3#'s help in a restaurant.", "prediction_text": "The dialogue is about a customer who is ordering food at a restaurant. The customer is asking for a tuna fish sandwich and a bowl of vegetable soup. The dialogue is a good example of how to order food in a restaurant."}
{"answers": "#Person2# thanks #Person1# for #Person1#'s help.", "prediction_text": "Dialogue: #Person1#: You're all set now?"}
{"answers": "#Person2# introduces #Person1# a website for personal tour guide service.", "prediction_text": "A dialogue between two people about a tour guide service."}
{"answers": "#Person1# attends Brian's birthday party. Brian thinks #Person1# looks great and charming.", "prediction_text": "A man and a woman are talking about the party they are attending. The man is happy to see the woman and they talk about the party. The woman says that the man is always popular with everyone and that he looks very pretty today. The man says that he is happy to see the woman and that he hopes that his necklace goes well with his dress. The woman says that the man looks great and that he is glowing. The man says that he is happy to see the woman and that they should have a drink together to celebrate the woman's birthday."}
{"answers": "#Person1# is checking the draft of an agreement and #Person2# will finish the agreement this evening.", "prediction_text": "The dialogue is about a business deal. The first person is a businessman who is here to sign the agreement. The second person is the person who is responsible for the agreement. The first person asks the second person to speed up the agreement and let them have it today. The second person says that he will try his best. He then gives the first person a draft of the agreement. The first person says that it contains basically all they have agreed upon. The second person asks about the terms concerning packing. The first person says that he doesn't think so. The second person says that if the first person totally agrees, he will type the agreement this evening and have it duplicated for signatures. The first person says that's fine."}
{"answers": "#Person1# and Bill talk about Bill's roommate Brain Locker.", "prediction_text": "A dialogue between two friends."}
{"answers": "Kalina calls Professor Clark that she needs to take this week off because she had a concussion.", "prediction_text": "The dialogue is between a student and a professor. The student is Kalina and the professor is Clark. The student is calling to tell the professor that she will miss a few days of school because she ran her car into a tree. The professor is concerned about the student and asks how much school she will miss. The student says she will only miss this week. The professor is glad that the student called and tells her to see her next week."}
{"answers": "#Person2# lost the credit card in travelling and is asking the Client Services for help.", "prediction_text": "The client is a tourist in the city and has lost his credit card. The client is calm and polite. The agent is calm and polite."}
{"answers": "Darlene calls to check the delayed order. Dan tells her the situation. Darlene will talk to Steve.", "prediction_text": "The speaker is calling to check on an order of computers that was supposed to be delivered on the tenth of September. However, the factory is short of hands and the delivery has been delayed for two days. The speaker asks for the phone number of the person who is in charge of the order. The person gives the number and the speaker asks if it is double 6 or double 3. The person says it is double 6. The speaker says that the delay shouldn't make that much difference and thanks the person for understanding."}
{"answers": "#Person1# and #Person2# talk about the accounting process.", "prediction_text": "The first is accounting entity, and the second is going concern. The third is measuring unit. The fourth is accounting period, and the fifth is objectivity."}
{"answers": "#Person2# wants to buy a gift for his mother's birthday. After knowing his mother's preference, #Person1# recommends a gold watch.", "prediction_text": "The dialogue is about a man who is looking for a gift for his mother. He asks the salesperson for help, but the salesperson is not very helpful. The man finally finds a gift that he thinks his mother will like, but the salesperson is not sure if it is the right choice."}
{"answers": "#Person1# helps #Person2# to order some food.", "prediction_text": "The dialogue is about a customer who orders a meal at a fast food restaurant. The dialogue is used to teach students about the use of the present simple tense and the use of the word \"please\" in requests."}
{"answers": "#Person2# tells #Person1# the address of the Grand Hotel and #Person1# drives her there.", "prediction_text": "A dialogue between a taxi driver and a passenger."}
{"answers": "#Person1# and #Person2# remember the chaotic situation of New York after 9-11, and #Person1#'s uncle ended up dying in the tower.", "prediction_text": "The dialogue is between two people who are talking about the terrorist attacks on 9-11. The first person was in New York City at the time of the attacks and the second person was in Beijing. The first person was in his apartment with his parents and the second person was in his apartment in Beijing. The first person saw the smoke and everything afterwards from his parents' apartment building. The second person didn't see the crash itself, but he did see the smoke and everything afterwards from his parents' apartment building. The first person's uncle's firm had an office in one of the towers. The first person's uncle didn't have any respect for human life not even their own. The second person's terrorist acts are deliberate and deadly and can"}
{"answers": "#Person1# and #Person2# are at an exciting party. #Person2# has a crush on Janice and #Person1# encourages him to have a try to talk to her.", "prediction_text": "A dialogue between two friends at a party. One of them is trying to get the other to talk to a girl, but the other is too shy."}
{"answers": "#Person1# and #Person2# think John's fallen for a girl.", "prediction_text": "A man and a woman are talking about a man's dating habits."}
{"answers": "Nathan is going to Chicago for practice and he is confident that he will do a good job and learn from others. Though worried at first, his mom thinks it's a great opportunity for him.", "prediction_text": "The speaker is talking to her son, Nathan, who is about to leave for a summer job in Chicago. She is happy for him, but she is also sad that he will be gone. She asks him how he thinks he will do in the big city. He says he is not worried about working for a big newspaper like the Chicago Tribune. He says he is not afraid of hard work and he knows how to organize his day. He says he is sure he will do a good job. She asks him if he knows what he will be doing. He says he is not worried about that. He says he has a lot of natural curiosity and he is very social, so he is sure he will learn from all the other people there."}
{"answers": "#Person1# is introducing the Eiffel Tower to #Person2#.", "prediction_text": "The dialogue is about the Eiffel Tower. The speaker tells the listener that the tower was built in 1889 for the World's Fair. The listener asks how tall the tower is and the speaker tells him that it is 320m ( 1050ft ) high. The listener asks if the tower is named after its designer and the speaker tells him that it is."}
{"answers": "#Person1# tells ABC Rent-a-car Company that #Person1# had a car accident. #Person2# will call an ambulance and the police.", "prediction_text": "The dialogue is about a car accident. The man is injured and the woman calls the police and an ambulance."}
{"answers": "Matthew and Steve meet after a long time. Steve is looking for a new place to live. Mathew's neighbor is moving out and They might be neighbors again.", "prediction_text": "The two friends are catching up after a long time. They are reminiscing about the old days and the good times they had together. They are also discussing the fact that they are both looking for a new place to live. The friend suggests that he can help his friend find a place to live. He offers to show his friend the apartment that his neighbor is moving into. The friend is excited about the prospect of living in the same neighborhood as his friend. He is also happy to be able to reminisce about the good times they had together."}
{"answers": "#Person2# thinks the movie industry should be revolutionized to survive the threat from DVD industry.", "prediction_text": "The dialogue between two people about the future of movie theatres and home video players."}
{"answers": "Mr. Brown is interviewing #Person2# and they are talking over #Person2#'s salary.", "prediction_text": "Dialogue: #Person1#: Are you paid on a commission or salary basis now?"}
{"answers": "#Person1# is crazy for Trump and voted for him but #Person2# will vote for Biden.", "prediction_text": "The dialogue is about the 2020 US Presidential Election. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but"}
{"answers": "According to the schedule planned by Brian, #Person1# will be picked up at 6, meet the Managing Director at the airport and attend the conference at noon.", "prediction_text": "Dialogue: #Person1#: Have you completed the arrangements for the trip yet, Brian?"}
{"answers": "#Person2#'s friend is a Ph.D. student at the University of Edinburgh. He is visiting #Person2# and will give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "The dialogue between two people. The first person is happy because his friend just dropped in from the United Kingdom. The second person is happy because he decided to put his friend up while he's here. They will have fun and the second person will invite his friend to give a speech at his lab. The first person is interested in the topic of the speech. The second person says that the topic is about multimodal text generation. The first person is interested in the topic and wants to go to the speech. The second person leaves a place for the first person."}
{"answers": "#Person1# is telling #Person2# how to play bowling.", "prediction_text": "The dialogue is about bowling. The first person is a teacher and the second person is a student. The teacher explains the rules of bowling to the student. The student asks the teacher to explain the rules again. The teacher explains the rules again. The student asks the teacher to play with him. The teacher agrees to play with the student."}
{"answers": "Adam's knee is getting better but #Person1# still suggests that he should not do any running today. Adam decides to watch the films of Michigan after practice and will be back tomorrow for full practice.", "prediction_text": "Adam is a basketball player. He has a knee injury. He is going to play in a game on Saturday. He is going to see the game films. He is going to rest his knee. He is going to play in a game on Saturday."}
{"answers": "#Person1# helps #Person2# collect an Export L /C from Tokyo.", "prediction_text": "A customer comes into a shop and asks for a L / C. The shop assistant gives them the L / C, but they ask for it to be from a different company. The shop assistant gives them the correct L / C, and the customer leaves."}
{"answers": "Leah informs Nathaniel that Ms. Collins won't be available for their appointment and explains the reason. Then Nathaniel decides to speak with someone else.", "prediction_text": "The International Student Office is calling to book an appointment with someone else today."}
{"answers": "#Person1# and Judy have a discussion on their financial budget and find out ways to save money.", "prediction_text": "Dialogue: #Person1#: Judy, have you ever made out how much money shall we spend? #Person2#: Oh, yeah. The total amount is no less than 13, 000 RMB, according to our itinerary. #Person1#: What? That's too much. #Person2#: Sit down. I'll show you the list of our financial budget. First of all, it is nearly 4, 000 RMB that we should spend in transportation. #Person1#: Ah, the transportation fee always takes a great part in the budget. #Person2#: Then we must pay a large amount of money for the hotel. But if we want to save money, we can choose a hotel which is not so"}
{"answers": "#Person1# introduces the services included in regular car wash package and #Person2# will take it.", "prediction_text": "The dialogue is about a car wash. The customer wants to get his car washed. The car wash has a special shampoo that gives the body that extra shine. The car wash also washes the windows inside and out. The car wash uses a vacuum cleaner that removes all the dirt, and throws away all of the trash that it can find. The customer is satisfied with the car wash package."}
{"answers": "#Person1# sends a package at #Person2#'s and buys some stamps at the other window.", "prediction_text": "The post office is a place where you can send letters, packages, and money orders. You can also get stamps and airmail."}
{"answers": "Since #Person2# is very social, #Person1# asks for the communication strategy.", "prediction_text": "The dialogue is about the importance of sincerity in communication. The first person asks the second person if he is a social person. The second person says that he is an outgoing person who likes to be with a lot of friends. The first person asks the second person what is his strategy in communicating with colleagues. The second person says that the most important thing in communication is your sincerity."}
{"answers": "#Person2#'s friend is visiting #Person2# and will give a speech about multimodal text generation. #Person1# thinks it interesting and will attend it.", "prediction_text": "The dialogue between two people. The first person is happy because his friend just dropped in from the United Kingdom. The second person is happy because he decided to put his friend up while he's here. They will have fun and the second person will invite his friend to give a speech at his lab. The first person is interested in the topic of the speech. The second person says that the topic is about multimodal text generation. The first person is interested in the topic and wants to go to the speech. The second person leaves a place for the first person."}
{"answers": "#Person2# is watching a programme about Islam's pilgrimage to Mecca. #Person1# has heard that there are often accidents happen during this pilgrimage. They also talk about pilgrimage in other countries.", "prediction_text": "Dialogue: #Person1#: Hi! What are you watching?"}
{"answers": "Sarah wants to move. #Person1# suggests buying a cheaper house and offers information on possible options.", "prediction_text": "The speaker is talking to a friend about a house that is for sale. The friend is interested in buying it but is worried about the location. The speaker tells her that the house is in a good location and that the neighbors are friendly."}
{"answers": "#Person1# serves #Person2# to borrow movies and tells #Person2# the price and when to return them.", "prediction_text": "Dialogue between a customer and a video store clerk."}
{"answers": "#Person1# and #Person2# are standing in line to buy food.", "prediction_text": "The dialogue is about a person who is going to a restaurant with his friend. They are going to order food and drinks."}
{"answers": "#Person1# feels sorry for Mr. Wilson's loss caused by #Person1# and assures that such mistakes will not happen again.", "prediction_text": "The dialogue is about the mistakes in goods. The first problem is supposed to be solved after the investigation. About the second problem, I admit it's our fault, so we will exchange all merchandise that falls short of our sample."}
{"answers": "Tony tells Steven that he cheated on the exam and realized his mistake.", "prediction_text": "Dialogue: #Person1#: Hi, Tony. You look unhappy. What's wrong? #Person2#: Oh, Steven, I made a big mistake. #Person1#: What happened? #Person2#: I really wish I hadn't done it. #Person1#: What on earth are you talking about? #Person2#: I got caught cheating. I feel so ashamed. The teacher saw me and told me I failed. #Person1#: What were you thinking? #Person2#: You know my father. If I fail, he'll kill me. I have to do well. #Person1#: But what you should do is study hard. #Person2#: I know... I know... it's"}
{"answers": "#Person1# wants a new bike from #Person2#, but she refuses. #Person1# promises to buy a plane for #Person2#.", "prediction_text": "The dialogue is about a mother and her son. The mother is talking to her son about his aunt. The son is talking about his bike. The mother is talking about her son. The son is talking about his bike. The mother is talking about her son. The son is talking about his bike. The mother is talking about her son. The son is talking about his bike. The mother is talking about her son. The son is talking about his bike. The mother is talking about her son. The son is talking about his bike. The mother is talking about her son. The son is talking about his bike. The mother is talking about her son. The son is talking about his bike. The mother is talking about her son. The"}
{"answers": "Harry tells #Person1# that he crossed the street while reading the newspaper and almost got hit by a car.", "prediction_text": "A man is almost hit by a car and has a bad experience."}
{"answers": "#Person2# finds that #Person1# has a fever and says #Person1# should've called in sick earlier.", "prediction_text": "The dialogue is about a student who has a headache and a small fever. The student's parents are not at home, so the student calls the school office to ask for advice. The school nurse takes the student's temperature and tells the student that he has a small fever. The student feels dizzy, so the nurse advises him to call in sick."}
{"answers": "#Person1# and #Person2# go back to work and will meet after work.", "prediction_text": "The dialogue between two people at work."}
{"answers": "Mark's absence was the result of a hangover. Bill's wife had a baby girl last night. Mark and Jill decide to celebrate it tomorrow.", "prediction_text": "Dialogue: #Person1#: Hello."}
{"answers": "#Person2# leaves the samples to Mr. Grant and will talk to him next week.", "prediction_text": "The speaker is making a decision about whether to buy something. The speaker is not ready to make a decision, but the speaker is willing to leave the samples with the speaker's contact and call the contact back later."}
{"answers": "Steve will try to persuade #Person1#'s wife not to divorce #Person1# as #Person1# swears to remain faithful forever.", "prediction_text": "Dialogue: #Person1#: Steven, I need badly your help. #Person2#: What's the matter? #Person1#: My wife has found that I have an affair with my secretary, and now she is going to divorce me. #Person2#: How could you cheat on your wife? You have been married for ten years. #Person1#: Yes, I know I'm wrong. But I swear that the affair lasts only for two months. And I still love my wife. I couldn't live without her. #Person2#: I will try my best to persuade her to reconsider the divorce. But are you sure that from now on you will be faithful to her forever? #Person1#: Yes, I swear."}
{"answers": "Ms. Dawson helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "prediction_text": "A boss gives a memo to his secretary to type up. The secretary asks a question about the memo, and the boss gives a curt answer. The secretary then types up the memo and distributes it to all employees."}
{"answers": "#Person2# wants to make a reservation for a round trip flight this month. #Person1# helps #Person2# book the cheapest flight and vegetarian meals.", "prediction_text": "Dialogue: #Person1#: Freedom Travel. How may I help you? #Person2#: Yes, I'd like to make a flight reservation for the twenty third of this month. #Person1#: Okay. What is your destination? #Person2#: Well. I'm flying to Helsinki, Finland. #Person1#: Okay. Let me check what flights are available?. #Person2#: Okay. #Person1#: And when will you be returning? #Person2#: Uh, well, I'd like to catch a return flight on the twenty ninth. Oh, and I'd like the cheapest flight available. #Person1#: Okay. Let me see. Um, hmm... #Person2#: Yeah? #Person1#: Well"}
{"answers": "#Person2# can develop #Person1#'s roll of film in six hours.", "prediction_text": "The dialogue is about a person who wants to develop the roll of film. The person is asked how long it takes to develop the film. The person says that it takes 6 hours. The person is asked if they can develop the film in 3 hours. The person says that they can develop the film in 3 hours. The person is asked if they can develop the film in 3 hours. The person says that they can develop the film in 3 hours. The person is asked if they can develop the film in 3 hours. The person says that they can develop the film in 3 hours. The person is asked if they can develop the film in 3 hours. The person says that they can develop the film in"}
{"answers": "#Person2# suggests #Person1# ask the policeman the way to Peking University.", "prediction_text": "Dialogue: #Person1#: Excuse me, could you tell me where Peking University is on this map?"}
{"answers": "#Person2# shows #Person2#'s house and courtyard to #Person1# and introduces the corn ears hunging on the tree.", "prediction_text": "The dialogue is about a house. The first person is a real estate agent, and the second person is the owner of the house. The real estate agent wants to show the house to the owner. The owner shows the courtyard to the real estate agent. The real estate agent is interested in the corn ears hanging on the tree branch. The owner tells the real estate agent why he hangs the corn ears in the tree. The real estate agent thinks the owner's idea is very good."}
{"answers": "#Person1# and #Person2# order chicken feet and wine.", "prediction_text": "Dialogue: #Person1#: Thank you for bringing me here. What shall we order?"}
{"answers": "#Person1# wants to take a date to a restaurant. #Person2# recommends the Gramercy Tavern.", "prediction_text": "The dialogue is between a hotel concierge and a guest. The guest is taking a date to a restaurant. The concierge is trying to help the guest choose a restaurant."}
{"answers": "Anne didn't enjoy the date with #Person2# last time, she rejects #Person2#'s invitation firmly.", "prediction_text": "Dialogue: #Person1#: Hello, three-five."}
{"answers": "#Person1# helps #Person2# to find a table in the centre and order some food at Beijing Tasty Restaurant.", "prediction_text": "Dialogue: #Person1#: Welcome to Beijing Tasty Restaurant. What can I do for you?"}
{"answers": "#Person2# tells #Person1# he falls in love with the girl living under him and she's very popular. #Person2#'s going to call her now and invite her to dinner tonight.", "prediction_text": "A dialogue between two people who are in love with the same girl."}
{"answers": "Ballam asks for a position with 4,000 yuan starting salary from #Person1#, given that 3,000 yuan cannot support his family.", "prediction_text": "The dialogue is about a job interview. The man is looking for a computer engineer. The woman is the one he is interviewing. The man offers her a job and they discuss the salary."}
{"answers": "#Person1# is interviewing #Person2# and asking #Person2# some information relating the job.", "prediction_text": "The job of a tour guide is to take people on tours of places of interest. The job is very interesting and the person who takes the job must be able to speak several languages."}
{"answers": "#Person1# and #Person2# will call an emergency meeting today even #Person2# doesn't think Ken can get back.", "prediction_text": "The speaker is trying to get the other person to agree to an emergency meeting. The other person is not sure if the speaker is serious, so the speaker has to make sure that the other person knows that the meeting is important."}
{"answers": "#Person2# has worked for a company for 6 years. With much experience and new skills, #Person2# tells #Person1# that #Person2# is ready for a promotion.", "prediction_text": "The dialogue is about a man who is thinking about leaving his job. He is talking to a friend who is a manager in the same company. The man is 28 years old and has been with the company for 6 years. He is thinking about leaving because he feels that he is not developing as much as he used to. He is also working longer hours and earning more money. The man's friend asks him if he is happy with the company and if he is developing new skills. The man says that he has done 2 management training courses, so he thinks he is ready for promotion. He also says that he doesn't travel much because he has to look after his mother."}
{"answers": "#Person1# and #Person2# talk about popular sports in their country. Rugby is popular in both countries.", "prediction_text": "The dialogue is about sports. The first person asks the second person about the sports that are popular in his country. The second person says that football is the most popular sport. He also says that more boys than girls play football. He says that a few girls play football well. He says that some people like playing basketball. He says that more and more people like tennis. He says that fewer people play table tennis than before. He says that many people like swimming. He says that swimming is fun and keeps you fit. He says that extreme sports are only for a small minority of people. He says that several people from his university enjoy extreme sports. He says that most people just watch extreme sports. He says that there are plenty of golf courses"}
{"answers": "#Person1# and #Person2# are concerned about environmental problems. #Person1# thinks that developing countries care more about economic development than environmental protection. #Person2# would like to join an organization committed to protecting the environment.", "prediction_text": "The world is facing many environmental problems. The atmosphere is being polluted by air pollution. The rainforests are being destroyed. There is also a problem with desertification. The environment is being damaged and this leads to conflict between people."}
{"answers": "Emily's never done paycheck in the States before, so she asks Susan questions about it. Susan explains what the number and terms on the paycheck mean. Emily thanks Susan for her help.", "prediction_text": "This dialogue is about a new employee who is confused about the deductions on his paycheck. The dialogue is between the employee and his new boss, who explains the deductions to him. The dialogue is a good example of how to explain complex financial concepts to someone who is unfamiliar with them."}
{"answers": "#Person2# is being interviewed for a music teacher. #Person1# is impressed by #Person2#'s background and knowledge of classical music. And #Person1# will try classical music.", "prediction_text": "Dialogue: #Person1#: Good morning, thanks for coming. Shall we begin? Why do you want to be a music teacher?"}
{"answers": "Mr. Sellers instructs Steven to solve the power failure by replacing bad fuses.", "prediction_text": "Dialogue: #Person1#: Mr. Sellers? It's Steven speaking, your tenant. We've just suffered a power failure. What should I do now?"}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells #Person2# she is pregnant. #Person2# feels happy. They go to the doctor. #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "A man is accused of being the father of a child he did not conceive."}
{"answers": "#Person1# helps #Person2# order a hard-boiled egg with dark toast and orange juice to follow.", "prediction_text": "A dialogue between a waiter and a customer in a restaurant."}
{"answers": "#Person2# fails to hold Mr. Fox's reservation for a mid-size vehicle. Mr. Fox thinks it unreasonable. #Person2# offers Mr. Fox a compact instead and offer him a discount for his costs.", "prediction_text": "The dialogue is about a man who is trying to rent a car. He is trying to get a mid-size car, but the car rental company does not have any mid-size cars available. The man is very upset and angry because he made a reservation for a mid-size car, but the car rental company does not have any mid-size cars available. The man is very frustrated and angry because he thinks that the car rental company does not know how to hold the reservation. The man is very upset and angry because he thinks that the car rental company does not know how to take the reservation. The man is very upset and angry because he thinks that the car rental company does not know how to hold the reservation. The man is very upset and angry because"}
{"answers": "#Person2# bought a personal stereo at #Person1#'s shop, but #Person2# found there was a scratch and other people could hear noise from it. #Person1# will exchange it for another model if #Person2# can provide the receipt.", "prediction_text": "A customer complains about a personal stereo he bought from a shop. The shop assistant tries to find out what the problem is, but the customer is not satisfied with the answers."}
{"answers": "The International Student Office calls Nathaniel to inform him that he has to book another appointment with someone else as Ms. Collins is not available. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "The International Student Office is calling to book an appointment with someone else today."}
{"answers": "#Person1# has been washing dishes for over a week and #Person2# will do the dishes next week.", "prediction_text": "The speaker is complaining about the other person not doing their share of the household chores."}
{"answers": "Greg Sonders from Brown College calls the applicant Mary to ask whether she is interested in college sports and will make a further decision later.", "prediction_text": "The speaker is trying to get in touch with Mary, who is a student at Brown College. The speaker is Greg Sonders, and he is calling to ask Mary if she would be interested in playing college sports. Mary is interested in playing sports, and she tells the speaker that she holds her school's all time record for points scored in a game. The speaker is impressed and tells Mary that they will let her know their decision soon."}
{"answers": "#Person2# recommends a personal tour guide service to #Person1#.", "prediction_text": "A dialogue between two people about a tour guide service."}
{"answers": "Karren and Tim say goodbye.", "prediction_text": "A short dialogue between two people."}
{"answers": "Tim and Karren say farewell words after meeting.", "prediction_text": "This is a dialogue between two people who are leaving a party. They are saying goodbye to each other."}
{"answers": "Benjamin has trouble writing a project report. #Person1# teaches him that a good report involves a final conclusion on his work of this year, experiences in dealing with some emergent events, plan for the next year, and the format.", "prediction_text": "Dialogue: #Person1#: Hey, Benjamin, have you finished your report on the project?"}
{"answers": "#Person1# pays #Person2# by credit card for some clothes.", "prediction_text": "The speaker is in a store and is trying to buy something. The speaker is trying to pay for the item with a credit card. The speaker is trying to sign the credit card slip. The speaker is trying to pay for the item with a credit card. The speaker is trying to sign the credit card slip. The speaker is trying to pay for the item with a credit card. The speaker is trying to sign the credit card slip. The speaker is trying to pay for the item with a credit card. The speaker is trying to sign the credit card slip. The speaker is trying to pay for the item with a credit card. The speaker is trying to sign the credit card slip. The speaker is trying to pay for the item with a credit"}
{"answers": "Judy explains to #Person1# about the budget for a trip and tries to lower the cost by choosing a economic hotel.", "prediction_text": "Dialogue: #Person1#: Judy, have you ever made out how much money shall we spend? #Person2#: Oh, yeah. The total amount is no less than 13, 000 RMB, according to our itinerary. #Person1#: What? That's too much. #Person2#: Sit down. I'll show you the list of our financial budget. First of all, it is nearly 4, 000 RMB that we should spend in transportation. #Person1#: Ah, the transportation fee always takes a great part in the budget. #Person2#: Then we must pay a large amount of money for the hotel. But if we want to save money, we can choose a hotel which is not so"}
{"answers": "#Person1# and #Person2# discuss grandpa's birthday. They decide on where and when to hold the party and what food and gift to prepare.", "prediction_text": "The dialogue is about a surprise party for grandpa. The two people are discussing the details of the party. The first person wants to have a party at home, but the second person thinks that it is too cold to have a party outside. The first person suggests a hotel, but the second person thinks that it is too expensive. The first person suggests that they should have a party at home, but the second person thinks that it is too cold to have a party outside. The first person suggests a hotel, but the second person thinks that it is too expensive. The first person suggests that they should have a party at home, but the second person thinks that it is too cold to have a party outside. The first person suggests a hotel,"}
{"answers": "Charlie helps Myrdal to think over where Myrdal lost the wallet.", "prediction_text": "The dialogue is about a person who lost his/her purse. The dialogue is about the person who lost his/her purse. The dialogue is about the person who lost his/her purse. The dialogue is about the person who lost his/her purse. The dialogue is about the person who lost his/her purse. The dialogue is about the person who lost his/her purse. The dialogue is about the person who lost his/her purse. The dialogue is about the person who lost his/her purse. The dialogue is about the person who lost his/her purse. The dialogue is about the person who lost his/her purse. The dialogue is about the person who lost his/her purse. The dialogue is about the person who lost his/her purse. The dialogue is about the person"}
{"answers": "#Person2# tells #Person1# about the family activities on Easter day.", "prediction_text": "The Easter Bunny is a myth, but the Easter egg hunt is a reality."}
{"answers": "#Person1# makes an appointment with Jane before their meeting.", "prediction_text": "The speaker is planning to meet with another person to discuss ideas. The speaker wants to meet on Monday afternoon, around three o'clock. The listener agrees to the time and date."}
{"answers": "#Person2# liked Michael's motorcycle but won't buy one.", "prediction_text": "The dialogue is about a person who went to see his friend Michael and tried his new motorcycle. The person liked it but was afraid of it. He said that he would like to buy a motorcycle but he would not because he would like to buy a bicycle instead."}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells him that she's pregnant. They go to see the doctor. However, #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "A man is accused of being the father of a child he did not conceive."}
{"answers": "#Person1# comes over to say hello to Mrs. Todd, and will go shopping afterwards.", "prediction_text": "The dialogue is between two people who are neighbors. The first person is a man who is visiting his neighbor, Mrs. Todd. The second person is Mrs. Todd. The first person is talking about his wife, Jane, and how they have been busy with work and children. The second person is talking about how she should have called on the first person first, since he has just moved into the neighborhood. The first person is offering to take the second person shopping, but the second person declines the offer. The first person is glad that he came over to visit the second person, and the second person is glad that the first person came over. The first person thanks the second person for the offer, and the second person thanks the first person for"}
{"answers": "#Person2# cannot understand abstract art so #Person2# decides to go to an art exhibition of greek and roman sculpture with #Person1# tomorrow.", "prediction_text": "Dialogue: #Person1#: Are you an art aficionado?"}
{"answers": "#Person1# cannot stand rainy days, but #Person2#'s been used to it.", "prediction_text": "The weather is really miserable. It has been raining hard all day long. Yes, it's so cold. Do you think the rain is going to let up soon? Yes, I think so. The rainy season will be over soon. How long does the rainy season last? About two months. I can hardly stand these wet and cold days. It seems that it doesn't bother you very much. I'm used to this kind of days. But I prefer warm weather. Spring will come soon. And we will have some pleasant weather then. I hope so."}
{"answers": "#Person1# helps #Person2# to order a drink and some food.", "prediction_text": "The dialogue is about a customer who is ordering food at a restaurant. The customer is asking for a glass of lemonade and an appetizer. The customer is also ordering a meal. The dialogue is used to show how a customer can order food at a restaurant."}
{"answers": "#Person1# interviews Mr. Kayne why he ran a bicycle shop. He says he loves it to be his own boss. His friends helped him and they could play when there were no customers.", "prediction_text": "The speaker is talking about a new bicycle shop that has just opened. The speaker asks the owner a few questions about his business. The owner says that he loves biking and fixing bikes. He also says that he is his own boss and that he can open and close the store whenever he wants. The speaker asks the owner if he has hired any employees to work with him. The owner says that he has hired a couple of friends who love biking as much as he does. The speaker thanks the owner and wishes him success in his new business."}
{"answers": "#Person2# bargains with #Person1# on a gold watch.", "prediction_text": "Dialogue: #Person1#: What can I do for you?"}
{"answers": "#Person2# tells #Person1# #Person2# enjoyed the weekend organized by #Person1#.", "prediction_text": "Dialogue: #Person1#: Have you enjoyed your weekend?"}
{"answers": "Frank invites Besty to the party to celebrate his big promotion. Besty couldn't wait for the party.", "prediction_text": "This is a dialogue between two people. The first person is excited about a promotion and wants to throw a party. The second person is excited about the party and wants to go."}
{"answers": "#Person1# is giving #Person2# some advice for upgrading #Person2#'s system, such as adding a painting program and a faster processor.", "prediction_text": "The dialogue is about a person who is considering upgrading his computer system. The dialogue is about the advantages of upgrading the system."}
{"answers": "Mr. Lee signs his parcel and has something to express.", "prediction_text": "A man is looking for Mr. Lee. He is surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking for. He is also surprised to find that Mr. Lee is the man he is looking"}
{"answers": "#Person1# buys shoes from #Person2#.", "prediction_text": "The customer is always right."}
{"answers": "#Person1# wants to go to Peking University. #Person2# suggests asking the policeman.", "prediction_text": "Dialogue: #Person1#: Excuse me, could you tell me where Peking University is on this map?"}
{"answers": "#Person2# claims #Person2# bought a personal stereo from #Person1#'s shop but the product has two problems that #Person2# is not happy with. #Person1# offers to exchange it for another one, but #Person2# has a problem with the receipt.", "prediction_text": "A customer complains about a personal stereo he bought from a shop. The shop assistant tries to find out what the problem is, but the customer is not satisfied with the answers."}
{"answers": "Ann interviews Robin at a university radio. Robin talks about the mountain lake cycle tour and their method to avoid roadblock problem.", "prediction_text": "The dialogue is about a cycle tour. The first person is the announcer and the second person is the president of the Martha Bicycle Club. The dialogue is about the tour, the time it takes, the number of people taking part, the road block and the starting time."}
{"answers": "The doctor prescribes some medicine for Mr. Smith and tells him where to get it.", "prediction_text": "A doctor is prescribing antibiotics and cream to a patient. The patient asks where to buy the medication, and the doctor tells him to go to the pharmacy on the fourth floor."}
{"answers": "#Person2# is ordering lemonade, barbeque wings, and baby-back ribs, and #Person1# serves #Person2# gently.", "prediction_text": "The customer is ordering food and drinks."}
{"answers": "#Person1# inquires Mary about the price of products online and their payments. Mary teaches #Person1# how to shop online.", "prediction_text": "Dialogue: #Person1#: Mary, do you often do your shopping online?"}
{"answers": "#Person2# gives #Person1# a book with English songs.", "prediction_text": "The dialogue is about a customer asking for English songs in a music store. The customer is polite and the shop assistant is helpful."}
{"answers": "#Person1# has a dance with Brian at Brian's birthday party. Brian thinks #Person1# looks great and is popular.", "prediction_text": "A man and a woman are talking about the party they are attending. The man is happy to see the woman and they talk about the party. The woman says that the man is always popular with everyone and that he looks very pretty today. The man says that he is happy to see the woman and that he hopes that his necklace goes well with his dress. The woman says that the man looks great and that he is glowing. The man says that he is happy to see the woman and that they should have a drink together to celebrate the woman's birthday."}
{"answers": "Tim's project is about how to lead a greener life. #Person1# consults about his motivation and his findings at school. Tim finds there are many aspects that can be improved for his school.", "prediction_text": "Tim and his family are trying to be greener. They are trying to reduce the amount of waste they produce and the amount of energy they use. They are also trying to eat more healthily."}
{"answers": "Ted likes Jenny but is afraid to ask her out. Mike encourages Ted to tell Jenny his feelings.", "prediction_text": "Dialogue: #Person1#: Hey, Mike, is Jenny coming with us?"}
{"answers": "#Person1# and #Person2# are enjoying the weather and may go to the beach this weekend.", "prediction_text": "Dialogue: #Person1#: It's a lovely day, isn't it?"}
{"answers": "#Person1# takes an interview workshop. #Person2# offer #Person1# some useful tips on getting ready for an interview.", "prediction_text": "The dialogue above is a conversation between two people who are taking a workshop to prepare for an interview. The first person is excited to be taking the workshop, and the second person is happy to have the opportunity to help the first person prepare for the interview. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second"}
{"answers": "Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies.", "prediction_text": "The dialogue is about two people who are talking about their favorite movies. The first person is Mary, and the second person is Frank. They are talking about their favorite movies, and they are both very interested in movies. They are also very different in their tastes. Mary likes art films, and Frank likes thrillers. They are also very different in their movie-watching habits. Mary usually watches movies at home, but Frank usually goes to the cinema. They are also very different in their movie-watching habits. Mary usually watches movies at home, but Frank usually goes to the cinema. They are also very different in their movie-watching habits. Mary usually watches movies at home, but Frank usually goes to the cinema. They are also very different"}
{"answers": "#Person2# wants a pair of Jordan shoes in size 41.", "prediction_text": "The dialogue is about a customer who wants to buy a pair of sport shoes. The customer asks the shop assistant to help him. The shop assistant asks the customer what size he wants. The customer says he wants size 41. The shop assistant gives him the shoes. The customer says they fit him. He asks the shop assistant to make it a little cheaper. The shop assistant says it's already on sale."}
{"answers": "#Person1# and #Person2# are talking about the founding time and founder of the New York Times, the Washington Post, and the Los Angeles Times were founded.", "prediction_text": "The dialogue is about the history of newspapers. The first question is about the Washington Post, which was founded in 1877. The second question is about the New York Times, which was founded in 1851. The third question is about the Los Angeles Times, which was founded in 1881."}
{"answers": "#Person2# introduces the surroundings and information about their company to #Person1#.", "prediction_text": "Dialogue: #Person1#: How long does it take to get to downtown from here?"}
{"answers": "Amy called Jimmy to return his book but Jimmy went to Beihai Park for picnic so Amy didn't reach him. They will meet later.", "prediction_text": "Dialogue: #Person1#: Jimmy, I called you yesterday, but you didn't pick up."}
{"answers": "#Person2# orders a pizza delivery at Pizza House. Marty promises it will arrive in thirty minutes or it's free.", "prediction_text": "A dialogue between a customer and a pizza house."}
{"answers": "Tom tells Catherine he had an underdone sandwich for lunch. Catherine doesn't like fast food because she thinks it's not healthy. She explains that two-thirds of Americans may avoid places like McDonald's and KFC.", "prediction_text": "Dialogue: #Person1#: Hey, Tom, did you enjoy your lunch?"}
{"answers": "#Person1# first introduces the 2006 fiscal year marketing plan then analyzes their performance and explains with the sale graph.", "prediction_text": "The first part of the talk was about the marketing plan for the 2006 fiscal year. The second part was about the sales figures for the 2005 and 2006 fiscal years. The third part was about the goals for the 2006 fiscal year."}
{"answers": "#Person1# is not satisfied with #Person1#'s steak and asks to change one, and #Person2# agrees.", "prediction_text": "Dialogue: #Person1#: Excuse me."}
{"answers": "#Person1# complains to Tony that Christmas has made #Person1# busier.", "prediction_text": "Dialogue: #Person1#: Hi Tony, I feel so tired."}
{"answers": "#Person1# interviews #Person2# about the understanding of this company, personal preference, and salary expectation. #Person2# asks about the fringe benefits.", "prediction_text": "Dialogue: #Person1#: What do you know about our company?"}
{"answers": "Mr. Kayne shares with the audience about how he took over and established his bicycle shop as a salesman and how he's running his business now.", "prediction_text": "The speaker is talking about a new bicycle shop that has just opened. The speaker asks the owner a few questions about his business. The owner says that he loves biking and fixing bikes. He also says that he is his own boss and that he can open and close the store whenever he wants. The speaker asks the owner if he has hired any employees to work with him. The owner says that he has hired a couple of friends who love biking as much as he does. The speaker thanks the owner and wishes him success in his new business."}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay the rent. Ayden is short on cash but still lends $20 to #Person1#. They decide to have dinner at #Person1#'s place.", "prediction_text": "This is a dialogue between two people, #Person1# and #Person2#. #Person1# is asking #Person2# for a favor, and #Person2# is trying to help #Person1# out. #Person1# is in a tight spot, and #Person2# is trying to help him out. #Person2# is also in a tight spot, and he is trying to help #Person1# out. #Person1# is grateful for #Person2#'s help, and he promises to pay #Person2# back on Monday. #Person2# is happy to help #Person1#, and he agrees to come over for dinner at #Person1#'s place. #Person1#"}
{"answers": "#Person1# gives #Person2# a few suggestions on clothes but none is taken.", "prediction_text": "The dialogue is about a woman who is shopping for a dress. She is trying to find a dress that is suitable for the weather. She is trying to find a dress that is light and not heavy. She is trying to find a dress that is suitable for the weather. She is trying to find a dress that is light and not heavy. She is trying to find a dress that is suitable for the weather. She is trying to find a dress that is light and not heavy. She is trying to find a dress that is suitable for the weather. She is trying to find a dress that is light and not heavy. She is trying to find a dress that is suitable for the weather. She is trying to find a dress that is light and"}
{"answers": "#Person2# wants to buy a pair of Jordan shoes in size 41.", "prediction_text": "The dialogue is about a customer who wants to buy a pair of sport shoes. The customer asks the shop assistant to help him. The shop assistant asks the customer what size he wants. The customer says he wants size 41. The shop assistant gives him the shoes. The customer says they fit him. He asks the shop assistant to make it a little cheaper. The shop assistant says it's already on sale."}
{"answers": "#Person1# and #Person2# exchange opinions toward the effect of earthquake and how people feel about it.", "prediction_text": "The dialogue is about the earthquake in Wenchuan, China. The earthquake is one of the most destructive natural disasters in the world. The dialogue is about the earthquake in Wenchuan, China. The earthquake is one of the most destructive natural disasters in the world. The dialogue is about the earthquake in Wenchuan, China. The earthquake is one of the most destructive natural disasters in the world. The dialogue is about the earthquake in Wenchuan, China. The earthquake is one of the most destructive natural disasters in the world. The dialogue is about the earthquake in Wenchuan, China. The earthquake is one of the most destructive natural disasters in the world. The dialogue is about the earthquake in Wenchuan, China. The earthquake"}
{"answers": "Mr. Murray wants a library card. #Person2# issues one to him after checking his application and driver license.", "prediction_text": "A customer is applying for a library card. The librarian asks for the customer's driver's license and application. The customer is told about the rules and limitations of the library card. The customer is thanked for joining the library."}
{"answers": "Frank explains to Judy the reason why he took a job at the Post office is that the government offers excellent health insurance benefits for its employees.", "prediction_text": "The dialogue is about a man who is working for the Post Office. He is very happy with his job because he can get free medical care for his family."}
{"answers": "#Person1# hosts the week's People You Meet, featuring Mark Leach. Mark introduces his work as an information officer in London.", "prediction_text": "This dialogue is about a tourist information officer in London."}
{"answers": "#Person2# is going to set up #Person2#'s own law office. #Person1# wishes #Person2# good luck.", "prediction_text": "Dialogue: #Person1#: You're going to set up your own law office, aren't you?"}
{"answers": "The workmen are walking across the wet cement to put up the notice.", "prediction_text": "Dialogue: #Person1#: You mustn't touch the wet paint, Bill."}
{"answers": "#Person1# asks #Person2# about the instructions for getting and using the discount coupons.", "prediction_text": "The dialogue is about a customer who wants to buy goods in a supermarket. The customer asks the cashier for special discount coupons. The cashier tells the customer that he can get more coupons if he buys more goods. The customer asks how much discount he can get if he uses the coupons to buy goods next time. The cashier tells the customer that he can get 10 pence off if he uses the coupons. The customer asks if he can buy everything in the supermarket by the coupons. The cashier tells the customer that he needs to take advantage of the coupons within their expiry date. The customer asks how long he can keep the coupons. The cashier tells the customer that the coupons can be used at least one year. The customer asks how many bags of"}
{"answers": "Ann interviews Robin about the mountain lake cycle tour. Robin introduces the event and explains its settings of the tour.", "prediction_text": "The dialogue is about a cycle tour. The first person is the announcer and the second person is the president of the Martha Bicycle Club. The dialogue is about the tour, the time it takes, the number of people taking part, the road block and the starting time."}
{"answers": "#Person1# gets lost on the way to Liu Ichang. #Person2# tells #Person1# to take Bus No. 713 because it's far.", "prediction_text": "Dialogue: #Person1#: Excuse me, I'm lost. Can you show me where I am in this map?"}
{"answers": "#Person1# and #Person2# are planning the places of interest they are going to visit in London, such as Nelson's Column, Westminster Abbey, Tower of London, Madame Tussaud's Waxworks Museum, and so on. They are both looking forward to it.", "prediction_text": "Dialogue: #Person1#: London is such a historic city! There's history everywhere you look. There's nelson's column, built as a monument to one of the Britain's great admirals and his important victory. He won the battle of #Person2#: I'm looking forward to seeing Westminster abbey, where many historic figures are buried, like Isaac Newton, the great mathematician and Winston Churchill, the great wartime leader. #Person1#: Nearby, on the banks of the thames, there's the statue of Boadicea. She fought the Romans when they invaded Britain. #Person2#: Women have often played an important role in british history. Queen Elizabeth I built a navy strong enough to fight off the Spanish arm"}
{"answers": "#Person1# offers some job choices to #Person2#, but #Person2# doesn't like them.", "prediction_text": "The speaker is looking for a job. The other person suggests that he or she could work in the media. The speaker is not interested in that. The other person suggests working with computers. The speaker is interested in that."}
{"answers": "Tom calls Sara for her help to take care of his son Ken, because he is taking his daughter to the hospital.", "prediction_text": "Dialogue: #Person1#: Hi, is that Sara?"}
{"answers": "#Person1# drives #Person2# from the airport to an inn and they have a casual talk about themselves.", "prediction_text": "This dialogue is a conversation between a young woman and a man who is older than her. The man is curious about the woman's age and where she is from. The woman is curious about the man's age and where he is from. The man is surprised that the woman is not Chinese, but Mexican. The woman is surprised that the man is not Chinese, but Colombian. The man is surprised that the woman speaks Spanish. The woman is surprised that the man speaks Spanish. The man and woman are both surprised that they speak the same language."}
{"answers": "#Person1# has difficulty getting access to the computers in the library to do #Person1#'s assignment.", "prediction_text": "The speaker is frustrated because he is unable to use the computers in the library. The other person understands the speaker's frustration and is looking forward to the day when he can afford to get his own computer."}
{"answers": "#Person1# and #Person2# talk about Abraham Lincoln and his glorious history. They both admire him.", "prediction_text": "Dialogue: #Person1#: Who stands out in your mind as a man or woman of sound character? #Person2#: If I think of famous people, I think of Abraham Lincoln. #Person1#: He's the US president, who walked five miles just to give a lady her change, isn't he? #Person2#: That's the one. He also was famous for never giving up on his goals. #Person1#: That's right. He ran for office quite a few times before he was finally elected. #Person2#: And I also admire him for his courage in fighting for equal rights. #Person1#: He had great vision, didn't he? #Person2#: And humility. I would have liked to"}
{"answers": "Sarah is upset and complains to #Person1# about an inefficient meeting and Bob's interruption. #Person1# gives Sarah some suggestions on how to keep her speech short and sweet.", "prediction_text": "Dialogue: #Person1#: Hey Sarah, are you all right? You look upset. #Person2#: As a matter of fact, I am a bit upset. I just came out of a meeting and it didn't go very well. #Person1#: What happened? #Person2#: No one would listen to any of my suggestions. Instead, they just kept arguing with each other. #Person1#: Who was chairing the meeting? #Person2#: Bob. #Person1#: Well, I can tell you from experience that Bob might come off a little strong sometimes. #Person2#: That's exactly what happened! He kept interrupting everyone with his own suggestions and did not want to hear what others had to say."}
{"answers": "Marquet suggests #Person1# take a good introductory course for non-science majors.", "prediction_text": "The dialogue is about a student who wants to take a science course. The student is not sure if he should take the course because he is not good in maps. The student asks the professor for advice. The professor tells the student that he should take the course because he wants to graduate this year. The professor also tells the student that he should take a good introductory course for non-science majors. The student agrees that he needs a good introductory course for non-science majors. The student also agrees that he is weak in maps. The professor tells the student that it is a problem."}
{"answers": "#Person2# thinks #Person2#'s meal as perfect and orders dessert and tea to share with friends.", "prediction_text": "The dialogue is about a restaurant. The first person is the waiter, and the second person is the customer. The waiter asks the customer if the meal was good, and the customer says that it was perfect. The waiter asks if the customer would like a dessert, and the customer says that they would like to split something with their friends. The waiter asks what dessert they would like, and the customer says that they would like the apple crisp. The waiter asks if they would like a second dessert, and the customer says that they would like a piece of chocolate mousse cake. The waiter asks if they would like some coffee and tea, and the customer says that they are all tea drinkers. The waiter says that he will prepare the desserts and bring"}
{"answers": "#Person2# describes to #Person1# about the relationship with the boss and what their boss strengths lie on.", "prediction_text": "The speaker is trying to find out how the other person feels about their boss. The speaker is trying to find out if the other person has a good relationship with their boss. The speaker is trying to find out if the other person has a good relationship with their boss. The speaker is trying to find out if the other person has a good relationship with their boss. The speaker is trying to find out if the other person has a good relationship with their boss. The speaker is trying to find out if the other person has a good relationship with their boss. The speaker is trying to find out if the other person has a good relationship with their boss. The speaker is trying to find out if the other person has a good relationship with their boss. The"}
{"answers": "#Person1# can tell #Person2# their organization's decision by phone or e-mail in about one week.", "prediction_text": "The dialogue between #Person1# and #Person2# is about how to get in touch with #Person1#."}
{"answers": "Ms. Dawson takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists.", "prediction_text": "A boss gives a memo to his secretary to type up. The secretary asks a question about the memo, and the boss gives a curt answer. The secretary then types up the memo and distributes it to all employees."}
{"answers": "#Person1# is starving and wants to have authentic Chinese cuisine so #Person2# recommends some. #Person1# chooses the Quanjude restaurant as #Person2# wants to try the Beijing roast duck and it's nearby.", "prediction_text": "Dialogue: #Person1#: Oh, I'm starving. It's my first time to China. And I'd like to try some real Chinese cuisine. What would you recommend?"}
{"answers": "#Person1# wants to start #Person1#'s own business, but #Person2# warns #Person1# of the hassle. #Person2# tells #Person1# what is needed in a business plan and #Person1# decides to stick to the old job for now.", "prediction_text": "A business plan is a document that outlines the goals and strategies of a business. It is a crucial tool for any entrepreneur, as it helps to define the company's mission, identify its target market, and develop a plan for achieving its goals. A well-written business plan can help to attract investors, secure loans, and establish credibility with customers and partners."}
{"answers": "#Person2# is teaching #Person1# how to use an ATM step by step because #Person1# doesn't know how to use.", "prediction_text": "This dialogue is a conversation between two people. The first person, #Person1#, needs to use the ATM, but they don't know how. The second person, #Person2#, helps them figure it out. The first person asks what they have to do, and the second person explains the steps. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to"}
{"answers": "#Person1# confirms Jack's reservation for a double for three nights. Jack checks in.", "prediction_text": "The dialogue is about a hotel reservation. The first person is a hotel receptionist and the second person is a guest. The guest asks for a reservation and the receptionist asks for his name. The guest gives his name and asks if the reservation is correct. The receptionist confirms that the reservation is correct and asks the guest to fill up the forms."}
{"answers": "#Person2# wants to have his coffee with his dinner and his order for dessert later.", "prediction_text": "The customer asks for water and coffee. The waiter asks if the customer wants the coffee with the dinner or later. The customer asks for water and coffee. The waiter asks if the customer wants the coffee with the dinner or later. The customer asks for water and coffee. The waiter asks if the customer wants the coffee with the dinner or later. The customer asks for water and coffee. The waiter asks if the customer wants the coffee with the dinner or later. The customer asks for water and coffee. The waiter asks if the customer wants the coffee with the dinner or later. The customer asks for water and coffee. The waiter asks if the customer wants the coffee with the dinner or later. The customer asks for water and coffee. The waiter asks if"}
{"answers": "Nathan is going to Chicago for his practice in Chicago Tribune. His mother is worried about him and keeps asking him questions about this internship. Nathan says he'll work with a regular writer to get experience and she's finally at ease.", "prediction_text": "The speaker is talking to her son, Nathan, who is about to leave for a summer job in Chicago. She is happy for him, but she is also sad that he will be gone. She asks him how he thinks he will do in the big city. He says he is not worried about working for a big newspaper like the Chicago Tribune. He says he is not afraid of hard work and he knows how to organize his day. He says he is sure he will do a good job. She asks him if he knows what he will be doing. He says he is not worried about that. He says he has a lot of natural curiosity and he is very social, so he is sure he will learn from all the other people there."}
