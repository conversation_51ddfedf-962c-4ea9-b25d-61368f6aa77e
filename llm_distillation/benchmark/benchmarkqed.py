import os
import sys
import json
import score
import torch
import logging
import argparse
import random
import numpy as np
from transformers import AutoTokenizer, AutoModelForCausalLM, AutoModelForSeq2SeqLM
from torch.utils.data import DataLoader
import wandb
# Add paths for imports
sys.path.append(f"{os.getenv('HOME')}/Multi-Level-OT/llm_distillation")
sys.path.append(f"{os.getenv('HOME')}/Multi-Level-OT")
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from datasets import load_dataset, load_from_disk
from itertools import chain
from tqdm import tqdm
import re
os.environ["TOKENIZERS_PARALLELISM"] = "true"

def get_device():
    device = "cpu"
    if torch.cuda.is_available():
        device = torch.device("cuda")
    if torch.backends.mps.is_available() and torch.backends.mps.is_built():
        device = torch.device("mps")
    return device

def remove_repeated_words(text):
    pattern = re.compile(r'\b(\w+?)\b(?:\s+\1\b)+$', re.IGNORECASE)
    match = pattern.search(text)
    if match:
        return text[:match.start(1)] + text[match.start(1):match.end(1)]
    return text

def tokenization(items, tokenizer):
    return tokenizer(items["prompt"], padding='longest')

def mapping(path, ds):
    with open(path, 'r') as f: mapping = json.load(f)
    for key, value in mapping.items():
        ds = ds.rename_column(key, value)
    return ds

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Script to benchmark a model on a dataset.")
    parser.add_argument("--model_id", type=str, default="meta-llama/Llama-2-7b-hf", help="Model ID")
    parser.add_argument("--model_tokenizer", type=str, help="Model tokenizer (default: model_id)")
    parser.add_argument("--dataset_id", type=str, help="Dataset hugging face ID")
    parser.add_argument("--split_name", type=str, default="validation", help="Dataset split name")
    parser.add_argument("--context", action="store_true", help="To pre prompt an explanation of the task")
    parser.add_argument("--title", action="store_true", help="To keep title in the prompt")
    parser.add_argument("--number_few_shot", type=int, default=0, help="Number of few-shot examples")
    parser.add_argument("--batch_size", type=int, default=4, help="Batch size")
    parser.add_argument("--num_workers", type=int, default=2, help="Number of data loader workers")
    parser.add_argument("--bfloat", action="store_true", help="Load model in bf16")
    parser.add_argument("--save_predictions", action="store_true", help="Save predictions in txt file")
    parser.add_argument("--from_disk", action="store_true", help="Load dataset from disk")
    parser.add_argument("--task", type=str, default="qa", help="Benchmark type (qa, qa_generative, summarization)")
    parser.add_argument("--mapping", type=str, default="", help="JSON file to map dataset column name")
    parser.add_argument("--mapping_dict", type=str, default="text", help="Field name in the answer dictionary.")
    parser.add_argument("--bert_score", action="store_true", help="To compute bert score")
    parser.add_argument("--output_path", type=str, default="", help="Output path")
    parser.add_argument("--context_length", type=int, default=None, help="Delete dataset row with length > context_length")
    parser.add_argument("--seq2seq", action="store_true", help="For encoder-decoder model")
    parser.add_argument("--max_samples", type=int, default=None, help="Maximum number of samples to evaluate")
    parser.add_argument("--wandb_api_key", type=str, default=None, help="Wandb API key for live logging")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for reproducibility")
    args = parser.parse_args()

    # Set seed for reproducibility
    def set_seed(seed):
        random.seed(seed)
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)
        os.environ['PYTHONHASHSEED'] = str(seed)
        logging.info(f"Random seed set to {seed} for reproducibility")

    set_seed(args.seed)

    if 'chat' in args.model_id.split('/n')[:-2] or "instruct" in args.model_id.lower().split('/n')[:-2]:
        from llm_distillation.prompt.prompt import create_chat_prompt as create_prompt
        is_chat = True
    else :
        from llm_distillation.prompt.prompt import create_prompt
        is_chat = False

    def create_prompt_column(task, few_shot, item, has_title):
        if False and True: exit()
        if task == "qa" or task == "qa_generative":
            #print(item.keys())
            item['prompt'] = create_prompt(
                task, few_shot,
                title = item['title'] if has_title else "",
                context = item['context'],
                question = item['question'],
                sys_user = True if "mistralai" in args.model_id or args.context else False,
                chat_template = tokenizer.apply_chat_template if is_chat else None
            )
        elif task == "qa_medical":
             item['prompt'] = create_prompt(
                task, few_shot,
                context = item['context'],
                question = item['question'],
                sys_user = True if "mistralai" in args.model_id or args.context else False,
                chat_template = tokenizer.apply_chat_template if is_chat else None
            )
        elif task == "summary_dialogue":
            item['prompt'] = create_prompt(
                task, few_shot,
                # context = item['paragraph_text'],
                context = item['dialogue'],
                sys_user = True if "mistralai" in args.model_id or args.context else False,
                chat_template = tokenizer.apply_chat_template if is_chat else None
            )
        elif task == "summary":
            item['prompt'] = create_prompt(
                task, few_shot,
                title = item['title'] if has_title else "",
                context = item['dialogue'],
                sys_user = True if "mistralai" in args.model_id or args.context else False,
                chat_template = tokenizer.apply_chat_template if is_chat else None
            )
        return item
    
    logging.basicConfig(level=logging.INFO)
    logging.info('Start')
    device = get_device()
    logging.info(f'Device: {device}')

    logging.info(f'Loading tokenizer...')
    tokenizer = AutoTokenizer.from_pretrained(args.model_tokenizer if args.model_tokenizer else args.model_id,trust_remote_code=True)
    tokenizer.add_special_tokens({"pad_token":tokenizer.eos_token})
    tokenizer.padding_side = 'left'
    logging.info(f'Tokenizer loaded.')

    logging.info('Loading model...')
    if args.bfloat and device != "cpu":
        if args.seq2seq: model = AutoModelForSeq2SeqLM.from_pretrained(args.model_id, torch_dtype=torch.bfloat16).to(device)
        else: model = AutoModelForCausalLM.from_pretrained(args.model_id, torch_dtype=torch.bfloat16).to(device)
    else:
        if args.seq2seq: model = AutoModelForSeq2SeqLM.from_pretrained(args.model_id).to(device)
        else: model = AutoModelForCausalLM.from_pretrained(args.model_id).to(device) 
    model.resize_token_embeddings(len(tokenizer))
    model.config.pad_token_id = tokenizer.pad_token_id
    model.eval()
    logging.info('Model loaded.')

    logging.info('Processing dataset...')
    if args.from_disk:
        # Check if dataset_id is a Python script (for QED)
        if args.dataset_id.endswith('.py'):
            # Load from local Python script
            dataset = load_dataset(args.dataset_id, split=args.split_name, trust_remote_code=True)
        else:
            # Load from disk (existing functionality)
            dataset = load_from_disk(args.dataset_id)
            if args.split_name: dataset = dataset[args.split_name]
        print(dataset)
        # for i in range(30):
        #     print(dataset['original_nq_answers'][:][i])
        #input()
    else: dataset = load_dataset(args.dataset_id, split=args.split_name)

    # Apply mapping before creating prompts
    if args.mapping: dataset = mapping(args.mapping, dataset)
    has_title = True if 'title' in dataset.column_names and args.title else False
    dataset = dataset.map(lambda item: create_prompt_column(args.task, args.number_few_shot, item, has_title))
    dataset = dataset.map(lambda items: tokenization(items, tokenizer=tokenizer), batched=True, batch_size=args.batch_size)
    dataset = dataset.filter(lambda item: len(item['input_ids']) <= args.context_length) if args.context_length else dataset

    # Limit number of samples if specified (with seeded shuffle for reproducibility)
    if args.max_samples and len(dataset) > args.max_samples:
        # Shuffle dataset with seed for reproducible sample selection
        dataset = dataset.shuffle(seed=args.seed)
        dataset = dataset.select(range(args.max_samples))
        logging.info(f"Limited dataset to {args.max_samples} samples (shuffled with seed {args.seed})")
    #print(args.model_id)
    #print(dataset['prompt'][0])
    dataset.set_format(type="torch", columns=["input_ids", "attention_mask"])
    dataloader = DataLoader(dataset, batch_size=args.batch_size, num_workers=args.num_workers)
    logging.info('Dataset processed...')

    # Initialize wandb if API key is provided
    wandb_instance = None
    if args.wandb_api_key:
        try:
            import socket

            # Set wandb directories to avoid permission issues
            base_dir = "/storage/nammt/KD-SLM/Multi-Level-OT"
            os.environ["WANDB_API_KEY"] = args.wandb_api_key
            os.environ["WANDB_DIR"] = base_dir
            os.environ["WANDB_CACHE_DIR"] = f"{base_dir}/.wandb_cache"
            os.environ["WANDB_CONFIG_DIR"] = f"{base_dir}/.wandb_config"
            os.environ["WANDB_DATA_DIR"] = f"{base_dir}/.wandb_data"

            # Create wandb directories if they don't exist
            for dir_path in [f"{base_dir}/.wandb_cache", f"{base_dir}/.wandb_config", f"{base_dir}/.wandb_data"]:
                os.makedirs(dir_path, exist_ok=True)

            wandb.init(
                project="qed-benchmark",
                name=f"{args.model_id.replace('/', '_')}_{args.dataset_id.replace('/', '_')}_{os.getpid()}",
                config=vars(args),
                notes=f"Host: {socket.gethostname()}"
            )
            wandb_instance = wandb
        except Exception as e:
            logging.warning(f"Could not initialize wandb: {e}")

    logging.info('Starting predictions...')
    predictions = []
    step = 0
    with torch.no_grad():
        for batch in tqdm(dataloader):
            # num += 1
            # if num>2:
            #     continue
            input_ids = batch['input_ids'].to(device)
            output = model.generate(
                input_ids,
                attention_mask=batch['attention_mask'].to(device),
                max_new_tokens=150,
                do_sample=False,
                eos_token_id= [193, tokenizer.eos_token_id] if "falcon" in args.model_id else tokenizer.eos_token_id
            )

            # For causal LM, remove input tokens from output (keep only generated tokens)
            if not args.seq2seq:
                # Handle each sequence individually to account for different input lengths
                generated_sequences = []
                for i in range(output.shape[0]):
                    input_length = (input_ids[i] != tokenizer.pad_token_id).sum().item()
                    generated_part = output[i, input_length:]
                    generated_sequences.append(generated_part)

                # Pad sequences to the same length for batch decoding
                max_length = max(len(seq) for seq in generated_sequences)
                padded_sequences = []
                for seq in generated_sequences:
                    if len(seq) < max_length:
                        # Pad with pad_token_id
                        padding = torch.full((max_length - len(seq),), tokenizer.pad_token_id,
                                           dtype=seq.dtype, device=seq.device)
                        seq = torch.cat([seq, padding])
                    padded_sequences.append(seq.unsqueeze(0))

                output = torch.cat(padded_sequences, dim=0)

            # print(output)
            # input()
            sentences = tokenizer.batch_decode(output, skip_special_tokens=True)
            for i in range(len(sentences)):
                # Clean up the generated text
                text = sentences[i].strip()

                # If text starts with newline, remove it and get the first meaningful line
                if text.startswith('\n'):
                    text = text[1:].strip()

                # Take the first line that's meaningful (not just punctuation)
                lines = text.split('\n')
                for line in lines:
                    line = line.strip()
                    # Skip empty lines and lines that are just punctuation
                    if line and len(line) > 1 and not line in [':', ';', '.', ',', '!', '?']:
                        text = line
                        break
                else:
                    # If no meaningful line found, take the first non-empty line anyway
                    for line in lines:
                        line = line.strip()
                        if line:
                            text = line
                            break
                    else:
                        text = ""  # If no non-empty lines found

                # Handle falcon-specific tokens
                if "falcon" in args.model_id and text.endswith("<|im_end|>"):
                    text = text[:-10]

                sentences[i] = text
                #     print(i)
                # print(sentences[i])
                # sentences[i] = remove_repeated_words( sentences[i])
                # print(sentences[i])
                # sentences[i] = re.sub(r'\b(\w+)\b(?:\s*(?:</?\w+>)*\s*\1\b)+$', r'\1', sentences[i])
                # sentences[i] = re.sub(r'(.)(?:</?\w+>)*\1+$', r'\1', sentences[i])
                # print("re")
                # sentences[i] = sentences[i].replace("</p>s", "")
                # sentences[i] = sentences[i].replace("</p>", "")
                # sentences[i] = sentences[i].replace("</s>", "")
                # sentences[i] = sentences[i].replace("</", "")
                # sentences[i] = re.sub(r'(\S)\s*(\1\s*)+$', r'\1', sentences[i])
            predictions.append(sentences)
            step += len(sentences)
            # Live wandb logging every 10 examples
            if wandb_instance and step % 10 == 0:
                flat_preds = list(chain(*predictions))
                # Extract answers for partial evaluation (same logic as final extraction)
                if isinstance(dataset['answers'][0], list) and len(dataset['answers'][0]) > 0 and isinstance(dataset['answers'][0][0], dict):
                    answers_partial = [item[0]['string'] if item and len(item) > 0 and 'string' in item[0] else "" for item in dataset['answers']]
                elif isinstance(dataset['answers'][0], dict):
                    field_name = 'string' if 'string' in dataset['answers'][0] else args.mapping_dict
                    answers_partial = [item[field_name] for item in dataset['answers']]
                elif isinstance(dataset['answers'][0][0], dict):
                    field_name = 'string' if 'string' in dataset['answers'][0][0] else args.mapping_dict
                    answers_partial = [item[0][field_name] for item in dataset['answers']]
                else:
                    answers_partial = dataset['answers']
                answers_partial = answers_partial[:len(flat_preds)]
                # Compute partial metrics
                try:
                    partial_results = score.f1_score(flat_preds, answers_partial)
                    partial_results['em'] = score.exact_match(flat_preds, answers_partial)
                    partial_results['squad'] = (partial_results['f1']+partial_results['em'])/2
                    partial_results.update(score.rouge(flat_preds, answers_partial))
                    for key in partial_results:
                        partial_results[key] = round(partial_results[key]*100, 2)
                    wandb_instance.log({**partial_results, "step": step, "samples": len(flat_preds)})
                except Exception as e:
                    logging.warning(f"Wandb partial log failed: {e}")
            
    logging.info('Predictions finished')

    logging.info('Computing scores...')

    # Extract answers from dataset based on mapping
    # For QED dataset, answers come from original_nq_answers which have 'string' field
    if isinstance(dataset['answers'][0], list) and len(dataset['answers'][0]) > 0 and isinstance(dataset['answers'][0][0], dict):
        # Handle QED format: original_nq_answers -> answers mapping
        answers = [item[0]['string'] if item and len(item) > 0 and 'string' in item[0] else "" for item in dataset['answers']]
    elif isinstance(dataset['answers'][0], dict):
        # Handle other formats with mapping_dict
        field_name = 'string' if 'string' in dataset['answers'][0] else args.mapping_dict
        answers = [item[field_name] for item in dataset['answers']]
    elif isinstance(dataset['answers'][0][0], dict):
        field_name = 'string' if 'string' in dataset['answers'][0][0] else args.mapping_dict
        answers = [item[0][field_name] for item in dataset['answers']]
    else:
        answers = dataset['answers']

    predictions = list(chain(*predictions))
    answers = answers[:len(predictions)]

    results = score.f1_score(predictions, answers)
    results['em'] = score.exact_match(predictions, answers)
    results['squad'] = (results['f1']+results['em'])/2
    print("f1",results['f1'])
    print("em",results['em'])
    results.update(score.rouge(predictions, answers))
    if args.bert_score:
        results_bert = score.bert_score(predictions, answers)
        results["f1_bert"] = sum(results_bert["f1"])/len(results_bert["f1"])
        print("len(results_bert['f1'])",len(results_bert["f1"]))
        print("results_bert['f1']",results_bert["f1"])
        results["precision_bert"] = sum(results_bert["precision"])/len(results_bert["precision"])
        results["recall_bert"] = sum(results_bert["recall"])/len(results_bert["recall"])
    for key in results: results[key] = round(results[key]*100, 2)
    logging.info(results)

    # Final wandb logging
    if wandb_instance:
        try:
            wandb_instance.log({**results, "step": len(predictions), "samples": len(predictions)})
            wandb_instance.finish()
        except Exception as e:
            logging.warning(f"Wandb final log failed: {e}")

    titled_folder = "titled" if has_title else "untitled"
    output = args.output_path if args.output_path else f"{os.getenv('HOME')}/Multi-Level-OT/llm_distillation/benchmark/results/{args.model_id.split('/')[-1]}/{args.dataset_id.split('/')[-1]}/{titled_folder}"

    # Create output directory if it doesn't exist
    os.makedirs(output, exist_ok=True)

    with open(f"{output}/{args.number_few_shot}shots.json", 'w') as json_file:
        json.dump(
            {
                "model": args.model_id,
                "dataset": args.dataset_id,
                "title": has_title,
                "number_few_shot": args.number_few_shot,
                "samples_number": len(predictions),
                **results,
            }, 
            json_file, indent=4
        )
    logging.info("Process completed.")

    if args.save_predictions:
        prediction_data = [{'answers': answers[index], 'prediction_text': item} for index, item in enumerate(predictions)]
        with open(f"{output}/predictions_{args.number_few_shot}shots.json", 'w') as file:
            for prediction_dict in prediction_data:
                json.dump(prediction_dict, file)
                file.write('\n')
