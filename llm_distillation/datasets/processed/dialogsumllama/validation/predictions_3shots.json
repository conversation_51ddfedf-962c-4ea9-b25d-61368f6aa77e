{"answers": "#Person2# has trouble breathing. The doctor asks #Person2# about it and will send #Person2# to a pulmonary specialist.", "prediction_text": "#Person1# is a doctor and #Person2# is a patient who is experiencing trouble breathing. #Person1# suggests sending #Person2# to a pulmonary specialist for testing and diagnosis."}
{"answers": "#Person1# invites <PERSON> to go workout and persuades him into working out on arms and stomach.", "prediction_text": "<PERSON> and #Person1# plan to workout at 3:30, but #Person1# wants to switch the days of their workout schedule. <PERSON> agrees to switch two days."}
{"answers": "#Person1# plans to stop eating unhealthy foods, and #Person2# shares #Person2#'s healthy recipe with #Person1#.", "prediction_text": "#Person1# and #Person2# are discussing their eating habits. #Person2# tends to stick to fruits, vegetables, and chicken, which are healthy foods."}
{"answers": "#Person2# believes in UFOs and can see them in dreams. #Person1# asks #Person2# about UFOs and aliens in #Person2#'s dreams and finds #Person2#'s dreams fantastic.", "prediction_text": "#Person1# doesn't believe in UFOs, but #Person2# does and claims to have seen them in their dreams. #Person2# explains that UFOs are aliens from outer space who are on a mission to make friends with humans and that they can speak English."}
{"answers": "#Person1# didn't go to school today. #Person2# wants to skip class tomorrow to go to the movies.", "prediction_text": "#Person1# and #Person2# are having a conversation. #Person1# didn't go to school and #Person2# wants to go to the movies but doesn't want to go alone."}
{"answers": "#Person1# asks #Person2# to quit smoking for health. #Person2# thinks it's hard but agrees.", "prediction_text": "#Person1# tells #Person2# to quit smoking because it can lead to lung cancer, and #Person2# agrees to quit."}
{"answers": "Sherry reminds Mr. White to sign.", "prediction_text": "Sherry needs Mr. White to sign some documents before she leaves. Mr. White apologizes for keeping her waiting and signs the documents."}
{"answers": "#Person1# asks Karen where Karen stayed and how Karen spent the weekend at the beach. #Person1# thinks it's relaxing and wishes to go there.", "prediction_text": "#Person1# asks #Person2# about their weekend at the beach, and #Person2# tells #Person1# about their activities there, including jogging, playing volleyball, and cooling off in the water. #Person1# expresses envy and mentions their own lack of relaxation."}
{"answers": "#Person1# asks about #Person2#'s hobbies. #Person2# likes photography and has a photo studio.", "prediction_text": "#Person1# asks #Person2# about their hobby, which is photography. #Person2# enjoys taking photos and developing and printing them. #Person1# finds it interesting."}
{"answers": "#Person1# and #Person2# talk about Bill Gate's home. #Person1# thinks it's fantastic but wouldn't want to live there. #Person2# asks about #Person1#'s dream house. #Person1# wants to live in a small and old cottage for its character.", "prediction_text": "#Person1# talks about Bill Gates' home, saying it has a library, theatre, swimming pool, and guest house. #Person1# does not want to live there, preferring a small cottage in a quiet village in England. #Person1# prefers old homes because they have character, and #Person1# wears second-hand clothes because they are cheaper. #Person1# would restore an old house to its original state, imagining what it would be like to live in another time in history."}
{"answers": "#Person1# feels tired because of #Person1#'s mother's over-protection.", "prediction_text": "#Person1# is tired of their life and wants to leave their family and spread their wings, while #Person2# is surprised and agrees."}
{"answers": "#Person1# and #Person2# talk about advertisements in Hong Kong. #Person2# likes the billboards, while #Person1# thinks there're too many advertisements. They both hate leaflets and broadcasts but like the way that advertising agencies use comedy in their campaign.", "prediction_text": "#Person1# and #Person2# are in Hong Kong and #Person1# thinks there are too many advertisements, while #Person2# thinks advertising is necessary to make companies known. #Person1# dislikes receiving spam and having to listen to advertisements in stores and on the subway, while #Person2# likes the way advertising agencies use comedy in their campaigns."}
{"answers": "Mr. Zhang was turned down again and asks #Person1# to teach some skills. #Person1# thinks American women like a man in good shape and suggests Mr. Zhang try to exercise.", "prediction_text": "#Person1# and #Person2# are talking about #Person2#'s dating life and #Person1# gives #Person2# some advice on how to improve his chances with American women. #Person2# is hesitant to exercise more often."}
{"answers": "#Person1# asks #Person2# what sports women excel at and is better than men. #Person2# thinks men and women could not be compared.", "prediction_text": "#Person1# asks #Person2# about sports that women excel at, but #Person2# points out that women are different from men and can't be compared. #Person1# changes the question to ask about sports that women like best."}
{"answers": "#Person1# wants to know the cities where it's always warm, hot, rains a lot, and cold respectively, and #Person2# tells #Person1# these cities accordingly.", "prediction_text": "#Person1# asks about cities with warm weather, and #Person2# mentions several cities in the southern United States, including San Diego, and also cities in the north, such as Boston and Chicago, which have cold winters but also pleasant spring and fall weather."}
{"answers": "Mr. George decides to give Julia a salary increase according to her performance, and Julia feels satisfied.", "prediction_text": "Mr. George gives Julia a salary increase of 2,000 yuan due to her performance. Julia is satisfied with the raise and thanks Mr. George."}
{"answers": "#Person1# tells #Person2# #Person2# hasn't got the position. #Person2# feels disappointed and #Person1# encourages #Person2# to keep working hard.", "prediction_text": "#Person1# tells #Person2# that they haven't got the position, despite #Person2#'s qualifications and experience. #Person2# is disappointed and worried about how to face the fellows in the office. #Person1# tries to comfort #Person2# by telling them to keep working hard and they will get another chance of promotion someday."}
{"answers": "#Person1# reminds Sara of several items to pack for the trip. Sara decides not to take them except her ID card because she likes to travel light.", "prediction_text": "#Person1# reminds #Person2# to pack important documents, a map, and some medication before their trip. #Person2# tells #Person1# that they have a tour guide and won't get sick, and #Person1# reminds #Person2# to take a raincoat and umbrella but #Person2# says they don't want to carry too many things on their trip."}
{"answers": "#Person2# asks for #Person1#'s help to keep the bags until 6 p.m.", "prediction_text": "#Person1# helps #Person2# by filling out a form and keeping their bags until 6 p.m."}
{"answers": "Ms. Murphy asks Brad to re-do the work badly organized.", "prediction_text": "Ms. Murphy tells Brad that the report is badly organized and cannot be presented to the board. Brad agrees to re-work it and give it back to Ms. Murphy later that day."}
{"answers": "#Person1# says drinking beer helps sing better, but #Person2# heard it helps speaking foreign languages.", "prediction_text": "#Person1# claims that drinking beer improves singing skills, and #Person2# jokes that drinking enough beer can help speak foreign languages."}
{"answers": "#Person1# visits Miss Qi in the hospital and brings some magazines to keep her from getting bored.", "prediction_text": "#Person1# visits Miss Qi and brings her flowers and magazines. Miss Qi is feeling better after her stomachache has gone, and she is scheduled for another endoscope examination to check if her ulcer has healed."}
{"answers": "Amy feels terrible. #Person1# thinks she's not agreeing with the local climate and will get her some medicine.", "prediction_text": "Amy is feeling unwell and coughing, and #Person1# believes she is not agreeing with the local climate and offers to get her some medicine."}
{"answers": "#Person1# helps #Person2# order a well-done steak, mushrooms, baked potatoes, ice tea, and soup.", "prediction_text": "#Person1# takes the order of #Person2# at a restaurant. #Person2# orders steak and mushrooms, well-done, baked potatoes, cream onion soup, and skips dessert."}
{"answers": "#Person1# will take an annual vacation, but #Person2# cannot take it until December.", "prediction_text": "#Person1# is looking forward to their annual vacation later this month, while #Person2# is not taking their vacation until December."}
{"answers": "#Person1# spilled the wine and asks #Person2# for rags.", "prediction_text": "#Person1# spills wine and #Person2# helps them clean it up."}
{"answers": "#Person2# failed to send the instruction book by E-mail. #Person1# helps and teaches #Person2# how to do it.", "prediction_text": "#Person1# helps #Person2# send the instruction book as an attachment by explaining how to do it step by step. #Person2# is not familiar with attaching files and has trouble sending it."}
{"answers": "#Person1# invites Bill to buy office supplies which Susan has listed for #Person1#.", "prediction_text": "Bill and #Person1# need to go downtown to buy a new filing cabinet and some office supplies. #Person1# has a list of the supplies from Susan."}
{"answers": "#Person1# asks #Person2# about health food and concrete examples. #Person2# lists warm and fresh water and yogurt and explains the reason.", "prediction_text": "#Person1# asks #Person2# about health food, and #Person2# gives some examples, including drinking warm and fresh water in the morning, eating yogurt, and reading books about health food."}
{"answers": "#Person1# and #Person2# are touched by the wedding, and #Person1# says #Person1# and Tom are preparing for their engagement.", "prediction_text": "#Person1# and #Person2# are at a wedding. #Person1# is happy for the bride and groom and cries during the ceremony. #Person2# tells #Person1# that Harris and Anne are perfect for each other and that #Person1# and Tom make a great couple. #Person1# and #Person2# talk about their own engagement preparations."}
{"answers": "#Person1# and #Person2# are preparing for the history exam. #Person1# suggests taking a break to listen to some music, but they have different music tastes. Then they get back to books.", "prediction_text": "#Person1# and #Person2# are studying for an exam and decide to take a break to listen to music. #Person1# gets beers while #Person2# picks out a record. #Person2# prefers classical music, but #Person1# doesn't like anything before the 1960s."}
{"answers": "#Person1# wants books on genetic engineering. #Person2# says they're sold out and asks #Person1# to come next week.", "prediction_text": "#Person1# is looking for books on genetic engineering but finds out that the bookstore has sold out. #Person2# tells #Person1# that they will have more books next week."}
{"answers": "#Person1# asks #Person2# to remind her when they get to the stop.", "prediction_text": "#Person1# asks #Person2# if the bus goes to the National Library of China and #Person2# replies that there are three stops left and the ride will take about a quarter of an hour. #Person1# thanks #Person2# and asks for a reminder when they reach the stop."}
{"answers": "#Person1# lost the job but is grateful for Mrs. Brandon from who #Person1# learned a lot.", "prediction_text": "#Person1# lost their job, and #Person2# is sorry to hear that. #Person1# asks about #Person2#'s students, who are nervous about their final test. #Person1# remembers a difficult final test given by #Person2# in college."}
{"answers": "John Keat comes to Dr. Anderson's office and describes his earache to #Person1#. John Keat wants to see the doctor, but #Person1# says the doctor will be busy until this evening. #Person1# tells John to come tomorrow at ten with his passport.", "prediction_text": "John Keat calls Dr. Anderson's office to inquire about his ear pain. The receptionist informs him that Dr. Anderson is busy at the hospital until evening and the earliest appointment available is the next morning at 10 am. The receptionist also advises John to take it easy and not press his ear or take a shower with water. John asks if he needs to bring anything and the receptionist suggests bringing his passport for hospital registration."}
{"answers": "#Person1# and #Person2# plans to hike. #Person2# wants to race #Person1#, but #Person1# thinks they should enjoy the sights along the way.", "prediction_text": "#Person1# and #Person2# are going on a hike. #Person2# is feeling energetic after a yoga class and wants to race #Person1# up the hills, but #Person1# wants to take their time to enjoy the sights and points out that snakes might be present."}
{"answers": "Gary thanks Anne for introducing him to Caroline, and Anne suggests a second date.", "prediction_text": "Gary thanks Anne for introducing him to Caroline and expresses his excitement about being in love. Anne advises Gary to ask Caroline out on a second date before proposing marriage."}
{"answers": "#Person1# suggests a three-bedroom flat. #Person2# wants fewer rooms.", "prediction_text": "#Person1# asks if #Person2# needs a room and #Person2# replies that they might need a three-bedroom flat."}
{"answers": "John Cruise calls Eve to register a complaint about his video recorder and explains what the problem is. Eve promises to urge their legal consultant to call him back.", "prediction_text": "Eve Wheeler answers the phone in the Administrative Department of Sound and Vision. John Cruise complains about his video recorder and demands compensation for lost tapes. Eve Wheeler notes the complaint and tells John Cruise that Mrs. Schroder will call him back later that afternoon."}
{"answers": "#Person1# asks #Person2# the way to the Rainbow Restaurant.", "prediction_text": "#Person1# asks #Person2# for directions to the Rainbow Restaurant. #Person2# gives clear instructions on how to get there."}
{"answers": "#Person1# asks Adam to show #Person1# around the school. #Person1# envies Adam and hopes to enter Adam's school one day.", "prediction_text": "Adam shows #Person1# around the school, pointing out the tallest building (the library), the new classroom building for the senior high school, and the swimming pool. #Person1# expresses envy and hopes to enter the school one day."}
{"answers": "#Person1# lost the way and asks #Person2# for the way to the central department stall and the national bank.", "prediction_text": "#Person1# is lost and asks #Person2# for directions to the central department store and the national bank. #Person2# gives clear directions and #Person1# thanks him."}
{"answers": "#Person1# and #Person2# talk about people taking advantage of other's hospitality and generosity. #Person1# shares #Person1#'s own experience.", "prediction_text": "#Person1# and #Person2# discuss people who manipulate others for their own gain. #Person1# shares an experience where someone lied to them about needing money."}
{"answers": "#Person1# wants a pan, and #Person2# recommends a big aluminum pan with a heat-resistant handle and a lid. #Person1# pays for it by cash.", "prediction_text": "#Person1# is looking for a large pan for their kitchen, and #Person2# suggests a pan that is the right size. #Person1# is hesitant at first but tries the pan and finds it to be lightweight and comfortable to handle. #Person2# offers the pan for sale and #Person1# decides to buy it."}
{"answers": "#Person2# and Gary misunderstood each other, so they missed the beginning of the movie. Gary apologizes, but #Person2# doesn't want to see the movie anymore.", "prediction_text": "Gary and Rebecca had plans to meet at the theater at 5:07 PM for a movie, but they got mixed up and missed the beginning of the movie."}
{"answers": "Daniel applies for the position of manager, and #Person2# interviews Daniel. Daniel talks about how he learned about the company and why he'd like to work here.", "prediction_text": "Daniel is applying for the position of manager and tells #Person2# that he learned about the company through famous brands and that he is interested in the job because it is the one he has been dreaming of. #Person2# tells Daniel that they will let him know about the job within the week."}
{"answers": "Lucy's dog Rusty was lost. Glenn asks Lucy about Rusty's identification and helps Lucy find Rusty when they walk around the neighborhood.", "prediction_text": "Lucy's dog Rusty is lost after someone left the backyard gate open. Lucy and #Person1# search the neighborhood and find Rusty."}
{"answers": "#Person1# asks Patrick to help make a call. Patrick tells #Person1# how to use the phone and get through successfully.", "prediction_text": "#Person1# can't get their phone to work and asks #Person2# for help. #Person2# shows them how to use the phone and #Person1# successfully makes a call to their partner."}
{"answers": "#Person1# helps Basil book three seats of economy class to Calgary, Canada on a flight next Sunday.", "prediction_text": "Basil wants to book a flight to Calgary, Canada, and Mr. #Person1# tells him about the available flight and the price."}
{"answers": "Hong thinks learning English is boring and wants something more exciting. So Miss Winters asks Hong to do a special project for the class to get more interested in the lessons.", "prediction_text": "#Person1# suggests a special project for #Person2# to watch YouTube videos of people speaking in different English accents and write an essay about it. #Person2# is excited about the project and thanks #Person1#."}
{"answers": "#Person1# asks Tom about his weekend plan and whether he needs a company. Tom is going for a hike and invites #Person1# and #Person1#'s friend to go together.", "prediction_text": "Tom wants to go hiking in the southern Rocky Mountains and invites #Person1# to join him. #Person1# has been hiking before and saw a bear and a mountain lion on his last hike."}
{"answers": "#Person2# wants to play the game friendly, but #Person1# wants to make a bet.", "prediction_text": "#Person1# suggests making the game more interesting by adding a bet, and #Person2# is hesitant but agrees to play a friendly game."}
{"answers": "#Person2# likes his spicy diced chicken with peanuts very much. #Person1# tells him how to cook it.", "prediction_text": "#Person1# serves #Person2# a spicy diced chicken with peanuts. #Person1# explains the traditional dish and how it is prepared."}
{"answers": "#Person1# helps #Person2# to dissolve the hair that clogs the drain and tells #Person2# to clean the bathtub.", "prediction_text": "#Person1# and #Person2# are trying to fix a clogged drain. #Person1# finds that the drain is clogged with hair and gives #Person2# some acid to help dissolve it."}
{"answers": "#Person1# requests for earlier shipment to catch the season. #Person2# finally agrees to manage it on 20th September.", "prediction_text": "#Person1# wants to know if the shipment can be made in September, but #Person2# says it can't be done. #Person1# asks when the shipment can be expected, and #Person2# says by the middle of October. #Person1# is disappointed and suggests putting down in the contract that the shipment will be on September 15 or earlier. #Person2# agrees to make it happen on September 20th."}
{"answers": "Mr. Black complains about the children's noise to Mr. Brown.", "prediction_text": "#Person1# is having trouble concentrating on his paper because of the noise made by #Person2#'s children. #Person2# apologizes and tries to make them quiet."}
{"answers": "#Person1# and #Person2# talk about Li Na who is pressed by her mother for marriage.", "prediction_text": "#Person1# and #Person2# are talking about Li Na's mother who is pressuring Li Na to get married. #Person1# suggests that Li Na should stand up to her mother."}
{"answers": "#Person2# enjoyed the dinner and asks #Person1# for ice cream as dessert.", "prediction_text": "#Person1# and #Person2# are having dinner together. #Person2# enjoys the food but is full, so #Person1# offers dessert."}
{"answers": "#Person2# wants to try windsurfing but is stopped by #Person1# who thinks it's hard and will cost a lot.", "prediction_text": "#Person1# and #Person2# are having breakfast. #Person2# wants to rent a windsurfer, but #Person1# advises against it, telling #Person2# that windsurfing is hard and expensive."}
{"answers": "#Person1# tells Ben that a young man should actively prepare for himself and his situation after relaxation.", "prediction_text": "#Person1# asks #Person2# why he is drifting in the pool and #Person2# replies that it is comfortable. #Person1# questions the purpose of college and suggests that #Person2# should take stock of himself and start thinking about getting off his ass after a few weeks of relaxation."}
{"answers": "#Person1# and #Person2# talk about the wicked stepmother.", "prediction_text": "#Person1# talks about a stepmother who is wicked and can't stand a happy smile on the boy's face. #Person2# shows empathy towards the boy."}
{"answers": "#Person1# and #Person2# talk about water pollution and its direct and indirect sources. They agree they should be an active participator in the prevention of it.", "prediction_text": "#Person1# and #Person2# discuss water pollution and its sources. #Person1# is concerned about the issue and suggests that everyone should participate in preventing it."}
{"answers": "#Person1# and #Person2# get off the bus too early due to #Person2#'s mistake.", "prediction_text": "#Person1# and #Person2# are unsure of their stop and get off the bus too early. They have to walk to their destination."}
{"answers": "#Person1# tells #Person2# about the deposit amount and how to withdraw it.", "prediction_text": "#Person1# and #Person2# are discussing initial deposits for Agreement Savings. #Person1# tells #Person2# that $100,000 RMB is required for an initial deposit. #Person2# asks how to withdraw money and #Person1# explains that a Type A Account can be used for withdrawals like a Settlement Account, but a Type B Account cannot be directly involved in transactions or used for external withdrawal services."}
{"answers": "#Person2# asks #Person1# to take the shuttle to catch the plane and will call the attendants at the gate.", "prediction_text": "#Person1# is trying to catch connecting flight 101 to New York City and #Person2# tells #Person1# the gate number and offers to call the attendants at the gate to let them know #Person1# is on the way."}
{"answers": "#Person1# and #Person2# think their pants are comfortable to lounge around.", "prediction_text": "#Person1# and #Person2# are talking about pants. #Person1# says their pants are baggy and #Person2# agrees. They decide to go out and lounge around."}
{"answers": "#Person1# wants to share the seat. #Person2# agrees.", "prediction_text": "#Person1# asks #Person2# if they can share the same table. #Person2# agrees."}
{"answers": "Monica calls the reception to make a room reservation for next Saturday and Sunday nights and #Person1# helps her.", "prediction_text": "#Person1# is working at Wangfujing Grand Hotels and #Person2# asks about room availability for next Saturday and Sunday night. #Person1# checks the availability and reserves a double room for Monica Cellar."}
{"answers": "#Person1# offers a discount but #Person2# is not satisfied. After negotiation, they agree on a 10% discount.", "prediction_text": "#Person1# offers a 5% discount, but #Person2# thinks it's too high and asks for a larger discount. #Person1# offers 8%, but #Person2# wants a larger reduction. #Person1# settles for a 10% discount."}
{"answers": "#Person2# is upset because #Person2# doesn't know the registering process of a trademark. #Person1# gives #Person2# some tips and suggests #Person2# learn more about this issue.", "prediction_text": "#Person1# explains the trademark registration process to #Person2#, including the time limit, similarity to existing trademarks, and the need for relevant documents and fees."}
{"answers": "#Person2# bought an MP3 online but #Person1# never tried e-shopping. #Person2# tells #Person1# about the advantages of shopping online. #Person1# thinks #Person1# should have a try.", "prediction_text": "#Person1# is curious about #Person2#'s MP3 player and asks about it. #Person2# explains the advantages of online shopping, including convenience, accessibility, and variety of choices. #Person1# is interested and wants to try it."}
{"answers": "Mr. Wang sees #Person1# off at the airport and they share good expectations of their business relationship.", "prediction_text": "#Person1# and #Person2# are at the airport and #Person1# thanks #Person2# for his hospitality and the productive trip. #Person2# expresses hope for future business opportunities and #Person1# thanks him and says goodbye."}
{"answers": "#Person2# helps #Person1# open a savings account and explains the differences between current and fixed accounts.", "prediction_text": "#Person1# wants to open a savings account and asks for advice. #Person2# explains the difference between fixed and current accounts and #Person1# decides to open a current account. #Person2# helps #Person1# complete the process and gives #Person1# the deposit book and ID card."}
{"answers": "#Person1# proposes to build maintenance procedures to reduce lost production during downtime.", "prediction_text": "#Person1# and #Person2# agree that unplanned shutdowns have been too frequent and they need to make changes to reduce downtime. #Person1# suggests establishing routine maintenance procedures to prevent problems before they break down."}
{"answers": "#Person1# asks #Person2#, who parked in #Person1#'s spot, to move the car away.", "prediction_text": "#Person1# tells #Person2# that they have parked in the wrong spot, #Person2# apologizes and moves their car."}
{"answers": "A car accident happens because #Person1# pulls out in front of #Person2#'s car. #Person1#'s arm and the cell phone were broken, so #Person2# needs to find a phone to call the police to decide whose fault this accident was.", "prediction_text": "#Person1# and #Person2# get into an argument after a car accident. #Person2# tells #Person1# to wait for the police to arrive, but #Person1# wants to go to the hospital immediately. #Person2# goes to find a phone to call the police, while #Person1# remains injured in the car."}
{"answers": "#Person1# and #Person2# talk about the advantages and disadvantages of the dog park.", "prediction_text": "#Person1# and #Person2# are discussing a dog park in their city. #Person1# thinks it's a good idea, while #Person2# suggests allowing people to walk their dogs in the streets instead."}
{"answers": "Laura and Kevin meet each other after a long time and share their personal latest news. Laura got a job promotion and an increase in wage while Kevin got married to Andrea after dating for 2 weeks.", "prediction_text": "Laura and Kevin talk about their lives. Laura got a promotion in her job and Kevin is getting married."}
{"answers": "Being sick, #Person2# didn't take the exam but can do a make-up. #Person2# finished a talk with the professor and is on the home.", "prediction_text": "#Person1# asks #Person2# where they were yesterday and #Person2# replies that they were sick and couldn't go to an exam. #Person1# is relieved that #Person2# will be able to take a make-up exam. #Person2# needs to stop at a drugstore on the way home and #Person1# suggests that they walk, but #Person2# says they will walk."}
{"answers": "#Person1# invites #Person2# to the beach at the weekend as Sara, whose mom is ill, cannot go. They list some food, drink, and other belongings to take.", "prediction_text": "Tom suggests going to the beach with Mr. Parsons, but Mr. Parsons doubts Sara will come, and Tom and Mr. Parsons plan to pack food and drinks for a picnic."}
{"answers": "#Person1# reports a theft to a policeman. #Person1# describes the appearance of the thief and the policeman recognizes the bearded woman who often removes the victim's left shoe and returns after days.", "prediction_text": "#Person1# reports a theft to #Person2#, who asks for details. #Person1# describes the thief, who is described as a \"bearded woman\" by #Person2#, who explains that the thief is a man who dresses up as a woman and steals shoes. #Person1# jokes that he will take off his left shoe every time he walks through the park."}
{"answers": "#Person1# doesn't understand why Jim grumbles. #Person2# thinks he's not as energetic as #Person1# is and the boss is not understanding with him. #Person1# wonders why.", "prediction_text": "#Person1# and #Person2# are talking about Jim's workload and how he is not as energetic as #Person1#. #Person2# thinks the boss is more understanding with #Person2# than with Jim."}
{"answers": "#Person1# wants to find a job at the computer center.", "prediction_text": "#Person1# wants to find a job and #Person2# gives #Person1# a list of part-time jobs available and #Person1# decides to apply for the job at the computer center."}
{"answers": "The boss asks the unhappy #Person2# to beef up in the job.", "prediction_text": "#Person1# asks #Person2# about what the boss said, #Person2# replies that the boss asked him to work more, #Person1# notices that #Person2# looks unhappy and asks if everything is okay, #Person2# replies that everything is fine."}
{"answers": "#Person1# and #Person2# share their feelings about the graduation day and different plans after graduation. #Person1# encourages #Person2# to have more power on a decision.", "prediction_text": "#Person1# and #Person2# talk about their college days. #Person1# graduated with pride, but #Person2# felt a sense of loss after graduation. #Person1# encourages #Person2# to make a decision and not be afraid to pursue it."}
{"answers": "#Person1# owes the landlord $200 and wants to borrow some from Jayden who is tight on cash as well. Jayden only lends #Person1# $20 and #Person1# will come over at 6 pm.", "prediction_text": "Jayden can only lend $20 to #Person1# because he has car problems and is short on cash. #Person1# thanks him and invites him over for dinner."}
{"answers": "#Person1# and #Person2# talk about their birthday.", "prediction_text": "#Person1# is older than #Person2#, who will turn ten on May 1st. #Person1# will turn ten on April 14th and may have a birthday party."}
{"answers": "#Person1# and #Person2# see a blond, but #Person2# is nervous talking with girls. After #Person1#'s encouragement and another cup of beer, #Person2# decides to talk to the blond.", "prediction_text": "#Person1# and #Person2# are at a party and #Person1# encourages #Person2# to talk to a girl named Janice, but #Person2# is nervous and hesitant."}
{"answers": "#Person1# asks Mary the result of the bid not very nicely so Mary doesn't want to answer.", "prediction_text": "#Person1# asks #Person2# if they won the bid for the copper Buddha, but #Person2# is uncooperative and insults #Person1#."}
{"answers": "Gav had a good sleep last night but worries about the traffic jam and classes to teach. #Person2# asks Gav's plan for the weekend and #Person2# gives the suggestions when Gav feels upset.", "prediction_text": "Gavin and #Person2# have a conversation about Gavin's sleep and traffic jams. Gavin is worried about getting stuck in rush-hour traffic on his way to school, and #Person2# suggests that he breathe deeply to relax. Gavin also mentions that a school called him to ask if he could teach some classes that weekend, and #Person2# advises him not to take on more than he can handle."}
{"answers": "Both #Person1# and #Person2# think the teacher should say to Myra privately if she saw Myra cheating.", "prediction_text": "#Person1# and #Person2# disagree with the teacher's actions of accusing Myra in front of the whole class. #Person1# thinks the teacher should have done it privately, while #Person2# thinks the teacher had the right to say anything she wants."}
{"answers": "#Person1# asks Karen for help to find a mature babysitter for two or three days because #Person1# will fly to L.A. to see #Person1#'s mother-in-law in the hospital and has to leave Suzy, who has a cold, at home. Karen recommends Sara who is mature and responsible, and she will contact her.", "prediction_text": "#Person1# needs someone to stay with their sick daughter Suzy while they go to L.A. to visit their mother-in-law. #Person2# suggests Sara Ralston, a responsible 17-year-old she knows from church, and offers to call her to see if she's available."}
{"answers": "Sabrina is worried about her sister because she hasn't heard from her sister for 2 weeks. #Person1# comforts her.", "prediction_text": "Sabrina is worried about not hearing from her sister for two weeks, but Jason tells her that the mail can be slow and that no news is good news."}
{"answers": "#Person1# asks #Person2# for some strong medicine to get through the important days. #Person2# agrees but still suggests #Person1# see a doctor.", "prediction_text": "#Person1# is feeling unwell and needs medicine to get through the next few days. #Person2# suggests taking cold medicine and getting rest, and warns #Person1# to see a doctor if they are still sick in three days."}
{"answers": "#Person2# will give #Person1# a lift to meet a friend.", "prediction_text": "#Person1# has an appointment in the City of Westminster and #Person2# offers to give #Person1# a lift. #Person1# is meeting a friend near the park and #Person2# agrees to take #Person1# to an underground station."}
{"answers": "#Person2# tells #Person1# about a funny experience about language confusion during the summer trip in India.", "prediction_text": "#Person1# asks #Person2# about their summer trip, and #Person2# tells the story of a time when they accidentally said the wrong word in a remote place in India and how it led to a funny misunderstanding."}
{"answers": "#Person2# has an interview schedule on Wednesday and will tell #Person1# about it over coffee.", "prediction_text": "#Person1# asks #Person2# if they have been asked for an interview, and #Person2# replies that they have an interview scheduled with the manager on Wednesday. #Person1# expresses their excitement and asks for more details."}
{"answers": "#Person1# wants to start a marathon and #Person2# gives #Person1# some suggestions.", "prediction_text": "#Person1# wants to start running and asks #Person2# for advice. #Person2# suggests starting with a mile without stopping and then increasing the distance, and suggests running either on a running machine at the gym or outside."}
{"answers": "#Person1# wants to research Christian and Izek helps to arrange a meeting with the priest.", "prediction_text": "#Person1# is doing an essay on the influence of Christian religion on western cultures and asks #Person2# if there is a Christian church nearby. #Person2# tells #Person1# that there is one nearby and offers to arrange a meeting between #Person1# and one of the priests."}
{"answers": "#Person2# tells #Person1# some matters needing attention when #Person1# lives in the room.", "prediction_text": "#Person1# is viewing a room and #Person2# tells him the rules of the room, including no smoking in the bedrooms, no sticking pictures on the walls, and closing the window when going out. #Person1# agrees to follow the rules."}
{"answers": "#Person2# didn't see the sign and parks at a no-parking area, so #Person1# gives #Person2# a ticket.", "prediction_text": "#Person1# gives #Person2# a ticket for parking in a no-parking zone. #Person2# argues that they didn't see the sign, but #Person1# explains that there is a sign at the corner of the street and #Person2# has to pay the fine."}
{"answers": "#Person1# tries on a little tight China-gown at a store and buys one with #Person2#'s assistance.", "prediction_text": "#Person1# wants to try on a China-gown of Tang-Dynasty style, and #Person2# shows it to her and allows her to try it on. #Person1# finds it a little tight around the waist and asks if they have a bigger one, but #Person2# tells her they don't have this color in her size."}
{"answers": "#Person2# tells #Person1# the place to use the internet.", "prediction_text": "#Person1# asks about using the internet in their room, and #Person2# tells them that the hotel doesn't offer internet access in the rooms, but they can go to the web bar on the 12th floor."}
{"answers": "#Person1# buys a camera with #Person2#'s assistance.", "prediction_text": "#Person1# wants to buy a digital camera and asks #Person2# for a look. #Person2# shows #Person1# the camera and tells #Person1# the price. #Person1# decides to buy it."}
{"answers": "#Person1# and #Person2# are discussing what to eat at a popular restaurant, and they decide to order until the waitress comes around.", "prediction_text": "#Person1# and #Person2# are lucky to get a table at a restaurant and decide to share an appetizer, #Person2# chooses a dahl for their main course and #Person1# wants to share kebabs. #Person2# suggests waiting for the waitress to take their order."}
{"answers": "#Person1# advises #Person2# to make another date when #Person2# was stood up by Linda.", "prediction_text": "#Person1# asks #Person2# if he went on a date last night, and #Person2# says he did but Linda stood him up. #Person1# tells #Person2# to make another date again and not jump to conclusions."}
{"answers": "#Person1# complains to #Person2# about the chicken #Person1# ordered and wants to change for steak.", "prediction_text": "#Person1# doesn't like the taste of the chicken and #Person2# offers to bring another order or order something else from the menu. #Person1# wants the steak cooked half done."}
{"answers": "#Person1# invites #Person2# to the bar. #Person2# concerns about the available seat, cover charge, and dressing code. #Person1# explains to #Person2#.", "prediction_text": "#Person1# invites #Person2# to join him and Billy at a new bar on 17th Street. #Person2# agrees and asks about the bar's dress code and cover charge. #Person1# tells #Person2# that the bar is casual and there is no cover charge."}
{"answers": "#Person2# asks #Person1# about the information on renting a car and then rents one.", "prediction_text": "#Person1# works at ABC Rent-a-Car and answers #Person2#'s questions about renting a car. #Person2# decides to rent a Pinto for one week."}
{"answers": "#Person1# and #Person2# discuss Roman's lifestyle and good food.", "prediction_text": "#Person1# says \"When in Rome, do as the Romans do\" and #Person2# replies that the Romans do what everyone else does. #Person1# also mentions that Roman likes good food."}
{"answers": "Jenny did a poor job in the test and is thinking of dropping out. Sally comforts Jenny.", "prediction_text": "Jenny is upset about failing a test and is considering dropping out of school. Sally tries to convince her not to drop out."}
{"answers": "#Person2# believes people now are healthier, wealthier and wiser while #Person1# doesn't.", "prediction_text": "#Person1# and #Person2# have different opinions on whether the world is improving. #Person1# believes the world is not improving while #Person2# thinks science, industry, and medicine are making the world better."}
{"answers": "#Person1# forgot about class registration, so #Person1# asks Mike for advice. Mike recommends #Person1# to get into psychology, sociology and philosophy. #Person1# thanks Mike for his help.", "prediction_text": "Mike forgot to register for classes and is worried about getting into any classes. Mike asks John for advice and John suggests that Mike go to the registration building and try to get into classes that are still open. John also tells Mike about a professor who will sign a card to let Mike into a full class."}
{"answers": "Mary had an argument with Ann who didn't meet her as planned due to Ann's boyfriend. #Person1# comforts Mary. Mary decides to call Ann to patch things up.", "prediction_text": "Mary and John have an argument with Ann, and Mary is upset. John tells Mary that she should be more understanding and not let a trivial thing stand in the way of their friendship."}
{"answers": "Neal and #Person1# haven't see each other for a while. Neal went to study aboard and #Person1# has been studying for exams. Neal is going to study and #Person1# is going to meet #Person1#'s supervisor.", "prediction_text": "#Person1# and #Person2# are having a casual conversation. #Person1# is studying for exams, while #Person2# has studied natural language processing in Singapore. #Person1# can't attend a seminar with #Person2# due to a meeting with their supervisor."}
{"answers": "#Person1# has to ask for leave because #Person1#'s wife is in the hospital. Susan will convey #Person1#'s leave to the manager.", "prediction_text": "#Person1# needs to take the day off to go to the hospital because his wife is going to deliver a baby, but the manager is meeting a client and #Person2# offers to convey #Person1#'s leave to the manager after the client leaves."}
{"answers": "#Person1# wants to buy a pan and chooses a satisfying one with #Person2#'s assistance.", "prediction_text": "#Person1# is looking for a big pan for their kitchen, but wants a lightweight handle. #Person2# shows #Person1# a pan with a heat-resistant handle and a lid that comes with it. #Person1# is pleased and decides to buy the pan."}
{"answers": "Monica calls ABC company to check the availability of the accountant position. Lucy introduces the requirements of the job and Monica will send the resume.", "prediction_text": "Monica calls ABC Company to inquire about the accountant position. Lucy tells Monica that the position is still available and emphasizes the importance of English for the job, as well as the possibility of traveling abroad. Monica agrees to send her resume."}
{"answers": "#Person2# calls #Person1# to send an ambulance because #Person2#'s husband is having a heart attack.", "prediction_text": "#Person1# answers a 911 call from #Person2# who lives on Oak lane in Smithfield. #Person2# tells #Person1# that her husband is having a heart attack and #Person1# tells #Person2# that an ambulance is on its way."}
{"answers": "#Person2# buys a one-way hard-seat train ticket to New York City with #Person1#'s assistance.", "prediction_text": "#Person1# helps #Person2# find the train schedule to New York City. #Person1# tells #Person2# the train leaves at 10:30 am every day except Sunday. #Person2# asks about earlier trains and #Person1# replies there are no trains before 10:30 am. #Person2# buys a hard seat ticket for $6."}
{"answers": "Both #Person1# and #Person2# voted for Obama and believe he will change America.", "prediction_text": "#Person1# and #Person2# are happy about Obama being the new President and express their confidence in him."}
{"answers": "#Person1# consults #Person2# about unemployment benefits. #Person2# suggests calling the unemployment office to check the formula.", "prediction_text": "#Person1# asks about qualifying for unemployment benefits and #Person2# explains the requirements and how to check if the job provides unemployment. #Person1# is unsure if their job pays unemployment and #Person2# suggests calling the unemployment office for more information."}
{"answers": "#Person1# talks about Oprah Winfrey with #Person2# and they think highly of Oprah Winfrey because she is concerned with charity rather than status symbols.", "prediction_text": "#Person1# and #Person2# are discussing Oprah Winfrey's charity work and how she is using her wealth to help underprivileged girls in Africa. #Person1# and #Person2# agree that it is commendable and that more celebrities should follow her example."}
{"answers": "#Person2# finishes the job interview and gives #Person1# #Person2#'s telephone number to be informed of the result.", "prediction_text": "#Person1# thanks #Person2# for their interest in the job and tells them they will be hearing from them by next Wednesday. #Person2# gives their telephone number and #Person1# thanks them again."}
{"answers": "#Person2# calls #Person1# to make an appointment for a checkup.", "prediction_text": "David Johnson calls the dental clinic to make an appointment for a checkup. Mr. Adams asks David which tooth is giving him pain and schedules the appointment for that afternoon."}
{"answers": "#Person1# and #Person2# talk about #Person2#'s new house which has a long history, a big yard and many rooms. #Person2# invites #Person1# to come and stay when the guest bedroom is ready. #Person1# is willing to go.", "prediction_text": "#Person1# asks Michael about his new house and Michael tells him about the house's age, location, size, and features. #Person1# expresses interest in visiting Michael's house and staying for a weekend."}
{"answers": "#Person2# helps #Person1# send an important letter to Hainan by registered email. #Person2# asks #Person1# to keep the receipt until the letter is delivered.", "prediction_text": "#Person1# wants to send a letter to Hainan by registered mail, but #Person2# tells him that the letter is overweight and he needs to pay extra. #Person1# pays the extra fee and receives the stamps and receipt."}
{"answers": "#Person1# thinks the earliest shipment #Person2# can provide is too late and requests #Person2# to advance the time of delivery. #Person2# refuses due to several specific reasons, but finally #Person2# is persuaded to contact the producers to see if they can help.", "prediction_text": "#Person1# wants to know if the shipment can be made during September, but #Person2# says it can't be done. #Person1# explains the importance of timely delivery and asks #Person2# to make a special effort to get an earlier delivery. #Person2# agrees to get in touch with the producers."}
{"answers": "#Person1# and #Person2# come to see a house and they both have good impressions of this house.", "prediction_text": "#Person1# and #Person2# are looking at a house and both like the outside and inside. #Person1# likes the privacy blinds, wine storage, and colors in the bedroom and bathroom."}
{"answers": "#Person1# asks #Person2# the favorite music genres and why #Person2# loves them.", "prediction_text": "#Person1# asks #Person2# about their favorite music genre, and #Person2# replies that they enjoy both Rock and R&B because of the different instruments used."}
{"answers": "#Person2# claims that her necklace has been stolen. #Person1# asks #Person2# about some details. #Person1# suggests sending one housemaid to look for it again thoroughly. If she doesn't find it, they will turn the case to the police, but the hotel will not be responsible for #Person2#'s loss. #Person2# is not satisfied with the answer and wants to speak to the general manager. #Person1# refuses.", "prediction_text": "#Person1# is an employee at a hotel. #Person2# has had their gold necklace stolen and comes to #Person1# to report it. #Person1# asks #Person2# questions to help find the necklace and suggests that #Person2# check their room and lock their door before leaving. #Person2# is upset and wants to speak to the general manager but #Person1# explains that the general manager is not in town and offers to get the assistant manager instead."}
{"answers": "#Person2# is having a bad toothache. #Person1# checks #Person2#'s teeth and suggests pulling out the wisdom tooth.", "prediction_text": "#Person1# examines #Person2#'s tooth and tells #Person2# that it needs to be pulled. #Person2# asks about the extraction process and #Person1# explains that they will take x-rays and see what they are dealing with before administering anesthesia."}
{"answers": "#Person1# interviews Peter Wilson who is the action organizer of Green Peace organization. #Person1# asks Peter to introduce to the audience what Green Peace is and what work it does. Peter also introduces detailed anti-nuclear campaigns.", "prediction_text": "Peter works for Green Peace and explains the organization's mission and his role in it. Peter talks about recent anti-nuclear campaigns, including harassing ships that dump radioactive waste in the Atlantic Ocean."}
{"answers": "#Person1#'s hand hurts and #Person2# advises #Person1# to rest.", "prediction_text": "#Person1# fell on the ice and is worried about breaking something, but #Person2# thinks it might just be a matter of resting it."}
{"answers": "#Person2# rents a compact car with full coverage insurance. #Person1# asks #Person2# to show the driver's license.", "prediction_text": "#Person1# helps #Person2# rent a compact car for one day. #Person1# asks about the type of car, the rate, and insurance. #Person2# chooses a model and fills out the form."}
{"answers": "#Person2# tells #Person1# #Person2# thinks it surprising that English is more serious and less polite than expected.", "prediction_text": "Maria talks to #Person1# about how British people are different from Spanish people. Maria thinks that British people are more serious than Spanish people and that politeness goes beyond just saying please and thank you."}
{"answers": "Mary calls her dad to check if everything is fine at home. Mary also tells her dad how her family members have been doing.", "prediction_text": "Mary calls her father to catch up and ask about his and her mother's well-being. Mary also asks about her siblings and their activities."}
{"answers": "#Person1# and #Person2# are talking about what they would do and what they wouldn't do if caught in a thunderstorm.", "prediction_text": "#Person1# and #Person2# are talking about a building that was struck by lightning four times. #Person1# shares safety tips and #Person2# agrees and adds that they take precautions even at home."}
{"answers": "Lucy is going to apply for the engineering college. She asks #Person1# to see her study record and write a recommendation for her. #Person1# agrees.", "prediction_text": "Lucy wants to apply to the engineering college next year and asks Mr. Parsons for a recommendation. Mr. Parsons agrees and Lucy thanks him."}
{"answers": "#Person1# sits next to #Person2# on the plane. They talk about why they are going to London.", "prediction_text": "Ali and another person are sitting next to each other on a flight to London. They introduce themselves and talk about their destinations and studies."}
{"answers": "#Person1# talks with #Person2# about what the situation will be when they arrive in England.", "prediction_text": "#Person1# and #Person2# are discussing the weather and whether they will need their overcoats. #Person1# thinks it will be wet and the bank will be closed, while #Person2# thinks it will be hot."}
{"answers": "#Person1# is an art aficionado. #Person2# doesn't appreciate paintings but enjoys sculptures. They decide to go to an exhibition of greek and roman sculpture together.", "prediction_text": "#Person1# loves going to art galleries, particularly when they are holding an exhibition of abstract art. #Person2# is not an art aficionado and finds it hard to understand the meaning of some paintings. #Person1# is going to an exhibition of Greek and Roman sculpture the next day and invites #Person2# to join them."}
{"answers": "Both #Person1# and #Person2# hate the evening rush.", "prediction_text": "#Person1# and #Person2# agree that it is nice to go home earlier sometimes, but they also mention the difficulty of commuting a long way and hating to get caught in a traffic jam."}
{"answers": "#Person2# helps #Person1# find the ingredients to make Chinese food.", "prediction_text": "#Person1# asks #Person2# for help buying ingredients for Chinese food. #Person2# suggests a supermarket in Kensington High Street and offers to help #Person1# find what they need. #Person1# gives #Person2# a trolley and they start shopping for ingredients such as Chinese cabbage and pork."}
{"answers": "#Person1# agrees with #Person2#'s suggestion to pay extra 5 euros to take a less heavy route to catch the train.", "prediction_text": "#Person1# wants to catch the 11:30 train and asks #Person2# for directions. #Person2# explains the different routes and their times, and #Person1# chooses the less heavy route which will take 5 Euro more."}
{"answers": "Mike talks with #Person1# about his enjoyable trip to China.", "prediction_text": "#Person1# and #Person2# are talking about #Person2#'s trip to China. #Person2# visited Beijing, Shanghai, Nanjing, and Guilin and enjoyed the beauty of each city."}
{"answers": "#Person1# asks #Person2# to tell the audience about the world soccer event.", "prediction_text": "Jerry Ryan talks about the latest news in sports."}
{"answers": "#Person1# interviews #Person2# about travel arrangements, hotels, and attitude towards the holiday.", "prediction_text": "#Person1# is conducting a survey for Sunny Tour Holidays and asks Rebecca about her last holiday. Rebecca tells #Person1# about her flight delay, car rental, and hotel. Rebecca also states that holidays are important to her and she always makes sure to go on one at least once a year."}
{"answers": "Bill tells #Person1# his legs were removed because of a terrible accident in the mountain, but he built a new leg and now he can go mountain climbing again.", "prediction_text": "Bill lost his legs due to being lost on a mountain for 4 days in bad weather, but he wants to stay active and has built new legs with modern technology to help him climb mountains again."}
{"answers": "#Person2# goes to a Chinese restaurant. Due to #Person2#'s taste, Zhejiang food is recommended by #Person1#.", "prediction_text": "#Person1# seats #Person2# at a table near the window and explains the menu options, including Hunan and Sichuan food which are spicy, and Zhejiang food which is sweet."}
{"answers": "Miss Yang wants to put in for a transfer and explains her reasons. Mr. Sun agrees.", "prediction_text": "#Person1# wants to leave the department and asks Mr. Sun if he can put in a transfer. Mr. Sun agrees and #Person1# tells him why she wants to leave."}
{"answers": "Michelle helps John Sandals book a non-smoking room with a queen-size bed from April 14th to April 17th.", "prediction_text": "John Sandals wants to reserve a hotel room in New York from April 14 to April 17. Michelle asks for his full name, dates of stay, and other preferences. John agrees to the price and provides his phone number for confirmation."}
{"answers": "#Person1# explains the exact difference between Dividend Deposit and Dividend Participated Deposit and the purchase process to #Person2#.", "prediction_text": "#Person1# explains the difference between Dividend Deposit and Dividend Participated Deposit to #Person2#. #Person2# wants to purchase it."}
{"answers": "#Person1# wants to rent an apartment from #Person2#. They negotiate over the rent but can't reach an agreement.", "prediction_text": "#Person1# wants to rent an apartment from #Person2#, but #Person2# is unwilling to lower the price. #Person1# offers $850, but #Person2# refuses."}
{"answers": "#Person2#'s trip to Hong Kong is canceled because of the SARS epidemic. #Person2# decides to exercise instead. #Person1# thinks it's a good idea.", "prediction_text": "#Person1# and #Person2# are discussing the cancellation of a trip to Hong Kong due to SARS. #Person1# suggests that #Person2# take a break and start a fitness plan."}
{"answers": "#Person1# and #Person2# have been waiting for the bus for a long time. They agree they need to get a car.", "prediction_text": "#Person1# is waiting for a bus that is late, and #Person2# agrees with #Person1# that they need a car."}
{"answers": "#Person2# felt that his girlfriend's bargaining with a sales cleck in Chinese sounded like a shouting match. #Person1# thinks he must be exaggerating.", "prediction_text": "#Person1# asks #Person2# how long they have been in Guangzhou and #Person2# replies four months. #Person1# asks if #Person2# knows Chinese better now and #Person2# explains that they have learned to understand Chinese faster after being in China for a while. #Person1# finds it funny and #Person2# explains that they used to think it was a violent shouting match but now understands it's a normal conversation."}
{"answers": "#Person2# is resigned to #Person1#'s thinking about #Person1#'s wearing every day. #Person1# will go to Nasi to change shoes as they are too small. #Person2# reminds #Person1# to bring the receipt.", "prediction_text": "#Person1# is thinking about what to wear and #Person2# points out that #Person1# goes shopping frequently and suggests that #Person1# check the size of the shoes they bought yesterday."}
{"answers": "Allan Parker calls Phyllis Seymour to thank her for her help.", "prediction_text": "Allan Parker calls Phyllis Seymour to thank her for her help last week. Phyllis Seymour is pleased to have helped and tells Allan Parker that it is her job."}
{"answers": "Mike explains the concept of nanometer to #Person1#. #Person1# understands it.", "prediction_text": "#Person1# asks #Person2# about nanometer and #Person2# explains it in detail. #Person1# finds it hard to understand the exact size."}
{"answers": "#Person2# can play drums but hasn't played for a while. #Person1# loves music but can't play any musical instrument. #Person2# suggests #Person1# be a DJ and mix dance music with #Person1#'s computer.", "prediction_text": "#Person1# and #Person2# are talking about Ben forming a rock and roll band. #Person2# can play drums but is unsure if they are good enough to join the band. #Person1# wishes they could play a musical instrument and #Person2# suggests they try being a DJ instead. #Person1# agrees and plans to search for information on the internet."}
{"answers": "#Person2# tells #Person1# where to buy textbooks and how to turn in #Person1#'s old textbooks for money.", "prediction_text": "#Person1# wants to know where to buy textbooks, #Person2# tells #Person1# to find the bookstore near the gym and explains that #Person1# can also turn in old textbooks for money. #Person1# is unable to go that day."}
{"answers": "#Person1# will try sky diving this weekend. #Person2# warns #Person1# it's dangerous.", "prediction_text": "#Person1# wants to try sky diving, but #Person2# warns #Person1# about the danger and tells #Person1# to take care of themselves."}
{"answers": "Emily tells #Person1# that she had a bad day because her manager blamed his mistakes on her. Emily says her manager treats everyone, especially her, badly, but she hopes to live with it for a while until she gets a promotion. #Person1# thinks Emily is sensible.", "prediction_text": "Emily had a bad day at work because her manager blamed her for mistakes on the monthly report, despite the fact that Emily had nothing to do with the mistakes. Emily feels that her manager treats her worse than the others and there is no one else she can talk to about it at her company."}
{"answers": "#Person2# is drawing #Person1#'s blood as the doctor wants to know #Person1#'s white blood cell count.", "prediction_text": "#Person1# goes to a lab to have their blood drawn. #Person2# explains what the test is for and how it will be done. #Person1# asks how much it will hurt and #Person2# reassures them it is only a pin prick."}
{"answers": "#Person1# wants to buy a specific design with one breast pocket but #Person2# doesn't one.", "prediction_text": "#Person1# is looking for a suit with a single breast pocket, but #Person2# doesn't have that design in that color. #Person1# is disappointed but thanks #Person2#."}
{"answers": "#Person2# asks #Person1# to show #Person2# all the styles of pullovers.", "prediction_text": "#Person1# shows #Person2# different styles of pullovers. #Person2# wants to see all the styles."}
{"answers": "#Person2# is satisfied with the monthly salary proposed by #Person1# and asks some questions about the work.", "prediction_text": "#Person1# asks #Person2# about their salary and expected salary, and #Person1# offers 5,000 yuan per month with raises after three months. #Person2# accepts the offer and asks about reporting to the general manager of the section, benefits, and working abroad. #Person1# confirms that there are opportunities for inspection abroad."}
{"answers": "#Person1# and #Person2# are planning to do something really nice for their family this Christmas.", "prediction_text": "#Person1# and #Person2# want to give a meaningful gift to their family this Christmas."}
{"answers": "#Person1# takes the bus these days because of the high gas prices.", "prediction_text": "#Person1# and #Person2# are having a conversation on the bus. #Person1# is not feeling well and #Person2# asks how they are. #Person1# replies they could be better and #Person2# asks how long they have been on the bus. #Person1# replies fifteen minutes and #Person2# asks how often they ride the bus. #Person1# replies they usually drive but are waiting for the gas prices to be lowered."}
{"answers": "#Person1# asks Liz to help #Person1# set up for #Person1#'s presentation Thursday morning at the Dayton Street office. Liz agrees and asks about #Person1#'s needs.", "prediction_text": "Liz needs help setting up for a presentation at the Dayton Street office. Liz has been to the office before and will help with everything else."}
{"answers": "#Person2# wants to go for the Petty Consumer Loan for the renovations of his apartment. #Person1# says the maximum they can lend #Person2# is 20,000 RMB and #Person2# accepts it.", "prediction_text": "#Person1# and #Person2# discuss the Petty Consumer Loan. #Person2# decides to go for the loan and #Person1# needs some documentation from #Person2# to proceed."}
{"answers": "#Person1# is interviewing #Person2# who wants to get a job as a stewardess. #Person1# asks #Person2# about #Person2#'s motivation, English level, health condition, nursing experience, height, eyesight, strengths, weaknesses, etc.", "prediction_text": "#Person1# asks #Person2# about their occupation, and #Person2# explains that they love the sky, traveling, and working with people. #Person1# asks about the responsibilities of a stewardess, and #Person2# explains that their main responsibility is to make passengers relaxed and happy during the flight. #Person1# also asks about #Person2#'s health, nursing experience, height, eyesight, strengths, and weaknesses. #Person2# explains that they are in good health, have two years of nursing experience, are one meter and sixty-eight centimeters tall, have good eyesight, and are friendly and open-"}
{"answers": "Vicky tells #Person1# she has been a secretary for two years in IBM and feels she has learned a lot. Vicky enjoys teamwork.", "prediction_text": "#Person1# asks Vicky about her job experience and Vicky replies that she has been a secretary for two years and enjoys working with others."}
{"answers": "#Person2# thinks #Person2# is active, energetic, and outgoing. #Person2# values friendship very much and tells #Person1# about #Person2#'s close friends. #Person2# likes to work with people who are honest, dedicated, and have integrity and finds it hard to work with slackers and those who violate working principles.", "prediction_text": "#Person1# asks #Person2# about their personality, what makes them happy, and what kind of people they would like to work with. #Person2# replies that they are active and energetic, having good friends is important for happiness, and they prefer to work with honest, dedicated, and principled people."}
{"answers": "#Person1# and #Person2# agree that a manager should discuss an employee's unsatisfactory work one-to-one in a positive way.", "prediction_text": "#Person1# and #Person2# discuss how a manager should handle an employee's unsatisfactory work. #Person2# suggests having a private discussion in an unthreatening situation without distractions. #Person1# agrees that it is not wise to scold an employee in front of others."}
{"answers": "#Person2# suggests #Person1# read the newspaper for renting information.", "prediction_text": "#Person1# wants to find a room to rent and #Person2# suggests reading the newspaper for information."}
{"answers": "#Person2# helps #Person1# book two tickets for Dark and Stormy Night on Saturday. The seats are next to each other and in the front row.", "prediction_text": "#Person1# wants to buy two tickets for the movie \"Dark and Stormy Night\" but finds out it's sold out. #Person2# tells her the movie is sold out but has tickets available for the next two days. #Person1# prefers to sit together and #Person2# offers her seats in the front or back row. #Person1# agrees to buy the tickets for $25."}
{"answers": "#Person1# can help #Person2# who wants to put smaller wheels on the bike given by #Person2#'s cousin, but #Person1# recommends #Person2# to sell this bike and buy a suitable one.", "prediction_text": "#Person1# works at a bike shop and #Person2# wants to trade in a bike for a smaller one. #Person1# tells #Person2# that it's better to buy a bike that fits properly, but offers to buy the bike from #Person2# if it's in good shape."}
{"answers": "#Person1# needs #Person2#'s suggestion on booking a hotel room. #Person2# is shocked by those expensive choices. #Person2# just wants a room on an upper floor at a reasonable price.", "prediction_text": "#Person1# and #Person2# are deciding on a hotel room. #Person1# explains the different types of rooms and their prices, but #Person2# is not interested in any of them."}
{"answers": "#Person1# helps #Person2# book a flight from Salt Lake City to Berlin for the 22nd this month and a return flight on the 29th.", "prediction_text": "#Person1# helps #Person2# book a flight to Berlin for the 23rd of this month, returning on the 29th. #Person1# finds a cheaper option by leaving a day earlier, and #Person2# agrees to take that flight."}
{"answers": "#Person1# covers the walls in #Person2#'s room with the prints #Person1# bought during a museum tour. #Person1# is planning to sign up for the next museum tour.", "prediction_text": "#Person1# tells #Person2# that they covered the dull walls in their room with prints from the Museum of Modern Art in New York, and #Person2# expresses interest in the print and asks if it is a copy of a museum collection piece. #Person1# confirms that it is and mentions that the Art History Department is planning another museum tour."}
{"answers": "#Person2#'s parents allow #Person2# out most evenings and give #Person2# pocket money weekly, but they don't allow #Person2# to travel with #Person2#'s boyfriend before #Person2#'s 17.", "prediction_text": "#Person1# asks #Person2# about their parents' freedom and money, and #Person2# tells #Person1# that they get two pounds a week and would like to go on holiday with their boyfriend but their parents won't let them until they are 17."}
{"answers": "#Person1# reminds #Person2# who is typing an article about #Person2#'s trip up to the Amazon last month that it's late at night. #Person2# hopes to sell the article to a news service and then #Person2# might be able to do other stories regularly.", "prediction_text": "#Person1# is surprised that #Person2# is still awake and writing at midnight, and #Person2# explains that she is writing an article about her recent trip to the Amazon and hopes to sell it to a news service."}
{"answers": "#Person1# tells #Person2# Rick will fly to Brazil in a week and do a month's work there. Rick is going alone and everything will be paid for.", "prediction_text": "#Person1# tells #Person2# that Rick, the boss of the law office, wants Rick to fly to Brazil and work there for a month. #Person2# is happy for Rick and mentions that everything will be paid for. #Person1# mentions that Rick is excited and promised to buy his sister gifts. #Person2# asks who else is going besides Rick and when he will be leaving."}
{"answers": "#Person2# is looking for a Father's Day gift, but she only has $10 or so even though she has been helping with housework for the past week to earn money. #Person1# sells a tie worth $13.99 to her at the price of $5.", "prediction_text": "#Person1# is a salesperson at a store. #Person2# is looking for a Father's Day gift and wants to spend around $10. #Person1# shows #Person2# different gifts, but none of them are within #Person2#'s budget. Finally, #Person1# offers a tie for $5, which #Person2# accepts."}
{"answers": "#Person1# wants to share a taxi with #Person2# because it's cheaper and #Person2#'s Chinese is better. #Person2# agrees.", "prediction_text": "#Person1# and #Person2# are considering sharing a taxi. #Person1# suggests it to save money and because #Person2# speaks Chinese better than #Person1#."}
{"answers": "#Person1#, Clyde, and #Person3# have a meal together and Clyde mentions his wish to be the first black president.", "prediction_text": "#Person1# and #Person2# are eating lamb and potatoes with their hands. #Person3# tells them about Clyde's dream to be the first black president. #Person2# takes Ronald Reagan as his role model."}
{"answers": "#Person1# wants a custom-made skirt from #Person2#.", "prediction_text": "#Person1# wants a custom-made skirt and asks about the time and cost of tailoring. #Person2# confirms that it will take about a week and the charge is 20 yuan."}
{"answers": "Isabelle thinks #Person1# is a dreamer because #Person1# doesn't do real things.", "prediction_text": "Isabelle and John have different views on John's career goals. Isabelle thinks John is a dreamer for wanting to spread the word of jazz in an underpass, while John is determined to find jazz students."}
{"answers": "#Person2# majors in Japanese and thinks the literature course is helpful.", "prediction_text": "#Person1# asks #Person2# if they are studying at the university, #Person2# replies that they are studying in the Eastern Asian language department, #Person1# asks what they are majoring in, #Person2# replies Japanese, #Person1# asks what they think of the literature course and #Person2# replies that it is very helpful and has let them into a world of literature."}
{"answers": "#Person2# is drawing #Person1#'s blood because the doctor wants to know #Person1#'s white blood cell count.", "prediction_text": "#Person1# goes to a lab to have their blood drawn. #Person2# explains the process and tells #Person1# what the test will reveal. #Person1# asks how much it will hurt and if their blood will be in a tube. #Person2# tells #Person1# that the blood that filled the tube is all they needed."}
{"answers": "#Person1# introduces the hat parade which is the most special event in Kentucky to #Person2#.", "prediction_text": "#Person1# tells #Person2# that the most special event in Kentucky is the hat parade, which takes place on the first Saturday in May each year and is a fashion tradition and good luck."}
{"answers": "John wants Miss Ames to oil her door because the door is noisy. Miss Ames agrees.", "prediction_text": "John asks Miss Ames if she would oil her door since she comes home late and it makes a lot of noise when she opens it. Miss Ames agrees to do it over the weekend."}
{"answers": "Mark was born in Montreal and grew up in Calgary. He was a big reader when he was young. His first trip abroad was to Liverpool when he was 14.", "prediction_text": "#Person1# asks #Person2# about his childhood and where he grew up, and #Person2# tells #Person1# about his childhood and first trip abroad."}
{"answers": "David tells #Person1# about his terrible day. David fell off his skateboard and landed in a pool this morning and had to walk all the way home.", "prediction_text": "David had a terrible day. He fell off his skateboard in the park and the bus driver didn't let him on the bus because he was wet and dirty."}
{"answers": "The cellphone of Tommy's mom was stolen when Tommy was using it. His mom asked him to report it to the policeman right now.", "prediction_text": "Tommy tells his mom that someone stole her cellphone while he was texting Jack outside a store. Tommy is upset but his mom tells him to report it to the police immediately."}
{"answers": "#Person1# and #Person2# are looking for a table for #Person2#'s new house. #Person1# thinks #Person2# is picky.", "prediction_text": "#Person1# shows #Person2# different tables, but #Person2# is picky and doesn't like any of them. #Person1# suggests the last table, but #Person2# is unsure."}
{"answers": "#Person1# helps George Damon reserve a room with chairs, a stage, and a projection screen for George's company's meeting.", "prediction_text": "George Damon calls Mabel Hotel to book a room for a sales meeting in January. #Person1# suggests rooms 13, 19, and 26, but George prefers a room ending in 9. #Person1# reserves room 9 for George."}
{"answers": "#Person1# and #Person2# talk about the main advantages of a successful invention, the bicycle.", "prediction_text": "#Person1# talks about the advantages of bicycles, including that they are cheap to run, easy to use, and safe. #Person2# agrees with #Person1# and adds that they are also easy to maintain."}
{"answers": "Maria wants to buy a pet, and Sam suggests going to Rachel's pet store. Maria wants to keep a snake, but Sam persuades her not to keep non-traditional pets for health concerns.", "prediction_text": "Sam suggests Rachel's pet store to Maria, who is looking to buy a pet. Maria is excited to find a variety of exotic animals, but Sam warns her about the health risks associated with reptiles and suggests she consider a more traditional pet like a dog instead."}
{"answers": "#Person1# interviews #Person2# several questions about #Person2#'s business with China. #Person2# says #Person2#'s business is protected by law and the Sino-American relation hasn't influenced the business from now.", "prediction_text": "#Person1# asks #Person2# about their business relationship with China and #Person2# explains that they have been doing business with China for five years and that they have not had any difficulties with their Chinese partners. #Person2# expresses concerns about the current political situation between China and the US and its potential impact on their business in China."}
{"answers": "#Person1# asks Kate's permission to change the channel, and Kate recommends a talk show to #Person1#.", "prediction_text": "#Person1# wants to change the channel because the current one has too many ads and no good TV programs. #Person2# agrees and suggests Channel Twelve, which is a talk show that #Person2# thinks is funny. #Person1# hasn't watched it before."}
{"answers": "#Person1# finds a mistake on the bill, and #Person2# will check it.", "prediction_text": "#Person1# points out a mistake on the bill and asks #Person2# to check it."}
{"answers": "Chrisopher's fed up with work and wants a break, so #Person1# invites him to go to the beach on Sunday. Christopher will bring his beach towel, and #Person1# will teach him how to surf since Christopher never tried.", "prediction_text": "Christopher is fed up with work and wants a break. Christopher is available on Sunday and wants to go to the beach with Mr. #Person1# and friends. Mr. #Person1# tells Christopher the details of the trip and asks him to bring $10 for food and gas money."}
{"answers": "#Person1# invites Doris to a sale, but Doris refuses.", "prediction_text": "#Person1# invites #Person2# to go to Helen Mall for a sale, but #Person2# declines due to financial reasons and #Person1# decides to go alone."}
{"answers": "#Person2# helps #Person1# to prepare for the party. They decide the style, food, and music and will plan it in detail on Friday.", "prediction_text": "#Person1# needs help with the office party and asks #Person2# for assistance. #Person2# suggests splitting the tasks and asks for clarification on the type of gathering and food. #Person1# prefers a formal affair and #Person2# suggests American food. #Person1# wants a DJ and #Person2# agrees to help plan."}
{"answers": "#Person1# comes to #Person2# to get #Person1#'s prescription filled. #Person1# also asks #Person2# how to take the medication.", "prediction_text": "#Person1# goes to the pharmacy to get their prescription filled. #Person2# tells #Person1# that the prescription will be ready in 20 minutes and that it can be renewed over the internet and delivered to their home. #Person2# also explains how to take the medication and warns #Person1# about potential side effects."}
{"answers": "Mom asks #Person1# to read, and she'll turn off the TV.", "prediction_text": "#Person1# wants to watch TV but #Person2# tells her it's time to read. #Person1# can't find the remote and #Person2# promises to find it."}
{"answers": "#Person2# writes #Person1# a ticket for driving through a red light.", "prediction_text": "#Person1# is stopped by a police officer for running a red light. #Person1# admits to not knowing and apologizes. The officer gives #Person1# a ticket."}
{"answers": "Alan's sister had a car accident last week. #Person1# and #Person2# are worried about Alan.", "prediction_text": "Alan's sister was in a car accident and is still in a coma."}
{"answers": "#Person1# greets John and his parents. Then John invites #Person1# to go shopping, but #Person1# has to meet someone.", "prediction_text": "#Person1# and #Person2# are having a casual conversation in the street. #Person1# is studying for exams and mentions that his school exams will begin next month. #Person2# asks about #Person1#'s parents and invites #Person1# to go shopping, but #Person1# declines and says goodbye."}
{"answers": "Zina suspects one of Vince's programmer is a spy. Vince can't believe it.", "prediction_text": "#Person1# suspects one of the programmers is spying on them for WebTracker, but #Person2# doubts it. #Person1# wants to borrow the tape recorder to investigate."}
{"answers": "#Person2# suggests going to a horror movie next on the second date, and #Person1# agrees.", "prediction_text": "#Person1# and #Person2# have different tastes in movies, but #Person2# suggests going to a horror movie together and holding hands to help #Person1# not get scared."}
{"answers": "#Person1# asks #Person2# for a salary raise and proposes a 5% rise. #Person2# thinks if #Person1# takes on extra responsibilities for scheduling, the rise will be reasonable.", "prediction_text": "#Person1# wants to discuss his salary with #Person2#. #Person1# likes working for the company but has trouble making ends meet due to having a wife and child to support. #Person2# proposes that #Person1# take on extra responsibilities in exchange for a 5% raise. #Person1# agrees and #Person2# offers to show #Person1# how to do the scheduling."}
{"answers": "Carole tells Rebecca about Simon's lie and Rebecca wants to help, but Carole decides to handle him herself.", "prediction_text": "#Person1# and #Person2# are talking about a movie and Simon. #Person2# tells #Person1# that Simon couldn't make it to the movie and gave an excuse about his car dying again, which #Person2# doesn't believe. #Person2# decides it's time to end things with Simon."}
{"answers": "#Person1# tells #Person2# not to smoke here.", "prediction_text": "#Person1# tells #Person2# that smoking is not allowed in the area due to a sign. #Person2# apologizes and is near-sighted."}
{"answers": "Amy wants dumplings, but Rick wants a hot pot. They finally decide to try Yuanyang pot and have some noodles.", "prediction_text": "#Person1# is hungry and suggests dumplings, but #Person2# suggests trying something new and suggests the Mongolian hot pot. #Person1# reminds #Person2# of the spicy lamb they had last time and suggests Yuanyang pot instead. #Person2# agrees and mentions that they serve noodles."}
{"answers": "#Person1#'s helping Mom with the meal. Mom asks #Person1# what Dad and Daniel are doing.", "prediction_text": "#Person1# helps with the washing up by peeling and washing onions, then chops them. #Person2# tells #Person1# to get their father and brother to help with the rest of the dishes."}
{"answers": "Ralph is more optimistic than #Person1# about traveling in space. Ralph thinks the real danger is radiation and Van Allen Belts.", "prediction_text": "#Person1# is concerned about the dangers of space travel, and #Person2# tries to reassure him by pointing out that things have improved over time and that the main danger is radiation from the sun and the Van Allen Belts."}
{"answers": "#Person1# and #Person2# are choosing a coffee table for their room.", "prediction_text": "#Person1# and #Person2# are shopping for a coffee table. #Person2# doesn't like the first one and suggests another one that matches their room and is easy to clean. #Person1# agrees."}
{"answers": "#Person2# wants an eye-shadow in a bright color, and #Person1# helps #Person2# choose one that #Person2#'s satisfied.", "prediction_text": "#Person1# helps #Person2# find an eye-shadow in a bright color. #Person2# tries it and decides to buy it."}
{"answers": "#Person1# congratulates #Person2#'s award, and #Person2# encourages #Person1#.", "prediction_text": "#Person1# congratulates #Person2# on their award and #Person2# expresses gratitude. #Person1# believes #Person2# deserves the award and encourages #Person2# to expect a nomination for their work."}
{"answers": "#Person1# hasn't received credit card bills, and #Person2# suggests #Person1# should take that up with #Person1#'s post office.", "prediction_text": "#Person1# hasn't received their Master Card bill and asks #Person2# about it. #Person2# tells #Person1# to contact the post office if the bill is late and provides proof of the mistake to get an extension on the bill."}
{"answers": "#Person1# pays the bill with #Person2#'s assistance. #Person2# reminds #Person1# to keep the receipt for insurance.", "prediction_text": "#Person1# pays the bill and #Person2# gives him a receipt. #Person2# asks if the bill will be covered by #Person1#'s work insurance and #Person1# replies that it will not be, but he has private medical insurance. #Person2# gives #Person1# a certificate to get reimbursed."}
{"answers": "#Person1# tells #Person2# #Person1# finds an beautiful and affordable apartment in the newspape", "prediction_text": "#Person1# found an apartment in the newspaper ads that is affordable and beautiful. #Person2# congratulates #Person1# on the new apartment."}
{"answers": "#Person2# plans to send the parcel of vases to Changchun by airmail. #Person1# suggests #Person2# pack the fragile articles in a proper strong box.", "prediction_text": "#Person1# helps #Person2# send a parcel to Changchun. #Person1# asks if there is anything valuable in the parcel, and #Person2# replies that there are only some vases. #Person1# advises #Person2# to use a proper strong box, and #Person2# buys one from #Person1#. #Person1# helps #Person2# fill out the customs forms and sends the parcel to the next window for examination."}
{"answers": "#Person1# asks #Person2# the working hours of the position.", "prediction_text": "#Person1# likes flexible working hours and #Person2# suggests changing the position or working place."}
{"answers": "#Person1# invites #Person2# to an exhibition. #Person2#'ll take the subway there.", "prediction_text": "#Person1# invites #Person2# to an exhibition of Picasso's paintings, but they have to take the subway due to the bus drivers' strike and high taxi fares."}
{"answers": "#Person1# tells #Person2# #Person1# chatted with a man online but nothing personal when #Person1# turned thirty.", "prediction_text": "#Person1# met someone online on their 30th birthday and they started chatting about books, music, and New York. They don't talk about anything personal and made a rule to keep it casual."}
{"answers": "#Person1# apologizes to #Person2# for the reservation mistake. #Person2# is angry but agrees to eat in the hall finally.", "prediction_text": "#Person1# tells #Person2# that there is no more room at the restaurant and suggests calling other restaurants. #Person2# is disappointed but agrees to be seated in the hall."}
{"answers": "Bobby tells Dr. Cardano that he had a sharp pain in his right foot.\nAfter seeing his right foot, Dr. Cardano decides to send him up to the lab on the third floor to get some blood tests.", "prediction_text": "Bobby has a pain in his right foot that started suddenly and is difficult to walk. Dr. Cardano examines his foot and sends him to the lab for blood tests."}
{"answers": "Sally and Tom talk about their job. Sally works part-time in a supermarket, while Tom does yard work for people. They both agree that every job has its shortcomings but they need to work to earn money for school.", "prediction_text": "Tom and Sally are having a casual conversation about their jobs. Tom works as a yard worker and Sally works at a supermarket in the produce section. They both agree that every job has its downsides, but they have to work to earn money for school."}
{"answers": "#Person1# introduced the four ugliest women who had great virtue in ancient China to Susan.", "prediction_text": "#Person1# talks to #Person2# about four ugly women in ancient China who were well respected for their virtue, not their appearance. #Person2# agrees that inner beauty is important."}
{"answers": "#Person1# feels terrible, and #Person2# thinks #Person1# might get an allergy. #Person2# tries to find out what triggers that, and it turns out that #Person1# is allergic to #Person2#'s cat.", "prediction_text": "#Person1# is sneezing and has watery eyes, #Person2# thinks it might be an allergy, #Person1# doesn't think it's a cold, #Person2# jokes about quarantine, #Person1# throws a peanut at #Person2#, #Person2# is not allergic to peanuts, #Person1# thinks it might be dust, #Person2# brings in her cat Mr. Snuffle for his birthday, and #Person1# has an allergic reaction to the cat."}
{"answers": "#Person2# thinks #Person1# is impatient and might not love #Person2# anymore, but #Person1# reassures #Person2# and they will watch the movie together.", "prediction_text": "#Person1# is worried about missing the movie \"Ghost\" and #Person2# is upset that #Person1# is not paying attention to her. #Person1# explains that they have work to do and #Person2# feels unloved. #Person1# reassures #Person2# that they love each other and they go to see the movie."}
{"answers": "#Person2# helps #Person1# to choose a big aluminum pan with a heat-resistant bamboo handle and a lid.", "prediction_text": "#Person1# wants a pan and #Person2# shows him different sizes. #Person1# wants the biggest one but finds it too heavy, so #Person2# shows him a lighter one with a bamboo handle. #Person1# decides to buy the pan and needs a lid as well."}
{"answers": "#Person1# asks Lily about Lizzy's family because #Person1# wants to pay a visit.", "prediction_text": "#Person1# asks #Person2# if they know Lizzy Smith and her family, including her mother. #Person2# confirms they know Lizzy and her family."}
{"answers": "#Person1# helps #Person2# to recall the Johnsons but fails.", "prediction_text": "#Person1# reminds #Person2# about the Johnsons, a couple they met in Bermuda last May, and #Person1# has invited them for brunch. #Person2# doesn't remember them."}
{"answers": "#Person1# helps Lincoln to keep the reservation.", "prediction_text": "#Person1# is at the Capital Hotel and #Person2# calls to request that her reservation be kept due to flight delays caused by fog. #Person2# provides her name."}
{"answers": "#Person1# helps #Person2# order the starter and main course.", "prediction_text": "#Person1# takes the order of #Person2#, who orders a chef's salad as a starter and steak as the main course, with peas and carrots as a side dish and boiled potatoes as another side dish."}
{"answers": "#Person1# asks #Person2# to hurry up, but #Person2# can't find the wallet. #Person1# complains but #Person2# doesn't think it's #Person2#'s fault. When they are about to take the taxi, #Person2# can't find the keys.", "prediction_text": "#Person1# is stressed out because their flight leaves soon and #Person2# is late because they can't find their wallet or keys. #Person1# is annoyed and #Person2# apologizes."}
{"answers": "#Person1# and #Person2# talk about the differences between American football and soccer. #Person2# thinks soccer is boring, but #Person1# disagrees.", "prediction_text": "#Person1# and #Person2# are talking about the Euro 2012 football tournament. #Person1# has been following it, while #Person2# has only watched a bit. #Person1# explains the different styles of playing and strategies involved in soccer, while #Person2# expresses their preference for faster-paced sports like basketball."}
{"answers": "Tony calls #Person2# from the airport to reserve a single room. #Person2# tells him the price and will hold the room until 9:00 PM.", "prediction_text": "Tony Chan calls the Park Hotel to reserve a single room for the night. The reservation agent asks for Tony's name and tells him the rate for a single room, which Tony agrees to pay. The agent informs Tony that the hotel will hold the room until 9:00 PM."}
{"answers": "#Person1# asks Phil Taylor about the fire. Taylor says that he forgot the chips in the pan, which caused the fire. Then he took action fast and the Fire Department put out the fire.", "prediction_text": "Phil Taylor talks to a reporter about the fire in his kitchen. He explains how he was deep frying chips when his mother called and he forgot about them, resulting in a fire. He describes how he put blankets over the flames to try to stop the fire and how the firefighters arrived quickly to put out the fire."}
{"answers": "#Person2# tells #Person1# that #Person2# has found a new job and wants to cancel the hotel reservation. #Person1# will return the money.", "prediction_text": "#Person1# is calling to cancel a reservation at the Monterrey Bay Inn. #Person2# tells #Person1# the dates of the reservation and #Person1# offers to put the deposit back on #Person2#'s visa."}
{"answers": "#Person2# applies for a job at a radio station. #Person1# interviews #Person2# about #Person2#'s working experience, abilities, and references. After #Person2# answering these questions, #Person1# tells #Person2# to wait for the result.", "prediction_text": "#Person1# interviews #Person2# for a job at a radio station. #Person2# explains why they want the job, their experience with answering phones and sending emails, and their ability to work early mornings. #Person1# thanks #Person2# and will give them a call back in a few days to let them know the decision."}
{"answers": "#Person1# likes cats, but #Person2# prefers dogs. They're not ready to get a pet for now.", "prediction_text": "#Person1# and #Person2# are at a pet store. #Person1# likes a cat, but #Person2# prefers a dog. #Person1# and #Person2# decide to get coffee to discuss getting a pet."}
{"answers": "#Person1# and #Person2# can't use TV or the Internet so they decided to watch a movie. After a discussion, they are going to watch Three Men and a Lady.", "prediction_text": "#Person1# asks #Person2# if the TV has been repaired, and #Person2# says it hasn't. #Person1# reminds #Person2# to pay the internet bill, and #Person2# forgets. #Person1# suggests going to the cinema, and #Person2# agrees to see a comedy film at 7:30."}
{"answers": "#Person1# asks #Person2# about #Person2#'s career as a cameraman. #Person2# tells #Person1# about why #Person2# chose this job, #Person2#'s prizes, future plan, and favorite part of the job.", "prediction_text": "#Person1# asks #Person2# why they became a cameraman and #Person2# tells the story of how they discovered their passion for filmmaking after reading a biography. #Person1# asks about future plans and #Person2# expresses their interest in working with students and film academy, enjoying the opportunity to see the world and meet interesting people."}
{"answers": "Mrs. Wang thinks Mr. Brown's price is so high. Mr. Brown agrees to offer a 5% reduction but Mrs. Wang asks for another same reduction. Mr. Brown will have to wait for a reply from the home office. He requests to meet with Mrs. Wang again.", "prediction_text": "#Person1# and #Person2# discuss the price of a product. #Person1# reduces the price by 5% but #Person2# still finds it too high. #Person1# asks #Person2# to wait for a reply from the home office before making a decision."}
{"answers": "#Person2# comes to an art shop to buy presents for #Person2#' s foreign friends. #Person1# recommends the paper-cuts which #Person2# thinks great. #Person1# tells #Person2# #Person1# has collected them on #Person1#'s own and #Person2# admires #Person1# as an expert.", "prediction_text": "#Person1# suggests that #Person2# take their time to find something they like in the shop, and #Person1# offers to help if there is any problem. #Person2# is interested in a wood carving but it is not for sale, so #Person1# recommends paper-cuts as they are easy to carry and foreign friends are interested in them. #Person1# collects different kinds of paper-cuts from all over the country and opened the shop because of their interest in the folk art of their country."}
{"answers": "#Person2#'s very busy but Jane has to go over the books with #Person2#. #Person2# suggests meeting at 1:30 pm.", "prediction_text": "Jane wants to meet with #Person2# to go over the books, but #Person2# is busy and suggests meeting at 1:30 after their lunch meeting."}
{"answers": "#Person2# picks the Kenmore refrigerator. #Person1# strongly recommends this one and introduces it. #Person2# likes it after seeing.", "prediction_text": "#Person1# helps #Person2# find a new refrigerator. #Person1# recommends the Kenmore refrigerator, which comes with other appliances. #Person2# wants to see it for themselves."}
{"answers": "#Person1# thinks computers are beneficial to kids but Jim thinks the opposite.", "prediction_text": "#Person1# and #Person2# have different opinions on the use of computers in schools. #Person1# believes that computers teach kids to think logically, while #Person2# thinks that computers weaken kids' basic skills and memory."}
{"answers": "George suggests climbing the Great Wall instead of taking the cable car. They reach the top and admire the long history of the Great Wall.", "prediction_text": "#Person1# and #Person2# are at the Great Wall of China. #Person1# suggests taking the cable car or climbing it, and #Person2# prefers climbing. #Person1# tells #Person2# that the Great Wall was built about twenty-five hundred years ago."}
{"answers": "#Person1# orders a delivery of sandwiches and drinks from the Bread and Cheese Deli.", "prediction_text": "#Person1# calls the Bread and Cheese Deli to place an order for sandwiches and drinks. The delivery minimum is $ 15."}
{"answers": "#Person1# asks #Person2# about the times of trains to and back from London, then #Person1# purchases an ordinary return.", "prediction_text": "#Person1# asks for the times of trains to London and #Person2# provides the information. #Person1# wants to know the time of the 7:59 train and the return train at 7:10 p.m. #Person2# tells #Person1# the cost of a return ticket."}
{"answers": "#Person2# tells #Person1# about #Person2#'s future plans, past work experience and inquires #Person1# about office hours and salary. #Person1#'ll inform #Person2# of the decision in a day or two by phone.", "prediction_text": "#Person1# asks #Person2# about their plan for the future, #Person2# wants to work in a law firm and has previous experience as a trainee, #Person1# explains the office hours and pay, and #Person2# asks when they will know the decision."}
{"answers": "Dave feels surprised that #Person2# can infer he was working for WebTracker work.", "prediction_text": "#Person1# is surprised that #Person2# knew about their work for WebTracker. #Person2# reveals that they met the WebTracker sales rep in LA and that everyone at WebTracker uses a green notepad. #Person1# is impressed by #Person2#'s sneakiness."}
{"answers": "#Person1# enjoyed playing video games at home last weekend. #Person2# thinks it's nice to do something at one's own home.", "prediction_text": "#Person1# stayed at home and played video games by himself last weekend, and #Person2# thinks it's nice to do some things in the comfort of one's own home."}
{"answers": "#Person1# and #Person2# are enjoying a pond. #Person1# and #Person2# had planned to stay in Hamburg tonight, but they decide to stay in Bremen since they are not in a rush.", "prediction_text": "#Person1# and #Person2# are enjoying a pond. #Person1# and #Person2# had planned to stay in Hamburg tonight, but they decide to stay in Bremen since they are not in a rush."}
{"answers": "#Person1# and #Person2# meet on the train to Beijing. #Person1#'s on a business trip while #Person2#'s on vacation. #Person1# went to Beijing once and shares #Person2# with #Person1#'s experience.", "prediction_text": "#Person1# and #Person2# are sitting next to each other on a train. #Person1# is going to Beijing on a business trip, while #Person2# is visiting a friend there on vacation. #Person1# has been to Beijing before and mentions the crowded subway during rush hour. #Person2# suggests taking the bus or renting a car instead."}
{"answers": "#Person2# looks tired because #Person2#'s been working overtime lately hoping for a promotion.", "prediction_text": "#Person1# notices that #Person2# looks tired and asks why. #Person2# explains that they have been working overtime on a big project and it was difficult. #Person1# tells #Person2# that they should not work so hard and #Person2# agrees that hard work can lead to success."}
{"answers": "#Person1# and #Person2# talk about their hobbies. #Person2# likes to dance, chess, collect stamps. #Person1# enjoys gardening.", "prediction_text": "#Person1# and #Person2# talk about their hobbies. #Person1# is not interested in Chinese chess, but #Person2# suggests they try Chinese checkers or military chess instead. #Person1# likes gardening and takes care of the flowers and swing in their garden."}
{"answers": "#Person1# and #Person2# have no weekend plans yet.", "prediction_text": "#Person1# and #Person2# have a casual conversation about their weekend plans. #Person1# hasn't decided what they will do yet, while #Person2# doesn't have any plans due to financial constraints."}
{"answers": "The shop assistant helps #Person1# compare two products. #Person1# decides to buy the cheaper one by cheque.", "prediction_text": "#Person1# wants to know the difference between two radios and decides to buy the cheaper one."}
{"answers": "Miss Liu and #Person2# are visiting a botanical garden. #Person2# enjoys it and Miss Liu offers to help #Person2# with taking pictures.", "prediction_text": "#Person1# and #Person2# are in a botanical garden and admire the tall camphor trees and yew trees. #Person1# and #Person2# enjoy the sound of nature and take pictures of the red and yellow peony flowers."}
{"answers": "The interview ends. #Person1# asks for #Person2#'s telephone number to inform #Person2# about the final decision.", "prediction_text": "#Person1# thanks Rebecca for her interest in the job and gives her contact information. #Person1# tells Rebecca they will contact her by the end of next week and asks if she needs the second interview."}
{"answers": "#Person1# assigns John to invite all clients to the conference next week and print out the meeting agenda.", "prediction_text": "John is asked to invite clients to a conference next week and is given instructions on how to get their contact information and where to put them up."}
{"answers": "#Person2# tells #Person1# #Person1# passed the driving test and points out #Person1#'s shortcoming on parallel parking.", "prediction_text": "#Person1# asks about their driving test and #Person2# tells them they did a great job and passed the test, but had a little trouble with parallel parking."}
{"answers": "#Person1# buys a student ticket and gets a museum guide with #Person2#'s assistance.", "prediction_text": "#Person1# asks about the admission fee for a student and #Person2# tells him it is $ 10 with a 50% reduction. #Person1# pays and #Person2# gives him the museum guide."}
{"answers": "#Person2# tells #Person1# the No. 16 bus goes to the railway station, but the interval is too long that #Person1# can't wait, so #Person2# suggests the No. 2 bus.", "prediction_text": "#Person1# asks #Person2# for directions to the railway station and #Person2# tells him to take the No. 16 bus or the No. 2 bus. #Person1# thanks #Person2# for the information."}
{"answers": "#Person2# made #Person1# and #Person2# get off the bus too early. #Person1# blames #Person2# and #Person2# apologizes.", "prediction_text": "#Person1# and #Person2# are on a bus and #Person1# asks where they are and if they are supposed to get off. #Person2# says they are wrong and they got off too early. #Person1# is upset and #Person2# suggests waiting for the next bus."}
{"answers": "#Person1# inquiries #Person2# about a two-bedroom apartment listed in the ad. #Person2# and #Person1# discuss the location, furniture, and payment.", "prediction_text": "#Person1# is interested in renting an apartment listed in The New York Times. #Person2# provides details about the apartment, including its location, furnishing, and rent. #Person1# asks about renting on a month-by-month basis and #Person2# confirms it can be arranged, but with a security deposit."}
{"answers": "It's #Person1#'s first time on a plane. #Person2# tells #Person1# how to check in.", "prediction_text": "#Person1# asks #Person2# how to check in for their flight and #Person2# explains the process. #Person1# has only a handbag to check and #Person2# tells #Person1# to wait in the departure area after going through security."}
{"answers": "Miss Li tells #Person1# about her career goals. #Person1# introduces #Person1#'s company and tells Miss Li about the daily schedule in #Person1#'s company.", "prediction_text": "Miss Li wants to work her way up to a senior management position with a company like Mr. Parsons'. Mr. Parsons tells Miss Li about the job and how it would be like, including the long hours and the use of computer and telephone."}
{"answers": "#Person1# wants to have high-speed internet installed and makes an appointment with #Person2# for Saturday at 11.", "prediction_text": "#Person1# wants high speed internet installed in their house and schedules an appointment for Friday at 3 pm."}
{"answers": "#Person1#'s angry that #Person2# gets home late without a call. #Person1# threats to throw #Person2# out if #Person2# comes home late again. #Person2# gets unhappy with #Person1#.", "prediction_text": "#Person1# is upset with #Person2# for not calling when they were going to be late. #Person1# tells #Person2# that they will have to leave if they continue to disregard the rules."}
{"answers": "#Person1# doesn't like #Person2#'s salty soup but #Person2# forces #Person1# to drink.", "prediction_text": "#Person1# doesn't like the taste of the soup and #Person2# tells #Person1# to finish it and gargle with salt water after their visit to the doctor."}
{"answers": "#Person1# and #Person2# discuss the differences between chess and xiangqi. Both of them think chess is more reasonable than xiangqi in certain rules, but #Person1# think xiangqi has a larger space in which to move around.", "prediction_text": "#Person1# and #Person2# are discussing their preferences for chess and xiangqi. #Person1# likes chess better because of the number of pawns, the promotion of pawns, and the social status of the queen. #Person2# agrees that xiangqi has a larger board and pieces can move longer distances."}
{"answers": "#Person1# and #Person2# run into each other again at the grocery.", "prediction_text": "#Person1# and #Person2# run into each other at the grocery store and realize they have the same idea of coming early to beat the crowds."}
{"answers": "Jane wants to buy that Murrberry handbag to carry to Mary's wedding, but John suggests renting one with Handbag Hire and tells her about the service in detail. Jane is pleased to have a try.", "prediction_text": "Jane wants to buy a Murrberry handbag to carry to Mary's wedding, but John suggests renting one with Handbag Hire and tells her about the service in detail. Jane is pleased to have a try."}
{"answers": "Elsa Tobin changed her seat and comes to sit next to Mike Gerard Hogan who is sitting by the window.", "prediction_text": "Elsa and Mike sit next to each other on a plane. Elsa is from Florida and Mike is also from Florida but just got on the plane. Elsa changes seats because the man next to her was smoking."}
{"answers": "Jennifer Benzes is sharing her divorce and its impact on her children with the audience. She thinks fixing a marriage isn't better for children though most people think the opposite and she wants society to understand them instead of cursing them.", "prediction_text": "Jennifer Benzes talks about the effect of divorce on children and how she and her husband are more aware of the impact on their children. She believes that staying together for the sake of the children is not always the best option and wants society to understand and help them build a better future."}
{"answers": "Tina didn't come to band practice last week because her guitar was broken. #Person1# blames her for not fixing the guitar sooner but Tina has been too busy this month.", "prediction_text": "Tina wants to know if Tina is coming to band practice, and Tina says she is. Tina mentions that her guitar was broken, but she fixed it on Monday night. Tina also mentions that she has been busy and has to work at the grocery store and watch her little sister."}
{"answers": "#Person2# got a doctorate in genetic and wants to find a job conducting genetic research. #Person1# suggests working for private companies and universities.", "prediction_text": "#Person1# congratulates #Person2# on getting a doctorate in genetics and asks what #Person2# plans to do next. #Person2# expresses interest in conducting scientific research into genes and mentions the potential for medical breakthroughs. #Person1# jokes about being cloned."}
{"answers": "#Person2# orders Cuervo Gold margaritas under #Person1#'s recommendation and asks #Person1# to bring it on the rocks with no salt.", "prediction_text": "#Person1# and #Person2# are at a restaurant, and #Person1# suggests ordering drinks. #Person2# asks for the wine list and is told it is on the board. #Person2# asks about house specials and #Person1# recommends the Cuervo Gold margaritas. #Person2# orders one on the rocks with no salt."}
{"answers": "#Person1# volunteers #Person2# to teach a 78-year-old illiterate American how to read. #Person2# thinks it challenging but agrees on the condition that #Person1# makes dinner.", "prediction_text": "#Person1# wants #Person2# to teach #Person1#'s friend how to read, and #Person2# agrees to do so."}
{"answers": "Steven lied to Veronica because of his mission. Veronica feels so deceived that she doesn't believe Steven anymore. Steven asks Veronica to move to India together and marry him but Veronica refuses. At last, Veronica doesn't appear at the airport when Steven's going to board the plane from Los Angeles to Hyderabad.", "prediction_text": "Steven and Veronica have a conversation where Steven reveals that he is an undercover agent and has been lying to Veronica. Veronica is upset and tells Steven that she can't go with him to India."}
{"answers": "#Person1# gets a chest X-ray step by step under #Person2#'s guidance.", "prediction_text": "#Person1# goes to #Person2# for a chest X-ray. #Person2# explains the procedure and #Person1# follows the instructions. #Person2# tells #Person1# to get dressed after the film is developed."}
{"answers": "Mark complains about the burning heat. #Person2# suggests drinking lots of liquids, so they will drink beer.", "prediction_text": "#Person1# and #Person2# are feeling the heat and decide to stay home. #Person1# suggests drinking a lot of liquids and avoiding the worst heat, and #Person2# offers #Person1# a beer."}
{"answers": "#Person1# thinks the shirt looks terrible on #Person1#. #Person2# thought #Person1# likes the eighties styles but #Person1# likes good eighties styles. #Person1# decides to find a more modern store.", "prediction_text": "#Person1# is unhappy with how they look in their shirt and wants to find a more modern store with better styles."}
{"answers": "#Person2# tells #Person1# about the terrible accident last weekend. #Person2# went swimming with Mr. Henry. #Person2#'s son who couldn't swim drowned in the pool and Mr. Henry saved him.", "prediction_text": "#Person1# asks #Person2# about the incident involving #Person2#'s son and the pool. #Person2# explains that #Person2#'s son cannot swim and accidentally went into the deep part of the pool while #Person1# and #Person2# were at the pool. #Person2# mentions that Henry, who is a swimming instructor, noticed what was happening and saved #Person2#'s son. #Person1# expresses her concern and #Person2# explains that they left the pool in a hurry after the incident."}
{"answers": "#Person1# inquires about #Person2#'s mother and suggests hiring someone to take care of her, but #Person2# insists on doing it by himself.", "prediction_text": "#Person1# asks about #Person2#'s mother and #Person2# replies that she is feeling better and will be back home soon. #Person1# suggests hiring someone to help with the cleaning, but #Person2# prefers to do it himself. #Person1# admires #Person2#'s kindness and thoughtfulness."}
{"answers": "Gina's car doesn't always start in the morning. #Person2# suggests Gina take her car to Jimmy's garage for repairs.", "prediction_text": "#Person1#'s car doesn't start in the morning and #Person2# suggests going to Jimmy's garage, which has 20 years of experience and is good at repairing older cars. #Person1# thanks #Person2# and plans to call and make an appointment."}
{"answers": "#Person1# suggests visiting Cambridge, but #Person2# went there yesterday. #Person2# wants to see some modern artwork so #Person1# recommends Tate Modern.", "prediction_text": "#Person1# suggests taking a tour around Cambridge, but #Person2# has already done that with a friend. #Person2# wants to see modern artwork and #Person1# suggests Tate Modern, a popular museum with a collection of international artwork."}
{"answers": "#Person1# tells Mrs. Brandon #Person1# lost the job today. #Person1# recalls the final exam Mrs. Brandom gave #Person1# in the past.", "prediction_text": "#Person1# lost her job today, and #Person2# is sorry to hear that. #Person1# asks about #Person2#'s students, who are nervous about their final exam. #Person1# remembers a difficult final exam in #Person2#'s class in college and how it helped her learn English."}
{"answers": "#Person2# stayed up and got up late because #Person2# has been addicted to computer games. #Person1# suggests #Person2# find other things to do.", "prediction_text": "#Person1# asks #Person2# how they are and what time they got up, and #Person2# replies that they got up late and spent the night playing computer games. #Person1# suggests that #Person2# should focus on important things instead of playing games all the time."}
{"answers": "Sue doesn't eat Bill's cake because she's trying to avoid being allergic to something by rejecting certain foods. Sue can only have salad at the party and she wants some hot soap. Bill suggests going to the restaurant to get some soup after the party.", "prediction_text": "Bill offers to get Sue a salad since she is on a diet and can't eat the cake or sandwiches, but Sue declines and tells Bill she is enjoying her party and will go afterward."}
{"answers": "#Person1# and #Person2# go over their plan of riding from Vancouver to Winnipeg.", "prediction_text": "#Person1# and #Person2# plan to bike from Vancouver to Winnipeg, then to Toronto and Quebec. #Person1# expresses concern about the distance, but #Person2# encourages them to go on the adventure."}
{"answers": "#Person2# interviews #Person1#. #Person1# tells #Person2# her self-assessment of personality, psychological resilience, and weakness.", "prediction_text": "#Person1# describes herself as honest, diligent, persevering, creative, and responsible. She also believes in being sincere and friendly to others and deals with difficult people by being kind. #Person1# admits that she cares too much about details and is always in pursuit of perfection."}
{"answers": "#Person1# and #Person2# talk about whether to travel by bus or by train. They finally choose to go by train as it is more convenient and arrives on time though it costs more.", "prediction_text": "#Person1# and #Person2# are deciding how to travel to their destination. #Person1# prefers traveling by bus, but #Person2# prefers traveling by train. They decide to buy return tickets and take a packed lunch on the journey."}
{"answers": "#Person1# tells #Person2# #Person1# has seasonal allergies and describes the symptoms of sneezing, runny nose, itchy throat, and watery eyes. #Person2# says #Person2# has a severe allergic reaction to alcohol.", "prediction_text": "#Person1# has seasonal allergies, while #Person2# has an allergy to alcohol. #Person1# experiences symptoms such as sneezing, runny nose, itchy throat, and watering eyes, while #Person2# experiences redness, bloodshot eyes, itching, and other symptoms when drinking alcohol."}
{"answers": "#Person1# and #Person2# are taking a train to the Wukesong stop. The train is not crowded by now so they get two seats.", "prediction_text": "#Person1# and #Person2# are planning to travel by train. They look at the map and decide to buy tickets. They find the train is not as crowded as they expected and find two empty seats to sit in."}
{"answers": "#Person2# tells #Person1# a talented athlete did not win because of drug problems. #Person2# and #Person1# discuss the reason why the player was disqualified until after the race, and the reason why the player took drugs.", "prediction_text": "#Person1# is disappointed that the athlete didn't win the gold medal due to drug use. #Person2# explains that the athlete was taking steroids and that the results from the test only came back after the race. #Person1# and #Person2# agree that there is no good reason to take drugs and hope that the new regulations will prevent athletes from cheating."}
{"answers": "#Person1# and #Person2# want to watch different channels and they finally decide to watch a quiz show together.", "prediction_text": "#Person1# and #Person2# are looking for something to watch on TV. #Person1# wants to watch a Western, but #Person2# wants to watch a football game. #Person1# suggests watching a sitcom on Channel 2 at 10 pm. #Person2# agrees to watch it together."}
{"answers": "#Person2# says #Person1# cannot put the luggage here. #Person2# will put them in the airport storage.", "prediction_text": "#Person1# tells #Person2# that the airport storage is over there. #Person2# wants to put their luggage there for three days."}
{"answers": "#Person1# invites #Person2# to watch the football match together tonight.", "prediction_text": "#Person1# and #Person2# are planning to watch a live football match together. #Person1# mentions that AC Milan and Real Madrid are playing, and #Person2# agrees that it will be a tough match."}
{"answers": "#Person1# wants to rent #Person2#'s apartment for $850 a month. #Person2# says the price cannot go lower than $1050.", "prediction_text": "#Person1# wants to rent an apartment but #Person2# is asking for $ 1050 a month, which #Person1# thinks is too much. #Person1# offers $ 850 but #Person2# won't go lower."}
{"answers": "#Person1# tells #Person2# Beethoven's music makes people more intelligent but #Person2# thinks it helps people relax. Then #Person1# and #Person2# exchange the preference of music type and #Person1# asks #Person2# how did #Person2# become interested in pop music.", "prediction_text": "#Person1# and #Person2# are talking about music. #Person1# likes pop music, but #Person2# prefers classical music. #Person2# tells #Person1# how she started listening to classical music when she was 10 years old and began playing the piano. #Person1# asks if #Person2# still plays the piano and #Person2# replies yes."}
{"answers": "#Person2# wants to plan a surprising getaway for his wife and him. #Person1# offers several options and #Person2# chooses to go to the countryside. Then #Person1# introduces the hotel and outdoor activities to him.", "prediction_text": "#Person1# works at Perfect Getaway Tours and helps #Person2# plan a surprise getaway for him and his wife. #Person1# explains the options and #Person2# chooses the countryside. #Person1# describes the hotel and outdoor activities available. #Person2# is excited and wants to book the trip."}
{"answers": "#Person1# shows Wang Mei her new dress. Wang Mei thinks it's beautiful and wants to buy one.", "prediction_text": "Wang Mei shows her new dress to #Person2#, who thinks it's beautiful and asks where it was bought. Wang Mei replies that she bought it near her school and it costs only thirty yuan. #Person2# is surprised at the price and wants to buy one too."}
{"answers": "#Person2# has done doing #Person1#'s hair. #Person1# is satisfied with it.", "prediction_text": "#Person1# gets their hair done by #Person2#. #Person1# likes the way it looks and asks how much it costs. #Person2# replies $55."}
{"answers": "#Person1# and #Person2# are having a walk in the park and talking about people doing different types of exercises.", "prediction_text": "#Person1# and #Person2# are enjoying a fine day and decide to take a walk in the park. #Person1# mentions the fresh air and #Person2# notices that they have the park to themselves. #Person2# points out people practicing various forms of martial arts in the park."}
{"answers": "#Person1# asks #Person2#'s major and #Person2#'s thinkings on a literature course.", "prediction_text": "#Person1# asks #Person2# if they are studying at the university and #Person2# replies that they are studying in the Eastern Asian language department and majoring in Japanese. #Person1# asks about the literature course and #Person2# responds that it is helpful and has introduced them to a new world of literature."}
{"answers": "#Person1# and #Person2# have two weeks off. #Person2# may stay at home. #Person1# invites #Person2# to join #Person1#'s family trip to Florida.", "prediction_text": "#Person1# is excited about their two-week break and invites #Person2# to join them in Florida. #Person2# accepts the invitation."}
{"answers": "#Person1# is hosting a dinner and #Person2# is the first to arrive. #Person1# offers #Person2# a glass of red wine and tells #Person2# two of their friends called to cancel. #Person2# thinks it's a pity.", "prediction_text": "#Person1# welcomes #Person2# to their home and offers them a drink. #Person2# asks about the number of guests for dinner and #Person1# tells them that Marcia and Paul cancelled at the last moment."}
{"answers": "#Person1# and #Person2# are starving and want to eat something. #Person1# prefers to eat Sichuan hotpot and so does #Person2#. Then they decide to go to an authentic tasting hotpot restaurant they like on Chang An Street.", "prediction_text": "#Person1# and #Person2# are hungry and want to get something to eat. #Person1# suggests Sichuan Hotpot, and #Person2# agrees. #Person1# remembers the name of a nearby authentic hotpot restaurant."}
{"answers": "#Person1# interviews #Person2#. #Person2# introduces the educational background of undergraduate and postgraduate studies and previous work experience at Oracle to #Person1#. #Person2# also tells #Person1# #Person2# can make significant contributions to the overall success of the company.", "prediction_text": "#Person1# interviews #Person2# and asks about their educational background and work experience. #Person2# graduated with honors from Chesterton University with a major in Business Administration and a minor in psychology. #Person2# worked at Oracle as a customer support manager and implemented new strategies to achieve better customer satisfaction, resulting in a lower churn rate."}
{"answers": "#Person1# calls #Person2# for room service. #Person2# apologizes for the inconvenience and tells #Person1# the service won't be available until 15 minutes later.", "prediction_text": "#Person1# calls room service to ask for an order, but the staff member tells them that it will take 15 minutes to deliver the order."}
{"answers": "#Person2# made a wrong turn on red. #Person1# checks #Person2#'s driver's license and insurance policy, and gives #Person2# a ticket.", "prediction_text": "#Person1# gives #Person2# a ticket for making a right turn on red. #Person1# asks for #Person2#'s driver's license and insurance policy, and #Person2# signs the ticket and thanks #Person1#."}
{"answers": "#Person1# urges #Person2# to get on the bus.", "prediction_text": "#Person1# tells #Person2# to get on the bus quickly, but #Person2# is unsure of where to get a ticket."}
{"answers": "#Person2# tells #Person1# the location of a grocery store, a laundromat, and a barber shop.", "prediction_text": "Jack is the new neighbor and asks for a grocery store and a laundromat near his place. The person #2 tells him where they are located."}
{"answers": "#Person1# thinks #Person2# is different from other teachers as #Person2# always looks happy and energetic. #Person2# shares with #Person1# that doing exercise helps people keep a good memory and make people stronger.", "prediction_text": "#Person1# is curious about #Person2#'s energy and happiness, and #Person2# explains that she exercises regularly and finds it helps her feel alive. #Person1# agrees that exercise is important and asks for suggestions, which #Person2# provides."}
{"answers": "#Person1# apologizes and will look into an empty promise of a journey for #Person2#.", "prediction_text": "#Person1# is asked by #Person2# about the bus tour and #Person1# apologizes for the mistake and promises to look into it."}
{"answers": "#Person1# asks #Person2# the sort of warranty comes with the stereo, the conditions of the store policy, and how to get the manufacturer's warranty.", "prediction_text": "#Person1# explains the warranty policy to #Person2#, including the store's 90-day return policy and the manufacturer's one-year warranty. #Person2# decides to purchase the stereo."}
{"answers": "#Person1# wants to buy a new bedroom set but doesn't know where to buy. #Person2# recommends IKEA.", "prediction_text": "#Person1# wants to buy a new bedroom set, and #Person2# suggests going to IKEA, where the furniture is not cheap but not too expensive either."}
{"answers": "#Person2# is diagnosing #Person1# that #Person1# feels tired is due to high blood pressure and suggesting #Person1# slow down and lose weight.", "prediction_text": "#Person1# visits the doctor and finds out that his blood pressure is too high and he needs to lose weight."}
{"answers": "#Person2# is arguing with #Person1# about making a trial order because the products are so new. #Person1# refuses at first but finally agrees.", "prediction_text": "#Person1# and #Person2# discuss a deal for a trial order of products. #Person1# agrees to the deal but makes it clear that the repeat order will not have the same terms."}
{"answers": "#Person1# calls Martin and discusses the exams next week. Then #Person1# asks Martin about #Person1#'s physics book which Martin says #Person1# could borrow his. They will meet at lunch to exchange the book and old exam papers.", "prediction_text": "Martin asks his friend if he knows where his physics book is, and they plan to meet outside Natbank at lunchtime so Martin can borrow Martin's book."}
{"answers": "David Paker calls #Person2# to ask about the location, facilities, and price of the advertised apartment. David will have a look at it at one o'clock this afternoon.", "prediction_text": "David Parker calls about the apartment advertised in the paper. #Person2# tells him about the apartment, including its location, size, and amenities. David asks about the rent and additional fees, and #Person2# tells him the rent is $500 per month, including utilities, and there is a $50 monthly parking fee. David asks to see the apartment that afternoon."}
{"answers": "#Person1# is asking Barbara about her trip and what is in Barbara's suitcase.", "prediction_text": "#Person1# welcomes #Person2# back and asks about their trip. #Person2# mentions Milan being bigger and noisier than expected, but not dirty. #Person1# has never been to Italy and expresses interest in going to Rome. #Person2# mentions their luggage being heavy and contains 20 pairs of shoes."}
{"answers": "#Person1# is dissatisfied with the raincoat as a birthday present but wants a leather jacket. #Person1# will return the raincoat and buy a jacket, #Person2# will lend #Person1# another $50.", "prediction_text": "#Person1# is angry with their parents for getting them a raincoat instead of a leather jacket, which they wanted for their birthday. #Person2# offers to lend #Person1# $50 to buy a leather jacket."}
{"answers": "#Person2# looks at #Person1#'s shopping receipt and is angry that there are so many things for a dog but few things for #Person2#. #Person1# wants to take a dog called Herbert home. And #Person2# is angrier when #Person2# finds the steaks are for Herbet but not #Person2#.", "prediction_text": "#Person1# and #Person2# go shopping together and #Person1# buys a lot of food, including a dog that #Person2# doesn't want. #Person1# tries to explain why they need the food and the dog, but #Person2# is not convinced."}
{"answers": "#Person2# tells #Person1# #Person1#'s working schedule for the following two days.", "prediction_text": "#Person1# and #Person2# discuss the next two days of #Person1#'s schedule. #Person1# has a busy day on the day after tomorrow, including a products exhibition, management classes, and a party at the Century Palace."}
{"answers": "#Person1# tells #Person2# #Person1#'s alarm is ringing for an appointment at 11.", "prediction_text": "#Person1# has an appointment at 11am, but their watch is set half an hour fast, so it is actually 10:30am. #Person2# comments on #Person1#'s punctuality."}
{"answers": "#Person1# wants to make a single-breasted suit. #Person2# measures #Person1#'s size, and tells the price is 357 dollars.", "prediction_text": "#Person1# wants to make a suit of a certain material and asks for the cost. #Person2# tells him the cost is $357 and the suit will be ready in two weeks."}
{"answers": "#Person2# congratulates #Person1# for receiving four university offers and asks #Person1# which one is preferred. #Person2# suggests #Person1# apply for financial aid.", "prediction_text": "#Person1# and #Person2# talk about their educational background. #Person1# graduated from high school and wants to get a BA, but is unsure where to go. #Person2# graduated from university with a BA in English and suggests applying for grants or financial aid to help make a decision about which school to attend."}
{"answers": "#Person1# and #Person2# are talking about the new rules of using a cellphone at work.", "prediction_text": "#Person1# and #Person2# are discussing the new rules about using cell phones at work. #Person1# is concerned about not being able to make important calls during meetings, while #Person2# points out that it is just a suggestion and not compulsory."}
{"answers": "#Person2# tells #Person1# the new laptop's functions, photography program, and how to surf the Internet in the cafe by using the wireless.", "prediction_text": "#Person1# sees the new laptop and asks about it. #Person2# explains that it is a wireless laptop and can access the internet for free in this cafe. #Person1# finds it useful for photography and asks to see some photos."}
{"answers": "#Person2# tells #Person1# the news of natural disasters reported in today's newspaper, including the massive forest fire in Australia, the starvation in Africa, an earthquake in Iran, and the help of the international community in Australia and Africa.", "prediction_text": "#Person1# asks #Person2# about natural disasters in the newspaper, including a massive forest fire in Australia, a drought in Africa, and an earthquake in Iran. #Person2# provides information on the affected areas, casualties, and international relief efforts."}
{"answers": "#Person1# serves #Person2# and his wife in the restaurant. #Person2# orders some courses and drinks recommended by #Person1#.", "prediction_text": "#Person1# takes the order of #Person2#, who wants the beef stew, tomato soup, Cayenne Pepper Steak, Fried Trout with mashed potatoes, and a mixed salad to drink Chablis 99."}
{"answers": "John is distracted by the ads that appeared on the computer screen. Harriet recommends John to buy an app at a reasonable price to stop the ads.", "prediction_text": "Harriet suggests buying an app to stop ads on John's computer, but John is hesitant to spend money on it. Harriet explains that it's worth it and offers to buy it for John once and for all."}
{"answers": "#Person2# shares knowledge of different teas with #Person1#, including the classification based on processing methods and their health benefits.", "prediction_text": "#Person1# asks about the tea and #Person2# explains that it is a kind of Wulong tea and tells #Person1# about the different kinds of tea and their health benefits. #Person1# expresses interest in learning more about tea culture in the future."}
{"answers": "#Person1# and #Person2# are going to a party but late. #Person2# needs #Person1#'s help with directions.", "prediction_text": "#Person1# is worried about being late for a surprise party at a restaurant, but #Person2# tells them that rush hour is almost over and they will make it on time. #Person1# offers to call the restaurant for directions."}
{"answers": "#Person2# tells #Person1# things that should be prepared for keeping fish, including a tank, some rocks, and underwater plants.", "prediction_text": "#Person1# wants to buy a fish, but #Person2# suggests getting a tank and tells #Person1# about the importance of having enough air and space for the fish, and also recommends getting underwater plants to keep the water clean and make the tank look prettier."}
{"answers": "Peter has problems with his assignment and turns to Mary for help. They will meet tomorrow after lunch.", "prediction_text": "Peter Parker wants Mary's help with their math assignment, but Mary is busy with swimming practice and studying for a biology exam. Peter offers to give Mary a copy of the Torch, a campus literary magazine, and asks Mary to meet after lunch the next day."}
{"answers": "Alexia and Joe are talking about their plans for the Thanksgiving holiday. Joe will stay at home while Alexia will travel with her family.", "prediction_text": "Alexia is going to travel with her family to Toronto and visit her aunt in Kingston, while Joe plans to stay at home and watch DVDs."}
{"answers": "Lisa and Net talk about how they go to work in bad weather.", "prediction_text": "Lisa and Net are talking about their commutes to work. Lisa usually takes the train while Net usually drives, but they both agree that the train is faster for them."}
{"answers": "#Person1# helps #Person2# withdraw 10,000 RMB from #Person2#'s account.", "prediction_text": "#Person1# helps #Person2# withdraw money from their account. #Person1# checks the account and gives #Person2# the money."}
{"answers": "The toner cartridge seems to have run out. #Person1# suggests #Person2# ask someone from the IT department to change it.", "prediction_text": "#Person1# can't read the fax because the words are too light, and #Person2# suggests checking with the IT department for help."}
{"answers": "Mr. Smith comes to China to place a clothes order with #Person1#. They decide to have some of their clients to join the discussion.", "prediction_text": "Mr. Smith is interested in importing some of China's latest clothes and wants to place an order. Mr. #Person1# agrees and suggests allowing some clients to join the discussion to meet everyone's needs."}
{"answers": "Hao Bo phones Mr. Stern to give information about his plane reservation and ask for the names of the other travelers.", "prediction_text": "Hao Bo from the International Travel Agency calls Mr. Stern to confirm the flight reservations. Mr. Stern confirms the details and Hao Bo tells him the seats are in the non-smoking section and the tickets have been charged to his credit card."}
{"answers": "Henry offers #Person2# a cigarette and gives #Person2# a light.", "prediction_text": "#Person1# offers #Person2# a cigarette and asks if they can stand in together, but #Person2# declines."}
{"answers": "#Person1# and #Person2# are discussing the scooter that has become so fashionable worldwide and its origin, usage mode, and merits.", "prediction_text": "#Person1# and #Person2# are talking about scooters. #Person1# mentions that people are riding scooters in the streets and back lanes, and #Person2# agrees. #Person1# describes the scooter as delicate and agile, easy to operate and foldable, and #Person2# agrees. #Person1# compares riding a scooter to surfing, saying that once you get the hang of it, you won't need to be taught."}
{"answers": "#Person2# tells #Person1# about the weather in #Person2#'s area.", "prediction_text": "#Person1# and #Person2# are talking about the weather. #Person1# asks about winter and #Person2# replies that it is cold and damp, and the roads are often icy. #Person1# also asks about summer and #Person2# replies that they get plenty of rain and suggests bringing an umbrella often."}
{"answers": "Alice is over an hour late for the appointment with Adam. She explains the reason for her lateness and apologizes.", "prediction_text": "Alice is late for her appointment with Adam because of traffic and work. Adam is upset but understands."}
{"answers": "#Person1# suggests buying movie tickets from scalpers but #Person2# rejects the idea, so they wait in the long line and it's finally their turn.", "prediction_text": "#Person1# and #Person2# want to see the movie \"The Matrix\" but the line is long. #Person1# suggests buying tickets from scalpers, but #Person2# is not interested. They decide to take their chances and wait in line."}
{"answers": "#Person2# wants to check out a magazine but #Person1# tells #Person2# it's not allowed. #Person2# thinks it's unreasonable.", "prediction_text": "#Person1# is at the library and #Person2# wants to check out a magazine but is not allowed to. #Person1# explains the library's policy."}
{"answers": "Mary comes to say good-bye to #Person1# before going back to the U.S..", "prediction_text": "Mary is going back to the U.S. and says goodbye to #Person1#. #Person1# wishes Mary a good trip and says they will miss her."}
{"answers": "Trina accepts Jared's proposal. Then, Jared is astonished to know that Trina already knew from Melissa who saw him buying the ring that he was planning this. Trina has chosen a date and has made a list of four hundred guests and she tells Jared about her arrangements in an ecstasy. Jared finds it hard to get through.", "prediction_text": "#Person1# and #Person2# are engaged and #Person2# has planned the wedding in detail. #Person2# tells #Person1# about the guest list, venue, music, caterers, decor, theme, favors, and more. #Person1# is overwhelmed."}
{"answers": "Ethan is not cheerful because his boss threats to fire him if he shows up late one more time. Ethan will look for another job with a more sensitive and optimistic boss. Ava offers to help him with it.", "prediction_text": "Ethan tells Ava he had a bad day at work, including sleeping through his alarm and being threatened with being fired. Ethan plans to look for another job and Ava offers to help with the job search."}
{"answers": "Monica had to give up her shopping plan with Lucy because of the sand storm. #Person1# tells her the government is working to try to stop the sand storm by planting trees.", "prediction_text": "Monica and #Person1# are discussing their plans for the afternoon, but the sand storm ruins Monica's plan to go shopping with Lucy. #Person1# expresses frustration with the frequent sand storms and suggests that the government is working on it. Monica decides to clean her apartment instead."}
{"answers": "#Person1# tells #Person2# to ask Elaine Robinson out but #Person2# refuses, so #Person1# resorts to inviting the Robinson's over for dinner.", "prediction_text": "#Person1# suggests asking Elaine out, but #Person2# declines and explains that they don't get along. #Person1# is disappointed and threatens to invite the Robinson's over for dinner."}
{"answers": "Helen is going to Chicago University to study Economics for three years. #Person1# wishes her good luck.", "prediction_text": "Helen is going to study in America and her classmates congratulate her. She will study for three years and major in Economics at Chicago University."}
{"answers": "#Person1# invites Charles to #Person1#'s house-warming party. Charles will bring some fold-up chairs for #Person1#.", "prediction_text": "#Person1# has found an apartment in Haitian and is having a party on Saturday night. #Person2# is free and will attend the party. #Person1# asks #Person2# to bring some fold-up chairs for guests."}
{"answers": "#Person2# makes a remittance to New Delhi by banker's draft with #Person1#'s assistance.", "prediction_text": "#Person1# works at a bank and #Person2# wants to make a remittance to New Delhi. #Person1# asks if #Person2# wants an international money order or a banker's draft, and #Person2# chooses a banker's draft."}
{"answers": "#Person2# tells #Person1# how to use the powder, the eye-drop, and the ointment.", "prediction_text": "#Person1# asks how to use the powder, eye-drop, and ointment for an injury. #Person2# explains how to use each one."}
{"answers": "#Person1# shows #Person2# a car and tells #Person2# #Person1# borrowed Sarah's car to go shopping.", "prediction_text": "#Person1# bought a car and a watch, and the company has a car club where people can buy paraphernalia and meet other drivers of the same car. #Person1# is excited about the sunroof and imagines a cool and starry night. #Person2# jokes about using Sarah's car to go cruising for girls."}
{"answers": "#Person1# wants to try local food and orders double filet steak under #Person2#'s recommendation.", "prediction_text": "#Person1# wants to try the local specialty and #Person2# recommends filet steak, which #Person1# orders double."}
{"answers": "#Person1# helps Mr. Wang move to another room because the maintenance of the air-conditioner in Mr. Wang's room might last for a long time.", "prediction_text": "Wang Wei wants to move to another room because the air-conditioner in his room doesn't work. The front desk clerk offers him another room and apologizes for the inconvenience."}
{"answers": "#Person2# shows #Person2#'s proficiency in English translation, spoken English, and English tests. #Person2# believes #Person2#'s English is sufficient to do office work in a South Korean firm.", "prediction_text": "#Person1# asks #Person2# about their English skills and #Person2# replies that they can speak English fluently and have rich translating experience. #Person2# also mentions that they have taken TOEFL and GRE and began learning English in primary school. #Person1# asks if #Person2# thinks their English is good enough for office work and #Person2# replies that they believe their English is sufficient for general desk work in a South Korean firm."}
{"answers": "#Person1# asks #Person2# for an estimated price for #Person1#'s order of 100 units and #Person1# thinks it's a little high. #Person2# tells #Person1# they can work something out if #Person1# orders more units.", "prediction_text": "#Person1# wants to know how much their order from the factory will cost and #Person2# tells them that he needs an idea of how large the order will be to give an estimate. #Person1# asks for a ballpark figure and #Person2# tells them that the asking price is $100 per unit, but #Person1# wants to pay $80 per unit. #Person2# tells them that they might be able to work something out if they were ordering more units."}
{"answers": "Terry Chen in Room 117 calls the housekeeper for a clean-up of her room.", "prediction_text": "Terry calls the hotel to request that room 117 be cleaned. The hotel staff member confirms they will send someone right away and asks for Terry's name and room number."}
{"answers": "#Person2# tells mom how to find a cheap plane ticket to China on the Internet. Mom'll buy a ticket for #Person2#, too.", "prediction_text": "#Person1# wants to go to China for vacation but can't find a cheap plane ticket. #Person2# suggests using the internet and gives instructions on how to do it."}
{"answers": "The shoes #Person1# wants to buy are out of stock. #Person2# recommends other shoes that are fashionable but uncomfortable.", "prediction_text": "#Person1# is looking for shoes like the ones their sister bought last month, but the store doesn't have any. #Person2# explains that the style is not in fashion this year and that women often wear uncomfortable shoes."}
{"answers": "#Person1# dislikes #Person2#'s idea of getting a tie for someone. #Person2# then shows #Person1# the tie and #Person1# starts to think it's cool.", "prediction_text": "#Person1# doesn't like the gift #Person2# wants to buy for someone, but #Person2# thinks it's interesting and funny. #Person2# shows #Person1# the tie's secret feature."}
{"answers": "#Person1# checks #Person2#'s passport and asks about #Person2#'s intention and plan of visiting Australia.", "prediction_text": "#Person1# asks #Person2# for their passport and asks if they are Chinese. #Person2# confirms and #Person1# welcomes them to Australia."}
{"answers": "#Person2# buys a pink T-shirt for $12.5 with #Person1#'s help.", "prediction_text": "#Person1# is working at a store and #Person2# is looking for a T-shirt. #Person1# shows #Person2# the different T-shirts and #Person2# chooses the pink one and pays for it."}
{"answers": "#Person2# buys two pairs of shoes in a similar style to #Person2#'s old shoes with #Person1#'s assistance.", "prediction_text": "#Person1# helps #Person2# find a new pair of shoes to replace their old ones. #Person2# tries on several pairs and decides on a light brown pair and a red pair. #Person1# offers to put both pairs in a box for #Person2#."}
{"answers": "#Person1# tells #Person2# #Person2#'s son is sometimes late for the class. #Person2# then realizes #Person2#'s son is lying and will punish him.", "prediction_text": "#Person1# reminds #Person2# that their son is sometimes late for class, but #Person2# denies it and says their son usually arrives early."}
{"answers": "Mrs. Brown gives #Person2# a physical check and diagnoses that #Person2# has a bad cold. Mrs. Brown suggests #Person2# get out of the air-conditioned offices as regularly as possible and do more exercise.", "prediction_text": "#Person1# examines #Person2# and diagnoses a bad cold caused by working in an air-conditioned office. #Person1# suggests that #Person2# get more exercise and fresh air to improve their health."}
{"answers": "David has left his position because of his rude boss. Susan becomes a successful dancer and plans to open a dance school.", "prediction_text": "David is leaving his job due to his boss's rudeness, while Susan is successful as a dancer and plans to open a dance school in the future."}
{"answers": "#Person1# and #Person2# exchange greetings. #Person2# tells #Person1# #Person2#'s going to PCC and #Person2# likes the classes there.", "prediction_text": "#Person1# and #Person2# are having a conversation, asking each other how they are doing and how school is going."}
{"answers": "#Person2# recommends the compact to #Person1# and introduces its features. #Person1# is satisfied with the price and then #Person2# suggests #Person1# have a test drive and think it over.", "prediction_text": "#Person1# is looking for a car, and #Person2# recommends a compact car with safety features such as anti-lock brakes, airbags, and impact collision design. #Person1# is concerned about safety and fuel economy, and #Person2# suggests taking the car for a test drive to think it over."}
{"answers": "#Person2# tells #Person1# about #Person2#'s auditory experience.", "prediction_text": "#Person1# asks #Person2# if they have experience in auditing work, and #Person2# replies that they have worked in the financial section of a university and a bank for a total of 6 years, and became an auditor within 3 months."}
{"answers": "#Person1#'s asking for opinions about #Person1#'s dressing. #Person2# thinks #Person1# looks too dressed up while Vicky thinks #Person1# should be confident.", "prediction_text": "#Person1# asks #Person2# and #Person3# for their opinion on how they look. #Person2# suggests a different outfit, and #Person3# advises #Person1# to be confident and wear what makes them feel comfortable."}
{"answers": "#Person1# waited for a long time to see #Person2#'s beautiful calligraphy. #Person2# today is writing a Chinese couplet and gives it away to #Person1#. #Person1#'s thrilled.", "prediction_text": "#Person1# waited in line for almost an hour to get to the front of the line and saw #Person2#'s calligraphy. #Person1# compliments #Person2#'s calligraphy and asks if it is for sale. #Person2# tells #Person1# that they never sell their artwork and gives it away instead."}
{"answers": "#Person1# and #Person2# learn that love built on the sand will soon be on the rocks from someone's divorce.", "prediction_text": "#Person1# and #Person2# talk about a couple who got a divorce. They say that their love was built on sand and that's why their marriage ended in rocks. They give a lesson about love built on sand."}
{"answers": "Elizabeth can speak French well. Since #Person1# has many Spanish customers, Elizabeth thinks she's fit for the position.", "prediction_text": "Elizabeth speaks French and Spanish, but her written Spanish is not good. Mr. #Person1# tells Elizabeth that she would not need to write any Spanish at work. Elizabeth thinks she is fit for the position."}
{"answers": "#Person2# tells #Person1# how to use the ticket machine and how to get to the Science Museum.", "prediction_text": "#Person1# is lost and needs help finding the Science Museum. #Person2# helps #Person1# use the ticket machine and tells him how often the trains come and where to get off the train."}
{"answers": "#Person1# and #Person2# are preparing a secret party for John and Anna who are getting married.", "prediction_text": "#Person1# asks #Person2# for the list of people coming to the party and #Person2# tells #Person1# that they have been told to keep the date a secret. #Person1# agrees to pick #Person2# up from the office and go there together."}
{"answers": "#Person2# tells #Person1# how to get to the nearest post office. Since it will 30 minutes if #Person1# walks, #Person1# decides to take a taxi.", "prediction_text": "#Person1# asks #Person2# for directions to the nearest post office. #Person2# gives detailed directions and tells #Person1# that it will take about 30 minutes to walk there. #Person1# decides to take a taxi instead."}
{"answers": "#Person1# tells #Person2# about #Person1#'s vacation plan to Canada.", "prediction_text": "#Person1# plans to visit Vancouver, Rocky Mountains, Toronto, Niagara Falls, and Nova Scotia on vacation."}
{"answers": "#Person2# is unhappy because #Person2#'s parents always want #Person2# to do what they wish regardless of #Person2#'s feelings. #Person1# thinks it's due to a generation gap.", "prediction_text": "#Person1# asks #Person2# why they are unhappy, and #Person2# replies that their parents always want them to do what they wish, regardless of their feelings. #Person1# explains the term \"generation gap\" and suggests that #Person2# try to understand their parents."}
{"answers": "Joe tells #Person1# about his trip to a lakeside house with his brother.", "prediction_text": "Joe and #Person2# went to a lakeside house with their family, and they had a good time. #Person2# enjoys long car journeys but found it tiring for their dad. They also had to go to a local shop on bicycles."}
{"answers": "#Person1# helps Nancy book a standard room and asks for her personal information.", "prediction_text": "Nancy Brown books a standard room at the hotel for December 23rd for four days, giving her name and phone number."}
{"answers": "#Person1# wants to return a pair of gloves but #Person2# tells #Person1# can only change them. #Person1# chooses a more expensive pair for which #Person1# will pay extra money.", "prediction_text": "#Person1# wants to return a pair of gloves because they are too small. #Person2# tells #Person1# that the store doesn't give cash refunds, but #Person2# can exchange the gloves for another pair. #Person1# tries on a new pair and decides to buy it."}
{"answers": "#Person1# introduces #Person1#'s course in automotive repair to #Person2# and tells #Person2# what they do on a work day. #Person2#'s interested and will register for the course.", "prediction_text": "#Person1# explains the program he teaches at a high school campus, which is a vocational course in automotive repair. #Person2# expresses interest in joining the course."}
{"answers": "#Person1# tells #Person2# they need to take the accounts system offline to carry out the upgrade. #Person2# asks the offline time and agrees.", "prediction_text": "#Person1# tells #Person2# that the accounts system will be taken offline for an upgrade over the weekend, and it will be down for at least twelve hours."}
{"answers": "#Person1# asks #Person2# to refill a prescription but #Person2# refuses her because it has been refilled twice. #Person1# insists as it's an emergency. #Person2# says it's illegal to sell medicine without a valid prescription and suggests #Person1# see another doctor. #Person2# says it's expensive to see a new doctor and thinks it is ridiculous as it's much easier to get medicine in Taiwan.", "prediction_text": "#Person1# needs to refill her prescription but the pharmacist tells her that the prescription has already been refilled twice and is against the law to sell the medicine without a proper prescription. #Person1# is frustrated and mentions that in Taiwan it is easier to get medicine when needed."}
{"answers": "#Person2# shares to #Person2# how to manage to be a good mother and a successful career woman.", "prediction_text": "#Person1# asks #Person2# if she considers herself a good mother and #Person2# replies that she is both a good mother and successful career woman. #Person1# wonders how she manages to do both and #Person2# suggests that being nice to oneself is important. #Person2#'s husband is also supportive."}
{"answers": "#Person2# is interviewed by #Person1# and provides a qualification, a technical post title, an original certificate, and one of #Person2#'s designs.", "prediction_text": "#Person1# asks #Person2# about their qualifications for the position and #Person2# explains their education and work experience. #Person2# shows their certificate and briefly describes one of their designs, a more powerful gasoline engine."}
{"answers": "#Person1# takes #Person2#'s taxi to the railway station. As #Person1# is not rush, #Person2# will drive slowly and carefully.", "prediction_text": "#Person1# wants to go to the railway station and asks the taxi driver how long it will take and if it's the rush hour. The driver confirms it is the rush hour and will take about 20 minutes to get there."}
{"answers": "#Person1# wants to get a red blouse though she has another one in blue.", "prediction_text": "#Person1# wants to buy a red blouse from a catalogue, but #Person2# suggests that #Person1# already has a blue one like it and jokes about women needing every color in the rainbow."}
{"answers": "#Person1# and #Person2# talk about their hobbies and spare-time entertainments.", "prediction_text": "#Person1# asks #Person2# about their hobbies and #Person2# replies that they like reading books and playing golf. #Person1# also asks about movies, but #Person2# declines. #Person1# mentions that they like to sleep, which #Person2# considers a bad habit."}
{"answers": "#Person1# and #Person2# are going to Beijing. #Person1# recalls the last trip if Beijing that #Person1# enjoyed the traditional culture and the vitality of Beijing but not the subway.", "prediction_text": "#Person1# and #Person2# are sitting next to each other on a train. #Person1# is going to Beijing on a business trip, while #Person2# is visiting a friend there on vacation. #Person1# has been to Beijing before and mentions the crowded subway during rush hour. #Person2# suggests taking the bus or renting a car instead."}
{"answers": "Irene tells #Person1# her dating partner David's appearance, height, weight, age, and job. #Person1# finds that David is #Person1#'s brother.", "prediction_text": "Irene tells #Person1# that she went on a date with a guy named David, who is tall, good looking, and has dark brown eyes. #Person1# is surprised to hear that David is Irene's date and her brother."}
{"answers": "#Person1# and #Person2# hold different views on show biz stars. #Person1# thinks they have an easy life, they love publicity to get them more films and social activities, they are overpaid, over-ambitious, and over-adored. But #Person2# shows sympathy to show biz stars and thinks #Person1# should give them some credit.", "prediction_text": "#Person1# thinks show business stars have an easy life, while #Person2# disagrees. #Person1# believes they have lots of money, fame, and publicity, while #Person2# thinks they have expenses and are over-ambitious."}
{"answers": "#Person1# and #Person2# discuss where to go on Friday night. #Person1# likes to dance and go to new bars but #Person2# wants to go to the Latin Club. They both hate Melissa's country rock and agree to go to the Latin Club. #Person1# wants to #Person2# teaches #Person1# the samba, tango, and Spanish.", "prediction_text": "#Person1# and #Person2# are deciding where to go on Friday night. #Person1# wants to go to the New Bar, but #Person2# prefers the Latin Club. #Person1# wants to learn Latin dance steps, but #Person2# thinks the New Bar will be better."}
{"answers": "#Person1# tells #Person2# the traits of #Person1#'s parents. #Person2# asks when #Person1# will see them, #Person1# thinks the time depends on #Person1#'s brother's schedule. #Person1# tells #Person2# #Person1# looks more like #Person1#'s mother.", "prediction_text": "#Person1#'s mom is three years older than their dad, and they are very different. #Person1#'s mom is 5'4\", nice, caring, and cute, while their dad is shorter and opposite in personality. #Person1# last talked to their parents two weeks ago and will see them soon for a Christmas celebration. #Person1# thinks they have traits of both their parents."}
{"answers": "#Person1# and #Person2# are watching a game and they are not satisfied with the referee.", "prediction_text": "#Person1# and #Person2# are watching a game together. #Person1# is upset about a call made by the referee, but #Person2# is more optimistic. #Person1# goes to get a beer."}
{"answers": "Billy teaches his grandpa to use an ATM card. The grandpa thinks it's not difficult to use these machines and it's faster than dealing with the bank clerk.", "prediction_text": "#Person1# asks #Person2# how to use an ATM card and #Person2# explains the process step by step. #Person1# finds it easy to use and prefers it to dealing with a bank clerk."}
{"answers": "#Person1# and #Person2# are taking on a bus. #Person1# doesn't drive #Person1#'s car because of the President and the gas price.", "prediction_text": "#Person1# and #Person2# are on a bus together, #Person1# has been on the bus for 15 minutes and #Person2# asks if #Person1# catches the bus often. #Person1# has their own car but is waiting for the gas prices to go down."}
{"answers": "#Person1# helps #Person2# ordering a baked fish and two apple pies.", "prediction_text": "#Person1# takes the order of #Person2# for baked fish in tomato sauce and apple pie."}
{"answers": "#Person2#'s looking for a DVD Player but #Person1# says it's not in stock for about two weeks.", "prediction_text": "#Person1# helps #Person2# find a Sharp DVD player, but informs #Person2# that the DS102 series is sold out and will be restocked in two weeks."}
{"answers": "#Person2# wants to pay with VISA but #Person2# says it was declined. #Person1# will be back tomorrow.", "prediction_text": "#Person1# tries to charge #Person2#'s purchase on their VISA card, but it is declined. #Person1# suggests using cash or returning the next day to pay."}
{"answers": "#Person2# first rejects Mike's magazine subscriptions because #Person2# is busy and not interested, but agrees and signs two cooking magazines and one magazine about pets after Mike's recommendations.", "prediction_text": "Mike is selling subscriptions to periodicals and tries to convince #Person2# to buy one. #Person2# is not interested at first but after Mike shows her a cooking magazine, she changes her mind and buys both the cooking and pet care magazines."}
{"answers": "#Person1# expresses to #Person2# the difference between the malls in China and the United States. #Person2# tells #Person1# the Americans have a habit of selling still new and useful things when moving and thinks it is similar to the yard sale in China.", "prediction_text": "#Person1# talks about how different the US is from China, mentions going to a mall and a flea market, and #Person2# explains the concept of a yard sale and how it is a good way to buy useful things."}
{"answers": "#Person2# is disturbed by roommates late at night and doesn't know how to stop them euphemistically. #Person1# advises #Person2# to tell them about it or make an agreement for keeping quiet. They both think having private rooms and sharing rooms have different advantages.", "prediction_text": "#Person1# and #Person2# are talking about #Person2#'s difficulty in telling his roommates to stop watching Korean soap operas late at night. #Person1# suggests telling them directly and making an agreement for quiet time, but #Person2# finds it hard to say. #Person1# shares his experience of living in a dorm and the importance of interpersonal skills."}
{"answers": "#Person2# tells #Person1# #Person2# wants to vote for the candidate for his intelligence and policies.", "prediction_text": "#Person1# asks #Person2# if they are going to vote on Tuesday, and #Person2# says they are excited and will vote for their preferred candidate because they think they are the most intelligent and have policies they agree with."}
{"answers": "#Person1# recommends #Person2# to buy a Mac for #Person2#'s daughter. #Person2# buys a Mac using VISA. #Person1# asks #Person2# to come back if #Person2#'s daughter needs accessories.", "prediction_text": "#Person1# wants to buy a laptop for their daughter and #Person2# suggests a Mac. #Person1# agrees and #Person2# helps them purchase it."}
{"answers": "#Person1# asks #Person2# how to handle a napkin at different dinners.", "prediction_text": "#Person1# asks #Person2# about the proper way to handle a napkin at dinner. #Person2# explains that at a formal dinner, you wait for your hostess to put hers on her lap first."}
{"answers": "#Person2# is helping #Person1# renting skis and boots and finding an instructor.", "prediction_text": "#Person1# wants to rent skis and boots and asks #Person2# for a ski instructor. #Person2# helps #Person1# find one."}
{"answers": "#Person2# introduces a department store, a clothing store next to the bank building, a theatre with signs of a new play, a hotel, and a post office.", "prediction_text": "#Person1# asks for directions in the shopping district and #Person2# provides information about the area, including the location of stores, office buildings, and theatres."}
{"answers": "#Person1# and #Person2# are waiting to order. They introduce each other as the summer school's students, the places they come from, and their experience with summer school.", "prediction_text": "#Person1# and #Person2# are at a restaurant and are unhappy with the service. #Person1# and #Person2# have a conversation about their classes and where they are from."}
{"answers": "#Person2# tells #Person1# the best part of the new job is the training programs. #Person1# hopes #Person1#'s company did so. #Person2# thinks it's a good thing in the long run.", "prediction_text": "#Person1# asks #Person2# about their new job and #Person2# mentions the benefits of the job, including better pay, shorter commute, and training programs."}
{"answers": "Johnny tells Wendy the city square will be turned into an amusement park. Johnny thinks they will lose their city's symbol and a quiet place for a walk, but Wendy thinks the park will create more income and jobs.", "prediction_text": "#Person1# and #Person2# are talking about the news of turning the city square into an amusement park. #Person1# is against the idea, thinking it will ruin the symbol of the city, while #Person2# thinks it will bring employment and income to the city."}
{"answers": "Micky asks Dad for some candy as a snack before dinner, but Dad refuses. They finally agree on having a small sandwich.", "prediction_text": "#Person1# wants a snack, but #Person2# suggests waiting for dinner. #Person1# suggests different snacks, but #Person2# is not pleased with any of them. #Person1# agrees to wait for a sandwich."}
{"answers": "#Person1# tells #Person2# their flight is delayed and suggests taking a nap.", "prediction_text": "#Person1# and #Person2#'s flight will be delayed, so they decide to find seats in the quiet part of the terminal to take a nap."}
{"answers": "#Person1# recommends a dress for #Person2#. #Person2# tries it and buys it.", "prediction_text": "#Person1# helps #Person2# find a green dress and tries it on. #Person1# tells #Person2# that the dress is on sale for $20. #Person2# decides to buy it."}
{"answers": "#Person2# buys a hardcover of Gone with the Wind for $25 with #Person1#'s help.", "prediction_text": "#Person1# helps #Person2# find the book \"Gone with the Wind\" and #Person2# buys the hardcover edition."}
{"answers": "It rains heavily, #Person2# shares an umbrella with #Person1#, and they go to the Garden Hotel.", "prediction_text": "#Person1# and #Person2# are walking together and #Person1# forgot her umbrella. #Person2# offers to share his umbrella and they are going to the Garden Hotel."}
{"answers": "Mary argued with Ann yesterday because Ann needed to cancel their trip and went to her boyfriend's plan. #Person1# tells Mary to be more understanding. Mary will call later to patch things up.", "prediction_text": "Mary and John have an argument with Ann about their plans to go to the beach. Mary is upset and John tells her to be more understanding and not let their friendship be affected by a trivial thing."}
{"answers": "Jenny thinks #Person1# is playing the field. #Person1# declares love to Jenny.", "prediction_text": "#Person1# denies cheating on #Person2# after #Person2# accuses #Person1# of playing the field."}
{"answers": "Lieb has a small cat and shows the pictures to #Person2#.", "prediction_text": "#Person1# and #Person2# are talking, and #Person2# shows #Person1# a picture of her new cat."}
{"answers": "#Person1# buys a watch that is convenient for right-handed people, so left-handed #Person2# won't borrow it.", "prediction_text": "#Person1# and #Person2# are talking about a watch. #Person1# mentions that the watch is meant to be worn on the left hand, and #Person2# jokes that #Person1# bought the watch to prevent #Person2# from borrowing it."}
{"answers": "#Person1# and #Person2# are talking about Rose's pregnancy and that Rose is under pressure from the old-fashioned elders.", "prediction_text": "#Person1# and #Person2# are talking about Rose's pregnancy. #Person1# hopes it's a boy, and #Person2# thinks baby girls are just as good as baby boys."}
{"answers": "#Person1# tells #Person2# #Person1#'s friend finally dumped her boyfriend. They both stand by her and she invites them to have dinner to show gratitude for helping her.", "prediction_text": "#Person1#'s friend broke up with her boyfriend and is taking time off to find the right man to marry. #Person1# is happy to help and is proud of herself. #Person2# doesn't want to be thanked but agrees to go to dinner with them."}
{"answers": "#Person1# tells #Person2# Susan got stomach cancer and she has lost all her hair.", "prediction_text": "Susan has stomach cancer and has lost all her hair due to medication side effects."}
{"answers": "#Person1# asks #Person2# to go paddling but #Person2# remembers today is #Person2#'s mother's birthday. #Person1# suggests #Person2# call her and wish her happy birthday and tells her #Person2# is out. Then they talk about their birthdays and later schedule.", "prediction_text": "#Person1# and #Person2# are camping on July 4th. #Person1# reminds #Person2# of the date and tells her to call their mom to wish her a happy birthday. #Person1# also gives #Person2# granola for breakfast and tells her they will take down their tent and pack their things into the canoe. #Person1# suggests going for a swim at 10 am."}
{"answers": "#Person1# points out a mistake on #Person1#'s bill.", "prediction_text": "#Person1# asks for the bill and #Person2# tells him the total is $20. #Person1# corrects #Person2# and tells him the total is $18."}
{"answers": "Mary gets cold. #Person1# hopes Mary gets better soon.", "prediction_text": "Mary is feeling unwell and #Person1# suggests she takes medicine and rests, hoping she will feel better soon."}
{"answers": "#Person1# tells #Person2# a cold joke about an Englishman, a Scotsman, and an Irishman taking some water, a map, and a car door on a trip across the desert.", "prediction_text": "#Person1# tells a joke about an Englishman, a Scotsman, and an Irishman going on a trip across the desert and bringing different things with them. The punchline is that the Irishman brought a car door, which he says they can use as a window if it gets hot."}
{"answers": "#Person1# asks #Person2# to take a message if there are calls for #Person1#.", "prediction_text": "#Person1# is at the airport to meet Mr. Dale and will be back around 5:30 PM. #Person2# asks how to reach #Person1# if there is urgent business."}
{"answers": "Alice explains to Adam she's late for the movie because of missing the bus and catching in a traffic jam. Adam thinks they can still have dinner together.", "prediction_text": "#Person1# is late to meet #Person2# for dinner because of traffic and missing the bus after staying late at work to finish some urgent letters. #Person2# is understanding but #Person1# apologizes."}
{"answers": "#Person2# has trouble taking notes because of hurting hands. #Person1# suggests #Person2# photo copy someone else's notes or record the classes.", "prediction_text": "#Person1# suggests that #Person2# could use a tape recorder to take notes in class instead of writing, as #Person2# has hurt their hand and finds it painful to write."}
{"answers": "#Person1#'s clock didn't alarm and will be late for the school field trip. #Person2# suggests #Person1# taking Mrs. Anderson's ride.", "prediction_text": "#Person1# is worried about being late for school, but #Person2# suggests that #Person1# can go with Mrs. Anderson and her son Billy since they are already on their way. #Person1# is relieved."}
{"answers": "#Person1# asks #Person2# the places for amusement. #Person2# recommends the garden for taking a walk, a Recreation Center for playing sports, and a music teahouse for enjoying music and drinks.", "prediction_text": "#Person1# asks about places to relax in the hotel and #Person2# suggests the garden and the Recreation Center, and also mentions a music teahouse where #Person1# can relax and enjoy music."}
{"answers": "After three years of cooperation, #Person1# applies for the sole agency of David's company's product in the local market. #Person1# tells David about #Person1#'s company's advantages and the minimum annual sales they can guarantee and promises to follow the sole agency's principles.", "prediction_text": "David wants to apply for the sole agency of the product in the local market and tells David about the cooperation in the previous years. David tells David that he will tell his boss about the agent application and asks David about the minimum annual sales that can be guaranteed. David tells David that he will not handle the same or similar products of other origins nor re-export the goods to any other area outside his own."}
{"answers": "#Person2# shows #Person1# the way to the nearest cinema.", "prediction_text": "#Person1# asks #Person2# for directions to the nearest cinema. #Person2# tells #Person1# to turn left at the second light and assures #Person1# that it's not far at all."}
{"answers": "Bill received Christmas e-cards from his friends. Jina asks for Haven's number. Bill offers Haven's email instead.", "prediction_text": "#Person1# and #Person2# are exchanging Christmas greetings. #Person1# is looking for Haven's number and #Person2# offers to send it to him."}
{"answers": "#Person1# and #Person2# agree that no theater pays attention to the ratings of movies anymore. Now with video rentals, it's all a personal matter.", "prediction_text": "#Person1# and #Person2# discuss the movie ratings and how they are not enforced as strictly as they used to be."}
{"answers": "#Person2# helps #Person1# get a quiet single room without reservation and pay the deposit.", "prediction_text": "#Person1# wants to book a single room with a bath and asks about the cost. #Person2# tells him the cost and that it includes a 10% service charge. #Person1# agrees to take the room and fills in the registration form. #Person2# tells him that as a hotel policy, they require one day's room charge as a deposit for guests without reservation."}
{"answers": "#Person1# decides to get #Person2# to an emergency room as #Person2# has acute stomachache without a history of stomach pain.", "prediction_text": "#Person1# helps #Person2# who is experiencing stomach pain and suggests going to an emergency room."}
{"answers": "#Person1#, as a Californian, thinks Taiwan's winter is cold. #Person2# suggests #Person1# get some more warm clothes.", "prediction_text": "#Person1# is from south California and is experiencing her first winter in Taiwan. #Person2# tells her that the temperature is about 5 degree Celsius."}
{"answers": "#Person1# helps #Person2# order some seafood and vegetables.", "prediction_text": "#Person1# is a waiter at a restaurant. #Person2# is a customer who wants to order food. #Person1# shows #Person2# the menu and recommends the seafood. #Person2# decides to order seafood and vegetables."}
{"answers": "Craig has years of English learning experience, but he can't speak English well due to a lack of practice. #Person1# invites Craig to a party tonight.", "prediction_text": "#Person1# asks #Person2# about their work and studies, and #Person2# replies that they are still a student at Boston University, studying English, math, and history, and that they have been studying English for more than six years, but they don't have many friends yet. #Person1# invites #Person2# to a party that night."}
{"answers": "B solves pressure by reading sports information while Fred goes to the bar when he's overtired.", "prediction_text": "#Person1# asks #Person2# what he is holding, and #Person2# replies that he reads the Sunday papers for relaxation since he is a sports fan. #Person1# agrees and suggests having a drink together. #Person2# declines."}
{"answers": "#Person1# asks #Person2# to be far away from a man. #Person2# agrees.", "prediction_text": "#Person1# and #Person2# are talking about someone who is not kind. #Person1# warns #Person2# to be careful and not get involved with him."}
{"answers": "#Person1# thinks #Person2#'s cousin is a sarcastic but fun lady.", "prediction_text": "#Person1# is thankful for a rosary given to them by #Person2#'s grandmother. #Person2# explains the meaning behind the phrase \"raising the pigs, raising the kids\" and talks about their cousin's personality."}
{"answers": "#Person1# and #Person2# hold different ideas about how espresso got its name.", "prediction_text": "#Person1# and #Person2# are at a car showroom. #Person1# is impressed by a car, and #Person2# explains the origin of the word \"espresso\"."}
{"answers": "#Person1# and #Person2# talk about their busy work and their welfare of traveling.", "prediction_text": "#Person1# and #Person2# are talking about their work and salary. #Person1# mentions that they work overtime and travel twice a year, while #Person2# expresses surprise that they only have one chance to travel per year."}
{"answers": "#Person1# wants to live on campus. #Person2# puts #Person1# on a waiting list and gives #Person1# a catalog with locations that meet campus requirements.", "prediction_text": "#Person1# wants to get housing on campus but is told that everything is full. #Person2# offers to put #Person1# on a waiting list and provides a catalog of available apartments in the area."}
{"answers": "Both #Person1# and #Person2# have been interrupted by Kate to talk about Kate's new boyfriend.", "prediction_text": "Kate called #Person1# last night and was too excited to go to sleep. #Person2# knows about Kate's new boyfriend."}
{"answers": "#Person2# answers #Person1#'s questions of whether the galaxies in the universe are moving through space and how the galaxies collide. #Person1# thinks #Person2# is informative.", "prediction_text": "#Person1# asks #Person2# if galaxies are moving through space, and #Person2# explains that galaxies are not moving through space but are carried apart by the expanding space between them. #Person1# then asks about the possibility of galaxy collisions, and #Person2# explains that while galaxies may collide due to gravitational disturbances, on average, galaxies are moving away from each other in large chunks of space."}
{"answers": "#Person1# and #Person2# want to choose a record player. #Person2# suggests consulting a salesman.", "prediction_text": "#Person1# and #Person2# are looking for a record player. #Person1# prefers one with separated speakers, while #Person2# suggests getting one that is not too big and fits with their living room furniture."}
{"answers": "#Person2# tells #Person1# the first year of #Person2#'s marriage with a foreigner was difficult. #Person2# also talks about her son.", "prediction_text": "#Person1# asks #Person2# about their marriage and #Person2# explains that they had difficulty getting along in the first year, but now they are happy with their two-year-old son who is half Chinese and half American and speaks mostly Chinese."}
{"answers": "#Person1# asks #Person2# about #Person2#'s birthday and the starting time of the film.", "prediction_text": "#Person1# asks #Person2# about their birthdate and the lunar calendar, and #Person2# tells #Person1# that the film will begin at 5:30 PM. #Person1# is unable to attend the film on time and decides to watch it the next day instead."}
{"answers": "Harry tells #Person1# why Harry doesn't like the opera.", "prediction_text": "Harry doesn't like the opera because it is expensive and the singers are singing in another language."}
{"answers": "#Person1# teaches #Person2# to play golf.", "prediction_text": "#Person1# explains the game of golf to #Person2#, showing how to place the ball on a tee and hit it with a golf club."}
{"answers": "Mary and #Person1# recall the first time they met.", "prediction_text": "Mary and #Person1# reminisce about when they first met."}
{"answers": "#Person2# enjoys #Person2#'s weekend at the highland hotel because of the hotel's excellent and reasonably priced restaurant and good service. #Person2# introduces the hotel's facilities, weekend discount, and its interesting tip policy and suggests #Person1# make a reservation in advance.", "prediction_text": "#Person1# asks #Person2# about their weekend stay at the Highland Hotel and #Person2# provides details about the hotel's facilities, service, and policy. #Person1# expresses frustration with tipping at hotels."}
{"answers": "#Person2# introduces a laptop to #Person1#. #Person1# is impressed and asks #Person2# about the sale price.", "prediction_text": "#Person1# wants to see the laptop on the left, #Person2# tells him about the laptop's features and tells him the sale price."}
{"answers": "#Person2# assists #Person1# to buy new furniture for #Person1#'s living room. #Person1# decides to take a black leather suite and two floor lamps. #Person1# then wants to browse through some cushion covers.", "prediction_text": "#Person1# is looking for new furniture for their living room and #Person2# helps them find the perfect suite and suggests additional items such as floor lamps and cushion covers."}
{"answers": "#Person2# helps #Person1# find a timesheet and teaches #Person1# to total hours, sign it, and turn it to the supervisor.", "prediction_text": "#Person1# needs help with filling out a timesheet and #Person2# explains how to do it, including using military time and signing and turning it in to the supervisor."}
{"answers": "#Person1# tells #Person2# #Person2# missed #Person2#'s stop, but it's not far to walk back.", "prediction_text": "#Person1# and #Person2# are on a subway. #Person2# realizes they got off at the wrong stop and will get off at the next stop and walk back."}
{"answers": "#Person1# thinks Mrs. Schmidt is so health-conscious. They share their own ways to stand on their heads.", "prediction_text": "#Person1# and #Person2# are health-conscious and #Person1# drinks carrot juice made by #Person2# before running to class."}
{"answers": "#Person1# recommends #Person2# to order desserts. #Person2# orders an apple crisp, a piece of chocolate mousse cake, and hot tea.", "prediction_text": "#Person1# and #Person2# are at a restaurant and #Person1# suggests a dessert. #Person2# chooses the spicy rum apple crisp and requests four dessert forks and four teas."}
{"answers": "#Person2# wants to keep fit. #Person1# introduces their customer-made work-out services to her. The woman finally chooses the membership for one month for a try.", "prediction_text": "#Person1# is a fitness center employee who is explaining the services they offer to #Person2#, who is interested in getting fit. #Person1# explains that they will tailor a work-out plan according to the customer's physical conditions and personal needs, and that they will provide a qualified personal trainer to help the customer find suitable exercise equipment and techniques. #Person2# decides to try it out for one month and pays 400 yuan."}
{"answers": "Mr. Parsons gives Rebecca his business card after the interview and tells Rebecca the decision will be made by early next week and Miss Childs will contact Rebecca.", "prediction_text": "Mr. Parsons gives Rebecca his business card after the interview and tells Rebecca the decision will be made by early next week and Miss Childs will contact Rebecca."}
{"answers": "#Person2#, a young customer, wants to become just like Bruce Lee. #Person1# suggests trying the treadmill or the skipping rope, but #Person2# leaves because #Person1# asks #Person2# change #Person2#'s clothes first.", "prediction_text": "#Person1# meets a young boy named #Person2# who wants to be like Bruce Lee. #Person1# tells #Person2# that he needs to grow up and suggests trying the treadmill or the skipping rope. #Person2# is not interested in changing his clothes and leaves."}
{"answers": "Susan calls Mr. Brown to ask why Ted missed the school trip. She suggests Mr. Brown change Ted's sleeping habits.", "prediction_text": "Susan calls Mr. Brown to ask why Ted missed the school trip. Mr. Brown explains that Ted got up late because the clock didn't work and he worked overtime last night, causing him to wake up late too. Susan tells Mr. Brown that he should change Ted's sleeping habits to avoid similar situations in the future."}
{"answers": "John wants to be a doctor after graduation while his dad wants him to work in information technology.", "prediction_text": "#Person1# asks John what he plans to do after graduation, and John says his father suggests working in information technology. John is interested in medicine and is good at math and science."}
{"answers": "#Person1# assists Mr. Bradley to fill out a form to check-in. #Person1# tells Mr. Bradley his room number, reminds Mr. Bradley to always carry the multifunctional key card, and then calls a bell-boy to help Mr. Bradley.", "prediction_text": "John Bradley makes a reservation for a single room with a bath at the hotel. #Person1# helps John Bradley fill out the form and gives him the key card with all the information on his booking, the hotel services, and the hotel rules and regulations. #Person1# calls the bell-boy to take John Bradley to his room."}
{"answers": "#Person1# asks #Person2# about the location of the post office. #Person2# tells #Person1# the nearest one and how to get there.", "prediction_text": "#Person1# asks for directions to the Post Office from #Person2#, who tells him the distance and how to get there. #Person1# thanks #Person2# for the help and goes on his way."}
{"answers": "#Person1# interviews Lisa Lynch about her opinion on the big leap in new jobs created last month. Lisa says the report is positive because it shows the job market's improvement after three lean years. All industries including catering, health care, and services experience positive growth, except for manufacturing which has neither job loss nor picking up.", "prediction_text": "Lisa Lynch, an economics professor at Tofts University, analyzes the new job numbers and finds that job creation is on the rise, with improvement seen across many industries except for manufacturing."}
{"answers": "#Person1# wants to add some Chinese traditional elements in the decoration of #Person1#'s new house. #Person2# gives #Person1# some suggestions. #Person1# hasn't decided on the sofa and the color of the wall yet.", "prediction_text": "#Person1# wants to add Chinese traditional elements to their new house, and #Person2# suggests using red as the most representative color in China. #Person1# agrees and also likes to use lattice for the door and windows. #Person2# suggests hanging Chinese landscape paintings and painting the walls with fresh colors. #Person1# is unsure about the sofa color."}
{"answers": "#Person2# complains about #Person2#'s roommate, Charlie, to #Person1# and asks #Person1# to talk to Charlie.", "prediction_text": "#Person1# asks #Person2# about their issues with Charlie, and #Person2# explains that they have tried talking to Charlie but it didn't work, so #Person1# offers to help."}
{"answers": "#Person1# tells Tom he and Julia got married hurriedly but they are happy.", "prediction_text": "Tom is surprised to hear that #Person1# married Julia last month, and #Person1# explains that they got married too soon. #Person2# congratulates #Person1# and says that marriage comes by destiny, and #Person1# agrees. #Person2# asks about #Person1#'s married life and #Person1# replies that they are happy together."}
{"answers": "#Person1# tells #Person2# what Bill Gate's home like. It's got its own library, theatre, swimming pool, and a guest house. #Person1# doesn't want to live there because it's too big to take care of by one person. #Person1# prefers old cottages in a quiet village in England because they've got characters.", "prediction_text": "#Person1# talks about Bill Gates' home, saying it has a library, theatre, swimming pool, and guest house. #Person1# thinks it's amazing but wouldn't want to live there because of the size and staff required. #Person1# dreams of living in a small cottage in a quiet village in England, preferring old homes with character. #Person1# also talks about wearing second-hand clothes because of the lack of money. #Person1# would restore an old house to its original state to feed their imagination."}
{"answers": "#Person2# orders tomato juice, a dish of cream of wheat, crisp bacon, and fried eggs.", "prediction_text": "#Person1# is a waiter at a restaurant. #Person2# orders tomato juice, cream of wheat, bacon and eggs with buttered toast, and fried eggs."}
{"answers": "#Person2# helps #Person1# put two tables together to make it for 8 people.", "prediction_text": "#Person1# wants to know if there is a table for 8 available, but #Person2# tells him that the tables are booked. #Person1# asks if they can put two small tables together, and #Person2# agrees to help."}
{"answers": "#Person1# complains to #Person2# about her boss's sexist comments. #Person1# is going to report him for sexual harassment. #Person2# thinks #Person1# is overreacting and she can't change her boss.", "prediction_text": "#Person1# is fed up with their boss's sexist comments and decides to report them for sexual harassment. #Person2# thinks #Person1# is overreacting and that it's common for male bosses in #Person1#'s industry to treat females this way."}
{"answers": "#Person1# decides to stop smoking and come out of the closet in the new year. #Person2# supports #Person1#.", "prediction_text": "#Person1# has decided to turn over a new leaf in the new year, including quitting smoking and coming out of the closet. #Person2# supports #Person1#'s decisions."}
{"answers": "#Person1# thought #Person2# married Joe. #Person2# denies.", "prediction_text": "#Person1# thinks #Person2# is married to Joe, but #Person2# denies it and says she has a crush on him but it comes and goes."}
{"answers": "#Person2#'s car makes noises. #Person1# thinks it needs new brakes but #Person1# can't get it fixed until tomorrow. #Person2# wants to drive to a show tonight; #Person1# suggests #Person2# take a bus.", "prediction_text": "#Person1# helps #Person2# with their car, which is making strange noises. #Person1# tells #Person2# that they need new brakes and will need to leave the car with them until tomorrow. #Person2# is disappointed but agrees to bring the car back in the morning."}
{"answers": "#Person2# calls Amazon's customer service because one page is missing in the book he got from Amazon. #Person1# says they will send him a new book after the problem is confirmed.", "prediction_text": "#Person1# is an Amazon customer service representative. #Person2# calls to report that one page is missing from the book he received from Amazon. #Person1# asks for the order number and checks the information before telling #Person2# to take a photo of the missing page and upload it to the website. #Person1# tells #Person2# to keep the book and ends the call."}
{"answers": "#Person2# tells #Person1# #Person2# is going to work for a company helping at parties during the summer vacation. #Person1# thinks that's a cool job.", "prediction_text": "#Person1# asks #Person2# what they will do this summer vacation, and #Person2# tells #Person1# that they will work for a company that prepares and serves food at parties. #Person2# explains their job duties, which include setting up before the party, serving food and drinks during the party, and cleaning up afterward. #Person1# thinks it sounds like a cool job."}
