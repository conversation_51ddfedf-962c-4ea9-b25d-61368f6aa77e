{"answers": "<PERSON><PERSON> <PERSON> helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "prediction_text": "The new policy restricts all office communications to email correspondence and official memos. The use of Instant Messaging programs by employees during working hours is strictly prohibited. Employees who persist in using Instant Messaging will first receive a warning and be placed on probation. At second offense, the employee will face termination. Any questions regarding this new policy may be directed to department heads."}
{"answers": "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks <PERSON><PERSON> <PERSON> to send out a memo to all employees by the afternoon.", "prediction_text": "The new policy restricts all office communications to email correspondence and official memos. The use of Instant Messaging programs by employees during working hours is strictly prohibited. Employees who persist in using Instant Messaging will first receive a warning and be placed on probation. At second offense, the employee will face termination. Any questions regarding this new policy may be directed to department heads."}
{"answers": "Ms<PERSON> <PERSON> takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists.", "prediction_text": "The new policy restricts all office communications to email correspondence and official memos. The use of Instant Messaging programs by employees during working hours is strictly prohibited. Employees who persist in using Instant Messaging will first receive a warning and be placed on probation. At second offense, the employee will face termination. Any questions regarding this new policy may be directed to department heads."}
{"answers": "#Person2# arrives late because of traffic jam. #Person1# persuades #Person2# to use public transportations to keep healthy and to protect the environment.", "prediction_text": "#Person1#: You're finally here! What took so long?"}
{"answers": "#Person2# decides to follow #Person1#'s suggestions on quitting driving to work and will try to use public transportations.", "prediction_text": "#Person1#: You're finally here! What took so long?"}
{"answers": "#Person2# complains to #Person1# about the traffic jam, #Person1# suggests quitting driving and taking public transportation instead.", "prediction_text": "#Person1#: You're finally here! What took so long?"}
{"answers": "#Person1# tells Kate that Masha and Hero get divorced. Kate is surprised because she thought they are perfect couple.", "prediction_text": "The story is about a couple who are getting divorced. The story is told from the point of view of the wife. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a"}
{"answers": "#Person1# tells Kate that Masha and Hero are getting a peaceful divorce. Kate feels surprised and asks about their kids.", "prediction_text": "The story is about a couple who are getting divorced. The story is told from the point of view of the wife. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a very simple way, with no unnecessary details. The story is told in a"}
{"answers": "#Person1# and Kate talk about the divorce between Masha and Hero. Kate feels surprised because she thought they are well matched", "prediction_text": "The story is about a couple who are getting divorced. The story is told from the point of view of the husband."}
{"answers": "#Person1# and Brian are at the birthday party of Brian. Brian thinks #Person1# looks great and is popular.", "prediction_text": "A man and a woman are having a party. The man is happy to see his friend, but the woman is not. The man asks the woman to dance with him. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to"}
{"answers": "#Person1# attends Brian's birthday party. Brian thinks #Person1# looks great and charming.", "prediction_text": "A man and a woman are having a party. The man is happy to see his friend, but the woman is not. The man asks the woman to dance with him. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to"}
{"answers": "#Person1# has a dance with Brian at Brian's birthday party. Brian thinks #Person1# looks great and is popular.", "prediction_text": "A man and a woman are having a party. The man is happy to see his friend, but the woman is not. The man asks the woman to dance with him. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to dance with him again. The woman agrees. The man asks the woman to"}
{"answers": "#Person1# is surprised at the Olympic Stadium'volume, capacity and interior setting to #Person1#.", "prediction_text": "The Olympic stadium is so big that it is hard to imagine."}
{"answers": "#Person2# shows #Person1# around the constructing Olympic stadium and introduces the stadium.", "prediction_text": "The Olympic stadium is so big that it is hard to imagine."}
{"answers": "#Person2# introduces the Olympic Stadium's finishing time, capacity and interior setting to #Person1#.", "prediction_text": "The Olympic stadium is so big that it is hard to imagine."}
{"answers": "#Person1# wants to create a company and is going to write a business plan. #Person2# gives #Person1# suggestions on how to summarise business ideas, describe the service, differ from competitors and attract investment in a good business plan. #Person1# decides to stick to the old job.", "prediction_text": "A business plan is a document that outlines the details of a business idea. It is a written document that includes a description of the business, its goals, and how it will be run. It is a document that is used to help investors understand the business and to help the business owner understand the business. A business plan is a document that is used to help investors understand the business and to help the business owner understand the business."}
{"answers": "#Person1# abandons the idea of creating a company after #Person2# explains what a business plan includes. #Person2# specifies that a well-written business plan includes an executive summary, goods and service description, strategy and implementation, and financial analysis.", "prediction_text": "A business plan is a document that outlines the details of a business idea. It is a written document that includes a description of the business, its goals, and how it will be run. It is a document that is used to help investors understand the business and to help the business owner understand the business. A business plan is a document that is used to help investors understand the business and to help the business owner understand the business."}
{"answers": "#Person1# wants to start #Person1#'s own business, but #Person2# warns #Person1# of the hassle. #Person2# tells #Person1# what is needed in a business plan and #Person1# decides to stick to the old job for now.", "prediction_text": "A business plan is a document that outlines the details of a business idea. It is a written document that includes a description of the business, its goals, and how it will be run. It is a document that is used to help investors understand the business and to help the business owner understand the business. A business plan is a document that is used to help investors understand the business and to help the business owner understand the business."}
{"answers": "#Person2# feels itchy. #Person1# doubts it is chicken pox and asks #Person2# to get away. #Person2# doesn't believe it.", "prediction_text": "A man and a woman are talking about their children. The man says that his son is a biohazard and that he is contagious. The woman says that she thinks that her daughter has chicken pox. The man says that he thinks that she is allergic to something. The woman says that she thinks that she is coming down with something. The man says that she is a biohazard and that she is contagious. The woman says that she thinks that she is coming down with something. The man says that she is a biohazard and that she is contagious. The woman says that she thinks that she is coming down with something. The man says that she is a biohazard and that she is contagious. The woman says that she"}
{"answers": "#Person1# suspects that #Person2# has chicken pox and wants to keep away from #Person2#. #Person2# thinks #Person1# is exaggerating.", "prediction_text": "A man and a woman are talking about their children. The man says that his son is a biohazard and that he is contagious. The woman says that she thinks that her daughter has chicken pox. The man says that he thinks that she is allergic to something. The woman says that she thinks that she is coming down with something. The man says that she is a biohazard and that she is contagious. The woman says that she thinks that she is coming down with something. The man says that she is a biohazard and that she is contagious. The woman says that she thinks that she is coming down with something. The man says that she is a biohazard and that she is contagious. The woman says that she"}
{"answers": "#Person1# thinks #Person2# has chicken pox and warns #Person2# about the possible hazards but #Person2# thinks it will be fine.", "prediction_text": "A man and a woman are talking about their children. The man says that his son is a little bit of a troublemaker. The woman says that her daughter is a little bit of a troublemaker. The man says that his daughter is a little bit of a troublemaker. The woman says that her son is a little bit of a troublemaker. The man says that his son is a little bit of a troublemaker. The woman says that her son is a little bit of a troublemaker. The man says that his son is a little bit of a troublemaker. The woman says that her son is a little bit of a troublemaker. The man says that his son is a little bit of a troublemaker. The woman says that"}
{"answers": "#Person2# is checking out and asks #Person1# for the bill. #Person1# gives #Person2# a wrong bill at first then corrects it.", "prediction_text": "The hotel staff is very helpful. They are very polite and friendly. They are very professional. They are very efficient. They are very good at their job. They are very good at their work. They are very good at their job. They are very good at their work. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their"}
{"answers": "#Person1# helps #Person2# correct a mischarged bill on laundry service and helps #Person2# check out.", "prediction_text": "The hotel staff is very helpful. They are very polite and friendly. They are very professional. They are very efficient. They are very good at their job. They are very good at their work. They are very good at their job. They are very good at their work. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their"}
{"answers": "#Person2# finds #Person2# being mischarged. #Person1# corrects the bill and #Person2# pays for it.", "prediction_text": "The hotel staff is very helpful. They are very polite and friendly. They are very professional. They are very efficient. They are very good at their job. They are very good at their work. They are very good at their job. They are very good at their work. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their job. They are very good at their"}
{"answers": "#Person1# is begging Steven to persuade his wife not to divorce him. Steven agrees.", "prediction_text": "A man is in love with his secretary. He is afraid that his wife will find out about his affair. He is trying to convince his wife to stay with him."}
{"answers": "Steve will try to persuade #Person1#'s wife not to divorce #Person1# as #Person1# swears to remain faithful forever.", "prediction_text": "A man is in love with his secretary. He is afraid that his wife will find out about his affair. He is trying to convince his wife to stay with him."}
{"answers": "#Person1# begs Steven's to persuade #Person1#'s wife not to divorce him, and Steven agrees.", "prediction_text": "A man is in love with his secretary. He is afraid that his wife will find out about his affair. He is trying to convince his wife to stay with him."}
{"answers": "#Person1# and #Person2# are talking about Abraham Lincoln. They think he was a noble man.", "prediction_text": "Abraham Lincoln was a great man. He was a great president, and he was a great man. He was a great man because he was a great president. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great"}
{"answers": "#Person2# admires Abraham Lincoln for his perseverance, courage and humility.", "prediction_text": "Abraham Lincoln was a great man. He was a great president, and he was a great man. He was a great man because he was a great president. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great"}
{"answers": "#Person1# and #Person2# talk about Abraham Lincoln and his glorious history. They both admire him.", "prediction_text": "Abraham Lincoln was a great man. He was a great president, and he was a great man. He was a great man because he was a great president. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great man. He was a great man because he was a great"}
{"answers": "#Person2# plans to have a trip in Hebei but #Person1# says there are sandstorms in there.", "prediction_text": "The report said that the number of people with respiratory tract infections tended to rise after sandstorms. The sand gets into people's noses and throats and creates irritation."}
{"answers": "#Person1# tells #Person2# Hebei was experiencing severe sandstorms and sandstorms threaten people's health.", "prediction_text": "The report said that the number of people with respiratory tract infections tended to rise after sandstorms. The sand gets into people's noses and throats and creates irritation."}
{"answers": "#Person2# wants to travel to Hebei but #Person1# informs #Person2# of terrible sandstorms there.", "prediction_text": "The report said that the number of people with respiratory tract infections tended to rise after sandstorms. The sand gets into people's noses and throats and creates irritation."}
{"answers": "#Person2# comes to the birthday party of Francis and sends him a remote car model.", "prediction_text": "The two people are having a birthday party. The first person is giving a gift to the second person. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift"}
{"answers": "#Person2# gives Francis a car model that both of them love as a birthday gift.", "prediction_text": "The two people are having a birthday party. The first person is giving a gift to the second person. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift"}
{"answers": "#Person2# gives Francis a nice car model as a birthday gift and Francis appreciates it.", "prediction_text": "The two people are having a birthday party. The first person is giving a gift to the second person. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift. The first person is happy to give the gift. The second person is happy to receive the gift"}
{"answers": "Tony got caught cheating and feels ashamed. Steven tells him to learn from it and study hard.", "prediction_text": "A student is caught cheating and feels ashamed. He feels that his father will kill him if he fails. He feels that he should study hard. He feels that he should learn from his mistakes."}
{"answers": "Tony tells Steven that he failed a class because of cheating and he feels awful.", "prediction_text": "A student is caught cheating and feels ashamed. He feels that his father will kill him if he fails. He feels that he should study hard. He feels that he should learn from his mistakes."}
{"answers": "Tony tells Steven that he cheated on the exam and realized his mistake.", "prediction_text": "A student is caught cheating and feels ashamed. He feels that his father will kill him if he fails. He feels that he should study hard. He feels that he should learn from his mistakes."}
{"answers": "#Person1# is catching a train. Tom asks #Person1# not to hurry.", "prediction_text": "A man and a woman are having a conversation. The man is late for a train. The woman is worried about him. She asks him what time it is. The man says it is ten to nine. The woman says it is ten to nine by her watch. The man says he must catch the nine-thirty train. The woman says she must catch the nine-thirty train. The man says he must catch the nine-thirty train. The woman says she must catch the nine-thirty train. The man says he must catch the nine-thirty train. The woman says she must catch the nine-thirty train. The man says he must catch the nine-thirty train. The woman says she must catch the nine-thirty train."}
{"answers": "#Person1# is in a hurry to catch a train. Tom tells #Person1# there is plenty of time.", "prediction_text": "A man and a woman are having a conversation. The man is late for a train. The woman is worried about him. She asks him what time it is. The man says it is ten to nine. The woman says it is ten to nine by her watch. The man says he must catch the nine-thirty train. The woman says she must catch the nine-thirty train. The man says he must catch the nine-thirty train. The woman says she must catch the nine-thirty train. The man says he must catch the nine-thirty train. The woman says she must catch the nine-thirty train. The man says he must catch the nine-thirty train. The woman says she must catch the nine-thirty train."}
{"answers": "#Person1# is rushing to catch a train but Tom thinks it isn't necessary.", "prediction_text": "A man and a woman are having a conversation. The man is late for a train. The woman is worried about him. She asks him what time it is. The man says it is ten to nine. The woman says it is ten to nine by her watch. The man says he must catch the nine-thirty train. The woman says she must catch the nine-thirty train. The man says he must catch the nine-thirty train. The woman says she must catch the nine-thirty train. The man says he must catch the nine-thirty train. The woman says she must catch the nine-thirty train. The man says he must catch the nine-thirty train. The woman says she must catch the nine-thirty train."}
{"answers": "#Person1# wants to adjust #Person1#'s life and #Person2# suggests #Person1# be positive and stay healthy.", "prediction_text": "The two people are talking about their problems. The first person says that he can't sleep well every night, and the second person says that he often feels tired. The first person says that he drinks a lot of wine, and the second person says that he should exercise every morning. The first person says that he often feels the shadow of death in front of him, and the second person says that he should take his advice. The first person says that he is young and will make great contribution to the world, and the second person says that he should take his advice."}
{"answers": "#Person1# has a bad lifestyle. #Person2# kindly advises #Person1# to be positive.", "prediction_text": "The two people are talking about their problems. The first person says that he can't sleep well every night, and the second person says that he often feels tired. The first person says that he drinks a lot of wine, and the second person says that he should exercise every morning. The first person says that he often feels the shadow of death in front of him, and the second person says that he should take his advice. The first person says that he is young and will make great contribution to the world, and the second person says that he should take his advice."}
{"answers": "#Person2# hopes #Person1# will become healthy and positive.", "prediction_text": "The two people are talking about their problems. The first person says that he is not able to sleep well at night. The second person says that he should get plenty of sleep. The first person says that he drinks a lot of wine. The second person says that he should not drink too much. The first person says that he often feels tired. The second person says that he should do some exercise every morning. The first person says that he sometimes finds the shadow of death in front of him. The second person says that he should not worry about his future. The first person says that he is very young and he will make great contribution to the world. The second person says that he hopes that he will take the advice of the first person."}
{"answers": "#Person1# tells #Person2# that Ruojia is married and will have a party tonight. #Person2#'s surprised to know that. They will bring their gifts to bless her.", "prediction_text": "The two friends are talking about Ruojia's marriage. #Person1# says that he saw the news from Ruojia's twitter and that she sent an email about it. #Person2# says that he didn't receive it. #Person1# says that he will bring a pair of wineglasses and a card to wish her happy marriage. #Person2# says that he will buy a tea set."}
{"answers": "#Person2# is surprised to know from #Person1# that Ruojia is married. Then #Person2# finds Ruojia has sent an email about it. They will go to Ruojia's party and give their presents to her.", "prediction_text": "The two friends are talking about Ruojia's marriage. #Person1# says that he saw the news from Ruojia's twitter and that she sent an email about it. #Person2# says that he didn't receive it. #Person1# says that he will bring a pair of wineglasses and a card to wish her happy marriage. #Person2# says that he will buy a tea set."}
{"answers": "#Person2# is surprised that Ruojia's married. #Person1# and #Person2# will go to her party and give their presents to wish her a happy marriage.", "prediction_text": "The two friends are talking about Ruojia's marriage. #Person1# says that he saw the news from Ruojia's twitter and that she sent an email about it. #Person2# says that he didn't receive it. #Person1# says that he will bring a pair of wineglasses and a card to wish her happy marriage. #Person2# says that he will buy a tea set."}
{"answers": "#Person2# at first thinks #Person1#'s behaviour cruel but finally joins #Person1#.", "prediction_text": "A man and a woman are in a car. The man is driving. The woman is sitting in the back seat. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The"}
{"answers": "#Person1# plans on playing a trick to others. #Person2# thinks it's cruel at first but then joins.", "prediction_text": "A man and a woman are in a car. The man is driving. The woman is sitting in the back seat. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The"}
{"answers": "#Person1# is about to make a prank. #Person2# thinks it's cruel at first but then joins.", "prediction_text": "A man and a woman are in a car. The man is driving. The woman is sitting in the back seat. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The woman is listening. The man is talking to the woman. The"}
{"answers": "Mike is describing his sister to #Person1#.", "prediction_text": "Mike and his sister have a conversation about their appearance. Mike says that his sister is tall and pretty, while his sister says that she is not as clever as Mike."}
{"answers": "Mike describes to #Person1# his sister's characters and personality.", "prediction_text": "Mike and his sister have a conversation about their appearance. Mike says that his sister is tall and pretty, while his sister says that she is not as clever as Mike."}
{"answers": "#Person1# asks Mike about his sister.", "prediction_text": "Mike and his sister have a conversation about their appearance. Mike says that his sister is tall and pretty, while his sister says that she is not as clever as Mike."}
{"answers": "#Person1# feels sick and #Person2# gives #Person1# a check-up.", "prediction_text": "A man and a woman are talking on the phone. The man says he has a headache. The woman says she thinks he has a small fever. The man says he felt dizzy this morning. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick"}
{"answers": "#Person2# finds that #Person1# has a fever and says #Person1# should've called in sick earlier.", "prediction_text": "A man and a woman are talking on the phone. The man says he has a headache. The woman says she thinks he has a small fever. The man says he felt dizzy this morning. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick"}
{"answers": "#Person2# checks #Person1#'s physical condition and finds #Person1# has a fever.", "prediction_text": "A man and a woman are talking on the phone. The man says he has a headache. The woman says she thinks he has a small fever. The man says he felt dizzy this morning. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick. The man says he thought so. The woman says he should've called in sick"}
{"answers": "#Person1# helps #Person2# to choose a new phone.", "prediction_text": "The customer is trying to buy a new mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to make video calls. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy a new mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy a new mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy a new mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer"}
{"answers": "#Person2# wants to buy a new mobile phone from #Person1#.", "prediction_text": "The customer is trying to buy a new mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to make video calls. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy a new mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy a new mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy a new mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer"}
{"answers": "#Person2# wants to buy a new mobile phone from #Person1#.", "prediction_text": "The customer is trying to buy a new mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to make video calls. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy a new mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy a new mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer is trying to buy a new mobile phone. The salesperson is trying to sell the customer a phone with a camera and MP3 player. The customer"}
{"answers": "Frank got a new job and is telling Judy not only the heavy schedule but also the benefits of this job.", "prediction_text": "A man and a woman are talking about their jobs. The man says that he has a heavy work schedule, and the woman says that she has excellent health insurance benefits. The man says that he wants to work for the Post Office because it offers excellent health insurance benefits. The woman says that she wants to work for the Post Office because it offers her and her children excellent health insurance benefits."}
{"answers": "Frank explains to Judy the reason why he took a job at the Post office is that the government offers excellent health insurance benefits for its employees.", "prediction_text": "A man and a woman are talking about their jobs. The man says that he has a heavy work schedule, and the woman says that she has excellent health insurance benefits. The man says that he wants to work for the Post Office because it offers excellent health insurance benefits. The woman says that she wants to work for the Post Office because it offers her and her children excellent health insurance benefits."}
{"answers": "Frank describes his new job's schedule and insurance at the Post Office to Judy.", "prediction_text": "A man and a woman are talking about their jobs. The man says that he has a heavy work schedule, and the woman says that she has excellent health insurance benefits. The man says that he wants to work for the Post Office because it offers excellent health insurance benefits. The woman says that she wants to work for the Post Office because it offers her and her children excellent health insurance benefits."}
{"answers": "#Person2# is describing the educational background and previous working experience to #Person1# in a job interview.", "prediction_text": "#Person1#: I have a good command of secretarial skills. #Person2#: I have a doctor license and a driving license. #Person1#: Do you get special training in office skills? #Person2#: I passed both the Cambridge Examinations, First Certificate and the Certificate of Proficiency in English. And studied for a year in London at the Lucas Secretarial College, ending with speeds of 120 words per minute in English shorthand and 50 words per minute in typing. I was also trained in office procedure. #Person1#: Please tell me about work you have done, which qualifies you for this job. #Person2#: I'Ve received some special training in typing, shorthand and operating a fax machine, etc"}
{"answers": "#Person2# is being interviewed by #Person1#. #Person2# is equipped with a bunch of valuable office skills.", "prediction_text": "#Person1#: I have a good command of secretarial skills. #Person2#: I have a doctor license and a driving license. #Person1#: Do you get special training in office skills? #Person2#: I passed both the Cambridge Examinations, First Certificate and the Certificate of Proficiency in English. And studied for a year in London at the Lucas Secretarial College, ending with speeds of 120 words per minute in English shorthand and 50 words per minute in typing. I was also trained in office procedure. #Person1#: Please tell me about work you have done, which qualifies you for this job. #Person2#: I'Ve received some special training in typing, shorthand and operating a fax machine, etc"}
{"answers": "#Person1# asks about #Person2#'s special skills, training, previous working experience and other qualification for the job.", "prediction_text": "#Person1#: I have a good command of secretarial skills. #Person2#: I have a doctor license and a driving license. #Person1#: Do you get special training in office skills? #Person2#: I passed both the Cambridge Examinations, First Certificate and the Certificate of Proficiency in English. And studied for a year in London at the Lucas Secretarial College, ending with speeds of 120 words per minute in English shorthand and 50 words per minute in typing. I was also trained in office procedure. #Person1#: Please tell me about work you have done, which qualifies you for this job. #Person2#: I'Ve received some special training in typing, shorthand and operating a fax machine, etc"}
{"answers": "#Person1# is not satisfied with the steak and #Person2# will change it.", "prediction_text": "A man is served a steak that is too well done. He asks for it to be changed, but the waiter is not happy about it."}
{"answers": "#Person2# will change a steak for #Person1# as that one doesn't satisfy #Person1#.", "prediction_text": "A man is served a steak that is too well done. He asks for it to be changed, but the waiter is not happy about it."}
{"answers": "#Person1# is not satisfied with #Person1#'s steak and asks to change one, and #Person2# agrees.", "prediction_text": "A man is served a steak that is too well done. He asks for it to be changed, but the waiter is not happy about it."}
{"answers": "#Person1# congratulates Tom for achieving the Nobel Prize.", "prediction_text": "A man and a woman are having a conversation. The man says, \"I've got good news for you.\" The woman says, \"What is it?\" The man says, \"Haven't you heard that your novel has won The Nobel Prize?\" The woman says, \"Really? I can't believe it. It's like a dream come true. I never expected that I would win The Nobel Prize!\" The man says, \"You did a good job. I'm extremely proud of you.\" The woman says, \"Thanks for the compliment.\" The man says, \"You certainly deserve it. Let's celebrate!\""}
{"answers": "#Person1# tells Tom that his novel has won the Nobel Prize.", "prediction_text": "A man and a woman are having a conversation. The man says, \"I've got good news for you.\" The woman says, \"What is it?\" The man says, \"Haven't you heard that your novel has won The Nobel Prize?\" The woman says, \"Really? I can't believe it. It's like a dream come true. I never expected that I would win The Nobel Prize!\" The man says, \"You did a good job. I'm extremely proud of you.\" The woman says, \"Thanks for the compliment.\" The man says, \"You certainly deserve it. Let's celebrate!\""}
{"answers": "#Person2# tells Tom he won the Nobel Prize.", "prediction_text": "A man and a woman are having a conversation. The man says, \"I've got good news for you.\" The woman says, \"What is it?\" The man says, \"Haven't you heard that your novel has won The Nobel Prize?\" The woman says, \"Really? I can't believe it. It's like a dream come true. I never expected that I would win The Nobel Prize!\" The man says, \"You did a good job. I'm extremely proud of you.\" The woman says, \"Thanks for the compliment.\" The man says, \"You certainly deserve it. Let's celebrate!\""}
{"answers": "#Person1# is interviewing #Person2# about #Person2#'s ability and previous experience.", "prediction_text": "#Person1#: I am a student engineer who mainly took charge of understanding of the mechanical strength and corrosion resistance of various materials. #Person2#: My major is Automobile Designing and I have received my master's degree in science. I think I can do it well."}
{"answers": "#Person1# asks #Person2# about #Person2#'s capacities and past working experience during an interview.", "prediction_text": "#Person1#: I am a student engineer who mainly took charge of understanding of the mechanical strength and corrosion resistance of various materials. #Person2#: My major is Automobile Designing and I have received my master's degree in science. I think I can do it well."}
{"answers": "#Person1# asks #Person2# about #Person2#'s qualification for the job.", "prediction_text": "#Person1#: I am a student engineer who mainly took charge of understanding of the mechanical strength and corrosion resistance of various materials. #Person2#: My major is Automobile Designing and I have received my master's degree in science. I think I can do it well."}
{"answers": "#Person1# and #Person2# are talking about some personal facts of drinking. #Person2# drinks a lot, while #Person1# cannot drink much for health reasons. They decide to have a drink together tomorrow night.", "prediction_text": "The two men are drinking. One is a light drinker, the other is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a"}
{"answers": "#Person2#, a heavy drinker, invites #Person1#, a light drinker to go out the next day. #Person2# chooses a place that has a special on pitchers.", "prediction_text": "The two men are drinking. One is a light drinker, the other is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a"}
{"answers": "#Person1# and #Person2# talk about their drinking capacity and their drinking habits. They decide to have a drink together tomorrow night.", "prediction_text": "The two men are drinking. One is a light drinker, the other is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a heavy drinker. The light drinker is a light weight. The heavy drinker is a"}
{"answers": "May is helping her mother to do some preparation for the picnic.", "prediction_text": "The two children are preparing for a picnic. The father asks the mother to help him take the things to the living room. The mother asks the son to help her. The son says he can manage it by himself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage"}
{"answers": "May's mother asks May for help in preparing for a picnic. May gives her a hand.", "prediction_text": "The two children are preparing for a picnic. The father asks the mother to help him take the things to the living room. The mother asks the son to help her. The son says he can manage it by himself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage"}
{"answers": "Mom asks May to help to prepare for the picnic and May agrees.", "prediction_text": "The two children are preparing for a picnic. The father asks the mother to help him take the things to the living room. The mother asks the son to help her. The son says he can manage it by himself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage it by himself. The mother says she can manage it by herself. The father says he can manage"}
{"answers": "Muriel Douglas and James meet each other and talk about what they have done during the holiday.", "prediction_text": "A man and a woman meet for the first time. The man is a financial advisor and the woman is a client. The man is a little nervous and the woman is a little nervous. The man is a little nervous because he is a little nervous. The woman is a little nervous because she is a little nervous. The man is a little nervous because he is a little nervous. The woman is a little nervous because she is a little nervous. The man is a little nervous because he is a little nervous. The woman is a little nervous because she is a little nervous. The man is a little nervous because he is a little nervous. The woman is a little nervous because she is a little nervous. The man is a little nervous because he"}
{"answers": "James and Muriel are talking while waiting for Susan, Muriel's associate. They talk about how they spent the holiday with their families.", "prediction_text": "A man and a woman meet for the first time. The man is a financial advisor and the woman is a client. The man is a little nervous and the woman is a little nervous. The man is a little nervous because he is a little nervous. The woman is a little nervous because she is a little nervous. The man is a little nervous because he is a little nervous. The woman is a little nervous because she is a little nervous. The man is a little nervous because he is a little nervous. The woman is a little nervous because she is a little nervous. The man is a little nervous because he is a little nervous. The woman is a little nervous because she is a little nervous. The man is a little nervous because he"}
{"answers": "Muriel Douglas and James send greetings and talk about their holiday before the meeting. Douglas stayed in L.A., while James went to Tahoe.", "prediction_text": "A man and a woman meet for the first time. The man is a financial advisor and the woman is a client. The man is a little nervous and the woman is a little nervous. The man is a little nervous because he is a little nervous. The woman is a little nervous because she is a little nervous. The man is a little nervous because he is a little nervous. The woman is a little nervous because she is a little nervous. The man is a little nervous because he is a little nervous. The woman is a little nervous because she is a little nervous. The man is a little nervous because he is a little nervous. The woman is a little nervous because she is a little nervous. The man is a little nervous because he"}
{"answers": "#Person1# wants to withdraw money from an ATM, but the ATM automatically transfers 10000 USD to the World Wildlife Foundation after confirming the withdrawal option. #Person1# gets mad and somehow locked in.", "prediction_text": "A man is trying to withdraw money from a bank ATM, but the machine is talking to him like he is an idiot. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get"}
{"answers": "#Person1# run out of money because of a girl, and is withdrawing money from an ATM. But the ATM seems to go wrong and transfers #Person1#'s money to the World Wildlife Foundation, driving #Person1# crazy.", "prediction_text": "A man is trying to withdraw money from a bank ATM, but the machine is talking to him like he is an idiot. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get"}
{"answers": "#Person1# is withdrawing money from an ATM. But the ATM wrongly transfers #Person1#'s money to the World Wildlife Foundation. It drives #Person1# crazy.", "prediction_text": "A man is trying to withdraw money from a bank ATM, but the machine is talking to him like he is an idiot. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get his money, but the machine won't let him. The man is trying to get"}
{"answers": "#Person2# tells #Person1# #Person2#'s communication strategy.", "prediction_text": "The two people are both outgoing and like to be with a lot of friends. They both have a strategy in communicating with colleagues."}
{"answers": "Since #Person2# is very social, #Person1# asks for the communication strategy.", "prediction_text": "The two people are both outgoing and like to be with a lot of friends. They both have a good strategy in communicating with colleagues."}
{"answers": "#Person2# shares #Person2#'s communication strategy with #Person1#.", "prediction_text": "The two people are both outgoing and like to be with a lot of friends. They both have a good strategy in communicating with colleagues."}
{"answers": "Mr. Polly is tired and wants a break from work. #Person1# cannot buy a bottle of soft drink for him.", "prediction_text": "A man is talking to a woman. The man is a salesman. The woman is a customer. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink."}
{"answers": "Mr. Polly wants to get a break from work and he asks #Person1# to buy a drink for him, but #Person1# refuses.", "prediction_text": "A man is talking to a woman. The man is a salesman. The woman is a customer. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink."}
{"answers": "Mr. Polly asks #Person1#'s help to buy a drink, but #Person1# refuses.", "prediction_text": "A man is talking to a woman. The man is a salesman. The woman is a customer. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink. The man is trying to sell the woman a bottle of soft drink. The woman is trying to buy the bottle of soft drink."}
{"answers": "Francis and Monica are discussing when to work on the financial report.", "prediction_text": "The dialogue above is a typical example of a conversation between two people. The first person (Person 1) is the speaker and the second person (Person 2) is the listener. The dialogue is a conversation because it is a two-way exchange of information between the two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is"}
{"answers": "Francis and Monica manage to find time to work on a report together.", "prediction_text": "The dialogue above is a typical example of a conversation between two people. The first person (Person 1) is the speaker and the second person (Person 2) is the listener. The dialogue is a conversation because it is a two-way exchange of information between the two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is"}
{"answers": "Francis and Monica negotiate on the time to work on the report.", "prediction_text": "The dialogue above is a typical example of a conversation between two people. The first person (Person 1) is the speaker and the second person (Person 2) is the listener. The dialogue is a conversation because it is a two-way exchange of information between the two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is also a conversation because it is a conversation between two people. The dialogue is"}
{"answers": "#Person1# joins #Person2#'s interview workshop. They discuss the tips to improve their interview performance.", "prediction_text": "The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the"}
{"answers": "#Person1# takes an interview workshop. #Person2# offer #Person1# some useful tips on getting ready for an interview.", "prediction_text": "The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the"}
{"answers": "In the workshop, #Person2# offer #Person1# some suggestions on how to perform well in interviews.", "prediction_text": "The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the job. The interviewee is looking for a good fit for the job. The interview is a two-way street. The interviewer is looking for a good fit for the"}
{"answers": "#Person1# and Mike are discussing what kind of emotion should be expressed by Mike in this play. They have different understandings.", "prediction_text": "The actors are given a scene to read, and then they are asked to improvise a scene with their partner. The actors are then asked to read the scene again, and then to improvise a scene with their partner. The actors are then asked to read the scene again, and then to improvise a scene with their partner. The actors are then asked to read the scene again, and then to improvise a scene with their partner. The actors are then asked to read the scene again, and then to improvise a scene with their partner. The actors are then asked to read the scene again, and then to improvise a scene with their partner. The actors are then asked to read the scene again, and then to improvise a"}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "The actors are given a scene to read, and then they are asked to improvise a scene with their partner. The scene is then filmed, and the actors are asked to watch the film and give their feedback. The actors are then asked to give their own feedback on the scene, and then the scene is edited together. The actors are then asked to give their final feedback on the scene, and then the scene is released to the public."}
{"answers": "#Person1# wants Mike to act more angry, but Mike thinks he should act both angry and sad.", "prediction_text": "The actors are given a scene to read, and then they are asked to improvise a scene with their partner. The scene is then filmed, and the actors are asked to watch the film and give their feedback. The actors are then asked to give their own feedback on the scene, and then the scene is edited together. The actors are then asked to give their final feedback on the scene, and then the scene is released to the public."}
{"answers": "#Person1# greets Mrs. Todd and then they say goodbye to each other.", "prediction_text": "A man and a woman meet in the garden of a house they have just moved into. They talk about their lives and their plans for the future."}
{"answers": "#Person1# comes over to say hello to Mrs. Todd, and will go shopping afterwards.", "prediction_text": "A man and a woman meet in the garden of a house they have just moved into. They talk about their lives and their plans for the future."}
{"answers": "#Person1# visits Mrs. Todd, and Mrs. Todd appreciates that.", "prediction_text": "A man and a woman meet in the garden of a house they have just moved into. They talk about their lives and their plans for the future."}
{"answers": "Bill is tired. Bill and #Person1# talk about Bill's brother.", "prediction_text": "A man and a woman are talking about their brother. The man says he is busy and the woman says she is tired. The man says he will go home and rest. The woman says she will too. The man says he is going to meet a friend at five o'clock sharp. The woman says she is going to meet a friend at five o'clock sharp. The man says he is going to meet a friend at five o'clock sharp. The woman says she is going to meet a friend at five o'clock sharp. The man says he is going to meet a friend at five o'clock sharp. The woman says she is going to meet a friend at five o'clock sharp. The man says he is going to meet a friend at five"}
{"answers": "#Person1# has a chat with Bill, and learns updates about Bill and his brother. #Person1# asks for time because #Person1# will go meet a friend.", "prediction_text": "A man and a woman are talking about their brother. The man says he is busy and the woman says she is tired. The man says he will go home and rest. The woman says she will too. The man says he is going to meet a friend at five o'clock sharp. The woman says she is going to meet a friend at five o'clock sharp. The man says he is going to meet a friend at five o'clock sharp. The woman says she is going to meet a friend at five o'clock sharp. The man says he is going to meet a friend at five o'clock sharp. The woman says she is going to meet a friend at five o'clock sharp. The man says he is going to meet a friend at five"}
{"answers": "#Person1# suggests Bill take it easy and asks him about his brother. #Person1# also synchronizes the time with Bill.", "prediction_text": "A man and a woman are talking about their brother. The man says he is busy and the woman says she is tired. The man says he will go home and rest. The woman says she will too. The man says he is going to meet a friend at five o'clock sharp. The woman says she is going to meet a friend at five o'clock sharp. The man says he is going to meet a friend at five o'clock sharp. The woman says she is going to meet a friend at five o'clock sharp. The man says he is going to meet a friend at five o'clock sharp. The woman says she is going to meet a friend at five o'clock sharp. The man says he is going to meet a friend at five"}
{"answers": "Simon and Cleo have different opinions towards the demonstration to help stop the spread of nuclear weapons. Cleo thinks it is useless, while Simon considers that Cleo should go to the demonstration.", "prediction_text": "The two students are discussing the upcoming demonstration to stop the spread of nuclear weapons. #Person1# is going to the demonstration and #Person2# is not. #Person1# says that he has heard that North Korea has nuclear weapons and that he does not want to be involved in a demonstration that could get him hurt. #Person2# says that he does not want to be involved in a demonstration either because he does not understand the issues and because he does not want to get hurt. #Person1# says that he has heard that demonstrations can change people's minds and that he thinks it is important to stand up for what he believes in. #Person2# says that he does not want to be involved in a demonstration because he"}
{"answers": "Cleo has no intention to attend the demonstration to help stop the spread of nuclear weapons, because Cleo hates police standing by with tear gas. Simon tries to change Cleo's mind but it doesn't work.", "prediction_text": "The two students are discussing the upcoming demonstration to stop the spread of nuclear weapons. #Person1# is going to the demonstration and #Person2# is not. #Person1# says that he has heard that North Korea has nuclear weapons and that he does not want to be involved in a demonstration that could get him hurt. #Person2# says that he does not want to be involved in a demonstration either because he does not understand the issues and because he does not want to get hurt. #Person1# says that he has heard that demonstrations can change people's minds and that he thinks it is important to stand up for what he believes in. #Person2# says that he does not want to be involved in a demonstration because he"}
{"answers": "Simon and Cleo argue the effectiveness of demonstrations. Simon thinks they are helpful, but Cleo disagrees and refuses to go to the demonstration of helping stop the spread of nuclear weapons with Simon.", "prediction_text": "The two students are discussing the upcoming demonstration to stop the spread of nuclear weapons. #Person1# is going to the demonstration and #Person2# is not. #Person1# says that he has heard that North Korea has nuclear weapons and that he does not want to be involved in a demonstration that could get him hurt. #Person2# says that he does not want to be involved in a demonstration either because he does not understand the issues and because he does not want to get hurt. #Person1# says that he has heard that demonstrations can change people's minds and that he thinks it is important to stand up for what he believes in. #Person2# says that he does not want to be involved in a demonstration because he"}
{"answers": "#Person2# let in someone and #Person1# thinks it was #Person2#'s fault.", "prediction_text": "A man is in a room with a woman. He is trying to get her to leave. She is trying to get him to leave. They are both trying to get the other to leave. They are both trying to get the other to stay. They are both trying to get the other to stay. They are both trying to get the other to stay. They are both trying to get the other to stay. They are both trying to get the other to stay. They are both trying to get the other to stay. They are both trying to get the other to stay. They are both trying to get the other to stay. They are both trying to get the other to stay. They are both trying to get the other to stay. They"}
{"answers": "#Person1# blames #Person2# for letting someone in without much discretion.", "prediction_text": "A man is attacked by a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he"}
{"answers": "#Person1# advises #Person2# not to let anyone in casually.", "prediction_text": "A man is attacked by a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he is. The man who is attacked is a man who is not who he says he"}
{"answers": "Mark wants to borrow Maggie's class notes. Maggie suggests Mark copy them in the library and invites him to be study partners.", "prediction_text": "A student and a teacher are studying together. The teacher is tired and the student is sleepy. The teacher suggests that they study together. The student agrees. The teacher gives the student a push when he/she falls asleep in class. The student and the teacher study together in the library."}
{"answers": "Mark asks Maggie for her history notes because Mark has been too tired in class. They become study partners at the end.", "prediction_text": "A student and a teacher are studying together. The teacher is tired and the student is sleepy. The teacher suggests that they study together. The student agrees. The teacher gives the student a push when he/she falls asleep in class. The student and the teacher study together in the library."}
{"answers": "Mark asks Maggie for her notes because Mark's notes aren't good. They decide to be study partners and study together.", "prediction_text": "A student and a teacher are studying together. The teacher is tired and the student is sleepy. The teacher suggests that they study together. The student agrees. The teacher gives the student a push when he/she falls asleep in class. The student and the teacher study together in the library."}
{"answers": "#Person2# wants to take a course of Professor Turner and Professor agrees if #Person2# achieves the allowance of Professor Burman.", "prediction_text": "A student asks a professor if he can enroll in a graduate course. The professor says that he is not sure if the student is ready for the course. The student says that he wants to learn something. The professor says that he will speak to the professor about the student."}
{"answers": "#Person2#, a junior student, wants to enroll in Professor Turner's course for seniors very much. Professor Turner will ask for another professor's opinion.", "prediction_text": "A student asks a professor if he can enroll in a graduate course. The professor says that he is not sure if the student is ready for the course. The student says that he wants to learn something. The professor says that he will speak to the professor about the student."}
{"answers": "#Person2# wants to enroll in Professor Turner's course, and Professor Turner agrees to consider his application.", "prediction_text": "A student asks a professor if he can enroll in a graduate course. The professor says that he is not sure if the student is ready for the course. The student says that he wants to learn something. The professor says that he will speak to the professor about the student."}
{"answers": "#Person1# wants to change the broken pendant in #Person2#'s shop.", "prediction_text": "The customer bought a pendant from the shop, but it was broken. The shopkeeper asked the customer to come back to the shop with the receipt to change it. The customer agreed to do so."}
{"answers": "#Person1# goes back to #Person2#'s shop to replace a broken pendant.", "prediction_text": "The customer bought a pendant from the shop, but it was broken. The shopkeeper asked the customer to come back to the shop with the receipt to change it. The customer agreed to do so."}
{"answers": "#Person1# wants a product changed from #Person2#, and #Person2# agrees.", "prediction_text": "The customer bought a pendant from the shop, but it was broken. The shopkeeper asked the customer to come back to the shop with the receipt to change it. The customer agreed to do so."}
{"answers": "#Person1# is showing the new clothes to #Person2#, but #Person2# is busy watching the games. They quarrel and get angry.", "prediction_text": "#Person1#: What do you think of this one? #Person2#: Eh, so so. #Person1#: And this one? Too flashy? #Person2#: Nah, not too flashy. #Person1#: Uhg! And this sweater from my aunt? Isn't it hideous? #Person2#: I guess. #Person1#: Are you even listening? I'm trying to have a conversation with you. #Person2#: And I'm trying to watch the game, but you're yapping on about your new clothes! #Person1#: Well I have to decide which gifts to keep and which to exchange for better ones when I go to the Boxing Day sales this afternoon! #Person2#: Well could you"}
{"answers": "#Person1# and #Person2# have a serious quarrel over whether shopping for clothes or watching a sports game is more important.", "prediction_text": "#Person1#: What do you think of this one? #Person2#: Eh, so so. #Person1#: And this one? Too flashy? #Person2#: Nah, not too flashy. #Person1#: Uhg! And this sweater from my aunt? Isn't it hideous? #Person2#: I guess. #Person1#: Are you even listening? I'm trying to have a conversation with you. #Person2#: And I'm trying to watch the game, but you're yapping on about your new clothes! #Person1#: Well I have to decide which gifts to keep and which to exchange for better ones when I go to the Boxing Day sales this afternoon! #Person2#: Well could you"}
{"answers": "#Person1# asks #Person2#'s opinion on new clothes, but #Person2# only focuses on the game and feels annoyed. They are both angry and argue.", "prediction_text": "#Person1#: What do you think of this one? #Person2#: Eh, so so. #Person1#: And this one? Too flashy? #Person2#: Nah, not too flashy. #Person1#: Uhg! And this sweater from my aunt? Isn't it hideous? #Person2#: I guess. #Person1#: Are you even listening? I'm trying to have a conversation with you. #Person2#: And I'm trying to watch the game, but you're yapping on about your new clothes! #Person1#: Well I have to decide which gifts to keep and which to exchange for better ones when I go to the Boxing Day sales this afternoon! #Person2#: Well could you"}
{"answers": "#Person1# gives suggestions on job choices to #Person2#. #Person2# likes interactive media.", "prediction_text": "#Person1# and #Person2# are two people who are considering different career paths. #Person1# is interested in working in the media, while #Person2# is interested in working with computers. #Person1# is interested in working in the media, but he thinks it would be stressful. #Person2# is interested in working with computers, but he thinks it would be exciting. #Person1# is interested in working in the media, but he thinks it would be fun. #Person2# is interested in working with computers, but he thinks it would be exciting. #Person1# is interested in working in the media, but he thinks it would be fun. #Person2# is interested in working with"}
{"answers": "#Person1# and #Person2# are looking at jobs. #Person2# is interested in the media industry.", "prediction_text": "#Person1# and #Person2# are two people who are considering different career paths. #Person1# is interested in working in the media, while #Person2# is interested in working with computers. #Person1# is interested in working in the media, but he thinks it would be stressful. #Person2# is interested in working with computers, but he thinks it would be exciting. #Person1# is interested in working in the media, but he thinks it would be fun. #Person2# is interested in working with computers, but he thinks it would be exciting. #Person1# is interested in working in the media, but he thinks it would be fun. #Person2# is interested in working with"}
{"answers": "#Person1# offers some job choices to #Person2#, but #Person2# doesn't like them.", "prediction_text": "#Person1# and #Person2# are two people who are considering different career paths. #Person1# is interested in working in the media, while #Person2# is interested in working with computers. #Person1# is interested in working in the media, but he thinks it would be stressful. #Person2# is interested in working with computers, but he thinks it would be exciting. #Person1# is interested in working in the media, but he thinks it would be fun. #Person2# is interested in working with computers, but he thinks it would be exciting. #Person1# is interested in working in the media, but he thinks it would be fun. #Person2# is interested in working with"}
{"answers": "#Person2# feels bored with the meeting. #Person1# gives #Person2# some suggestions on how to entertain but finally asks #Person2# to keep quiet.", "prediction_text": "#Person1#: Don't tell me you're bored already? #Person2#: Well, yeah, I am. #Person1#: We just got here. You will have to find a way to entertain yourself then. #Person2#: What can I do? There are just a bunch of boring people giving boring speeches. #Person1#: Too bad. I have to sit through it, so you do, too. Even if it's a little boring, it's important for me to be here. I have to listen carefully, take notes and then interview some of the speakers afterward. I work for the Daily News after all. Didn't you bring anything to read? #Person2#: I brought my iPod and some headphones. Would"}
{"answers": "#Person1#, a reporter, takes #Person2# to a forum. #Person2# is bored and tries to find a pastime.", "prediction_text": "#Person1#: Don't tell me you're bored already? #Person2#: Well, yeah, I am. #Person1#: We just got here. You will have to find a way to entertain yourself then. #Person2#: What can I do? There are just a bunch of boring people giving boring speeches. #Person1#: Too bad. I have to sit through it, so you do, too. Even if it's a little boring, it's important for me to be here. I have to listen carefully, take notes and then interview some of the speakers afterward. I work for the Daily News after all. Didn't you bring anything to read? #Person2#: I brought my iPod and some headphones. Would"}
{"answers": "#Person2# tells #Person1# that #Person2# is bored and explains the reasons, so #Person1# suggests #Person2# read some old newspapers and be quiet.", "prediction_text": "#Person1#: Don't tell me you're bored already? #Person2#: Well, yeah, I am. #Person1#: We just got here. You will have to find a way to entertain yourself then. #Person2#: What can I do? There are just a bunch of boring people giving boring speeches. #Person1#: Too bad. I have to sit through it, so you do, too. Even if it's a little boring, it's important for me to be here. I have to listen carefully, take notes and then interview some of the speakers afterward. I work for the Daily News after all. Didn't you bring anything to read? #Person2#: I brought my iPod and some headphones. Would"}
{"answers": "Sarah is considering moving. #Person1# gives her advice on buying a house.", "prediction_text": "#Person1#: Have you ever thought of moving, Sarah?"}
{"answers": "Sarah wants to move. #Person1# suggests buying a cheaper house and offers information on possible options.", "prediction_text": "#Person1#: Have you ever thought of moving, Sarah?"}
{"answers": "Sarah tells #Person1# that she can't afford a new house. #Person1# says #Person1#'s sister-in-law bought a cheap house and suggests Sarah should buy a house far from the city center.", "prediction_text": "#Person1#: Have you ever thought of moving, Sarah?"}
{"answers": "#Person1# introduces Mark Leach to this week's People You Meet. Mark Leach describes his job responsibilities and gives advice to people coming to Britain.", "prediction_text": "Mark Leach is an information officer at the Britain Business Center in London. He speaks 13 languages and offers tourist information to visitors from overseas."}
{"answers": "#Person1# hosts the week's People You Meet, featuring Mark Leach. Mark introduces his work as an information officer in London.", "prediction_text": "Mark Leach is an information officer at the Britain Business Center in London. He speaks 13 languages and offers tourist information to visitors from overseas."}
{"answers": "Mark Leach introduces his information service and his work to the audience. He also offers suggestions for people coming to Britain.", "prediction_text": "Mark Leach is an information officer at the Britain Business Center in London. He speaks 13 languages and offers tourist information to visitors from overseas."}
{"answers": "Lin Fang and Lucy are talking about their favourite subjects.", "prediction_text": "Nancy and Lucy are two students in a Chinese class. They are both studying Chinese. They are both studying English. They are both studying PE. They are both studying science. They are all studying different subjects."}
{"answers": "Lin Fang and Lucy are talking about how they like different subjects.", "prediction_text": "Nancy and Lucy are two students in a Chinese class. They are both studying Chinese. They are both studying English. They are both studying PE. They are both studying science. They are all studying different subjects."}
{"answers": "Lucy likes English and P.E. best, but Lin Fang's favorite is Chinese and Science.", "prediction_text": "Nancy and Lucy are in a class together. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the same class. They are both in the"}
{"answers": "James comes to help Mrs. Thomas to do some housework on the weekend. James is saving up to buy a bike.", "prediction_text": "A man and a woman are talking about their jobs. The man is a gardener and the woman is a housekeeper. The man is cleaning up the yard and the woman is cleaning the house. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the"}
{"answers": "James helped Mrs. Thomas with housework since she has broken her legs, and he wanted to save money for a bike.", "prediction_text": "A man and a woman are talking about their jobs. The man is a gardener and the woman is a housekeeper. The man is cleaning up the yard and the woman is cleaning the house. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the"}
{"answers": "James helps Mrs. Thomas to do chores because James wanted to save money and Mrs. Thomas is incapable.", "prediction_text": "A man and a woman are talking about their jobs. The man is a gardener and the woman is a housekeeper. The man is cleaning up the yard and the woman is cleaning the house. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the garbage. The man is sweeping the front steps and the woman is taking out the"}
{"answers": "#Person1# and #Person2# are talking about the low temperature at night, although spring has come.", "prediction_text": "#Person1#: I think spring is finally here. #Person2#: Yep, it sure seems like it. However, it's still very cold at night. #Person1#: Yes, they turned the heat off 6 days ago. It's absolutely freezing in my apartment at night. I have to turn on the air conditioner to blow hot air in order to warm things up a little. #Person2#: Well, and if you are outside and is a bit of a breeze. It feels cold quickly. #Person1#: It sure does. I think I'm going to follow my cats example and just sit in the sum that shining in through the windows."}
{"answers": "#Person1# and #Person2# agree that it still felt very cold in spring.", "prediction_text": "#Person1#: I think spring is finally here. #Person2#: Yep, it sure seems like it. However, it's still very cold at night. #Person1#: Yes, they turned the heat off 6 days ago. It's absolutely freezing in my apartment at night. I have to turn on the air conditioner to blow hot air in order to warm things up a little. #Person2#: Well, and if you are outside and is a bit of a breeze. It feels cold quickly. #Person1#: It sure does. I think I'm going to follow my cats example and just sit in the sum that shining in through the windows."}
{"answers": "#Person1# and #Person2# talk about the weather and how to keep warm.", "prediction_text": "#Person1#: I think spring is finally here. #Person2#: Yep, it sure seems like it. However, it's still very cold at night. #Person1#: Yes, they turned the heat off 6 days ago. It's absolutely freezing in my apartment at night. I have to turn on the air conditioner to blow hot air in order to warm things up a little. #Person2#: Well, and if you are outside and is a bit of a breeze. It feels cold quickly. #Person1#: It sure does. I think I'm going to follow my cats example and just sit in the sum that shining in through the windows."}
{"answers": "#Person1# and Mike are discussing what kind of emotion should be expressed by Mike in this play. They have different understandings.", "prediction_text": "The actors are given a scene to read and then they are given a chance to try it out with the director. The director then gives his/her opinion on the scene and the actors are given the chance to try it out again. The actors are then given a chance to try it out with the director again. The director then gives his/her opinion on the scene and the actors are given the chance to try it out again. The actors are then given a chance to try it out with the director again. The director then gives his/her opinion on the scene and the actors are given the chance to try it out again. The actors are then given a chance to try it out with the director again. The director then gives his/her opinion on the scene"}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "The actors are given a scene to read and then they are given a chance to try it out with the director. The director then gives his/her opinion on the scene and the actors are given the chance to try it out again. The actors are then given a chance to try it out with the director again. The director then gives his/her opinion on the scene and the actors are given the chance to try it out again. The actors are then given a chance to try it out with the director again. The director then gives his/her opinion on the scene and the actors are given the chance to try it out again. The actors are then given a chance to try it out with the director again. The director then gives his/her opinion on the scene"}
{"answers": "#Person1# wants Mike to act more angry, but Mike thinks he should act both angry and sad.", "prediction_text": "The actors are given a scene to read and then they are given a chance to try it out with the director. The director then gives his/her opinion on the scene and the actors are given the chance to try it out again. The actors are then given a chance to try it out with the director again. The director then gives his/her opinion on the scene and the actors are given the chance to try it out again. The actors are then given a chance to try it out with the director again. The director then gives his/her opinion on the scene and the actors are given the chance to try it out again. The actors are then given a chance to try it out with the director again. The director then gives his/her opinion on the scene"}
{"answers": "#Person1# takes a taxi to the Friendship Hotel for something important.", "prediction_text": "A taxi driver and a passenger are chatting in a taxi. The passenger asks the driver to go to a certain place, and the driver says that he can't go there because he has something important to do. The passenger says that he will pay the driver 20 yuan, and the driver says that he will keep the change. The passenger says that he will pay 20 yuan, and the driver says that he will keep the change. The passenger says that he will pay 20 yuan, and the driver says that he will keep the change. The passenger says that he will pay 20 yuan, and the driver says that he will keep the change. The passenger says that he will pay 20 yuan, and the driver says that he will"}
{"answers": "#Person2# drives #Person1# to the Friendship Hotel as fast as #Person2# can be.", "prediction_text": "A taxi driver and a passenger are chatting in a taxi. The passenger asks the driver to go to a certain place, and the driver says that he can't go there because he has something important to do. The passenger says that he will pay the driver 20 yuan, and the driver says that he will keep the change. The passenger says that he will pay 20 yuan, and the driver says that he will keep the change. The passenger says that he will pay 20 yuan, and the driver says that he will keep the change. The passenger says that he will pay 20 yuan, and the driver says that he will keep the change. The passenger says that he will pay 20 yuan, and the driver says that he will"}
{"answers": "The taxi driver takes #Person1# to the Friendship Hotel quickly.", "prediction_text": "A taxi driver and a passenger are chatting. The passenger asks the driver to take him to the Friendship Hotel. The driver says that he has something important to do, and he can't take the passenger to the hotel. The passenger says that he has something important to do, and he can't pay the driver. The driver says that he can't take the passenger to the hotel, and he can't pay the passenger. The passenger says that he can't pay the driver, and he can't take the driver to the hotel. The driver says that he can't take the passenger to the hotel, and he can't pay the passenger. The passenger says that he can't take the driver to the hotel, and he can't pay the driver"}
{"answers": "#Person1# doesn't have any change for the bus because #Person1# bought some chips. #Person2# suggests #Person1# get a transfer from the bus driver.", "prediction_text": "A man and a woman are talking about how they are broke and have to catch another bus. The man says he bought some chips and the woman says that was smart. The man says he doesn't know how he's going to get home and the woman says she doesn't have any change. The man says he doesn't know how he's going to get home and the woman says she doesn't have any change. The man says he doesn't know how he's going to get home and the woman says she doesn't have any change. The man says he doesn't know how he's going to get home and the woman says she doesn't have any change. The man says he doesn't know how he's going to get home and the"}
{"answers": "#Person1#'s broke, so #Person2# suggests #Person1# get a free bus transfer.", "prediction_text": "A man and a woman are talking about how they are broke and have to catch another bus. The man says he bought some chips and the woman says that was smart. The man says he doesn't know how he's going to get home and the woman says she doesn't have any change. The man says he doesn't know how he's going to get home and the woman says she doesn't have any change. The man says he doesn't know how he's going to get home and the woman says she doesn't have any change. The man says he doesn't know how he's going to get home and the woman says she doesn't have any change. The man says he doesn't know how he's going to get home and the"}
{"answers": "#Person1#'s broke, so #Person2# suggests #Person1# get a transfer from the bus driver to go home.", "prediction_text": "A man and a woman are talking about how they are broke and have to catch another bus. The man says he bought some chips and the woman says that was smart. The man says he doesn't know how he's going to get home and the woman says she doesn't have any change. The man says he doesn't know how he's going to get home and the woman says she doesn't have any change. The man says he doesn't know how he's going to get home and the woman says she doesn't have any change. The man says he doesn't know how he's going to get home and the woman says she doesn't have any change. The man says he doesn't know how he's going to get home and the"}
{"answers": "#Person2# tells #Person1# information about their company and its surroundings.", "prediction_text": "#Person1#: How long does it take to get to downtown from here?"}
{"answers": "#Person1# asks #Person2# about the company's surroundings, location, and staff restaurant.", "prediction_text": "#Person1#: How long does it take to get to downtown from here?"}
{"answers": "#Person2# introduces the surroundings and information about their company to #Person1#.", "prediction_text": "#Person1#: How long does it take to get to downtown from here?"}
{"answers": "#Person1# and #Person2# are gossiping about Lulu and Vic's break-up.", "prediction_text": "#Person1#: Did you hear about Lulu?"}
{"answers": "#Person1# and #Person2# are talking about Lulu and Vic's breakup.", "prediction_text": "#Person1#: Did you hear about Lulu?"}
{"answers": "#Person1# and #Person2# talk about Lulu and Vic's breakup.", "prediction_text": "#Person1#: Did you hear about Lulu?"}
{"answers": "Dave Thomson calls Jim but Jim went out. Sally will tell Jim Dave called.", "prediction_text": "Sally is a receptionist at a local pub. She is talking to Dave, a customer, who is looking for Jim, the pub's owner. Sally tells Dave that Jim is not in at the moment, but that he should be back soon. She tells Dave that Jim has gone out to get some paint, but that he may have stopped off at the pub on the way back. Sally tells Dave that she will try again later."}
{"answers": "Dave calls Jim but Jim is out. Sally tells Dave Jim's whereabouts.", "prediction_text": "Sally is a receptionist at a local pub. She is talking to Dave, a customer, who is looking for Jim, the pub's owner. Sally tells Dave that Jim is not in at the moment, but that he should be back soon. She tells Dave that Jim has gone out to get some paint, but that he may have stopped off at the pub on the way back. Sally tells Dave that she will try again later."}
{"answers": "Dave Thomson calls to speak to Jim but Jim is out.", "prediction_text": "Sally is a receptionist at a local pub. She is talking to Dave, a customer, who is looking for Jim, the pub's owner. Sally tells Dave that Jim is not in at the moment, but that he should be back soon. She tells Dave that Jim has gone out to get some paint, but that he may have stopped off at the pub on the way back. Sally tells Dave that she will try again later."}
{"answers": "#Person2# shows #Person1# the way to City Hall patiently.", "prediction_text": "A man and a woman are walking down the street. The man says, \"Excuse me. Can you tell me how to get to City Hall?\" The woman says, \"Yes. Go to the next corner and turn left onto Center Street. Walk three blocks to Second Avenue and turn right. Walk two more blocks and you'll see City Hall on the left.\" The man says, \"I'm sorry. I didn't follow you. Could you please repeat that?\" The woman says, \"Okay. First, go to the next corner and turn left onto Center Street. Then, walk three blocks to Second Avenue and turn right. Are you with me so far?\" The man says, \"Yes. I'm following you.\" The woman says"}
{"answers": "#Person1# asks #Person2# the way to City Hall and #Person2# answers with patience.", "prediction_text": "A man and a woman are walking down the street. The man says, \"Excuse me. Can you tell me how to get to City Hall?\" The woman says, \"Yes. Go to the next corner and turn left onto Center Street. Walk three blocks to Second Avenue and turn right. Walk two more blocks and you'll see City Hall on the left.\" The man says, \"I'm sorry. I didn't follow you. Could you please repeat that?\" The woman says, \"Okay. First, go to the next corner and turn left onto Center Street. Then, walk three blocks to Second Avenue and turn right. Are you with me so far?\" The man says, \"Yes. I'm following you.\" The woman says"}
{"answers": "#Person2# patiently shows #Person1# the way to City Hall.", "prediction_text": "A man and a woman are walking down the street. The man says, \"Excuse me. Can you tell me how to get to City Hall?\" The woman says, \"Yes. Go to the next corner and turn left onto Center Street. Walk three blocks to Second Avenue and turn right. Walk two more blocks and you'll see City Hall on the left.\" The man says, \"I'm sorry. I didn't follow you. Could you please repeat that?\" The woman says, \"Okay. First, go to the next corner and turn left onto Center Street. Then, walk three blocks to Second Avenue and turn right. Are you with me so far?\" The man says, \"Yes. I'm following you.\" The woman says"}
{"answers": "#Person1# lost #Person1#'s passport. #Person2# suggests #Person1# fill in the lost property report and inform the embassy of the situation so they can issue a new passport in case it cannot be found.", "prediction_text": "A man is looking for his passport in a department store. He asks a woman if she has seen it. She says she doesn't know where it is. He asks her if she has lost it. She says she hasn't. He asks her if she has seen it in the shoe department. She says she hasn't. He asks her if she has seen it in the lost property department. She says she hasn't. He asks her if she has seen it in the lost property department. She says she hasn't. He asks her if she has seen it in the lost property department. She says she hasn't. He asks her if she has seen it in the lost property department. She says she hasn't. He asks her if"}
{"answers": "#Person1# asks #Person2# for help to find #Person1#'s passport. #Person2# checks the shoe department but finds nothing. Thus, #Person2# lets #Person1# fill in a report and contact the embassy.", "prediction_text": "A man is looking for his passport in a department store. He asks a woman if she has seen it. She says she doesn't know where it is. He asks her if she has lost it. She says she hasn't. He asks her if she has seen it in the shoe department. She says she hasn't. He asks her if she has seen it in the lost property department. She says she hasn't. He asks her if she has seen it in the lost property department. She says she hasn't. He asks her if she has seen it in the lost property department. She says she hasn't. He asks her if she has seen it in the lost property department. She says she hasn't. He asks her if"}
{"answers": "#Person1# lost the passport. #Person2# suggests #Person1# fill in the lost property report and contact the embassy.", "prediction_text": "A man is looking for his passport in a department store. He asks a woman if she has seen it. She says she doesn't know where it is. He asks her if she has lost it. She says she hasn't. He asks her if she has seen it in the shoe department. She says she hasn't. He asks her if she has seen it in the lost property department. She says she hasn't. He asks her if she has seen it in the lost property department. She says she hasn't. He asks her if she has seen it in the lost property department. She says she hasn't. He asks her if she has seen it in the lost property department. She says she hasn't. He asks her if"}
{"answers": "Leah informs Nathaniel that Ms. Collins will not be able to call him back until next week, so he needs to book an appointment with someone else from the International Student Office. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "Ms. Collins is not available today. Ms. Fonda is available at 11:30."}
{"answers": "Leah informs Nathaniel that Ms. Collins won't be available for their appointment and explains the reason. Then Nathaniel decides to speak with someone else.", "prediction_text": "Ms. Collins is not available today. Ms. Fonda is available at 11:30."}
{"answers": "The International Student Office calls Nathaniel to inform him that he has to book another appointment with someone else as Ms. Collins is not available. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "Ms. Collins is not available today. Ms. Fonda is available at 11:30."}
{"answers": "#Person1# and #Person2# feel scared when heard Sarah insists on marrying Dick.", "prediction_text": "The two friends, #Person1# and #Person2#, are discussing the marriage of their friend, #Person3#. #Person1# says that he is not sure whether he will marry #Person3# or not. #Person2# says that he is sure that he will marry #Person3#."}
{"answers": "#Person1# and #Person2# are shocked about Sarah's love for Dick.", "prediction_text": "The two friends, #Person1# and #Person2#, are discussing the marriage of their friend, #Person3#. #Person1# says that he is not sure whether he will marry #Person3# or not. #Person2# says that he is sure that he will marry #Person3#."}
{"answers": "#Person1# and #Person2# are surprised to know Sarah wants to marry Dick.", "prediction_text": "The two friends, #Person1# and #Person2#, are discussing the marriage of their friend, #Person3#. #Person1# says that #Person3# is a very good girl, but #Person2# says that she is a very bad girl. #Person1# says that #Person3# is a very good girl, but #Person2# says that she is a very bad girl. #Person1# says that #Person3# is a very good girl, but #Person2# says that she is a very bad girl. #Person1# says that #Person3# is a very good girl, but #Person2# says that she is a very bad girl. #Person1# says"}
{"answers": "#Person1# and #Person2# are at a nice party. #Person2# has a crush on Janice but is afraid of talking to her. #Person1# wishes #Person2# good luck.", "prediction_text": "#Person1#: this party rocks! #Person2#: you said it. Good beer, good music, and on top of that, there are so many hot girls here! #Person1#: I'm with you on that. Check out that one over there. I think I'm in love! #Person2#: well, she's alright, but not really my cup of tea. What about the blond with the red dress? #Person1#: oh, that's Janice. She's just new in town. You have a thing for blonds, eh? #Person2#: yeah! She could do anything she wants to me! #Person1#: I bet. Why not go over and talk to her? She's really"}
{"answers": "#Person1# and #Person2# talk about girls at a party and #Person2# tells #Person1# that #Person2# likes Janice but he feels nervous talking to girls. #Person1# encourages #Person2#.", "prediction_text": "#Person1#: this party rocks! #Person2#: you said it. Good beer, good music, and on top of that, there are so many hot girls here! #Person1#: I'm with you on that. Check out that one over there. I think I'm in love! #Person2#: well, she's alright, but not really my cup of tea. What about the blond with the red dress? #Person1#: oh, that's Janice. She's just new in town. You have a thing for blonds, eh? #Person2#: yeah! She could do anything she wants to me! #Person1#: I bet. Why not go over and talk to her? She's really"}
{"answers": "#Person1# and #Person2# are at an exciting party. #Person2# has a crush on Janice and #Person1# encourages him to have a try to talk to her.", "prediction_text": "#Person1#: this party rocks! #Person2#: you said it. Good beer, good music, and on top of that, there are so many hot girls here! #Person1#: I'm with you on that. Check out that one over there. I think I'm in love! #Person2#: well, she's alright, but not really my cup of tea. What about the blond with the red dress? #Person1#: oh, that's Janice. She's just new in town. You have a thing for blonds, eh? #Person2#: yeah! She could do anything she wants to me! #Person1#: I bet. Why not go over and talk to her? She's really"}
{"answers": "Jack tells #Person1# that business communication is his favorite last year and #Person1# will check it.", "prediction_text": "#Person1# and #Person2# are both students at the same university. #Person1# is a freshman and #Person2# is a sophomore. #Person1# is in a political science class and #Person2# is in a business communication class. #Person1# is interested in his class and #Person2# is interested in hers. #Person1# asks #Person2# if she considers her class her favorite. #Person2# says that she doesn't know if she would call it her favorite, but it ranks up there. #Person1# asks #Person2# what class was her favorite. #Person2# says that she took a business communication class last year and it was terrific. #"}
{"answers": "#Person1# and Jack talk about Jack's classes last year.", "prediction_text": "#Person1# and #Person2# are both students at the same university. #Person1# is a freshman and #Person2# is a sophomore. #Person1# is in a political science class and #Person2# is in a business communication class. #Person1# is interested in his class and #Person2# is interested in hers. #Person1# asks #Person2# if she considers her class her favorite. #Person2# says that she doesn't know if she would call it her favorite, but it ranks up there. #Person1# asks #Person2# what class was her favorite. #Person2# says that she took a business communication class last year and it was terrific. #"}
{"answers": "#Person1# and Jack are talking about Jack's favorite classes.", "prediction_text": "#Person1# and #Person2# are both students at the same university. #Person1# is a freshman and #Person2# is a sophomore. #Person1# is in a political science class and #Person2# is in a business communication class. #Person1# is interested in his class and #Person2# is interested in hers. #Person1# asks #Person2# if she considers her class her favorite. #Person2# says that she doesn't know if she would call it her favorite, but it ranks up there. #Person1# asks #Person2# what class was her favorite. #Person2# says that she took a business communication class last year and it was terrific. #"}
{"answers": "#Person1# and #Person2# are talking about the weather in different seasons in Beijing. #Person2# reminds #Person1# to put on more clothes in the evening and not to catch a cold.", "prediction_text": "The weather in Beijing is very different in the summer and the winter. The summer is hot and dry, while the winter is cold and wet. The summer is also the time when the air is freshest, while the winter is the time when the air is the most polluted."}
{"answers": "#Person1# and #Person2# are talking about the weather in Beijing. #Person2# tells #Person1# it's cold in the evening around this time.", "prediction_text": "The weather in Beijing is very different in the summer and the winter. The summer is hot and dry, while the winter is cold and wet. The summer is also the time when the air is freshest, while the winter is the time when the air is the most polluted."}
{"answers": "#Person1# and #Person2# are talking about the heavy rain last night and the weather in Beijing.", "prediction_text": "The weather in Beijing is very different in the summer and the winter. The summer is hot and dry, while the winter is cold and wet. The summer is also the time when the air is freshest, while the winter is the time when the air is the most polluted."}
{"answers": "#Person1# invites #Person2# to watch a movie together that night. #Person2# wants to see nothing but a war movie.", "prediction_text": "The two people in the conversation are discussing what they would like to see at the movies. The first person says that he would like to see a horror movie, but the second person says that he doesn't like horror movies. The first person says that he would like to see a detective movie, but the second person says that he doesn't like detective movies. The first person says that he would like to see a comedy, but the second person says that he doesn't like comedies. The first person says that he would like to see a love story, but the second person says that he doesn't like love stories. The first person says that he can't imagine that a girl like the second person would want to see a war movie, but"}
{"answers": "#Person1# and #Person2# talk about what kind of movies to watch and they decided to watch a war movie.", "prediction_text": "The two people in the conversation are discussing what they would like to see at the movies. The first person says that he would like to see a horror movie, but the second person says that he doesn't like horror movies. The first person says that he would like to see a detective movie, but the second person says that he doesn't like detective movies. The first person says that he would like to see a comedy, but the second person says that he doesn't like comedies. The first person says that he would like to see a love story, but the second person says that he doesn't like love stories. The first person says that he can't imagine that a girl like the second person would want to see a war movie, but"}
{"answers": "#Person1# invites #Person2# to movie and #Person2# wants to see a war movie.", "prediction_text": "The two people in the conversation are discussing what they would like to see at the movies. The first person says that he would like to see a horror movie, but the second person says that he doesn't like horror movies. The first person says that he would like to see a detective movie, but the second person says that he doesn't like detective movies. The first person says that he would like to see a comedy, but the second person says that he doesn't like comedies. The first person says that he would like to see a love story, but the second person says that he doesn't like love stories. The first person says that he can't imagine that a girl like the second person would want to see a war movie, but"}
{"answers": "Adam is showing #Person1# around his school. #Person1# envies Adam and hoped #Person1# could enter this school one day.", "prediction_text": "The two students are talking about their school. The first student says that he wants to go to a big school. The second student says that he wants to go to a small school. The first student says that he wants to go to a school with a big swimming pool. The second student says that he wants to go to a school with a small swimming pool. The first student says that he wants to go to a school with a big library. The second student says that he wants to go to a school with a small library. The first student says that he wants to go to a school with a big classroom building. The second student says that he wants to go to a school with a small classroom building. The first student says that he wants"}
{"answers": "Adam shows #Person1# around his school and introduces the library, the new classroom building, and the swimming pool.", "prediction_text": "The two students are talking about their school. The first student says that he wants to go to a big school. The second student says that he wants to go to a small school. The first student says that he wants to go to a school with a big swimming pool. The second student says that he wants to go to a school with a small swimming pool. The first student says that he wants to go to a school with a big library. The second student says that he wants to go to a school with a small library. The first student says that he wants to go to a school with a big classroom building. The second student says that he wants to go to a school with a small classroom building. The first student says that he wants"}
{"answers": "Adam shows #Person1# around the school and introduces buildings and facilities in the school to #Person1#.", "prediction_text": "The two students are talking about their school. The first student says that he wants to go to a big school. The second student says that he wants to go to a small school. The first student says that he wants to go to a school with a big swimming pool. The second student says that he wants to go to a school with a small swimming pool. The first student says that he wants to go to a school with a big library. The second student says that he wants to go to a school with a small library. The first student says that he wants to go to a school with a big classroom building. The second student says that he wants to go to a school with a small classroom building. The first student says that he wants"}
{"answers": "#Person1# and #Person2# feel happy because #Person1# is pregnant.", "prediction_text": "A man and a woman are talking about their pregnancy. The man is happy and the woman is sad."}
{"answers": "#Person1# and #Person2# are happy about #Person1#'s pregnancy.", "prediction_text": "A man and a woman are talking about their pregnancy. The man is happy and the woman is sad."}
{"answers": "#Person1# is pregnant. She and #Person2# feel happy.", "prediction_text": "A man and a woman are talking about their pregnancy. The man is happy and the woman is sad."}
{"answers": "#Person1# and #Person2# think John's fallen for a girl.", "prediction_text": "A man and a woman meet and fall in love. They are married and have a child. The man is a successful businessman. The woman is a homemaker. The man is a good father. The woman is a good mother. The man is a good husband. The woman is a good wife. The man is a good father. The woman is a good mother. The man is a good husband. The woman is a good wife. The man is a good father. The woman is a good mother. The man is a good husband. The woman is a good wife. The man is a good father. The woman is a good mother. The man is a good husband. The woman is a good wife. The man is a good"}
{"answers": "#Person1# and #Person2# talk about John's dating life.", "prediction_text": "A man and a woman meet and fall in love. They are married and have a child. The man is a successful businessman. The woman is a homemaker. The man is a good father. The woman is a good mother. The man is a good husband. The woman is a good wife. The man is a good father. The woman is a good mother. The man is a good husband. The woman is a good wife. The man is a good father. The woman is a good mother. The man is a good husband. The woman is a good wife. The man is a good father. The woman is a good mother. The man is a good husband. The woman is a good wife. The man is a good"}
{"answers": "#Person1# and #Person2# are talking about a couple.", "prediction_text": "A man and a woman meet and fall in love. They are married and have a child. The man is a successful businessman. The woman is a homemaker. The man is a good father. The woman is a good mother. The man is a good husband. The woman is a good wife. The man is a good father. The woman is a good mother. The man is a good husband. The woman is a good wife. The man is a good father. The woman is a good mother. The man is a good husband. The woman is a good wife. The man is a good father. The woman is a good mother. The man is a good husband. The woman is a good wife. The man is a good"}
{"answers": "#Person1# is giving #Person2# some advice for upgrading #Person2#'s system, such as adding a painting program and a faster processor.", "prediction_text": "#Person1#: Have you considered upgrading your system?"}
{"answers": "#Person1# tells #Person2# how to upgrade #Person2#'s system for better software and hardware.", "prediction_text": "#Person1#: Have you considered upgrading your system?"}
{"answers": "#Person1# teaches #Person2# how to upgrade software and hardware in #Person2#'s system.", "prediction_text": "#Person1#: Have you considered upgrading your system?"}
{"answers": "#Person1# is driving #Person2# to an inn. They talk about their careers, ages, and where they was born.", "prediction_text": "A young man and a young woman meet at a Holiday Inn in Los Angeles. They are both on business trips, and they are both from Mexico. They are both 26 years old, and they are both fluent in Spanish. They are both very nice people, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they"}
{"answers": "#Person1# drives #Person2# to an inn and they have a talk. #Person2# is 26 and had a business trip to China. #Person1# is 40 years old American.", "prediction_text": "A young man and a young woman meet at a Holiday Inn in Los Angeles. They are both on business trips, and they are both from Mexico. They are both 26 years old, and they are both fluent in Spanish. They are both very nice people, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they"}
{"answers": "#Person1# drives #Person2# from the airport to an inn and they have a casual talk about themselves.", "prediction_text": "A young man and a young woman meet at a Holiday Inn in Los Angeles. They are both on business trips, and they are both from Mexico. They are both 26 years old, and they are both fluent in Spanish. They are both very nice people, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they are both very interested in each other. They are both very interested in each other, and they"}
{"answers": "#Person1# wants to lose weight. #Person2# suggests #Person1# take an exercise class to exercise more.", "prediction_text": "The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight."}
{"answers": "#Person2# offers #Person1# some suggestions to lose weight.", "prediction_text": "The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight."}
{"answers": "#Person2# gives #Person1# some suggestions on how to lose weight.", "prediction_text": "The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight. The dialogue is about a person who is overweight and wants to lose weight."}
{"answers": "James reserves a dining room for eight at a restaurant. #Person1# will ask the waitress to show him the way.", "prediction_text": "The restaurant is a popular place for dinning. The dinning room is always full. The dinning hall is a place for dinning. The dinning hall is a place for dinning. The dinning hall is a place for dinning. The dinning hall is a place for dinning. The dinning hall is a place for dinning. The dinning hall is a place for dinning. The dinning hall is a place for dinning. The dinning hall is a place for dinning. The dinning hall is a place for dinning. The dinning hall is a place for dinning. The dinning hall is a place for dinning. The dinning hall is a place for dinning."}
{"answers": "#Person1# confirms Jame's reservation at the restaurant and asks a waitress to show him the way.", "prediction_text": "The restaurant is a popular place for dinning. The dinning room is always full. The dinning hall is a place for the dinning room. The dinning room is a place for the dinning hall. The dinning hall is a place for the dinning room. The dinning room is a place for the dinning hall. The dinning hall is a place for the dinning room. The dinning room is a place for the dinning hall. The dinning hall is a place for the dinning room. The dinning room is a place for the dinning hall. The dinning hall is a place for the dinning room. The dinning room is a place for the dinning hall. The din"}
{"answers": "James has reserved a dining room and #Person1#'ll asks the waitress to show him the way.", "prediction_text": "The restaurant is a popular place for dinning. The dinning room is always full. The dinning hall is a place for the dinning room. The dinning room is a place for the dinning hall. The dinning hall is a place for the dinning room. The dinning room is a place for the dinning hall. The dinning hall is a place for the dinning room. The dinning room is a place for the dinning hall. The dinning hall is a place for the dinning room. The dinning room is a place for the dinning hall. The dinning hall is a place for the dinning room. The dinning room is a place for the dinning hall. The din"}
{"answers": "#Person1# is visiting a large plant and #Person2# introduces its basic information.", "prediction_text": "The plant is large and has a long history. It has a production shop where the raw material is processed and the finished product is assembled. The plant has three shifts and employs 500 people."}
{"answers": "#Person2# introduces the area of the plant, its history, size, and function to #Person1#.", "prediction_text": "The plant is large and has a long history. It has a production shop where the raw material is processed and the finished product is assembled. The plant has three shifts and employs 500 people."}
{"answers": "#Person1# and #Person2# visit a plant. #Person2# introduces its occupy, history, and employee number.", "prediction_text": "The plant is large and has a long history. It has three shifts and employs 500 people. The plant is involved in the production of raw materials and finished products."}
{"answers": "#Person1# is asking Rebecca about her working experience. She worked for York Herald as an intern and then as a junior local news reporter. After that, she began to work for London Weekly.", "prediction_text": "#Person1#: I see. And you were a journalist for a while?"}
{"answers": "Rebecca tells #Person1# about her working experiences. Rebecca first worked for the York Herald after her graduation, and then for the London Weekly.", "prediction_text": "#Person1#: I see. And you were a journalist for a while?"}
{"answers": "#Person1# interviews Rebecca and asks her about her previous working experiences.", "prediction_text": "#Person1#: I see. And you were a journalist for a while?"}
{"answers": "#Person1# and #Person2# will do a group report, so they plan to buy something to make posters. They make a shopping list before they go and find items according to signs on shelves.", "prediction_text": "#Person1#: We need to do a group report tomorrow. I need to go to the stationer to buy something for the posters."}
{"answers": "#Person1# and #Person2# make a shopping list and #Person1# helps #Person2# to find goods to make a poster for their group report tomorrow.", "prediction_text": "#Person1#: We need to do a group report tomorrow. I need to go to the stationer to buy something for the posters."}
{"answers": "#Person1# and #Person2# are preparing for the group report tomorrow and making a shopping list of things used to make the posters.", "prediction_text": "#Person1#: We need to do a group report tomorrow. I need to go to the stationer to buy something for the posters."}
{"answers": "Mary is tired because of finding jobs in the market. #Person1# suggests Mary apply for jobs on the Internet and says she could place her information on the Job Wanted channel on a recruitment web. Mary will try at once.", "prediction_text": "The Internet is a very convenient way to job hunt. You can apply for a job on the Internet, and you can also find a job on the Internet."}
{"answers": "#Person1# advises Mary to try applying for a job online and teaches her how to do it. Mary will try it.", "prediction_text": "The Internet is a very convenient way to job hunt. You can apply for a job on the Internet, and you can also find a job on the Internet."}
{"answers": "Mary goes to the personnel market every day and is tired. #Person1# suggests she go job hunting online. Mary will try it.", "prediction_text": "The Internet is a very convenient way to job hunt. You can apply for a job on the Internet, and you can also find a job on the Internet."}
{"answers": "#Person2# is making a shopping budget to save the rest of the money. #Person1# thinks it's a good idea.", "prediction_text": "#Person1#: What are you working on?"}
{"answers": "#Person2# tells #Person1# #Person2# plan to save money and #Person1# thinks it is good.", "prediction_text": "#Person1#: What are you working on?"}
{"answers": "#Person2# introduces the shopping budget which is used to save money to #Person1#. #Person1# thinks it's good.", "prediction_text": "#Person1#: What are you working on?"}
{"answers": "Jane is going to the hospital to see Susan. Henry tells her to catch the number 13.", "prediction_text": "The two people are talking about a hospital. The first person says that he saw Susan yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The"}
{"answers": "Jane will go to the hospital to visit Susan. Henry tells her she can take the number 13.", "prediction_text": "The two people are talking about a hospital. The first person says that he saw Susan yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The"}
{"answers": "Jane is going to visit Suman in the hospital, Henry suggests she catch a number 13 to get there.", "prediction_text": "The two people are talking about a hospital. The first person says that he saw Susan yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The first person says that he saw her yesterday. The second person says that he saw her yesterday. The"}
{"answers": "#Person1# wants to talk to #Person2# about the sales projections for next year. They decide to meet on Tuesday at 2:30 the next week.", "prediction_text": "#Person1# and #Person2# are both salespeople. #Person1# is trying to figure out how to use the spreadsheet #Person2# set up for him. #Person2# is trying to explain the spreadsheet to #Person1#."}
{"answers": "#Person1# and #Person2# negotiate on a time to talk about the sales projections for next year. They decide to meet next Tuesday.", "prediction_text": "#Person1# and #Person2# are both salespeople. #Person1# is trying to figure out how to use the spreadsheet #Person2# set up for him. #Person2# is trying to explain the spreadsheet to #Person1#."}
{"answers": "#Person1# and #Person2# are scheduling to talk about the sales projections for next year and they decide to meet next Tuesday.", "prediction_text": "#Person1# and #Person2# are both salespeople. #Person1# is trying to figure out how to use the spreadsheet #Person2# set up for him. #Person2# is trying to explain the spreadsheet to #Person1#."}
{"answers": "#Person1# is going to New York for the first time. #Person2# suggests #Person1# use a personal tour guide service to make #Person1#'s trip plan.", "prediction_text": "A friend in New York is a personal tour guide service that helps you create a perfect trip for you according to your budget."}
{"answers": "#Person2# recommends a personal tour guide service to #Person1#.", "prediction_text": "A friend in New York is a personal tour guide service that helps you create a perfect trip for you according to your budget."}
{"answers": "#Person2# introduces #Person1# a website for personal tour guide service.", "prediction_text": "A friend in New York is a personal tour guide service that helps you create a perfect trip for you according to your budget."}
{"answers": "#Person1# is interviewing #Person2#. They discuss department #Person2# wants to work in, salary, and fringe benefits.", "prediction_text": "The interviewer asked the applicant about his current job, his salary, fringe benefits, and insurance. The applicant said he would like to work in sales department. The interviewer asked the applicant how much he would be paid if he worked in the sales department. The applicant said he would be paid 2, 000 yuan per month plus some allowances and bonus. The interviewer asked the applicant how much he would be paid if he worked in the sales department. The applicant said he would be paid 2, 500 yuan per month. The interviewer asked the applicant how much he would be paid if he worked in the sales department. The applicant said he would be paid 2, 000 yuan per month plus some allowances and bonus. The interviewer asked the"}
{"answers": "#Person1# asks #Person2# how well #Person2# knows about the company, #Person2#'s ideal department, and current salary. #Person2# also asks #Person1# about the company's benefits and insurance.", "prediction_text": "The interviewer asked the applicant about his current job, his salary, fringe benefits, and insurance. The applicant said he would like to work in sales department. The interviewer asked the applicant how much he would be paid if he worked in the sales department. The applicant said he would be paid 2, 000 yuan per month plus some allowances and bonus. The interviewer asked the applicant how much he would be paid if he worked in the sales department. The applicant said he would be paid 2, 500 yuan per month. The interviewer asked the applicant how much he would be paid if he worked in the sales department. The applicant said he would be paid 2, 000 yuan per month plus some allowances and bonus. The interviewer asked the"}
{"answers": "#Person1# interviews #Person2# about the understanding of this company, personal preference, and salary expectation. #Person2# asks about the fringe benefits.", "prediction_text": "The interviewer asked the applicant about his current job, his salary, fringe benefits, and insurance. The applicant said he would like to work in sales department. The interviewer asked the applicant how much he would be paid if he worked in the sales department. The applicant said he would be paid 2, 000 yuan per month plus some allowances and bonus. The interviewer asked the applicant how much he would be paid if he worked in the sales department. The applicant said he would be paid 2, 500 yuan per month. The interviewer asked the applicant how much he would be paid if he worked in the sales department. The applicant said he would be paid 2, 000 yuan per month plus some allowances and bonus. The interviewer asked the"}
{"answers": "#Person1# comes to sign an agreement but it isn't ready until evening. So #Person1# is going through the draft.", "prediction_text": "The agreement was signed."}
{"answers": "#Person1# is checking the draft of an agreement and #Person2# will finish the agreement this evening.", "prediction_text": "The agreement was signed."}
{"answers": "#Person2# gives #Person1# an agreement draft and #Person1# thinks it good. #Person2# will get the agreement ready this evening.", "prediction_text": "The agreement was signed."}
{"answers": "#Person1# rent a car from ABC Rent-a-car Company this morning and met an accident. #Person2# will call an ambulance and police for #Person1#.", "prediction_text": "A man and a woman were driving a car when they ran into a guardrail. The man was seriously injured and the woman was not. The woman called the police and the ambulance. The police and the ambulance arrived at the scene and took the woman to the hospital. The police and the ambulance left the scene. The man was taken to the hospital by the ambulance. The man was in a serious condition. The police and the ambulance left the scene. The man was taken to the hospital by the ambulance. The man was in a serious condition. The police and the ambulance left the scene. The man was taken to the hospital by the ambulance. The man was in a serious condition. The police and the ambulance left the scene. The man was"}
{"answers": "#Person1# tells ABC Rent-a-car Company that #Person1# had a car accident. #Person2# will call an ambulance and the police.", "prediction_text": "A man and a woman were driving a car when they ran into a guardrail. The man was seriously injured and the woman was not. The woman called the police and the ambulance. The police and the ambulance arrived at the scene and took the woman to the hospital. The police and the ambulance left the scene. The man was taken to the hospital by the ambulance. The man was in a serious condition. The police and the ambulance left the scene. The man was taken to the hospital by the ambulance. The man was in a serious condition. The police and the ambulance left the scene. The man was taken to the hospital by the ambulance. The man was in a serious condition. The police and the ambulance left the scene. The man was"}
{"answers": "#Person1# calls ABC Rent-a-car Company to report #Person1#'s car accident. #Person2# will call an ambulance and the police.", "prediction_text": "A man and a woman were driving a car when they ran into a guardrail. The man was seriously injured and the woman was not. The woman called the police and the ambulance. The police and the ambulance arrived at the scene and took the woman to the hospital. The police and the ambulance left the scene. The man was taken to the hospital by the ambulance. The man was in a serious condition. The police and the ambulance left the scene. The man was taken to the hospital by the ambulance. The man was in a serious condition. The police and the ambulance left the scene. The man was taken to the hospital by the ambulance. The man was in a serious condition. The police and the ambulance left the scene. The man was"}
{"answers": "#Person1# is lost on the way to the school clinic. #Person2# shows #Person1# the correct direction.", "prediction_text": "A man and a woman are walking along a road. The man says, \"Excuse me, could you tell me how to get to the school clinic? I've lost my way.\" The woman says, \"Yes. Go straight ahead till you come to the traffic lights, turn left there and it's the first turning on the right.\" The man says, \"Thank you very much.\""}
{"answers": "#Person1# lost #Person1#'s way to the school clinic, #Person2# guides #Person1#.", "prediction_text": "A man and a woman are walking along a road. The man says, \"Excuse me, could you tell me how to get to the school clinic? I've lost my way.\" The woman says, \"Yes. Go straight ahead till you come to the traffic lights, turn left there and it's the first turning on the right.\" The man says, \"Thank you very much.\""}
{"answers": "#Person1# is lost, #Person2# shows the direction to the school clinic.", "prediction_text": "A man and a woman are walking along a road. The man says, \"Excuse me, could you tell me how to get to the school clinic? I've lost my way.\" The woman says, \"Yes. Go straight ahead till you come to the traffic lights, turn left there and it's the first turning on the right.\" The man says, \"Thank you very much.\""}
{"answers": "#Person2# wants to change her room because the baggage elevator is too noisy. #Person1# is sorry and tells her #Person1# will manage it when there's a spare room.", "prediction_text": "A woman is disturbed by the noise of the baggage elevator. She asks the hotel staff to change her room. The hotel staff is sorry but they don't have any spare rooms. The woman is not happy and leaves the hotel."}
{"answers": "#Person2# wants to change a room because it's too noisy, but #Person1# tells #Person2# there are no rooms left so #Person2# has to wait till tomorrow.", "prediction_text": "A woman is disturbed by the noise of the baggage elevator. She asks the hotel staff to change her room. The hotel staff is sorry but they don't have any spare rooms. The woman is not happy and leaves the hotel."}
{"answers": "#Person2# cannot stand the noise near her room and #Person1# promises to change her room tomorrow as there is no room available today.", "prediction_text": "A woman is disturbed by the noise of the baggage elevator. She asks the hotel staff to change her room. The hotel staff is sorry but they don't have any spare rooms. The woman is not happy and leaves the hotel."}
{"answers": "#Person1# is driving #Person2# to the Beijing hotel. #Person2# will attend a banquet at six o'clock in the International Hotel.", "prediction_text": "The two people are in a taxi. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking"}
{"answers": "#Person1# warmly welcomes #Person2# and drives #Person2# to the Beijing Hotel. Mr. Wu has arranged a banquet for #Person2# in the evening.", "prediction_text": "The two people are in a taxi. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking"}
{"answers": "#Person1# has arranged everything for #Person2# after #Person2# arrives in Beijing and invites #Person2# to a banquet.", "prediction_text": "The two people are in a taxi. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking. The driver is driving the car. The two people are talking"}
{"answers": "#Person1# gets lost on the way to Liu Ichang. #Person2# tells #Person1# to take Bus No. 713 because it's far.", "prediction_text": "A man is lost in a city. He asks a woman for directions. She tells him to go to the Olympic Park. He asks her how to get there. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No"}
{"answers": "#Person2# helps #Person1# to find out the location of Liu Ichang and tells #Person1# to take Bus No. 173.", "prediction_text": "A man is lost in a city. He asks a woman for directions. She tells him to go to the Olympic Park. He asks her how to get there. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No"}
{"answers": "#Person1# gets lost and #Person2# shows the way to Liu Ichang to #Person1#.", "prediction_text": "A man is lost in a city. He asks a woman for directions. She tells him to go to the Olympic Park. He asks her how to get there. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No. 713. He asks her how to get to the Olympic Park. She tells him to take Bus No"}
{"answers": "#Person2# finds that the computer has stopped running. #Person1# suggests #Person2# ring up the repairmen.", "prediction_text": "#Person1#: What's the matter with this computer?"}
{"answers": "#Person1# advises #Person2# to call the repairmen to fix #Person2#'s computer.", "prediction_text": "#Person1#: What's the matter with this computer?"}
{"answers": "#Person2#'s computer breaks down and #Person1# suggests ringing up the repairman.", "prediction_text": "#Person1#: What's the matter with this computer?"}
{"answers": "#Person2# wants to buy a gift for his mother's birthday. After knowing his mother's preference, #Person1# recommends a gold watch.", "prediction_text": "A woman is looking for a gift for her mother's birthday. She asks a salesperson for advice. The salesperson suggests a watch. The woman says she doesn't like jewels. The salesperson suggests a gold watch. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson"}
{"answers": "#Person1# helps #Person2# pick a gift for #Person2#'s mother, and #Person2# decides to buy a watch.", "prediction_text": "A woman is looking for a gift for her mother's birthday. She asks a salesperson for advice. The salesperson suggests a watch. The woman says she doesn't like jewels. The salesperson suggests a gold watch. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson"}
{"answers": "#Person2# is looking for a gift for his mother in a shop and #Person1# gives him some helpful suggestions.", "prediction_text": "A woman is looking for a gift for her mother's birthday. She asks a salesperson for advice. The salesperson suggests a watch. The woman says she doesn't like jewels. The salesperson suggests a gold watch. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson suggests a gold watch with big, clear numbers. The woman says she doesn't like the gold. The salesperson"}
{"answers": "Ms. Ross and Mr. Fisher are having a coffee chat face to face. They talk about their career, child, life, and then get down to business. Ms. Ross will provide service for a presentation on Saturday 24 October in the morning held by Mr. Fisher. They are discussing the preparations as time, food, furniture, guest numbers. A problem is Mr. Fisher sent out 100 invitations but the room's capacity is only about 50.", "prediction_text": "#Person1#: Ah, good morning, Ms. Ross, do come in."}
{"answers": "Ms. Ross helps Mr. Fisher make arrangements for a presentation. Mr. Fisher decides to serve a pre-lunch snack buffet and designs the room setting. Mr. Fisher isn't sure about how many people to come and Ms. Ross is worried that there might be too many people because the room is small.", "prediction_text": "#Person1#: Ah, good morning, Ms. Ross, do come in."}
{"answers": "Ms. Ross and Mr. Fisher start the conversation with greetings. Then they discuss the preparation for a presentation. Ms. Ross confirms the details of the presentation, including schedule, food service. Ms. Ross is concerned that Mr. Fisher's sent too many invitations than the room capacity.", "prediction_text": "#Person1#: Ah, good morning, Ms. Ross, do come in."}
{"answers": "#Person2# tells #Person1#, in Russia, things happen very fast except for bank business, while in Canada people are calmer.", "prediction_text": "In Russia, people are fast movers by nature. In Canada, people are more relaxed. In Russia, people are more calm. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people"}
{"answers": "#Person1# and #Person2# are talking about the differences between Russia and Canada. #Person2# tells #Person1# that almost everything in Russia happens fast while it's the opposite in Canada.", "prediction_text": "In Russia, people are fast movers by nature. In Canada, people are more relaxed. In Russia, people are more calm. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people"}
{"answers": "#Person1# and #Person2# are sharing their opinions towards how different is Russia from Canada in lifestyle, especially the speed in life.", "prediction_text": "In Russia, people are fast movers by nature. In Canada, people are more relaxed. In Russia, people are more calm. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people are more relaxed. In Canada, people are more relaxed. In Russia, people"}
{"answers": "#Person2# thought to travel to the Caribbean Sea. #Person1# went there last year and thinks the weather can sometimes be really bad.", "prediction_text": "The two people in the story are talking about going on a vacation. The first person says that they were thinking about going to the Caribbean Sea. The second person says that they were thinking about going to Jamaica and the Cayman Islands. The first person says that they were reading weather reports on the internet. The second person says that they were reading weather reports on the internet. The first person says that they were hoping for the best. The second person says that they were hoping for the best."}
{"answers": "#Person2# wanted to travel to the Caribbean Sea and but is worried about the storms.", "prediction_text": "#Person1#: Where are you going on vacation this year?"}
{"answers": "#Person2# hasn't decided to go to the Caribbean Sea on vacation because #Person2# is concerned about the storms there.", "prediction_text": "#Person1#: Where are you going on vacation this year?"}
{"answers": "#Person1# and #Person1#'s mother are preparing the fruits they are going to take to the picnic.", "prediction_text": "The dialogue is a simple exchange between two people who are trying to decide what to take on a picnic. The dialogue is a good example of a simple exchange between two people who are trying to decide what to take on a picnic. The dialogue is a good example of a simple exchange between two people who are trying to decide what to take on a picnic. The dialogue is a good example of a simple exchange between two people who are trying to decide what to take on a picnic. The dialogue is a good example of a simple exchange between two people who are trying to decide what to take on a picnic. The dialogue is a good example of a simple exchange between two people who are trying to decide what to take on a picnic. The dialogue is a"}
{"answers": "#Person1# and #Person2# prepare the fruits for the picnic.", "prediction_text": "The dialogue is a simple exchange between two people who are trying to decide what to take on a picnic. The dialogue is a good example of a simple exchange between two people who are trying to decide what to take on a picnic. The dialogue is a good example of a simple exchange between two people who are trying to decide what to take on a picnic. The dialogue is a good example of a simple exchange between two people who are trying to decide what to take on a picnic. The dialogue is a good example of a simple exchange between two people who are trying to decide what to take on a picnic. The dialogue is a good example of a simple exchange between two people who are trying to decide what to take on a picnic. The dialogue is a"}
{"answers": "#Person1# and #Person2# are preparing fruits for the picnic.", "prediction_text": "The two people are having a picnic. One of them has a basket with some fruit. The other person has a basket with some other things. The two people are talking about what they should take."}
{"answers": "#Person1# wants to rent an economy car from #Person2# for $19 a day or $129 a week.", "prediction_text": "The person who rented the car was asked to fill out a form. The form asked for the person's name, address, phone number, and the car's make, model, and color. The person was also asked to sign the form. The person was then asked to pay for the car. The person was asked to pay $19 a day or$129 a week, unlimited mileage. The person was also asked to provide a driver's license. The person was then asked to complete a form. The form asked for the person's name, address, phone number, and the car's make, model, and color. The person was also asked to sign the form. The person was then asked to pay for the car. The person was"}
{"answers": "#Person1# rents a car from #Person2#", "prediction_text": "The person who rented the car was asked to fill out a form. The form asked for the person's name, address, phone number, and the car's make, model, and color. The person was also asked to sign the form. The person was then asked to pay for the car. The person was asked to pay $19 a day or$129 a week, unlimited mileage. The person was also asked to provide a driver's license. The person was then asked to complete a form. The form asked for the person's name, address, phone number, and the car's make, model, and color. The person was also asked to sign the form. The person was then asked to pay for the car. The person was"}
{"answers": "#Person1# rents an economy car in #Person2#'s shop.", "prediction_text": "The person who rented the car was asked to fill out a form. The form asked for the person's name, address, phone number, and the car's make, model, and color. The person was also asked to sign the form. The person was then asked to pay for the car. The person was asked to pay $19 a day or$129 a week, unlimited mileage. The person was also asked to provide a driver's license. The person was then asked to complete a form. The form asked for the person's name, address, phone number, and the car's make, model, and color. The person was also asked to sign the form. The person was then asked to pay for the car. The person was"}
{"answers": "#Person1# hates those who keep smiling at #Person1# and #Person2# is fed up with those who keep a straight face in front of #Person2#.", "prediction_text": "#Person1#: I am fed up with those who keep smiling to me. #Person2#: That means you don't like them. If you hit it off with them, you will like to see their smiles."}
{"answers": "#Person2# is fed up with people with a straight face, and #Person1# hates people keeping smiling to #Person1#.", "prediction_text": "#Person1#: I am fed up with those who keep smiling to me. #Person2#: That means you don't like them. If you hit it off with them, you will like to see their smiles."}
{"answers": "#Person1# and #Person2# hold different opinions towards people with different facial expressions.", "prediction_text": "#Person1#: I am fed up with those who keep smiling to me. #Person2#: That means you don't like them. If you hit it off with them, you will like to see their smiles."}
{"answers": "#Person1# and #Person2# are talking about the good performance of their business for last year. #Person2# thinks the success is partly because of Wallace's contribution, and partly because of their new marketing strategy.", "prediction_text": "The two people are discussing the sales of the company. The first person says that the sales of the company have increased a lot this year. The second person says that the sales of the company have increased a lot this year. The first person says that the sales of the company have increased a lot this year. The second person says that the sales of the company have increased a lot this year. The first person says that the sales of the company have increased a lot this year. The second person says that the sales of the company have increased a lot this year. The first person says that the sales of the company have increased a lot this year. The second person says that the sales of the company have increased a lot this year. The first person"}
{"answers": "#Person1# and #Person2# talk about the success of their business and attribute the success to Wallace's contribution and the new marketing strategy. They hope good luck can last.", "prediction_text": "The two people are discussing the sales figures of the company. The first person says that the sales have increased by 120% and the second person says that the sales have increased by 8%. The first person says that the company has made a profit and the second person says that the company has made a profit. The first person says that the company has made a profit and the second person says that the company has made a profit. The first person says that the company has made a profit and the second person says that the company has made a profit. The first person says that the company has made a profit and the second person says that the company has made a profit. The first person says that the company has made a profit and the second person"}
{"answers": "#Person1# and #Person2# are pleasant to see their business performance has improved last year. They think the credits should be given to Wallace and the new marketing strategy.", "prediction_text": "The two people are discussing the sales figures of the company. The first person says that the sales have increased by 120% and the second person says that the sales have increased by 8%. The first person says that the company has made a profit and the second person says that the company has made a profit. The first person says that the company has made a profit and the second person says that the company has made a profit. The first person says that the company has made a profit and the second person says that the company has made a profit. The first person says that the company has made a profit and the second person says that the company has made a profit. The first person says that the company has made a profit and the second person"}
{"answers": "#Person2# left #Person2#'s backpack and wallet in a taxi and failed to contact the driver because #Person2# didn't have the number. #Person1# lends 50 dollars to #Person1# and will drive #Person2# home.", "prediction_text": "A man lost his wallet and his backpack. He called the taxi driver to ask for the receipt. The driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver"}
{"answers": "#Person2# borrows some money from #Person1# because #Person2#'s backpack and wallet were lost and cannot take it back. #Person1# will drive #Person2# home.", "prediction_text": "A man lost his wallet and his backpack. He called the taxi driver to ask for the receipt. The driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver"}
{"answers": "#Person2# lost #Person2#'s backpack, wallet, and money in a taxi. #Person1# lends #Person2# some money and will take #Person2# home.", "prediction_text": "A man lost his wallet and his backpack. He called the taxi driver to ask for the receipt. The driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver didn't have the receipt, so he called the taxi company. The taxi company didn't have the receipt, so he called the taxi driver. The taxi driver"}
{"answers": "Steven and Lin just had a great meal. Then they talk about the different tipping cultures between America and China.", "prediction_text": "The Chinese are very hospitable, but they don't like to be asked for money."}
{"answers": "Steven buys Lin a magnificent dinner in America and they then talk about the tipping culture in China and America.", "prediction_text": "The Chinese are very hospitable, but they don't like to be asked for money."}
{"answers": "Steven treats Lin to a nice meal. Then they talk about the tipping cultures in their countries.", "prediction_text": "The Chinese are very hospitable, but they don't like to be asked for money."}
{"answers": "Bill is happy because he made a move to know his roommate today.", "prediction_text": "#Person1#: Bill, how can you hear so happy today?"}
{"answers": "#Person1# and Bill talk about Bill's roommate Brain Locker.", "prediction_text": "#Person1#: Bill, how can you hear so happy today?"}
{"answers": "Bill tells #Person1# that he has made a move to read of his roommate.", "prediction_text": "#Person1#: Bill, how can you hear so happy today?"}
{"answers": "#Person2# checks Tom Wilson's information and Tom pays his hotel and meal bill.", "prediction_text": "The hotel charges a fixed rate for each room, and the bill is calculated by adding the room rate and the meals. The hotel also offers a credit card payment option. The customer can pay by credit card, and the hotel will charge a 2% fee for credit card payments. The customer can also pay by cash, but the hotel will charge a 5% fee for cash payments. The customer can also pay by check, but the hotel will charge a 10% fee for check payments. The customer can also pay by credit card, and the hotel will charge a 2% fee for credit card payments. The customer can also pay by cash, but the hotel will charge a 5% fee for cash payments. The customer can"}
{"answers": "Tom Wilson pays for his bill for hotel and food by credit card.", "prediction_text": "The hotel charges a fixed rate for each room, and the bill is calculated by adding the room rate and the meals. The hotel also offers a credit card payment option. The customer can pay by credit card and sign the bill."}
{"answers": "Tom Wilson pays the bill for his room and meal cost by credit card.", "prediction_text": "The hotel charges a fixed rate for each room, and the bill is calculated by adding the room rate and the meals. The hotel also offers a credit card payment option. The customer can pay by credit card and sign the bill."}
{"answers": "Susan calls Carol to ask about the party time. But Carol is taking a shower so #Person1# answers for her.", "prediction_text": "Carol is a woman who is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is"}
{"answers": "Carol is taking a shower when Carol calls her, so #Person1# answers the telephone and tells her the party time.", "prediction_text": "Carol is a woman who is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is"}
{"answers": "Susan calls to ask Carol about the party time. #Person1# answers the phone and tells her.", "prediction_text": "Carol is a woman who is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is a bit of a busybody. She is a bit of a busybody because she is"}
{"answers": "#Person1# thinks that she knows #Person2# somewhere, but #Person2# denies it.", "prediction_text": "A man and a woman are having a conversation. The man is trying to remember the name of the woman he has just met. The woman is trying to remember the name of the man she has just met. The man is trying to remember the name of the woman he has just met. The woman is trying to remember the name of the man she has just met. The man is trying to remember the name of the woman he has just met. The woman is trying to remember the name of the man she has just met. The man is trying to remember the name of the woman he has just met. The woman is trying to remember the name of the man she has just met. The man is trying to remember the name of the woman he"}
{"answers": "#Person1# thinks she has met #Person2# somewhere, but #Person2# thinks it's a mistake.", "prediction_text": "A man and a woman are having a conversation. The man is trying to remember the name of the woman he has just met. The woman is trying to remember the name of the man she has just met. The man is trying to remember the name of the woman he has just met. The woman is trying to remember the name of the man she has just met. The man is trying to remember the name of the woman he has just met. The woman is trying to remember the name of the man she has just met. The man is trying to remember the name of the woman he has just met. The woman is trying to remember the name of the man she has just met. The man is trying to remember the name of the woman he"}
{"answers": "#Person1# keeps asking where #Person2#'s from because she thinks she knows #Person2# but #Person2# denies it.", "prediction_text": "A man and a woman are having a conversation. The man is trying to remember the name of the woman he has just met. The woman is trying to remember the name of the man she has just met. The man is trying to remember the name of the woman he has just met. The woman is trying to remember the name of the man she has just met. The man is trying to remember the name of the woman he has just met. The woman is trying to remember the name of the man she has just met. The man is trying to remember the name of the woman he has just met. The woman is trying to remember the name of the man she has just met. The man is trying to remember the name of the woman he"}
{"answers": "#Person1# is crazy for Trump and voted for him. #Person2# doesn't agree with #Person1# on Trump and will vote for Biden.", "prediction_text": "The two people are talking about the election. The first person is a Trump supporter, and the second person is a Biden supporter. The first person is saying that he is not sure if Trump will be a good president, and the second person is saying that he is sure that Trump will be a good president. The first person is saying that he is not sure if Trump will be a good president, and the second person is saying that he is sure that Trump will be a good president. The first person is saying that he is not sure if Trump will be a good president, and the second person is saying that he is sure that Trump will be a good president. The first person is saying that he is not sure if Trump will be a good"}
{"answers": "#Person1# is a crazy fan of Trump and wants him to be re-elected. #Person2# will vote for Biden.", "prediction_text": "The two people are talking about the election. The first person is a Trump supporter, and the second person is a Biden supporter. The first person is saying that he is not sure if Trump will be a good president, and the second person is saying that he is sure that Trump will be a good president. The first person is saying that he is not sure if Trump will be a good president, and the second person is saying that he is sure that Trump will be a good president. The first person is saying that he is not sure if Trump will be a good president, and the second person is saying that he is sure that Trump will be a good president. The first person is saying that he is not sure if Trump will be a good"}
{"answers": "#Person1# is crazy for Trump and voted for him but #Person2# will vote for Biden.", "prediction_text": "The two people are talking about the election. The first person is a Trump supporter, and the second person is a Biden supporter. The first person is saying that he is not sure if Trump will be a good president, and the second person is saying that he is sure that Trump will be a good president. The first person is saying that he is not sure if Trump will be a good president, and the second person is saying that he is sure that Trump will be a good president. The first person is saying that he is not sure if Trump will be a good president, and the second person is saying that he is sure that Trump will be a good president. The first person is saying that he is not sure if Trump will be a good"}
{"answers": "#Person1# doesn't know how to use the ATM. #Person2# teaches #Person1# step by step.", "prediction_text": "This is a great way to teach a new skill to a child. It is also a great way to teach a child to be patient and to be persistent. It is also a great way to teach a child to be polite and to be respectful. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child"}
{"answers": "#Person1# doesn't know how to use an ATM. #Person2# teaches #Person1#.", "prediction_text": "This is a great way to teach a new skill to a child. It is also a great way to teach a child to be patient and to be persistent. It is also a great way to teach a child to be polite and to be respectful. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child"}
{"answers": "#Person2# is teaching #Person1# how to use an ATM step by step because #Person1# doesn't know how to use.", "prediction_text": "This is a great way to teach a new skill to a child. It is also a great way to teach a child to be patient and to be persistent. It is also a great way to teach a child to be polite and to be respectful. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child to be responsible and to be accountable. It is also a great way to teach a child"}
{"answers": "Susan wants to find the memo. John has no idea where it is.", "prediction_text": "Susan Miller is a secretary in a large corporation. She is a very good secretary, and she is very good at her job. She is also a very good friend. She is also a very good friend of John. John is a very good friend of Susan's. John is also a very good friend of Susan's. Susan is a very good friend of John's. John is a very good friend of Susan's. Susan is a very good friend of John's. John is a very good friend of Susan's. Susan is a very good friend of John's. John is a very good friend of Susan's. Susan is a very good friend of John's. John is a very good friend of Susan's. Susan is a very good"}
{"answers": "Susan Miller is asking John if he knows where the memo is. John has no idea.", "prediction_text": "Susan Miller is a secretary in a large corporation. She is a very good secretary, and she is very good at her job. She is also a very good friend. She is also a very good friend of John. John is a very good friend of Susan's. John is also a very good friend of Susan's. Susan is a very good friend of John's. John is a very good friend of Susan's. Susan is a very good friend of John's. John is a very good friend of Susan's. Susan is a very good friend of John's. John is a very good friend of Susan's. Susan is a very good friend of John's. John is a very good friend of Susan's. Susan is a very good"}
{"answers": "Susan asks John where a memo is but John doesn't know.", "prediction_text": "Susan Miller is a secretary in a large corporation. She is a very good secretary, but she is also a very good friend. She is also a very good friend to John, who is a very good friend to her. She is also a very good friend to John's boss, who is a very good friend to her. She is also a very good friend to John's boss's boss, who is a very good friend to her. She is also a very good friend to John's boss's boss's boss, who is a very good friend to her. She is also a very good friend to John's boss's boss's boss's boss, who is a very good friend to her. She is also a very good friend to John"}
{"answers": "#Person1# invites Lily to picnic this weekend. Lily accepts.", "prediction_text": "Lily and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't want to go there. Lily wants to go to the river, but her friend doesn't want to go there. Lily and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't want to go there. Lily and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't want to go there. Lily and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't want to go there. Lily and her friend are going to a picnic. Lily wants to go to the river, but her"}
{"answers": "#Person1# is inviting Lily to take part in their picnic this weekend, and she accepts.", "prediction_text": "Lily and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't want to go there. Lily wants to go to the river, but her friend doesn't want to go there. Lily and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't want to go there. Lily and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't want to go there. Lily and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't want to go there. Lily and her friend are going to a picnic. Lily wants to go to the river, but her"}
{"answers": "#Person1# invites Lily to take part in their weekend picnic and Lily accepts.", "prediction_text": "Lily and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't want to go there. Lily wants to go to the river, but her friend doesn't want to go there. Lily and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't want to go there. Lily and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't want to go there. Lily and her friend are going to a picnic. Lily wants to go to the river, but her friend doesn't want to go there. Lily and her friend are going to a picnic. Lily wants to go to the river, but her"}
{"answers": "#Person1# asks #Person2# about the table manners in China. #Person2# says there are many hazy rules that are different from Western. And #Person2# tells #Person1# stabbing chopsticks into a bowl resembles sacrifices for the death and is very inauspicious.", "prediction_text": "The Chinese are known for their hospitality and warmth. But they also have a number of customs and practices that are not familiar to foreigners."}
{"answers": "#Person1# and #Person2# are discussing the differences between China and Western feasts. There are so many rules on the Chinese table, and they both feel hazy about its etiquette.", "prediction_text": "The Chinese are known for their hospitality and warmth. But they also have a number of customs and practices that are not familiar to foreigners."}
{"answers": "#Person1# and #Person2# talk about the difference in table etiquette in China. They both feel hazy about Chinese table etiquette and wrong use of chopsticks can lead to people's enrage.", "prediction_text": "The Chinese are known for their hospitality and warmth. But they also have a number of customs and practices that are not familiar to foreigners."}
{"answers": "Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies.", "prediction_text": "The two friends, Frank and Mary, are talking about their favorite movies. Frank says that he watches a lot of movies, and Mary says that she watches whatever movies there are. Frank says that he likes art films, and Mary says that she likes both art films and thrillers. Frank says that he goes to the cinema once in a while, and Mary says that she goes to the cinema once in a while. Frank says that he likes Movie Salon, and Mary says that she likes Movie Salon. Frank says that he has a membership there, and Mary says that she has a membership there. Frank says that he likes to watch movies, and Mary says that she likes to watch movies. Frank says that he goes to the cinema once in a"}
{"answers": "Frank and Mary both like watching movies in their spare time. Mary usually rents movies at Movie Salon, and Frank is interested in signing up for its membership.", "prediction_text": "The two friends had a conversation about their favorite movies. They also talked about their favorite genres."}
{"answers": "Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies", "prediction_text": "The two friends had a conversation about their favorite movies. They also talked about their favorite genres."}
{"answers": "#Person2# wanted to join a small political party and thinks the smaller group can influence the larger one. #Person1# and #Person2# agree that most people don't fully understand politics.", "prediction_text": "#Person1#: Have you ever belonged to a political party? #Person2#: No, I haven't, but I thought about joining the green party. #Person1#: Really? I know you are very concerned about the environment. You were a member of the pressure group Greenpeace, weren't you? #Person2#: Yes. I was. But I didn't have enough time to devote to it. #Person1#: The green party have no chance of winning an election. The other parties are too big and popular. #Person2#: You're right. But smaller political and pressure groups can often influence large political parties. Any member of parliament can propose legislation and parties and pressure groups can raise awareness #Person1#: Most people"}
{"answers": "#Person2# believes smaller political and pressure groups can influence large parties. #Person1# and #Person2# both agree that most people often don't understand politics fully.", "prediction_text": "#Person1#: Have you ever belonged to a political party? #Person2#: No, I haven't, but I thought about joining the green party. #Person1#: Really? I know you are very concerned about the environment. You were a member of the pressure group Greenpeace, weren't you? #Person2#: Yes. I was. But I didn't have enough time to devote to it. #Person1#: The green party have no chance of winning an election. The other parties are too big and popular. #Person2#: You're right. But smaller political and pressure groups can often influence large political parties. Any member of parliament can propose legislation and parties and pressure groups can raise awareness #Person1#: Most people"}
{"answers": "#Person2# thought about joining a small party and thinks smaller political and pressure groups can influence larger ones. #Person1# and #Person2# agree most people don't understand political issues fully.", "prediction_text": "#Person1#: Have you ever belonged to a political party? #Person2#: No, I haven't, but I thought about joining the green party. #Person1#: Really? I know you are very concerned about the environment. You were a member of the pressure group Greenpeace, weren't you? #Person2#: Yes. I was. But I didn't have enough time to devote to it. #Person1#: The green party have no chance of winning an election. The other parties are too big and popular. #Person2#: You're right. But smaller political and pressure groups can often influence large political parties. Any member of parliament can propose legislation and parties and pressure groups can raise awareness #Person1#: Most people"}
{"answers": "#Person1# apologizes for mistakes in goods. #Person1# will be responsible for Mr. Wilson's loss, and take measures to avoid such mistakes.", "prediction_text": "The two parties have a dialogue about the mistake in the goods. The first party is sorry for the mistake and the second party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The second party is sorry for the mistake and the first party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The first party is sorry for the mistake and the second party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The second party is sorry for the mistake and the first party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The second party is sorry"}
{"answers": "#Person1# feels sorry for Mr. Wilson's loss caused by #Person1# and assures that such mistakes will not happen again.", "prediction_text": "The two parties have a dialogue about the mistake in the goods. The first party is sorry for the mistake and the second party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The second party is sorry for the mistake and the first party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The first party is sorry for the mistake and the second party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The second party is sorry for the mistake and the first party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The second party is sorry"}
{"answers": "#Person1# apologizes for the loss caused by them to Mr. Wilson and assures that it will never happen again.", "prediction_text": "The two parties have a dialogue about the mistake in the goods. The first party is sorry for the mistake and the second party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The second party is sorry for the mistake and the first party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The first party is sorry for the mistake and the second party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The second party is sorry for the mistake and the first party is responsible for the mistake. The first party will be responsible for the second party to exchange the goods. The second party is sorry"}
{"answers": "#Person1# asks #Person2# who saw the robbery some questions. #Person2# agrees to come to the station for more questioning.", "prediction_text": "#Person1#: Did you see the robbery?"}
{"answers": "#Person1# asks #Person2# who saw a robbery some questions and #Person2# is willing to go to the station for more questioning.", "prediction_text": "#Person1#: Did you see the robbery?"}
{"answers": "#Person2# tells #Person1# #Person2# witnessed the robbery and agrees to take more questions in the station.", "prediction_text": "#Person1#: Did you see the robbery?"}
{"answers": "#Person1# and #Person2#'s parents are out on a date and will go out for dinner regularly.", "prediction_text": "A man and a woman are having dinner at a restaurant. The man is starving and the woman is not. The man asks the woman if she knows what's for dinner. The woman says that they are on their own tonight. The man asks what they mean by that. The woman says that they are going out on a date. The man asks what they mean by that. The woman says that they are going to dinner. The man asks what they mean by that. The woman says that they are going to a restaurant. The man asks what they mean by that. The woman says that they are going to a restaurant. The man asks what they mean by that. The woman says that they are going to a restaurant. The man asks what"}
{"answers": "The parents of #Person1# and #Person2# are out on a date.", "prediction_text": "A man and a woman are having dinner at a restaurant. The man is starving and wants to order something. The woman says that they are on their own tonight, and that her husband is taking her out. The man says that he doesn't know what to order, and the woman says that they are going to order pizza. The man says that he doesn't want to order pizza, and the woman says that they are going to order pizza. The man says that he doesn't want to order pizza, and the woman says that they are going to order pizza. The man says that he doesn't want to order pizza, and the woman says that they are going to order pizza. The man says that he doesn't want to order pizza,"}
{"answers": "#Person1# and #Person2#'s parent are out on a date to revive their old tradition.", "prediction_text": "A man and a woman are having dinner at a restaurant. The man is starving and wants to order something. The woman says that they are on their own tonight, and that her husband is taking her out. The man says that he doesn't know what to order, and the woman says that they are going to order pizza. The man says that he doesn't want to order pizza, and the woman says that they are going to order pizza. The man says that he doesn't want to order pizza, and the woman says that they are going to order pizza. The man says that he doesn't want to order pizza, and the woman says that they are going to order pizza. The man says that he doesn't want to order pizza,"}
{"answers": "#Person1# has decided to go on a diet in the new year. Carol reasonably doubts it.", "prediction_text": "A man and a woman are having a conversation. The man is trying to convince the woman to go on a diet. The woman is not convinced."}
{"answers": "#Person1#'s decided to go on a diet for New Year's resolution. Carol doesn't believe #Person1# will stick to it.", "prediction_text": "A man and a woman are having a conversation. The man is trying to convince the woman to go on a diet. The woman is not convinced."}
{"answers": "#Person1# has decided to go on a diet in the new year. Carol doesn't believe #Person1# will stick to it", "prediction_text": "A man and a woman are having a conversation. The man is trying to convince the woman to go on a diet. The woman is not convinced."}
{"answers": "Karen Huang tried to register for Comp Lit 287 but failed because it's full. So Karen comes to the department office. #Person1# finds Karen is a Comp Lit major and the university has saved extra places for them. #Person1# gives Karen a special code and tells Karan how to use it to get into the class.", "prediction_text": "A student who is a Comp Lit major is trying to get into a required class. The student is told that the class is full. The student asks the professor for a code to get into the class. The professor gives the student the code and tells the student that the student will be able to get into the class. The student calls the touch-tone registration system and follows the directions on the sheet. The student gets into the class."}
{"answers": "Karen Huang couldn't register for Comp Lit 287 so Karen comes to talk to #Person1# for a solution. #Person1# says it is full and will put Karen on the waiting list. But after #Person1# knows Karen majors in comparative literature, #Person1# gives her a special code to register for the class, as they've saved extra places for them.", "prediction_text": "A student who is a Comp Lit major is trying to get into a required class. The student is told that the class is full. The student asks the professor for a code to get into the class. The professor gives the student the code and tells the student that the student will be able to get into the class. The student calls the touch-tone registration system and follows the directions on the sheet. The student gets into the class."}
{"answers": "Karen Huang wants to register for a class. #Person1# says it's full and will put Karen on the waiting list. But then #Person1# gives Karen a special code to register the class after #Person1# knows Karen is a Comparative Literature major.", "prediction_text": "A student who is a Comp Lit major is trying to get into a required class. The student is told that the class is full. The student asks the professor for a code to get into the class. The professor gives the student the code and tells the student that the student will be able to get into the class. The student calls the touch-tone registration system and follows the directions on the sheet. The student gets into the class."}
{"answers": "#Person2# voluntarily shares an umbrella with #Person1# who doesn't bring the umbrella when it's rainy.", "prediction_text": "A man and a woman are walking together. The man says, \"Excuse me, Sir, do you know what time it is?\" The woman says, \"Oh, it's almost eleven twenty.\" The man says, \"Thank you.\" The woman says, \"You're welcome.\" The man says, \"I was in a hurry and I forgot my umbrella today.\" The woman says, \"I have one. We could share it. Which way are you going?\" The man says, \"Oh, that's very kind of you. I am going to the Garden Hotel.\" The woman says, \"I am going there, too. We can walk together.\" The man says, \"Sure.\""}
{"answers": "#Person1# forgot to take an umbrella while it's raining. #Person2# shares an umbrella with #Person1#.", "prediction_text": "A man and a woman are walking together. The man says, \"Excuse me, Sir, do you know what time it is?\" The woman says, \"Oh, it's almost eleven twenty.\" The man says, \"Thank you.\" The woman says, \"You're welcome.\" The man says, \"I was in a hurry and I forgot my umbrella today.\" The woman says, \"I have one. We could share it. Which way are you going?\" The man says, \"Oh, that's very kind of you. I am going to the Garden Hotel.\" The woman says, \"I am going there, too. We can walk together.\" The man says, \"Sure.\""}
{"answers": "#Person2# shares an umbrella with #Person1# when it's rainy.", "prediction_text": "A man and a woman are walking together. The man says, \"Excuse me, Sir, do you know what time it is?\" The woman says, \"Oh, it's almost eleven twenty.\" The man says, \"Thank you.\" The woman says, \"You're welcome.\" The man says, \"I was in a hurry and I forgot my umbrella today.\" The woman says, \"I have one. We could share it. Which way are you going?\" The man says, \"Oh, that's very kind of you. I am going to the Garden Hotel.\" The woman says, \"I am going there, too. We can walk together.\" The man says, \"Sure.\""}
{"answers": "Jack gives Daisy a ride in his new car. Daisy praises it.", "prediction_text": "Jack and Daisy are driving their new car on the highway. They are talking about their new car. Jack says that it is the fastest car he has ever owned. Daisy says that it is the best car she has ever owned. Jack says that it is a great feeling to drive it. Daisy says that it is a great feeling to drive it. Jack says that it is a great feeling to drive it. Daisy says that it is a great feeling to drive it. Jack says that it is a great feeling to drive it. Daisy says that it is a great feeling to drive it. Jack says that it is a great feeling to drive it. Daisy says that it is a great feeling to drive it. Jack says that it is a great feeling"}
{"answers": "Jack bought a new car and invites Daisy to hop in and take a ride. Daisy thinks it's a good feeling.", "prediction_text": "Jack and Daisy are driving their new car on the highway. They are talking about their new car. Jack says that it is the fastest car he has ever owned. Daisy says that it is the best car she has ever owned. Jack says that it is a great feeling to drive it. Daisy says that it is a great feeling to drive it. Jack says that it is a great feeling to drive it. Daisy says that it is a great feeling to drive it. Jack says that it is a great feeling to drive it. Daisy says that it is a great feeling to drive it. Jack says that it is a great feeling to drive it. Daisy says that it is a great feeling to drive it. Jack says that it is a great feeling"}
{"answers": "Jack takes Daisy for a ride to experience his new car. Daisy thinks he makes a perfect choice.", "prediction_text": "Jack and Daisy are driving their new car on the highway. They are talking about their new car. Jack says that it is the fastest car he has ever owned. Daisy says that it is the best car she has ever owned. Jack says that it is a great feeling to drive it. Daisy says that it is a great feeling to drive it. Jack says that it is a great feeling to drive it. Daisy says that it is a great feeling to drive it. Jack says that it is a great feeling to drive it. Daisy says that it is a great feeling to drive it. Jack says that it is a great feeling to drive it. Daisy says that it is a great feeling to drive it. Jack says that it is a great feeling"}
{"answers": "#Person1# asks #Person2# the exact time when the fire broke out. #Person2# says it was about 10 o'clock.", "prediction_text": "The fire broke out at 10 o'clock at night."}
{"answers": "#Person1# asks the exact time when the big fire broke out. #Person2# remembers that it was about 10 o'clock.", "prediction_text": "The fire broke out at 10 o'clock at night."}
{"answers": "#Person1# asks #Person2# the exact time when the fire broke out. #Person2# says it was about 10 o'clock.", "prediction_text": "The fire broke out at 10 o'clock at night."}
{"answers": "#Person2# doesn't have enough cash to pay 905 yuan and asks to cut the service charge. #Person1# refuses. So #Person2# pays by her husband's credit card because it can be accepted.", "prediction_text": "A man and a woman are in a restaurant. The man says, \"I'd like to pay now.\" The woman says, \"No, thanks. My husband said, We'd like to pay now.\" The man says, \"Let me show you to the cashier's. It's 905 yuan in total.\" The woman says, \"What? Where does that five come from?\" The man says, \"Oh, it's a service charge.\" The woman says, \"But I only have 900 in cash here. Can you let it go for 900 yuan?\" The man says, \"Sorry, but we can't do that. It's store's rule.\" The woman says, \"My husband said he had a credit card."}
{"answers": "#Person1# requires 905 yuan in total, but #Person2# only has 900 in cash and asks for a release. #Person1# refuses, so #Person2# pays by her husband's credit card because it can be accepted.", "prediction_text": "A man and a woman are in a restaurant. The man says, \"I'd like to pay now.\" The woman says, \"No, thanks. My husband said, We'd like to pay now.\" The man says, \"Let me show you to the cashier's. It's 905 yuan in total.\" The woman says, \"What? Where does that five come from?\" The man says, \"Oh, it's a service charge.\" The woman says, \"But I only have 900 in cash here. Can you let it go for 900 yuan?\" The man says, \"Sorry, but we can't do that. It's store's rule.\" The woman says, \"My husband said he had a credit card."}
{"answers": "#Person2# is asked to pay 905 yuan but she just has 900 in cash and asks for a release from #Person1#. #Person1# refuses. So #Person2# pays by her husband's credit card because it can be accepted.", "prediction_text": "A man and a woman are in a restaurant. The man says, \"I'd like to pay now.\" The woman says, \"No, thanks. My husband said, We'd like to pay now.\" The man says, \"Let me show you to the cashier's. It's 905 yuan in total.\" The woman says, \"What? Where does that five come from?\" The man says, \"Oh, it's a service charge.\" The woman says, \"But I only have 900 in cash here. Can you let it go for 900 yuan?\" The man says, \"Sorry, but we can't do that. It's store's rule.\" The woman says, \"My husband said he had a credit card."}
{"answers": "#Person1# describes the contents of the regular car wash package. #Person2# will take that.", "prediction_text": "The customer is satisfied with the service."}
{"answers": "#Person1# introduces the content of regular car wash package and #Person2# accepts.", "prediction_text": "The customer is satisfied with the service."}
{"answers": "#Person1# introduces the services included in regular car wash package and #Person2# will take it.", "prediction_text": "The customer is satisfied with the service."}
{"answers": "Harry and his wife may go abroad during the holiday, but Harry's wife worries too much, so they may not go anywhere.", "prediction_text": "The two friends are discussing their plans for the holidays. The first person says that he is going to Egypt, but his wife wants to go to Egypt, too. The second person says that he is going to Egypt, but he is not sure. The first person says that he is going to Egypt, but he is not sure. The second person says that he is going to Egypt, but he is not sure. The first person says that he is going to Egypt, but he is not sure. The second person says that he is going to Egypt, but he is not sure. The first person says that he is going to Egypt, but he is not sure. The second person says that he is going to Egypt, but he is not"}
{"answers": "Harry and his wife may go abroad during the holidays. However, they might not go anywhere because his wife always worries too much.", "prediction_text": "The two friends are discussing their plans for the holidays. The first person says that he is going to Egypt, but his wife wants to go to Egypt, too. The second person says that he is going to Egypt, but he is not sure. The first person says that it is cheaper to go by sea, but the second person says that it is cheaper to go by air. The first person says that he is sure that they will enjoy themselves, but the second person says that they may not go anywhere. The first person says that he is sure that they will look after the dog, but the second person says that he is not sure. The first person says that he is sure that they will look after the house, but the second person"}
{"answers": "Harry is not sure about the holiday plan of going abroad to spend the holiday, because his wife worries about things after they leave.", "prediction_text": "The two friends are discussing their plans for the holidays. The first person says that he is going to Egypt, but his wife wants to go to Egypt, too. The second person says that he is going to Egypt, but he is not sure. The first person says that it is cheaper to go by sea, but the second person says that it is cheaper to go by air. The first person says that he is sure that they will enjoy themselves, but the second person says that they may not go anywhere. The first person says that he is sure that they will look after the dog, but the second person says that he is not sure. The first person says that he is sure that they will look after the house, but the second person"}
{"answers": "#Person1# is a new member who wants to use the machines. Johnson teaches #Person1# to get a card to keep track of the progress and record the suitable weight for each machine. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "#Person1#: Excuse me. Can you tell me how to get started using these machines?"}
{"answers": "Johnson is teaching #Person1# how to use the training machines. He introduces a training card that allows #Person1# to keep track of the weight used on each machine. #Person1# is exhausted when there are still 7 machines left. Johnson suggests #Person1# stop to train and go through the rest of the machines next time.", "prediction_text": "#Person1#: Excuse me. Can you tell me how to get started using these machines?"}
{"answers": "Johnson shows #Person1# the correct way to use weights machine and tells #Person1# using a card to keep track of exercise intensity. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "#Person1#: Excuse me. Can you tell me how to get started using these machines?"}
{"answers": "#Person1# and #Person2# are both unemployed. #Person2# suggests applying for the electrician program and #Person1# agrees.", "prediction_text": "The two people in this dialogue are having a conversation about what they are doing. The first person is unemployed, and the second person is looking for a job. The first person is not interested in interviews, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The"}
{"answers": "Both #Person1# and #Person2# lost their jobs. They would like to apply for the electrician program.", "prediction_text": "The two people in this dialogue are having a conversation about what they are doing. The first person is unemployed, and the second person is looking for a job. The first person is not interested in interviews, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The"}
{"answers": "#Person1# and #Person2# plan to apply for the electrician program after they were laid off.", "prediction_text": "The two people in this dialogue are having a conversation about what they are doing. The first person is unemployed, and the second person is looking for a job. The first person is not interested in interviews, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The first person is not interested in the electrician program, and the second person is interested in the electrician program. The"}
{"answers": "#Person1# asks something about #Person2#'s care with puppies and reminds #Person2# of the vet appointment.", "prediction_text": "The two people in the conversation are talking about a dog that they have. The first person says that they have fed the dog today, and the second person says that they have fed the dog today. The first person says that they have given the dog a bath, and the second person says that they have given the dog a bath. The first person says that they have a vet appointment with the dog on Saturday, and the second person says that they have a vet appointment with the dog on Saturday. The first person says that they have to remember to give the dog a bath, and the second person says that they have to remember to give the dog a bath."}
{"answers": "#Person1# and #Person2# are talking about taking care of their puppies and the time of vet appointment.", "prediction_text": "The two people in the conversation are talking about a dog that they have. The first person says that they have fed the dog today, and the second person says that they have fed the dog today. The first person says that they have given the dog a bath, and the second person says that they have given the dog a bath. The first person says that they have a vet appointment with the dog on Saturday, and the second person says that they have a vet appointment with the dog on Saturday. The first person says that they have to remember to give the dog a bath, and the second person says that they have to remember to give the dog a bath."}
{"answers": "#Person1# inquires about #Person2#'s care with the puppies and reminds #Person2# of the vet appointment.", "prediction_text": "The two people in the conversation are talking about a dog that they have. The first person says that they have fed the dog today, and the second person says that they have fed the dog today. The first person says that they have given the dog a bath, and the second person says that they have given the dog a bath. The first person says that they have a vet appointment with the dog on Saturday, and the second person says that they have a vet appointment with the dog on Saturday. The first person says that they have to remember to give the dog a bath, and the second person says that they have to remember to give the dog a bath."}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay the rent. Ayden is short on cash but still lends $20 to #Person1#. They decide to have dinner at #Person1#'s place.", "prediction_text": "A man and a woman are having a conversation. The man is asking the woman for money. The woman is saying that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money"}
{"answers": "#Person1# borrows $ 20 from Ayden because #Person1# owes the landlady $ 200. Then #Person1# invites Ayden to have dinner at #Person1#'s place.", "prediction_text": "A man and a woman are having a conversation. The man is asking the woman for money. The woman is saying that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money"}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay for the house rent but Ayden lends $20 because of the lack of money. They decide to have dinner at #Person1#'s place.", "prediction_text": "A man and a woman are having a conversation. The man is asking the woman for money. The woman is saying that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money. The man says that he has some money. The woman says that she doesn't have any money"}
{"answers": "#Person2# had been in the charity organization for 2 months. Social activities had a direct impact on #Person2#'s way of thinking.", "prediction_text": "#Person1#: I think it is very important to have a good social life. #Person2#: I think it is very important to have a good social life."}
{"answers": "#Person2# had been in the charity organization for 2 months and the social activities influenced #Person2#'s way of thinking.", "prediction_text": "#Person1#: I think it is very important to have a good social life. #Person2#: I think it is very important to have a good social life."}
{"answers": "#Person2# had been in the charity organization for 2 months. Social activities influenced #Person2#'s way of thinking.", "prediction_text": "#Person1#: I think it is very important to have a good social life. #Person2#: I think it is very important to have a good social life."}
{"answers": "#Person1#'s organization will make a decision in about one week and contact #Person2# via phone or e-mail.", "prediction_text": "The person who is the subject of the dialogue is the one who is asking for help. The person who is the object of the dialogue is the one who is being helped. The person who is the subject of the dialogue is the one who is asking for help. The person who is the object of the dialogue is the one who is being helped. The person who is the subject of the dialogue is the one who is asking for help. The person who is the object of the dialogue is the one who is being helped. The person who is the subject of the dialogue is the one who is asking for help. The person who is the object of the dialogue is the one who is being helped. The person who is the subject of the dialogue is"}
{"answers": "#Person1# can inform #Person2# by phone or e-mail when the organization makes a decision in about one week.", "prediction_text": "The person who is the subject of the dialogue is the one who is asking for help. The person who is the object of the dialogue is the one who is being helped. The person who is the subject of the dialogue is the one who is asking for help. The person who is the object of the dialogue is the one who is being helped. The person who is the subject of the dialogue is the one who is asking for help. The person who is the object of the dialogue is the one who is being helped. The person who is the subject of the dialogue is the one who is asking for help. The person who is the object of the dialogue is the one who is being helped. The person who is the subject of the dialogue is"}
{"answers": "#Person1# can tell #Person2# their organization's decision by phone or e-mail in about one week.", "prediction_text": "The person who is the subject of the dialogue is the one who is asking for help. The person who is the object of the dialogue is the one who is being helped. The person who is the subject of the dialogue is the one who is asking for help. The person who is the object of the dialogue is the one who is being helped. The person who is the subject of the dialogue is the one who is asking for help. The person who is the object of the dialogue is the one who is being helped. The person who is the subject of the dialogue is the one who is asking for help. The person who is the object of the dialogue is the one who is being helped. The person who is the subject of the dialogue is"}
{"answers": "#Person2# recommends Moutan and Roast Beijing Duck. #Person1# will take that in the hotel.", "prediction_text": "#Person1#: Would you please recommend some Chinese wine?"}
{"answers": "#Person2# recommends #Person1# to have Roast Beijing Duck along with Moutan in the hotel. #Person1# will have a try.", "prediction_text": "#Person1#: Would you please recommend some Chinese wine?"}
{"answers": "#Person2# recommends Moutan and Roast Beijing Duck. #Person1# will have a try in the hotel.", "prediction_text": "#Person1#: Would you please recommend some Chinese wine?"}
{"answers": "Angela calls Dan to make sure he's coming to the wedding with him next week and also asks him to invite Megan. They'll drive to the wedding together.", "prediction_text": "Dan and Angela are talking about a friend of theirs who is getting married next week. Dan wants to invite Megan to the wedding, but Angela is not sure if Megan knows Dan's friend."}
{"answers": "Angela calls to remind Dan that they are going to the wedding of her friend next week and asks him to invite Megan. The three of them will go together.", "prediction_text": "Dan and Angela are talking about a friend of theirs who is getting married next week. Dan wants to invite Megan to the wedding, but Angela is not sure if Megan knows Dan's friend."}
{"answers": "Angela reminds Dan to come to the wedding next week and asks him to invite Megan as well. They agree to go there together.", "prediction_text": "Dan and Angela are talking about a friend of theirs who is getting married next week. Dan wants to invite Megan to the wedding, but Angela is not sure if Megan knows Dan's friend."}
{"answers": "#Person2# has traditional Greek yogurt, which #Person1# thinks rather plain. #Person1# has an Italian tiramisu, which #Person2# thinks delicious. #Person1# goes and gets both of them a fried banana.", "prediction_text": "#Person1#: It's time for desserts! Are you still hungry?"}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. They both decide to have fried bananas and #Person1# will bring bananas back.", "prediction_text": "#Person1#: It's time for desserts! Are you still hungry?"}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. Then they want to have fried bananas and #Person1# goes out to buy some.", "prediction_text": "#Person1#: It's time for desserts! Are you still hungry?"}
{"answers": "Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week.", "prediction_text": "The two men are talking about their jobs. The first man is a bank employee and the second is a student. The first man is twenty-seven years old and the second is twenty-seven years old. The first man has a B. A. in Economics and the second has a B. A. in Economics. The first man has worked in a bank for the last five years and the second has worked in a bank for the last five years. The first man's salary is $ 500 a week and the second man's salary is $ 500 a week. The first man is twenty-seven years old and the second man is twenty-seven years old. The first man is a bank employee and the second man is a student. The first man"}
{"answers": "Mr. Smith tells #Person1# that he graduated from Yale University with a B. A. in Economics, and he has worked in a bank for the last five years with $500 a week.", "prediction_text": "The two men are talking about their jobs. The first man is a bank employee and the second is a student. The first man is twenty-seven years old and the second is twenty-seven years old. The first man has a B. A. in Economics and the second has a B. A. in Economics. The first man has worked in a bank for the last five years and the second has worked in a bank for the last five years. The first man's salary is $ 500 a week and the second man's salary is $ 500 a week. The first man is twenty-seven years old and the second man is twenty-seven years old. The first man is a bank employee and the second man is a student. The first man"}
{"answers": "Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week.", "prediction_text": "The two men are talking about their jobs. The first man is a bank employee and the second is a student. The first man is twenty-seven years old and the second is twenty-seven years old. The first man has a B. A. in Economics and the second has a B. A. in Economics. The first man has worked in a bank for the last five years and the second has worked in a bank for the last five years. The first man's salary is $ 500 a week and the second man's salary is $ 500 a week. The first man is twenty-seven years old and the second man is twenty-seven years old. The first man is a bank employee and the second man is a student. The first man"}
{"answers": "#Person2# recommends Digital Barbie for #Person1#'s niece. #Person1# pays $32.42 for it by cash.", "prediction_text": "A man and a woman are shopping for a Barbie doll. The man says he wants to get something nice for his niece. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says he likes the suggestion. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says he likes the suggestion. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says he likes the suggestion. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says he likes the suggestion. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says"}
{"answers": "#Person2# recommends #Person1# to buy a $32. 42 Digital Barbie for #Person1#'s niece. #Person1# pays it by cash.", "prediction_text": "A man and a woman are shopping for a Barbie doll. The man says he wants to get something nice for his niece. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says he likes the suggestion. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says he likes the suggestion. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says he likes the suggestion. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says he likes the suggestion. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says"}
{"answers": "#Person1# paid $32.42 by cash to buy a Digital Barbie as a niece's gift after listening to #Person2#'s suggestion.", "prediction_text": "A man and a woman are shopping for a Barbie doll. The man says he wants to get something nice for his niece. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says he likes the suggestion. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says he likes the suggestion. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says he likes the suggestion. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says he likes the suggestion. The woman says she has never heard of a little girl that didn't love Barbie dolls. The man says"}
{"answers": "#Person2# wants a pair of Jordan shoes in size 41.", "prediction_text": "#Person1#: Can I help you? #Person2#: Yes, I want a pair of sport shoes, Jordan. #Person1#: What size do you want? #Person2#: Size 41. #Person1#: Here you are. #Person2#: They fit me. Can you make it a little cheaper? #Person1#: It's already on sale now."}
{"answers": "#Person2# wants to buy a pair of Jordan shoes in size 41.", "prediction_text": "#Person1#: Can I help you? #Person2#: Yes, I want a pair of sport shoes, Jordan. #Person1#: What size do you want? #Person2#: Size 41. #Person1#: Here you are. #Person2#: They fit me. Can you make it a little cheaper? #Person1#: It's already on sale now."}
{"answers": "#Person2# wants to buy a pair of Jordan shoes in size 41.", "prediction_text": "#Person1#: Can I help you? #Person2#: Yes, I want a pair of sport shoes, Jordan. #Person1#: What size do you want? #Person2#: Size 41. #Person1#: Here you are. #Person2#: They fit me. Can you make it a little cheaper? #Person1#: It's already on sale now."}
{"answers": "#Person1# wants to go to the science museum but loses the way. #Person2# helps #Person1# buy the ticket and gives #Person1# directions.", "prediction_text": "A man and a woman are lost in a city. The man is looking for a museum, and the woman is looking for a train. The man is looking for a ticket machine, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a museum. The man is looking for a train, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a train. The man"}
{"answers": "#Person1# is lost on the way to the science museum. #Person2# helps #Person1# to buy the tickets for the train and gives #Person1# directions.", "prediction_text": "A man and a woman are lost in a city. The man is looking for a museum, and the woman is looking for a train. The man is looking for a ticket machine, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a museum. The man is looking for a train, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a train. The man"}
{"answers": "#Person2# helps #Person1# operate the ticket machine to buy a train ticket and tells #Person1# who loses the way how to go to the science museum.", "prediction_text": "A man and a woman are lost in a city. The man is looking for a museum, and the woman is looking for a train. The man is looking for a ticket machine, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a museum. The man is looking for a train, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a train. The man is looking for a train, and the woman is looking for a train. The man"}
{"answers": "#Person1# asks Simon about his retirement and finds out Simon is on a trial scheme called phased retirement. He can work with his former company but manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "Simon is a retired employee who has been working on a phased retirement scheme. He has been able to work on a project for the company he used to work for, and he has been able to decide when he wants to work. He has been able to use his contacts from work to get things done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work"}
{"answers": "Simon tells #Person1# that he is on a scheme called phased retirement during which he had a six-month break from work, and after that he could apply for projects of the company he used to work for but manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "Simon is a retired employee who has been working on a phased retirement scheme. He has been able to work on a project for the company he used to work for, and he has been able to decide when he wants to work. He has been able to use his contacts from work to get things done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work"}
{"answers": "Simon tells #Person1# that he joined in a phased retirement scheme which offers retired people work positions from the former company, and he can manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "Simon is a retired employee who has been working on a phased retirement scheme. He has been able to work on a project for the company he used to work for, and he has been able to decide when he wants to work. He has been able to use his contacts from work to get things done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work done. He has been able to use his skills to get work"}
{"answers": "Rocky doesn't want to dance because no one at the party fits his type, but #Person1# likes different kinds of women from him. Finally, Rocky decides to go home to a TV dinner and his dog, Rusty", "prediction_text": "The two men are having a conversation about women. The first man says that he doesn't want a woman who will fulfill his every need, and the second man says that he doesn't want a woman who will stay home and cook. The first man says that he doesn't want a woman who will throw out the garbage, and the second man says that he doesn't want a woman who will watch TV. The first man says that he doesn't want a woman who will eat, and the second man says that he doesn't want a woman who will cook. The first man says that he doesn't want a woman who will pick up after him, and the second man says that he doesn't want a woman who will clean the house. The"}
{"answers": "Rocky wants a woman who's affectionate and fulfills his every need. #Person1# likes different kinds of women from him. Rocky doesn't mind and plans to go home and spend the night with a TV dinner and his dog, Rusty.", "prediction_text": "The two men are having a conversation about women. The first man says that he doesn't want a woman who will fulfill his every need, and the second man says that he doesn't want a woman who will stay home and cook. The first man says that he doesn't want a woman who will throw out the garbage, and the second man says that he doesn't want a woman who will watch TV. The first man says that he doesn't want a woman who will eat, and the second man says that he doesn't want a woman who will cook. The first man says that he doesn't want a woman who will pick up after him, and the second man says that he doesn't want a woman who will clean the house. The"}
{"answers": "Rocky has particular requirements and cannot find a suitable woman in the party, so he doesn't want to dance. #Person1# likes different kinds of women from him. He sticks with his views and decides to go home to a TV dinner and his dog, Rusty.", "prediction_text": "The two men are having a conversation about women. The first man says that he doesn't want a woman who will fulfill his every need, and the second man says that he doesn't want a woman who will stay home and cook. The first man says that he doesn't want a woman who will throw out the garbage, and the second man says that he doesn't want a woman who will watch TV. The first man says that he doesn't want a woman who will eat, and the second man says that he doesn't want a woman who will cook. The first man says that he doesn't want a woman who will pick up after him, and the second man says that he doesn't want a woman who will clean the house. The"}
{"answers": "#Person1# and #Person2# is talking about the heavy storm last night. #Person2# thinks the weather is terrible. #Person1# is positive towards that.", "prediction_text": "The weather is a topic of conversation in this dialogue. The speaker is complaining about the weather, but the other person is not convinced. The speaker is also complaining about the damage to the tree, but the other person is not sure if it was a big enough branch to cause any damage. The speaker is also complaining about the weather, but the other person is not sure if it is a good thing. The speaker is also complaining about the weather, but the other person is not sure if it is a good thing. The speaker is also complaining about the weather, but the other person is not sure if it is a good thing. The speaker is also complaining about the weather, but the other person is not sure if it is a good thing."}
{"answers": "#Person1# and #Person2# are talking about the heavy storm last night, and #Person1#'s positive. #Person2# thinks the weather is terrible. #Person1# thinks they should not complain.", "prediction_text": "The weather is a topic of conversation in this dialogue. The speaker is complaining about the weather, but the other person is not listening. The speaker is also complaining about the damage to the tree, but the other person is not listening. The speaker is also complaining about the weather, but the other person is not listening. The speaker is also complaining about the weather, but the other person is not listening. The speaker is also complaining about the weather, but the other person is not listening. The speaker is also complaining about the weather, but the other person is not listening. The speaker is also complaining about the weather, but the other person is not listening. The speaker is also complaining about the weather, but the other person is not listening. The"}
{"answers": "#Person2# complains the terrible storm last night and wishes for nice weather. #Person1# thinks they should not complain.", "prediction_text": "The weather is a topic of conversation in this dialogue. The speaker is complaining about the weather, but the other person is not listening. The speaker is also complaining about the damage to the tree, but the other person is not listening. The speaker is also complaining about the weather, but the other person is not listening. The speaker is also complaining about the weather, but the other person is not listening. The speaker is also complaining about the weather, but the other person is not listening. The speaker is also complaining about the weather, but the other person is not listening. The speaker is also complaining about the weather, but the other person is not listening. The speaker is also complaining about the weather, but the other person is not listening. The"}
{"answers": "#Person1# wants to watch TV together with #Person2#, but #Person2# finds it boring. #Person2# suggests doing music together.", "prediction_text": "The two people are arguing about whether or not they should go out to enjoy themselves. The first person says that he doesn't want to watch TV, but the second person says that he doesn't want to do anything. The first person says that he doesn't want to watch TV because it's boring, but the second person says that he doesn't want to do anything because he doesn't think it's good for him. The first person says that he and the second person should share something, but the second person says that he doesn't want to do anything. The first person says that he and the second person should do something, but the second person says that he doesn't want to do anything. The first person says that he and the second"}
{"answers": "#Person1# asks #Person2# to watch TV with #Person1# at home. However, #Person2# finds it boring and suggests doing music together.", "prediction_text": "The two people are arguing about whether or not they should go out to enjoy themselves. The first person says that he doesn't want to watch TV, but the second person says that he doesn't want to do anything. The first person says that he doesn't want to watch TV because it's boring, but the second person says that he doesn't want to do anything because he doesn't think it's good for him. The first person says that he and the second person should share something, but the second person says that he doesn't want to do anything. The first person says that he and the second person should do something, but the second person says that he doesn't want to do anything. The first person says that he and the second"}
{"answers": "#Person1# wants #Person2# to watch TV with #Person1#. #Person2# thinks it is boring and suggests doing their music together.", "prediction_text": "The two people are arguing about whether or not they should go out to enjoy themselves. The first person says that he doesn't want to watch TV, but the second person says that he doesn't want to do anything. The first person says that he doesn't want to watch TV because it's boring, but the second person says that he doesn't want to do anything because he doesn't think it's good for him. The first person says that he and the second person should share something, but the second person says that he doesn't want to do anything. The first person says that he and the second person should do something, but the second person says that he doesn't want to do anything. The first person says that he and the second"}
{"answers": "Ben feels a bit nervous about his new school life. #Person1# cheers him up and tells him about the schedule of the new school.", "prediction_text": "The students are getting ready for the first day of school. They are nervous because they don't know what to expect. They are also worried about how they will get along with their classmates. The teacher tells them that they will have 10 minutes to hand in homework and 20 minutes for morning reading. The students are also worried about lunchtime. They are worried that they will be hungry by then. The teacher tells them that they can buy something to eat during the break after the second class."}
{"answers": "Ben is nervous about his new school life. #Person1# is giving him some brief information about the school.", "prediction_text": "The students are getting ready for the first day of school. They are nervous because they don't know what to expect. They are also worried about how they will get along with their classmates. The teacher tells them that they will have 10 minutes to hand in homework and 20 minutes for morning reading. The students are also worried about lunchtime. They are worried that they will be hungry by then. The teacher tells them that they can buy something to eat during the break after the second class."}
{"answers": "Ben is nervous about the upcoming new school life. #Person1# comforts him and tells him the school daily routine.", "prediction_text": "The students are getting ready for the first day of school. They are nervous because they don't know what to expect. They are also worried about how they will get along with their classmates. The teacher tells them that they will have 10 minutes to hand in homework and 20 minutes for morning reading. The students are also worried about lunchtime. They are worried that they will be hungry by then. The teacher tells them that they can buy something to eat during the break after the second class."}
{"answers": "Adam's knee is getting better but #Person1# still suggests that he should not do any running today. Adam decides to watch the films of Michigan after practice and will be back tomorrow for full practice.", "prediction_text": "#Person1#: Adam, how is your knee today? Is it still giving you trouble?"}
{"answers": "Adam's knee is getting better but #Person1# suggests that he should not do any running today. Adam decides to see films of Michigan after practice and says he'll be back tomorrow for full practice.", "prediction_text": "#Person1#: Adam, how is your knee today? Is it still giving you trouble?"}
{"answers": "Adam's knee is getting better but #Person1# suggests that he should not do any running today. #Person1# says he can watch some films of Michigan after practice. Adam decides to go back tomorrow for full practice.", "prediction_text": "#Person1#: Adam, how is your knee today? Is it still giving you trouble?"}
{"answers": "#Person1# wants #Person2# to help to print up a copy. #Person2# accepts and edits it for #Person1#.", "prediction_text": "The dialogue is a conversation between two people. The first person asks the second person to do something for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks"}
{"answers": "#Person1# asks for #Person2#'s help to print up a copy. #Person2# accepts and edits it for #Person1#.", "prediction_text": "The dialogue is a conversation between two people. The first person asks the second person to do something for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks"}
{"answers": "#Person1# asks for #Person2#'s help to print up a copy. #Person2# accepts and edits it for #Person1#.", "prediction_text": "The dialogue is a conversation between two people. The first person asks the second person to do something for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks the second person to do something else for him. The second person agrees to do it. The first person then asks"}
{"answers": "#Person2# lets #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "A mother and daughter are discussing how to put up a curtain. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold"}
{"answers": "#Person2# suggests that #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "A mother and daughter are discussing how to put up a curtain. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold"}
{"answers": "#Person2# lets #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "A mother and daughter are discussing how to put up a curtain. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold the ladder. The mother says she wants to hold the ladder, and the daughter says she will hold"}
{"answers": "Jack is available for a camping trip the weekend after next.", "prediction_text": "Jack and #Person2# are planning a weekend camping trip. Jack wants to know which weekend is best for everyone. #Person2# says that the weekend after next looks good, but Jack is not convinced. #Person2# says that he is booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced."}
{"answers": "Jack will be available for a weekend camping trip the weekend after next.", "prediction_text": "Jack and #Person2# are planning a weekend camping trip. Jack wants to know which weekend is best for everyone. #Person2# says that the weekend after next looks good, but Jack is not convinced. #Person2# says that he is booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced."}
{"answers": "The weekend after next is suitable for Jack to have a camping trip.", "prediction_text": "Jack and #Person2# are planning a weekend camping trip. Jack wants to know which weekend is best for everyone. #Person2# says that the weekend after next looks good, but Jack is not convinced. #Person2# says that he is booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced. #Person2# says that he is all booked up, but Jack is not convinced."}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells #Person2# she is pregnant. #Person2# feels happy. They go to the doctor. #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "A man and a woman are having a conversation. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been un"}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells him that she's pregnant. They go to see the doctor. However, #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "A man and a woman are having a conversation. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been un"}
{"answers": "#Person1# apologises to #Person2# after the quarrel and tells #Person2# she's pregnant. #Person2# feels so happy and they decide to see the doctor. After the pregnancy test about the date, #Person2# finds that it is not his baby, because he was away the first week of February at a training seminar.", "prediction_text": "A man and a woman are having a conversation. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been unfaithful. The woman is upset that the man has been unfaithful. The man is upset that the woman has been un"}
{"answers": "#Person2# is worried about #Person2#'s daughter. #Person1# suggests #Person2# should encourage #Person2#'s daughter to make her own decision.", "prediction_text": "The speaker is worried about his daughter's decision about college. The speaker thinks that his daughter is making a mistake. The speaker thinks that his daughter should make up her own mind. The speaker thinks that his daughter should not be influenced by him. The speaker thinks that his daughter should not be influenced by others. The speaker thinks that his daughter should not be influenced by her friends. The speaker thinks that his daughter should not be influenced by her family. The speaker thinks that his daughter should not be influenced by her teachers. The speaker thinks that his daughter should not be influenced by her classmates. The speaker thinks that his daughter should not be influenced by her friends. The speaker thinks that his daughter should not be influenced by her family. The speaker thinks that"}
{"answers": "#Person1# advises #Person2# to let #Person2#'s daughter make her own decisions instead of worrying about her.", "prediction_text": "The speaker is worried about his daughter's decision about college. The speaker thinks that his daughter is making a mistake. The speaker thinks that his daughter should make up her own mind. The speaker thinks that his daughter should not be influenced by him. The speaker thinks that his daughter should not be influenced by others. The speaker thinks that his daughter should not be influenced by her friends. The speaker thinks that his daughter should not be influenced by her family. The speaker thinks that his daughter should not be influenced by her teachers. The speaker thinks that his daughter should not be influenced by her classmates. The speaker thinks that his daughter should not be influenced by her friends. The speaker thinks that his daughter should not be influenced by her family. The speaker thinks that"}
{"answers": "#Person1# suggests #Person2# letting the child make own decision instead of worrying about her.", "prediction_text": "The speaker is worried about his daughter's decision about college. The speaker thinks that his daughter is making a mistake. The speaker thinks that his daughter should make up her own mind. The speaker thinks that his daughter should not be influenced by him. The speaker thinks that his daughter should not be influenced by others. The speaker thinks that his daughter should not be influenced by her friends. The speaker thinks that his daughter should not be influenced by her family. The speaker thinks that his daughter should not be influenced by her teachers. The speaker thinks that his daughter should not be influenced by her classmates. The speaker thinks that his daughter should not be influenced by her friends. The speaker thinks that his daughter should not be influenced by her family. The speaker thinks that"}
{"answers": "#Person1# may lose #Person1#'s job because of a mistake that may cause a huge loss and a dishonest supervisor. #Person2# suggests #Person1# live with #Person2# and stop buying all the junk to save money if #Person1# loses the job. #Person1# agrees.", "prediction_text": "#Person1#: I don't know what I'm going to do! It's going to be impossible to make ends meet if I lose my job!"}
{"answers": "#Person1# makes a big mistake which may cause a huge loss and #Person1# may lose this job because of the loss and #Person1#'s dishonest supervisor. #Person2# suggests that #Person1# move in with #Person2# until #Person1# find another job and stop buying all the junk, if #Person1# loses the job.", "prediction_text": "#Person1#: I don't know what I'm going to do! It's going to be impossible to make ends meet if I lose my job!"}
{"answers": "#Person1# might lose the job because of a working mistake and is worried about financial problems after losing a job. #Person2# suggests #Person1# save money until #Person1# find another job, if #Person1# loses the job.", "prediction_text": "#Person1#: I don't know what I'm going to do! It's going to be impossible to make ends meet if I lose my job!"}
{"answers": "#Person2#'s friend is visiting #Person2#. #Person2# invites him to give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "A Ph.D. student at the University of Edinburgh is going to give a speech at my lab. He is going to talk about multimodal text generation. He will also talk about discourse modeling and video summarization. I am going to invite him to my lab."}
{"answers": "#Person2#'s friend is a Ph.D. student at the University of Edinburgh. He is visiting #Person2# and will give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "A Ph.D. student at the University of Edinburgh is going to give a speech at my lab. He is going to talk about multimodal text generation. He will also talk about discourse modeling and video summarization. I am going to invite him to my lab."}
{"answers": "#Person2#'s friend is visiting #Person2# and will give a speech about multimodal text generation. #Person1# thinks it interesting and will attend it.", "prediction_text": "A Ph.D. student at the University of Edinburgh is going to give a speech at my lab. He is going to talk about multimodal text generation. He will also talk about discourse modeling and video summarization. I am going to invite him to my lab."}
{"answers": "#Person1# suggests going to John's house, but #Person2# feels sick and decides to go to sleep.", "prediction_text": "#Person1# and #Person2# are having a conversation. #Person1# is asking #Person2# if he should go to John's house tonight. #Person2# says that he is not hungry and that he might just go to sleep. #Person1# says that he should drink some tea and stay warm."}
{"answers": "#Person2# prefers to stay at home and rest rather than go over to John's house tonight because #Person2# gets sick.", "prediction_text": "#Person1# and #Person2# are having a conversation. #Person1# is asking #Person2# if he should go to John's house tonight. #Person2# says that he is not hungry and that he might just go to sleep. #Person1# says that he should drink some tea and stay warm."}
{"answers": "#Person2# doesn't want to go to John's house tonight because of getting sick. #Person2# decides to go to sleep.", "prediction_text": "#Person1# and #Person2# are having a conversation. #Person1# is asking #Person2# if he should go to John's house tonight. #Person2# says that he is not hungry and that he might just go to sleep. #Person1# says that he should drink some tea and stay warm."}
{"answers": "Mr. Faber calls #Person1# to book a double room for 3 nights at York Hotel.", "prediction_text": "The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his"}
{"answers": "Mr. Faber books a double room for 3 nights from July 20th at York Hotel.", "prediction_text": "The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his"}
{"answers": "Mr. Faber books a double room for three nights from July 20th at York Hotel.", "prediction_text": "The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his name and address. The hotel receptionist asks the guest to provide his name and address. The guest provides his"}
{"answers": "#Person1# wants a cheap single room. #Person2# recommends calling John Godfrey and see him on Saturday.", "prediction_text": "#Person1#:  So is there any other area I should look at as well?"}
{"answers": "#Person1# is looking for a cheap single room. #Person2# recommends #Person1# to contact John Godfrey on Saturday.", "prediction_text": "#Person1#:  So is there any other area I should look at as well?"}
{"answers": "#Person1# is looking for a cheap single room. #Person2# gives #Person1# John Godfrey's phone number that could help to find a room and suggests that they meet on Saturday.", "prediction_text": "#Person1#:  So is there any other area I should look at as well?"}
{"answers": "#Person1# and #Person2# blame each other for the series of bad experiences during the journey.", "prediction_text": "A man and a woman are arguing about who is to blame for a traffic accident."}
{"answers": "#Person1# and #Person2# are blaming each other for lots of bad experience during the journey.", "prediction_text": "A man and a woman are arguing about who is to blame for a traffic accident."}
{"answers": "#Person1# and #Person2# quarrel and blame each other because of bad experiences during the journey.", "prediction_text": "A man and a woman are arguing about who is to blame for a traffic accident."}
{"answers": "Darlene calls to check the delayed order. Dan tells her the situation. Darlene will talk to Steve.", "prediction_text": "A call to a factory to check on an order of computers has been delayed for two days. The factory manager is not happy about this."}
{"answers": "Darlene calls Dan to check on the delay of the order. Dan explains to her the reason for the delay. Darlene decides to talk to Steven.", "prediction_text": "A call to a factory to check on an order of computers has been delayed for two days. The factory manager is not happy about this."}
{"answers": "Darlen calls Dan to check the delayed order of computers. Dan explains to her the reason for the delay. Darlene decides to talk to Steven.", "prediction_text": "A call to a factory to check on an order of computers has been delayed for two days. The factory manager is not happy about this."}
{"answers": "#Person2# tells #Person1# the founders and founding times of the Washington Post, the New York Times, and the Los Angeles Times.", "prediction_text": "The Washington Post was founded in 1877 by Stilson Hutchens. The New York Times was founded in 1789 by Henry Raymond and George Jones. The Los Angeles Times was founded in 1881 by Nathan Cole Junior and Thomas Guardineer."}
{"answers": "#Person1# and #Person2# are talking about the founding time and founder of the New York Times, the Washington Post, and the Los Angeles Times were founded.", "prediction_text": "The Washington Post was founded in 1877 by Stilson Hutchens. The New York Times was founded in 1789 by Henry Raymond and George Jones. The Los Angeles Times was founded in 1881 by Nathan Cole Junior and Thomas Guardineer."}
{"answers": "#Person2# tells #Person1# the founder and the founding time of the New York Times, the Washington Post, and the Los Angeles Times.", "prediction_text": "The Washington Post was founded in 1877 by Stilson Hutchens. The New York Times was founded in 1789 by Henry Raymond and George Jones. The Los Angeles Times was founded in 1881 by Nathan Cole Junior and Thomas Guardineer."}
{"answers": "#Person1# finds that #Person2# e-mail exceeds capacity and suggests #Person2# compress the email.", "prediction_text": "#Person1#: What's up? #Person2#: I guess there is some kind of virus seeking into my computer, I can't send out this e-mail. Do you have the number of the text port? #Person1#: Do you mind I have a look at your computer? #Person2#: Of course not, I appreciate that. #Person1#: Well, it has nothing to do with virus. The problem is your attachment is a bit larger. It has exceeded the e-mail capacity. #Person2#: I see. What can I do now? #Person1#: You can send a compressed one."}
{"answers": "#Person2#'s attachment exceeds the e-mail capacity, #Person1# suggests compressing it.", "prediction_text": "#Person1#: What's up? #Person2#: I guess there is some kind of virus seeking into my computer, I can't send out this e-mail. Do you have the number of the text port? #Person1#: Do you mind I have a look at your computer? #Person2#: Of course not, I appreciate that. #Person1#: Well, it has nothing to do with virus. The problem is your attachment is a bit larger. It has exceeded the e-mail capacity. #Person2#: I see. What can I do now? #Person1#: You can send a compressed one."}
{"answers": "#Person2# can't send out an email. #Person1# suggests #Person2#'s attachment be compressed.", "prediction_text": "#Person1#: What's up? #Person2#: I guess there is some kind of virus seeking into my computer, I can't send out this e-mail. Do you have the number of the text port? #Person1#: Do you mind I have a look at your computer? #Person2#: Of course not, I appreciate that. #Person1#: Well, it has nothing to do with virus. The problem is your attachment is a bit larger. It has exceeded the e-mail capacity. #Person2#: I see. What can I do now? #Person1#: You can send a compressed one."}
{"answers": "#Person2# tells #Person1# #Person2# enjoyed the weekend organized by #Person1#.", "prediction_text": "#Person1# and #Person2# are friends. #Person1# is a student at a university in Changsha. #Person2# is a professor at the university. #Person1# and #Person2# have been friends for a long time. #Person1# has invited #Person2# to stay with him in Changsha. #Person2# has accepted the invitation. #Person1# and #Person2# have a good time together."}
{"answers": "#Person1# and #Person2# has enjoyed a nice weekend together.", "prediction_text": "#Person1# and #Person2# are friends. #Person1# is a student at a university in Changsha. #Person2# is a professor at the university. #Person1# and #Person2# have been friends for a long time. #Person1# has invited #Person2# to stay with him in Changsha. #Person2# has accepted the invitation. #Person1# and #Person2# have a good time together."}
{"answers": "#Person1# invited #Person2# to spend the weekend and #Person2# has enjoyed it.", "prediction_text": "#Person1# and #Person2# are friends. #Person1# is a student at a university in Changsha. #Person2# is a professor at the university. #Person1# and #Person2# have been friends for a long time. #Person1# has invited #Person2# to stay with him in Changsha. #Person2# has accepted the invitation. #Person1# and #Person2# have a good time together."}
{"answers": "#Person2# offers #Person1# suggestions about what to eat. #Person1# will go to Panda Express.", "prediction_text": "#Person1#: I want to get a bite to eat."}
{"answers": "#Person1# wants to eat something and #Person2# gives some advice. #Person1# finally goes to eat Chinese food.", "prediction_text": "#Person1#: I want to get a bite to eat."}
{"answers": "#Person1# is hungry. #Person2# says the Chinese food from Panda Express is good.", "prediction_text": "#Person1#: I want to get a bite to eat."}
{"answers": "Mary from Hans furniture tells Tom they decide not to employ him but Tom asks the store to reconsider him.", "prediction_text": "The sales position is offered to someone else. The candidate is willing to work overtime to learn the business. The candidate is fast learner."}
{"answers": "Marry calls Tom to inform him that they decide to offer the sales position to someone else. Tom wants them to reconsider.", "prediction_text": "The sales position is offered to someone else. The candidate is willing to work overtime to learn the business. The candidate is fast learner."}
{"answers": "Tom wants Marry to reconsider the decision of offering the sales position to someone else instead of him.", "prediction_text": "The sales position is offered to someone else. The candidate is willing to work overtime to learn the business. The candidate is fast learner."}
{"answers": "#Person1# is angry about the crank calls.", "prediction_text": "A man calls a woman who he knows is a police officer. He tells her that he knows who she is and that he has called her ten times. She tells him that he is not allowed to call her again. He tells her that he will call the police and report her. She tells him that he will be arrested."}
{"answers": "#Person1# gets a crank call and is angry about it.", "prediction_text": "A man calls a woman who he knows is a police officer. He tells her that he knows who she is and that he has called her ten times. She tells him that he is not allowed to call her again. He tells her that he will call the police and report her. She tells him that he will be arrested."}
{"answers": "#Person1# receives a phone call but no one speaks.", "prediction_text": "A man calls a woman who he knows is a police officer. He tells her that he knows who she is and that he knows she is a police officer. He tells her that he knows she is a police officer because he has been calling her for the past ten times. He tells her that if she calls him again, he will call the police and report her. He tells her that he has her number."}
{"answers": "#Person2# is being interviewed for a music teacher. #Person2# says #Person2# has degrees in music and specializes in classic music. Then #Person2# shares some research about classic music that can make people relaxed. #Person1# is very satisfied with #Person2#. #Person2# gives some suggestions on how to start listening to classical music.", "prediction_text": "The interview was about a music teacher. The teacher said that she likes classical music and that she listens to it while she is cooking. She also said that she listens to classical music to reduce stress. The teacher said that she can find plenty of classical music on the internet and that she can buy it cheaply online. The teacher said that she can listen to different kinds of classical music and that she can choose the one that she likes the most."}
{"answers": "#Person1# interviews #Person2# for a music teacher position. #Person1# is very satisfied with #Person2#'s educational background in music and #Person2#'s understanding of classical music. After the interview, #Person2# suggests #Person1# can develop interests in classic music by listening to different classic music online.", "prediction_text": "The interview was about a music teacher. The teacher said that she likes classical music and that she listens to it while she is cooking. She also said that she listens to classical music to reduce stress. The teacher said that she can find plenty of classical music on the internet and that she can buy it cheaply online. The teacher said that she can listen to different kinds of classical music and that she can choose the one that she likes the most."}
{"answers": "#Person2# is being interviewed for a music teacher. #Person1# is impressed by #Person2#'s background and knowledge of classical music. And #Person1# will try classical music.", "prediction_text": "The interview was about a music teacher. The teacher said that she likes classical music and that she listens to it while she is cooking. She also said that she listens to classical music to reduce stress. The teacher said that she can find plenty of classical music on the internet and that she can buy it cheaply online. The teacher said that she can listen to different kinds of classical music and that she can choose the one that she likes the most."}
{"answers": "#Person2# likes his neibourhood girl who is popular. Although #Person1# analyses the disadvantages, #Person2# still decides to date with her.", "prediction_text": "A man and a woman meet and fall in love. They are both single and live in the same apartment building. The man is a bit of a flirt and the woman is a bit of a flirt too. The man is also a bit of a flirt with the other two American guys who live in the building. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of"}
{"answers": "#Person2# tells #Person1# that he falls in love with his neighbour and #Person1# thinks he has some competition and wishes him good luck.", "prediction_text": "A man and a woman meet and fall in love. They are both single and live in the same apartment building. The man is a bit of a flirt and the woman is a bit of a flirt too. The man is also a bit of a flirt with the other two American guys who live in the building. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of"}
{"answers": "#Person2# tells #Person1# he falls in love with the girl living under him and she's very popular. #Person2#'s going to call her now and invite her to dinner tonight.", "prediction_text": "A man and a woman meet and fall in love. They are both single and live in the same apartment building. The man is a bit of a flirt and the woman is a bit of a flirt too. The man is also a bit of a flirt with the other two American guys who live in the building. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of a flirt with the other two American guys. The woman is also a bit of"}
{"answers": "#Person1# and #Person2# talk about Mirella's dressing style in the office which is different from others. The management decided to give Mirella formal clothes directly to warn her.", "prediction_text": "Mirella is a new employee at the company. She is a young woman who has just returned from a conference in Silicon Valley. She is very excited about the new job and is eager to start working. She is surprised to find that her new boss, Mr. Smith, is very casual in his dress and that he is not wearing a tie. She is also surprised to find that her new boss is very relaxed and informal in his work style. She is not sure how to react to this. She is not sure how to react to this. She is not sure how to react to this. She is not sure how to react to this. She is not sure how to react to this. She is not sure how to react to this"}
{"answers": "#Person1# thinks it's strange of Mirella wearing casually in the office. #Person2# thinks she was influenced by the casual atmosphere in California and tells #Person1# management decided to give her a new white dress to warn her.", "prediction_text": "The two people in the office are discussing the new dress code that has been introduced at the company. The first person says that it is strange that the company is trying to change the way that people dress. The second person says that it is not as bad as it sounds. The first person says that the company is trying to tell Mirella that her new style of dressing is not acceptable. The second person says that the company is trying to tell Mirella that she is not allowed to dress in jeans and sweatshirts. The two people agree that it is not as bad as it sounds."}
{"answers": "#Person1# and #Person2# talks about Mirella dressed casually after she came back from Silicon Valley. Managements put white shirts on her desk to warn her.", "prediction_text": "The two people in the office are discussing the new dress code that has been introduced at the company. The first person says that it is strange that the company is trying to change the way that people dress. The second person says that it is not as bad as it sounds. The first person says that the company is trying to tell Mirella that her new style of dressing is not acceptable. The second person says that the company is trying to tell Mirella that she is not allowed to dress in jeans and sweatshirts. The two people agree that it is not as bad as it sounds."}
{"answers": "#Person1# congratulates #Person2# on #Person2#'s new venture and expresses #Person1#'s willingness to help.", "prediction_text": "A lawyer and a client discuss the advantages of having an office of one's own."}
{"answers": "#Person1# is willing to offer #Person2# help in setting up a law office.", "prediction_text": "A lawyer and a client discuss the advantages of having an office of one's own."}
{"answers": "#Person2# is going to set up #Person2#'s own law office. #Person1# wishes #Person2# good luck.", "prediction_text": "A lawyer and a client discuss the advantages of having an office of one's own."}
{"answers": "Peter tells Kate he played games and listened to music the whole last night.", "prediction_text": "#Person1#: Hello, Kate. #Person2#: Hi, Peter. #Person1#: I was almost on line the whole night. #Person2#: Did you play network games the whole night? #Person1#: No, I listened to the music, too. #Person2#: What are you going to do now? #Person1#: I'm tired. I need a rest."}
{"answers": "Peter tells Kate he stayed up and he's tired now.", "prediction_text": "#Person1#: Hello, Kate. #Person2#: Hi, Peter. #Person1#: I was almost on line the whole night. #Person2#: Did you play network games the whole night? #Person1#: No, I listened to the music, too. #Person2#: What are you going to do now? #Person1#: I'm tired. I need a rest."}
{"answers": "Peter tells Kate he stayed up. He's tired now and needs a rest.", "prediction_text": "#Person1#: Hello, Kate. #Person2#: Hi, Peter. #Person1#: I was almost on line the whole night. #Person2#: Did you play network games the whole night? #Person1#: No, I listened to the music, too. #Person2#: What are you going to do now? #Person1#: I'm tired. I need a rest."}
{"answers": "#Person2# explains alternatives of sea transportation but #Person1# addresses the importance of boat in transoceanic cargo transportation.", "prediction_text": "The speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats"}
{"answers": "#Person1# and #Person2# are talking about the development of transportation and the importance of boats.", "prediction_text": "The speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats"}
{"answers": "#Person2# tells #Person1# that ships and boats have been giving places to other transportations because people's life is getting faster, but #Person1# thinks they are still important.", "prediction_text": "The speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats is getting slower and slower. The reason is that the speed of ships and boats"}
{"answers": "Fanny had a nightmare because of the pressure from school applications. Her mother and Andy both comfort her.", "prediction_text": "Andy and Fanny are talking about their dreams. Andy had a bad dream and Fanny had a good dream. Andy is worried about getting into the University of Michigan. Fanny is worried about getting into the University of Michigan."}
{"answers": "Fanny had a nightmare last night and her mother consoled her. Andy listens to her experience and thinks she has a good mother.", "prediction_text": "Andy and Fanny are talking about their dreams. Andy had a bad dream and Fanny had a good dream. Andy is worried about getting into the University of Michigan. Fanny is worried about getting into the University of Michigan."}
{"answers": "Fanny had a bad dream last night. She is worried about getting into the University of Michigan. Andy comforts her.", "prediction_text": "Andy and Fanny are talking about their dreams. Andy had a bad dream and Fanny had a good dream. Andy is worried about getting into the University of Michigan. Fanny is worried about getting into the University of Michigan."}
{"answers": "#Person1# and Ernie plan to start a band and they decide to play hip hop music.", "prediction_text": "#Person1# and #Person2# are two students at a college. #Person1# is excited about starting a band with #Person2# and wants to play hip hop music. #Person2# is excited about the idea of playing Vanilla Ice songs, but doesn't know any of his other songs."}
{"answers": "#Person1# and Ernie are preparing to start the band at school.", "prediction_text": "#Person1# and #Person2# are two students at a college. #Person1# is excited about starting a band with #Person2# and wants to play hip hop music. #Person2# is excited about the idea of playing Vanilla Ice songs, but doesn't know any of his other songs."}
{"answers": "#Person1# and Ernie start their own band on campus. #Person1# suggests they play Vanilla Ice songs.", "prediction_text": "#Person1# and #Person2# are two students at a college. #Person1# is excited about starting a band with #Person2# and wants to play hip hop music. #Person2# is excited about the idea of playing Vanilla Ice songs, but doesn't know any of his other songs."}
{"answers": "#Person1# and #Person2# are discussing where to have fun, and they decide to go to the theater tonight.", "prediction_text": "A man and a woman are talking about their trip to New Orleans. The man says he likes the city and would like to do something tonight. The woman says she has been to several clubs and would like to go to a jazz club. The man says he has been to the theater and would like to go to the Sanger Theater. The woman says she has not been to the theater in a long time and would like to go. The man says he has been to the theater twice this week and would like to make a reservation."}
{"answers": "#Person1# and #Person2# are talking about what to do tonight and they finally decide to go to watch a show.", "prediction_text": "A man and a woman are talking about their trip to New Orleans. The man says he likes the city and would like to do something tonight. The woman says she has been to several clubs and would like to go to a jazz club. The man says he has been to the theater and would like to go to the Sanger Theater. The woman says she has not been to the theater in a long time and would like to go. The man says he has been to the theater twice this week and would like to make a reservation."}
{"answers": "#Person2# hasn't been to the theater for a long time, so #Person1# and #Person2# decide to make a reservation for a show at the Sanger Theater.", "prediction_text": "A man and a woman are talking about their trip to New Orleans. The man says he likes the city and would like to do something tonight. The woman says she has been to several clubs and would like to go to a jazz club. The man says he has been to the theater and would like to go to the Sanger Theater. The woman says she has not been to the theater in a long time and would like to go. The man says he has been to the theater twice this week and would like to make a reservation."}
{"answers": "#Person1# buys some nice clothes by credit card with #Person2#'s assistance.", "prediction_text": "The customer is not always right."}
{"answers": "#Person1# pays #Person2# by credit card for some clothes.", "prediction_text": "The customer is not always right."}
{"answers": "#Person1# purchases some clothes by credit card with #Person2#'s assistance.", "prediction_text": "The customer is not always right."}
{"answers": "Mr. Blake explains the training manuals cannot be sent today because they are still being copied.", "prediction_text": "#Person1#: Mr. Blake? Mr. Foster's on the phone. He'd like to know if you can send over those training manuals?"}
{"answers": "#Person1# is transferring the message between Mr. Blake and Mr. Foster about the training manuals.", "prediction_text": "#Person1#: Mr. Blake? Mr. Foster's on the phone. He'd like to know if you can send over those training manuals?"}
{"answers": "Mr. Foster wants the training manuals to be sent this afternoon but Mr. Blake explains that they haven't been printed out yet.", "prediction_text": "#Person1#: Mr. Blake? Mr. Foster's on the phone. He'd like to know if you can send over those training manuals?"}
{"answers": "#Person2# tells David about #Person2#'s planned a long trip for #Person2#'s vacation. David thinks it's nice.", "prediction_text": "#Person1#: Are you going anywhere for your vacation?"}
{"answers": "David and #Person2# are talking about #Person2#'s plan for the vacation. David thinks it sounds good.", "prediction_text": "#Person1#: Are you going anywhere for your vacation?"}
