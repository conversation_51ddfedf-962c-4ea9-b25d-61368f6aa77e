{"answers": "<PERSON><PERSON> <PERSON> helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "prediction_text": "A boss gives a memo to his secretary to type up. The secretary asks a question about the memo, and the boss gives a curt answer. The secretary then types up the memo and distributes it to all employees."}
{"answers": "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks <PERSON><PERSON> to send out a memo to all employees by the afternoon.", "prediction_text": "A boss gives a memo to his secretary to type up. The secretary asks a question about the memo, and the boss gives a curt answer. The secretary then types up the memo and distributes it to all employees."}
{"answers": "Ms<PERSON> takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists.", "prediction_text": "A boss gives a memo to his secretary to type up. The secretary asks a question about the memo, and the boss gives a curt answer. The secretary then types up the memo and distributes it to all employees."}
{"answers": "#Person2# arrives late because of traffic jam. #Person1# persuades #Person2# to use public transportations to keep healthy and to protect the environment.", "prediction_text": "Dialogue: #Person1#: You're finally here! What took so long?"}
{"answers": "#Person2# decides to follow #Person1#'s suggestions on quitting driving to work and will try to use public transportations.", "prediction_text": "Dialogue: #Person1#: You're finally here! What took so long?"}
{"answers": "#Person2# complains to #Person1# about the traffic jam, #Person1# suggests quitting driving and taking public transportation instead.", "prediction_text": "Dialogue: #Person1#: You're finally here! What took so long?"}
{"answers": "#Person1# tells Kate that Masha and Hero get divorced. Kate is surprised because she thought they are perfect couple.", "prediction_text": "Dialogue: #Person1#: Kate, you never believe what's happened."}
{"answers": "#Person1# tells Kate that Masha and Hero are getting a peaceful divorce. Kate feels surprised and asks about their kids.", "prediction_text": "Dialogue: #Person1#: Kate, you never believe what's happened."}
{"answers": "#Person1# and Kate talk about the divorce between Masha and Hero. Kate feels surprised because she thought they are well matched", "prediction_text": "Dialogue: #Person1#: Kate, you never believe what's happened."}
{"answers": "#Person1# and Brian are at the birthday party of Brian. Brian thinks #Person1# looks great and is popular.", "prediction_text": "A man and a woman are talking about a party."}
{"answers": "#Person1# attends Brian's birthday party. Brian thinks #Person1# looks great and charming.", "prediction_text": "A man and a woman are talking about a party."}
{"answers": "#Person1# has a dance with Brian at Brian's birthday party. Brian thinks #Person1# looks great and is popular.", "prediction_text": "A man and a woman are talking about a party."}
{"answers": "#Person1# is surprised at the Olympic Stadium'volume, capacity and interior setting to #Person1#.", "prediction_text": "The dialogue is about the Olympic park. The first person is a foreign visitor. The second person is a guide. The first person is surprised by the size of the Olympic park. The second person tells him that the whole stadium is to be finished this June. The first person asks how many seats are there in the stand. The second person tells him that there are 5000 seats in total. The first person is surprised by the size of the Olympic park. The second person tells him that the whole stadium is to be finished this June. The first person asks how many seats are there in the stand. The second person tells him that there are 5000 seats in total. The first person is surprised by the size of the Olympic park."}
{"answers": "#Person2# shows #Person1# around the constructing Olympic stadium and introduces the stadium.", "prediction_text": "The dialogue is about the Olympic park. The first person is a foreign visitor. The second person is a guide. The first person is surprised by the size of the Olympic park. The second person tells him that the whole stadium is to be finished this June. The first person asks how many seats are there in the stand. The second person tells him that there are 5000 seats in total. The first person is surprised by the size of the Olympic park. The second person tells him that the whole stadium is to be finished this June. The first person asks how many seats are there in the stand. The second person tells him that there are 5000 seats in total. The first person is surprised by the size of the Olympic park."}
{"answers": "#Person2# introduces the Olympic Stadium's finishing time, capacity and interior setting to #Person1#.", "prediction_text": "The dialogue is about the Olympic park. The first person is a foreign visitor. The second person is a guide. The first person is surprised by the size of the Olympic park. The second person tells him that the whole stadium is to be finished this June. The first person asks how many seats are there in the stand. The second person tells him that there are 5000 seats in total. The first person is surprised by the size of the Olympic park. The second person tells him that the whole stadium is to be finished this June. The first person asks how many seats are there in the stand. The second person tells him that there are 5000 seats in total. The first person is surprised by the size of the Olympic park."}
{"answers": "#Person1# wants to create a company and is going to write a business plan. #Person2# gives #Person1# suggestions on how to summarise business ideas, describe the service, differ from competitors and attract investment in a good business plan. #Person1# decides to stick to the old job.", "prediction_text": "A business plan is a document that outlines the goals and strategies of a business. It is a crucial tool for any entrepreneur, as it helps to define the company's mission, identify its target market, and develop a plan for achieving its goals. A well-written business plan can help to attract investors, secure loans, and establish credibility with customers and partners."}
{"answers": "#Person1# abandons the idea of creating a company after #Person2# explains what a business plan includes. #Person2# specifies that a well-written business plan includes an executive summary, goods and service description, strategy and implementation, and financial analysis.", "prediction_text": "A business plan is a document that outlines the goals and strategies of a business. It is a crucial tool for any entrepreneur, as it helps to define the company's mission, identify its target market, and develop a plan for achieving its goals. A well-written business plan can help to attract investors, secure loans, and establish credibility with customers and partners."}
{"answers": "#Person1# wants to start #Person1#'s own business, but #Person2# warns #Person1# of the hassle. #Person2# tells #Person1# what is needed in a business plan and #Person1# decides to stick to the old job for now.", "prediction_text": "A business plan is a document that outlines the goals and strategies of a business. It is a crucial tool for any entrepreneur, as it helps to define the company's mission, identify its target market, and develop a plan for achieving its goals. A well-written business plan can help to attract investors, secure loans, and establish credibility with customers and partners."}
{"answers": "#Person2# feels itchy. #Person1# doubts it is chicken pox and asks #Person2# to get away. #Person2# doesn't believe it.", "prediction_text": "A person with chicken pox is contagious and can spread the disease to others."}
{"answers": "#Person1# suspects that #Person2# has chicken pox and wants to keep away from #Person2#. #Person2# thinks #Person1# is exaggerating.", "prediction_text": "A person with chicken pox is contagious and can spread the disease to others."}
{"answers": "#Person1# thinks #Person2# has chicken pox and warns #Person2# about the possible hazards but #Person2# thinks it will be fine.", "prediction_text": "A person with chicken pox is contagious and can spread the disease to others."}
{"answers": "#Person2# is checking out and asks #Person1# for the bill. #Person1# gives #Person2# a wrong bill at first then corrects it.", "prediction_text": "The dialogue is about a hotel bill. The dialogue is between a hotel receptionist and a guest. The guest is checking out of the hotel. The guest is complaining about the bill. The hotel receptionist is trying to solve the problem."}
{"answers": "#Person1# helps #Person2# correct a mischarged bill on laundry service and helps #Person2# check out.", "prediction_text": "The dialogue is about a hotel bill. The dialogue is between a hotel receptionist and a guest. The guest is checking out of the hotel. The guest is complaining about the bill. The hotel receptionist is trying to solve the problem."}
{"answers": "#Person2# finds #Person2# being mischarged. #Person1# corrects the bill and #Person2# pays for it.", "prediction_text": "The dialogue is about a hotel bill. The dialogue is between a hotel receptionist and a guest. The guest is checking out of the hotel. The guest is complaining about the bill. The hotel receptionist is trying to solve the problem."}
{"answers": "#Person1# is begging Steven to persuade his wife not to divorce him. Steven agrees.", "prediction_text": "Dialogue: #Person1#: Steven, I need badly your help. #Person2#: What's the matter? #Person1#: My wife has found that I have an affair with my secretary, and now she is going to divorce me. #Person2#: How could you cheat on your wife? You have been married for ten years. #Person1#: Yes, I know I'm wrong. But I swear that the affair lasts only for two months. And I still love my wife. I couldn't live without her. #Person2#: I will try my best to persuade her to reconsider the divorce. But are you sure that from now on you will be faithful to her forever? #Person1#: Yes, I swear."}
{"answers": "Steve will try to persuade #Person1#'s wife not to divorce #Person1# as #Person1# swears to remain faithful forever.", "prediction_text": "Dialogue: #Person1#: Steven, I need badly your help. #Person2#: What's the matter? #Person1#: My wife has found that I have an affair with my secretary, and now she is going to divorce me. #Person2#: How could you cheat on your wife? You have been married for ten years. #Person1#: Yes, I know I'm wrong. But I swear that the affair lasts only for two months. And I still love my wife. I couldn't live without her. #Person2#: I will try my best to persuade her to reconsider the divorce. But are you sure that from now on you will be faithful to her forever? #Person1#: Yes, I swear."}
{"answers": "#Person1# begs Steven's to persuade #Person1#'s wife not to divorce him, and Steven agrees.", "prediction_text": "Dialogue: #Person1#: Steven, I need badly your help. #Person2#: What's the matter? #Person1#: My wife has found that I have an affair with my secretary, and now she is going to divorce me. #Person2#: How could you cheat on your wife? You have been married for ten years. #Person1#: Yes, I know I'm wrong. But I swear that the affair lasts only for two months. And I still love my wife. I couldn't live without her. #Person2#: I will try my best to persuade her to reconsider the divorce. But are you sure that from now on you will be faithful to her forever? #Person1#: Yes, I swear."}
{"answers": "#Person1# and #Person2# are talking about Abraham Lincoln. They think he was a noble man.", "prediction_text": "Dialogue: #Person1#: Who stands out in your mind as a man or woman of sound character? #Person2#: If I think of famous people, I think of Abraham Lincoln. #Person1#: He's the US president, who walked five miles just to give a lady her change, isn't he? #Person2#: That's the one. He also was famous for never giving up on his goals. #Person1#: That's right. He ran for office quite a few times before he was finally elected. #Person2#: And I also admire him for his courage in fighting for equal rights. #Person1#: He had great vision, didn't he? #Person2#: And humility. I would have liked to"}
{"answers": "#Person2# admires Abraham Lincoln for his perseverance, courage and humility.", "prediction_text": "Dialogue: #Person1#: Who stands out in your mind as a man or woman of sound character? #Person2#: If I think of famous people, I think of Abraham Lincoln. #Person1#: He's the US president, who walked five miles just to give a lady her change, isn't he? #Person2#: That's the one. He also was famous for never giving up on his goals. #Person1#: That's right. He ran for office quite a few times before he was finally elected. #Person2#: And I also admire him for his courage in fighting for equal rights. #Person1#: He had great vision, didn't he? #Person2#: And humility. I would have liked to"}
{"answers": "#Person1# and #Person2# talk about Abraham Lincoln and his glorious history. They both admire him.", "prediction_text": "Dialogue: #Person1#: Who stands out in your mind as a man or woman of sound character? #Person2#: If I think of famous people, I think of Abraham Lincoln. #Person1#: He's the US president, who walked five miles just to give a lady her change, isn't he? #Person2#: That's the one. He also was famous for never giving up on his goals. #Person1#: That's right. He ran for office quite a few times before he was finally elected. #Person2#: And I also admire him for his courage in fighting for equal rights. #Person1#: He had great vision, didn't he? #Person2#: And humility. I would have liked to"}
{"answers": "#Person2# plans to have a trip in Hebei but #Person1# says there are sandstorms in there.", "prediction_text": "The dialogue is about the sandstorms in Hebei, a province in northern China. The speaker says that the sandstorms are very strong and can cause respiratory tract infections. The other speaker says that the sandstorms are a problem for everyone."}
{"answers": "#Person1# tells #Person2# Hebei was experiencing severe sandstorms and sandstorms threaten people's health.", "prediction_text": "The dialogue is about the sandstorms in Hebei, a province in northern China. The speaker says that the sandstorms are very strong and can cause respiratory tract infections. The other speaker says that the sandstorms are a problem for everyone."}
{"answers": "#Person2# wants to travel to Hebei but #Person1# informs #Person2# of terrible sandstorms there.", "prediction_text": "The dialogue is about sandstorms in Hebei, a province in northern China. The speaker says that the sandstorms are very strong and that the people in Hebei are suffering from respiratory tract infections. The speaker also says that sandstorms are a problem for everybody."}
{"answers": "#Person2# comes to the birthday party of Francis and sends him a remote car model.", "prediction_text": "The dialogue is about a birthday party. The first person is the host and the second person is the guest. The host is happy that the guest can come to the party. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that"}
{"answers": "#Person2# gives Francis a car model that both of them love as a birthday gift.", "prediction_text": "The dialogue is about a birthday party. The first person is the host and the second person is the guest. The host is happy that the guest can come to the party. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that"}
{"answers": "#Person2# gives Francis a nice car model as a birthday gift and Francis appreciates it.", "prediction_text": "The dialogue is about a birthday party. The first person is the host and the second person is the guest. The host is happy that the guest can come to the party. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that the guest is happy. The guest is happy that the host is happy. The host is happy that"}
{"answers": "Tony got caught cheating and feels ashamed. Steven tells him to learn from it and study hard.", "prediction_text": "Dialogue: #Person1#: Hi, Tony. You look unhappy. What's wrong? #Person2#: Oh, Steven, I made a big mistake. #Person1#: What happened? #Person2#: I really wish I hadn't done it. #Person1#: What on earth are you talking about? #Person2#: I got caught cheating. I feel so ashamed. The teacher saw me and told me I failed. #Person1#: What were you thinking? #Person2#: You know my father. If I fail, he'll kill me. I have to do well. #Person1#: But what you should do is study hard. #Person2#: I know... I know... it's"}
{"answers": "Tony tells Steven that he failed a class because of cheating and he feels awful.", "prediction_text": "Dialogue: #Person1#: Hi, Tony. You look unhappy. What's wrong? #Person2#: Oh, Steven, I made a big mistake. #Person1#: What happened? #Person2#: I really wish I hadn't done it. #Person1#: What on earth are you talking about? #Person2#: I got caught cheating. I feel so ashamed. The teacher saw me and told me I failed. #Person1#: What were you thinking? #Person2#: You know my father. If I fail, he'll kill me. I have to do well. #Person1#: But what you should do is study hard. #Person2#: I know... I know... it's"}
{"answers": "Tony tells Steven that he cheated on the exam and realized his mistake.", "prediction_text": "Dialogue: #Person1#: Hi, Tony. You look unhappy. What's wrong? #Person2#: Oh, Steven, I made a big mistake. #Person1#: What happened? #Person2#: I really wish I hadn't done it. #Person1#: What on earth are you talking about? #Person2#: I got caught cheating. I feel so ashamed. The teacher saw me and told me I failed. #Person1#: What were you thinking? #Person2#: You know my father. If I fail, he'll kill me. I have to do well. #Person1#: But what you should do is study hard. #Person2#: I know... I know... it's"}
{"answers": "#Person1# is catching a train. Tom asks #Person1# not to hurry.", "prediction_text": "Dialogue: #Person1#: What time is it, Tom?"}
{"answers": "#Person1# is in a hurry to catch a train. Tom tells #Person1# there is plenty of time.", "prediction_text": "Dialogue: #Person1#: What time is it, Tom?"}
{"answers": "#Person1# is rushing to catch a train but Tom thinks it isn't necessary.", "prediction_text": "Dialogue: #Person1#: What time is it, Tom?"}
{"answers": "#Person1# wants to adjust #Person1#'s life and #Person2# suggests #Person1# be positive and stay healthy.", "prediction_text": "Dialogue: #Person1#: I don't know how to adjust my life. Would you give me a piece of advice?"}
{"answers": "#Person1# has a bad lifestyle. #Person2# kindly advises #Person1# to be positive.", "prediction_text": "Dialogue: #Person1#: I don't know how to adjust my life. Would you give me a piece of advice?"}
{"answers": "#Person2# hopes #Person1# will become healthy and positive.", "prediction_text": "Dialogue: #Person1#: I don't know how to adjust my life. Would you give me a piece of advice?"}
{"answers": "#Person1# tells #Person2# that Ruojia is married and will have a party tonight. #Person2#'s surprised to know that. They will bring their gifts to bless her.", "prediction_text": "The dialogue is about a party. The first person is the host. The second person is the guest. The first person asks the second person if he/she wants to go to the party. The second person asks whose party it is. The first person says it is Ruojia's. The second person says he/she didn't know that. The first person says Ruojia has got married. The second person says he/she can't believe it. The first person says yes. The second person says he/she feels so happy for her. The first person says yes, he/she feels so happy for her too. The second person says he/she didn't receive the email. The first person says maybe he/she should check his/her email. The"}
{"answers": "#Person2# is surprised to know from #Person1# that Ruojia is married. Then #Person2# finds Ruojia has sent an email about it. They will go to Ruojia's party and give their presents to her.", "prediction_text": "The dialogue is about a party. The first person is the host. The second person is the guest. The first person asks the second person if he/she wants to go to the party. The second person asks whose party it is. The first person says it is Ruojia's. The second person says he/she didn't know that. The first person says Ruojia has got married. The second person says he/she can't believe it. The first person says yes. The second person says he/she feels so happy for her. The first person says yes, he/she feels so happy for her too. The second person says he/she didn't receive the email. The first person says maybe he/she should check his/her email. The"}
{"answers": "#Person2# is surprised that Ruojia's married. #Person1# and #Person2# will go to her party and give their presents to wish her a happy marriage.", "prediction_text": "The dialogue is about a party. The first person is the host. The second person is the guest. The first person asks the second person if he/she wants to go to the party. The second person asks whose party it is. The first person says it is Ruojia's. The second person says he/she didn't know that. The first person says Ruojia has got married. The second person says he/she can't believe it. The first person says yes. The second person says he/she feels so happy for her. The first person says yes, he/she feels so happy for her too. The second person says he/she didn't receive the email. The first person says maybe he/she should check his/her email. The"}
{"answers": "#Person2# at first thinks #Person1#'s behaviour cruel but finally joins #Person1#.", "prediction_text": "A man and a woman are having a conversation. The man is trying to convince the woman to help him rob a bank."}
{"answers": "#Person1# plans on playing a trick to others. #Person2# thinks it's cruel at first but then joins.", "prediction_text": "A man and a woman are having a conversation. The man is trying to convince the woman to help him rob a bank."}
{"answers": "#Person1# is about to make a prank. #Person2# thinks it's cruel at first but then joins.", "prediction_text": "A man and a woman are having a conversation. The man is trying to convince the woman to help him rob a bank."}
{"answers": "Mike is describing his sister to #Person1#.", "prediction_text": "The speaker is asking the other person about his sister. The other person describes her as tall and pretty. The speaker asks if she is like him. The other person says that they are both friendly and easy-going. The speaker asks if she is as clever as he is. The other person says that she is not as clever as he is. The speaker says that she has a big head."}
{"answers": "Mike describes to #Person1# his sister's characters and personality.", "prediction_text": "The speaker is asking the other person about his sister. The other person describes her as tall and pretty. The speaker asks if she is like him. The other person says that they are both friendly and easy-going. The speaker asks if she is as clever as he is. The other person says that she is not as clever as he is. The speaker says that she has a big head."}
{"answers": "#Person1# asks Mike about his sister.", "prediction_text": "The speaker is asking the other person about his sister. The other person describes her as tall and pretty. The speaker asks if she is like him. The other person says that they are both friendly and easy-going. The speaker asks if she is as clever as he is. The other person says that she is not as clever as he is. The speaker says that she has a big head."}
{"answers": "#Person1# feels sick and #Person2# gives #Person1# a check-up.", "prediction_text": "The dialogue is about a student who has a headache and a small fever. The student's parents are not at home, so the student calls the school office to ask for help. The school office staff member takes the student's temperature and tells the student that he has a small fever. The student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's"}
{"answers": "#Person2# finds that #Person1# has a fever and says #Person1# should've called in sick earlier.", "prediction_text": "The dialogue is about a student who has a headache and a small fever. The student's parents are not at home, so the student calls the school office to ask for help. The school office staff member takes the student's temperature and tells the student that he has a small fever. The student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's"}
{"answers": "#Person2# checks #Person1#'s physical condition and finds #Person1# has a fever.", "prediction_text": "The dialogue is about a student who has a headache and a small fever. The student's parents are not at home, so the student calls the school office to ask for help. The school office staff member takes the student's temperature and tells the student that he has a small fever. The student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's parents are not at home, so the student's"}
{"answers": "#Person1# helps #Person2# to choose a new phone.", "prediction_text": "This dialogue is about buying a mobile phone. The customer wants a phone with camera and MP3 player and the ability to make video calls."}
{"answers": "#Person2# wants to buy a new mobile phone from #Person1#.", "prediction_text": "This dialogue is about buying a mobile phone. The customer wants a phone with camera and MP3 player and the ability to make video calls."}
{"answers": "#Person2# wants to buy a new mobile phone from #Person1#.", "prediction_text": "This dialogue is about buying a mobile phone. The customer wants a phone with camera and MP3 player and the ability to make video calls."}
{"answers": "Frank got a new job and is telling Judy not only the heavy schedule but also the benefits of this job.", "prediction_text": "The dialogue is about a man who is working for the Post Office. He is happy with his job because he can get free medical care for his family."}
{"answers": "Frank explains to Judy the reason why he took a job at the Post office is that the government offers excellent health insurance benefits for its employees.", "prediction_text": "The dialogue is about a man who is working for the Post Office. He is happy with his job because he can get free medical care for his family."}
{"answers": "Frank describes his new job's schedule and insurance at the Post Office to Judy.", "prediction_text": "The dialogue is about a man who is working for the Post Office. He is happy with his job because he can get free medical care for his family."}
{"answers": "#Person2# is describing the educational background and previous working experience to #Person1# in a job interview.", "prediction_text": "The candidate is a graduate of the University of London, with a doctor license and a driving license. She has a good command of secretarial skills and has passed both the Cambridge Examinations, First Certificate and the Certificate of Proficiency in English. She has also studied for a year in London at the Lucas Secretarial College, ending with speeds of 120 words per minute in English shorthand and 50 words per minute in typing. She has received some special training in typing, shorthand and operating a fax machine, etc. She is experienced in IBM-PC. She can operate familiarly a word-processor, a fax machine, a photo and other office equipment."}
{"answers": "#Person2# is being interviewed by #Person1#. #Person2# is equipped with a bunch of valuable office skills.", "prediction_text": "The candidate is a graduate of the University of London, with a doctor license and a driving license. She has a good command of secretarial skills and has passed both the Cambridge Examinations, First Certificate and the Certificate of Proficiency in English. She has also studied for a year in London at the Lucas Secretarial College, ending with speeds of 120 words per minute in English shorthand and 50 words per minute in typing. She has received some special training in typing, shorthand and operating a fax machine, etc. She is experienced in IBM-PC. She can operate familiarly a word-processor, a fax machine, a photo and other office equipment."}
{"answers": "#Person1# asks about #Person2#'s special skills, training, previous working experience and other qualification for the job.", "prediction_text": "The candidate is a graduate of the University of London, with a doctor license and a driving license. She has a good command of secretarial skills and has passed both the Cambridge Examinations, First Certificate and the Certificate of Proficiency in English. She has also studied for a year in London at the Lucas Secretarial College, ending with speeds of 120 words per minute in English shorthand and 50 words per minute in typing. She has received some special training in typing, shorthand and operating a fax machine, etc. She is experienced in IBM-PC. She can operate familiarly a word-processor, a fax machine, a photo and other office equipment."}
{"answers": "#Person1# is not satisfied with the steak and #Person2# will change it.", "prediction_text": "A dialogue between a customer and a waiter in a restaurant."}
{"answers": "#Person2# will change a steak for #Person1# as that one doesn't satisfy #Person1#.", "prediction_text": "A dialogue between a customer and a waiter in a restaurant."}
{"answers": "#Person1# is not satisfied with #Person1#'s steak and asks to change one, and #Person2# agrees.", "prediction_text": "Dialogue: #Person1#: Excuse me."}
{"answers": "#Person1# congratulates Tom for achieving the Nobel Prize.", "prediction_text": "The dialogue is about a person who wins the Nobel Prize. The person is very happy and proud of himself. He thanks the other person for his compliment."}
{"answers": "#Person1# tells Tom that his novel has won the Nobel Prize.", "prediction_text": "The dialogue is about a person who wins the Nobel Prize. The person is very happy and proud of himself. He thanks the other person for his compliment."}
{"answers": "#Person2# tells Tom he won the Nobel Prize.", "prediction_text": "The dialogue is about a person who wins the Nobel Prize. The person is very happy and proud of himself. He thanks the other person for his compliment."}
{"answers": "#Person1# is interviewing #Person2# about #Person2#'s ability and previous experience.", "prediction_text": "The candidate has a master's degree in science and is a student engineer. He has worked on understanding the mechanical strength and corrosion resistance of various materials. He is confident that he can do the job well."}
{"answers": "#Person1# asks #Person2# about #Person2#'s capacities and past working experience during an interview.", "prediction_text": "The candidate has a master's degree in science and is a student engineer. He has worked on understanding the mechanical strength and corrosion resistance of various materials. He is confident that he can do the job well."}
{"answers": "#Person1# asks #Person2# about #Person2#'s qualification for the job.", "prediction_text": "The candidate has a master's degree in science and is a student engineer. He has worked on understanding the mechanical strength and corrosion resistance of various materials. He is confident that he can do the job well."}
{"answers": "#Person1# and #Person2# are talking about some personal facts of drinking. #Person2# drinks a lot, while #Person1# cannot drink much for health reasons. They decide to have a drink together tomorrow night.", "prediction_text": "The dialogue is between two people who are talking about drinking. The first person is a light weight and the second person is a heavy drinker. The first person is trying to build up a tolerance to drinking. The second person is trying to convince the first person to drink more."}
{"answers": "#Person2#, a heavy drinker, invites #Person1#, a light drinker to go out the next day. #Person2# chooses a place that has a special on pitchers.", "prediction_text": "The dialogue is between two people who are talking about drinking. The first person is a light weight and the second person is a heavy drinker. The first person is trying to build up a tolerance to drinking. The second person is trying to convince the first person to drink more."}
{"answers": "#Person1# and #Person2# talk about their drinking capacity and their drinking habits. They decide to have a drink together tomorrow night.", "prediction_text": "The dialogue is between two people who are talking about drinking. The first person is a light weight and the second person is a heavy drinker. The first person is trying to build up a tolerance to drinking. The second person is trying to convince the first person to drink more."}
{"answers": "May is helping her mother to do some preparation for the picnic.", "prediction_text": "Dialogue: #Person1#: May, do you mind helping me prepare for the picnic? #Person2#: Sure. Have you checked the weather report? #Person1#: Yes. It says it will be sunny all day. No sign of rain at all. This is your father's favorite sausage. Sandwiches for you and Daniel. #Person2#: No, thanks Mom. I'd like some toast and chicken wings. #Person1#: Okay. Please take some fruit salad and crackers for me. #Person2#: Done. Oh, don't forget to take napkins disposable plates, cups and picnic blanket. #Person1#: All set. May, can you help me take all these things to the living room? #Person2"}
{"answers": "May's mother asks May for help in preparing for a picnic. May gives her a hand.", "prediction_text": "Dialogue: #Person1#: May, do you mind helping me prepare for the picnic? #Person2#: Sure. Have you checked the weather report? #Person1#: Yes. It says it will be sunny all day. No sign of rain at all. This is your father's favorite sausage. Sandwiches for you and Daniel. #Person2#: No, thanks Mom. I'd like some toast and chicken wings. #Person1#: Okay. Please take some fruit salad and crackers for me. #Person2#: Done. Oh, don't forget to take napkins disposable plates, cups and picnic blanket. #Person1#: All set. May, can you help me take all these things to the living room? #Person2"}
{"answers": "Mom asks May to help to prepare for the picnic and May agrees.", "prediction_text": "Dialogue: #Person1#: May, do you mind helping me prepare for the picnic? #Person2#: Sure. Have you checked the weather report? #Person1#: Yes. It says it will be sunny all day. No sign of rain at all. This is your father's favorite sausage. Sandwiches for you and Daniel. #Person2#: No, thanks Mom. I'd like some toast and chicken wings. #Person1#: Okay. Please take some fruit salad and crackers for me. #Person2#: Done. Oh, don't forget to take napkins disposable plates, cups and picnic blanket. #Person1#: All set. May, can you help me take all these things to the living room? #Person2"}
{"answers": "Muriel Douglas and James meet each other and talk about what they have done during the holiday.", "prediction_text": "This dialogue is a sample of a conversation between a salesperson and a potential client. The salesperson is trying to get the client to agree to a meeting to discuss a new account. The client is a little hesitant at first but eventually agrees to meet. The salesperson offers the client something to drink while they wait for the other person to arrive. The client says no thanks and then asks the salesperson about their holiday. The salesperson says they had a nice one and asks the client how theirs was. The client says they stayed in L. A. and it was sunny the entire weekend. They spent most of the time at home but they did go see King Kong on Christmas day. The salesperson asks how they liked it and the client"}
{"answers": "James and Muriel are talking while waiting for Susan, Muriel's associate. They talk about how they spent the holiday with their families.", "prediction_text": "This dialogue is a sample of a conversation between a salesperson and a potential client. The salesperson is trying to get the client to agree to a meeting to discuss a new account. The client is a little hesitant at first but eventually agrees to meet. The salesperson offers the client something to drink while they wait for the other person to arrive. The client says no thanks and then asks the salesperson about their holiday. The salesperson says they had a nice one and asks the client how theirs was. The client says they stayed in L. A. and it was sunny the entire weekend. They spent most of the time at home but they did go see King Kong on Christmas day. The salesperson asks how they liked it and the client"}
{"answers": "Muriel Douglas and James send greetings and talk about their holiday before the meeting. Douglas stayed in L.A., while James went to Tahoe.", "prediction_text": "This dialogue is a sample of a conversation between a salesperson and a potential client. The salesperson is trying to get the client to agree to a meeting to discuss a new account. The client is a little hesitant at first but eventually agrees to meet. The salesperson offers the client something to drink while they wait for the other person to arrive. The client says no thanks and then asks the salesperson about their holiday. The salesperson says they had a nice one and asks the client how theirs was. The client says they stayed in L. A. and it was sunny the entire weekend. They spent most of the time at home but they did go see King Kong on Christmas day. The salesperson asks how they liked it and the client"}
{"answers": "#Person1# wants to withdraw money from an ATM, but the ATM automatically transfers 10000 USD to the World Wildlife Foundation after confirming the withdrawal option. #Person1# gets mad and somehow locked in.", "prediction_text": "A man is trying to withdraw money from the ATM, but the machine is not cooperating. The man gets angry and starts yelling at the machine, but the machine continues to ignore him. The man eventually gives up and leaves, but not before the machine locks the doors and seals the exits. The man is left stranded and has to wait for the authorities to arrive."}
{"answers": "#Person1# run out of money because of a girl, and is withdrawing money from an ATM. But the ATM seems to go wrong and transfers #Person1#'s money to the World Wildlife Foundation, driving #Person1# crazy.", "prediction_text": "A man is trying to withdraw money from the ATM, but the machine is not cooperating. The man gets angry and starts yelling at the machine, but the machine continues to ignore him. The man eventually gives up and leaves, but not before the machine locks the doors and seals the exits. The man is left stranded and has to wait for the authorities to arrive."}
{"answers": "#Person1# is withdrawing money from an ATM. But the ATM wrongly transfers #Person1#'s money to the World Wildlife Foundation. It drives #Person1# crazy.", "prediction_text": "A man is trying to withdraw money from the ATM, but the machine is not cooperating. The man gets angry and starts yelling at the machine, but the machine continues to ignore him. The man eventually gives up and leaves, but not before the machine locks the doors and seals the exits. The man is left stranded and has to wait for the authorities to arrive."}
{"answers": "#Person2# tells #Person1# #Person2#'s communication strategy.", "prediction_text": "The dialogue is about the importance of sincerity in communication. The first person asks the second person if he is a social person. The second person says that he is an outgoing person who likes to be with a lot of friends. The first person asks the second person what is his strategy in communicating with colleagues. The second person says that the most important thing in communication is your sincerity."}
{"answers": "Since #Person2# is very social, #Person1# asks for the communication strategy.", "prediction_text": "The dialogue is about the importance of sincerity in communication. The first person asks the second person if he is a social person. The second person says that he is an outgoing person who likes to be with a lot of friends. The first person asks the second person what is his strategy in communicating with colleagues. The second person says that the most important thing in communication is your sincerity."}
{"answers": "#Person2# shares #Person2#'s communication strategy with #Person1#.", "prediction_text": "The dialogue is about the importance of sincerity in communication. The first person asks the second person if he is a social person. The second person says that he is an outgoing person who likes to be with a lot of friends. The first person asks the second person what is his strategy in communicating with colleagues. The second person says that the most important thing in communication is your sincerity."}
{"answers": "Mr. Polly is tired and wants a break from work. #Person1# cannot buy a bottle of soft drink for him.", "prediction_text": "Dialogue: #Person1#: What's wrong with you, Mr. Polly?"}
{"answers": "Mr. Polly wants to get a break from work and he asks #Person1# to buy a drink for him, but #Person1# refuses.", "prediction_text": "Dialogue: #Person1#: What's wrong with you, Mr. Polly?"}
{"answers": "Mr. Polly asks #Person1#'s help to buy a drink, but #Person1# refuses.", "prediction_text": "Dialogue: #Person1#: What's wrong with you, Mr. Polly?"}
{"answers": "Francis and Monica are discussing when to work on the financial report.", "prediction_text": "Francis and Monica are working on a financial report. They have to find a time to meet and discuss the report. They agree to meet on Friday afternoon."}
{"answers": "Francis and Monica manage to find time to work on a report together.", "prediction_text": "Francis and Monica are working on a financial report. They have to find a time to meet and discuss the report. They agree to meet on Friday afternoon."}
{"answers": "Francis and Monica negotiate on the time to work on the report.", "prediction_text": "Francis and Monica are working on a financial report. They have to find a time to meet and discuss the report. They agree to meet on Friday afternoon."}
{"answers": "#Person1# joins #Person2#'s interview workshop. They discuss the tips to improve their interview performance.", "prediction_text": "The dialogue above is a conversation between two people who are taking a workshop to prepare for an interview. The first person is excited to be taking the workshop, and the second person is happy to have the opportunity to help the first person prepare for the interview. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second"}
{"answers": "#Person1# takes an interview workshop. #Person2# offer #Person1# some useful tips on getting ready for an interview.", "prediction_text": "The dialogue above is a conversation between two people who are taking a workshop to prepare for an interview. The first person is excited to be taking the workshop, and the second person is happy to have the opportunity to help the first person prepare for the interview. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second"}
{"answers": "In the workshop, #Person2# offer #Person1# some suggestions on how to perform well in interviews.", "prediction_text": "The dialogue above is a conversation between two people who are taking a workshop to prepare for an interview. The first person is excited to be taking the workshop, and the second person is happy to have the opportunity to help the first person prepare for the interview. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second person has to say about the interview, and the second person is happy to share his or her thoughts. The first person is interested in what the second"}
{"answers": "#Person1# and Mike are discussing what kind of emotion should be expressed by Mike in this play. They have different understandings.", "prediction_text": "Dialogue between two actors, one of whom is the director."}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "Dialogue between two actors, one of whom is the director."}
{"answers": "#Person1# wants Mike to act more angry, but Mike thinks he should act both angry and sad.", "prediction_text": "Dialogue between two actors, one of whom is the director."}
{"answers": "#Person1# greets Mrs. Todd and then they say goodbye to each other.", "prediction_text": "The dialogue is between two people who are neighbors. The first person is a man who is visiting his neighbor, Mrs. Todd. The second person is Mrs. Todd. The first person is talking to Mrs. Todd about his wife, Jane, and how they are busy with work and children. He also mentions that he and Jane have been talking about visiting Mrs. Todd. The second person says that she is glad he came over and that she should have called on him first. She also says that she does not need anything from the store. The first person thanks her for the offer and says that it is a pleasure to visit her."}
{"answers": "#Person1# comes over to say hello to Mrs. Todd, and will go shopping afterwards.", "prediction_text": "The dialogue is between two people who are neighbors. The first person is a man who is visiting his neighbor, Mrs. Todd. The second person is Mrs. Todd. The first person is talking to Mrs. Todd about his wife, Jane, and how they are busy with work and children. He also mentions that he and Jane have been talking about visiting Mrs. Todd. The second person says that she is glad he came over and that she should have called on him first. She also says that she does not need anything from the store. The first person thanks her for the offer and says that it is a pleasure to visit her."}
{"answers": "#Person1# visits Mrs. Todd, and Mrs. Todd appreciates that.", "prediction_text": "The dialogue is between two people who are neighbors. The first person is a man who is visiting his neighbor, Mrs. Todd. The second person is Mrs. Todd. The first person is talking to Mrs. Todd about his wife, Jane, and how they are busy with work and children. The second person is talking to the first person about how she should have called on the first person first, since they have just moved into the neighborhood. The second person is also thanking the first person for coming over and offering to help with anything the first person needs. The first person is thanking the second person for the offer."}
{"answers": "Bill is tired. Bill and #Person1# talk about Bill's brother.", "prediction_text": "The speaker is worried about his friend Bill. He is tired and pale. The speaker suggests that Bill should go home and take it easy. The speaker asks about Bill's brother. He is in the States on a business trip. The speaker wants to be sure of the time because he is going to meet a friend at five o'clock sharp. The speaker sets his watch with the radio."}
{"answers": "#Person1# has a chat with Bill, and learns updates about Bill and his brother. #Person1# asks for time because #Person1# will go meet a friend.", "prediction_text": "The speaker is worried about his friend Bill. He is tired and pale. The speaker suggests that Bill should go home and take it easy. The speaker asks about Bill's brother. He is in the States on a business trip. The speaker wants to be sure of the time because he is going to meet a friend at five o'clock sharp. The speaker sets his watch with the radio."}
{"answers": "#Person1# suggests Bill take it easy and asks him about his brother. #Person1# also synchronizes the time with Bill.", "prediction_text": "The speaker is worried about his friend Bill. He is tired and pale. The speaker suggests that Bill should go home and take it easy. The speaker asks about Bill's brother. He is in the States on a business trip. The speaker wants to be sure of the time because he is going to meet a friend at five o'clock sharp. The speaker sets his watch with the radio."}
{"answers": "Simon and Cleo have different opinions towards the demonstration to help stop the spread of nuclear weapons. Cleo thinks it is useless, while Simon considers that Cleo should go to the demonstration.", "prediction_text": "Dialogue: #Person1#: Are you going to the demonstration to help stop the spread of nuclear weapons tomorrow, Cleo?"}
{"answers": "Cleo has no intention to attend the demonstration to help stop the spread of nuclear weapons, because Cleo hates police standing by with tear gas. Simon tries to change Cleo's mind but it doesn't work.", "prediction_text": "Dialogue: #Person1#: Are you going to the demonstration to help stop the spread of nuclear weapons tomorrow, Cleo?"}
{"answers": "Simon and Cleo argue the effectiveness of demonstrations. Simon thinks they are helpful, but Cleo disagrees and refuses to go to the demonstration of helping stop the spread of nuclear weapons with Simon.", "prediction_text": "Dialogue: #Person1#: Are you going to the demonstration to help stop the spread of nuclear weapons tomorrow, Cleo?"}
{"answers": "#Person2# let in someone and #Person1# thinks it was #Person2#'s fault.", "prediction_text": "A man is talking to his wife about a man who has been coming to the door asking for gas and electricity."}
{"answers": "#Person1# blames #Person2# for letting someone in without much discretion.", "prediction_text": "A man is talking to his wife about a man who has been coming to the door asking for gas and electricity."}
{"answers": "#Person1# advises #Person2# not to let anyone in casually.", "prediction_text": "A man is talking to his wife about a man who has been coming to the door asking for gas and electricity."}
{"answers": "Mark wants to borrow Maggie's class notes. Maggie suggests Mark copy them in the library and invites him to be study partners.", "prediction_text": "Dialogue: #Person1#: Maggie, can I borrow your notes for history? I'll return them tomorrow."}
{"answers": "Mark asks Maggie for her history notes because Mark has been too tired in class. They become study partners at the end.", "prediction_text": "Dialogue: #Person1#: Maggie, can I borrow your notes for history? I'll return them tomorrow."}
{"answers": "Mark asks Maggie for her notes because Mark's notes aren't good. They decide to be study partners and study together.", "prediction_text": "Dialogue: #Person1#: Maggie, can I borrow your notes for history? I'll return them tomorrow."}
{"answers": "#Person2# wants to take a course of Professor Turner and Professor agrees if #Person2# achieves the allowance of Professor Burman.", "prediction_text": "The dialogue is about a student who wants to take a graduate course in geology. The professor is reluctant to let him enroll, but the student is persistent and eventually convinces the professor to let him take the course."}
{"answers": "#Person2#, a junior student, wants to enroll in Professor Turner's course for seniors very much. Professor Turner will ask for another professor's opinion.", "prediction_text": "The dialogue is about a student who wants to take a graduate course in geology. The professor is reluctant to let him enroll, but the student is persistent and eventually convinces the professor to let him take the course."}
{"answers": "#Person2# wants to enroll in Professor Turner's course, and Professor Turner agrees to consider his application.", "prediction_text": "The dialogue is about a student who wants to take a graduate course in geology. The professor is reluctant to let him enroll, but the student is persistent and eventually convinces the professor to let him take the course."}
{"answers": "#Person1# wants to change the broken pendant in #Person2#'s shop.", "prediction_text": "The dialogue is about a customer who bought a pendant in a shop. The customer comes back to the shop to show the pendant to his friend, but it is broken. The shopkeeper offers to change it to a new one."}
{"answers": "#Person1# goes back to #Person2#'s shop to replace a broken pendant.", "prediction_text": "The dialogue is about a customer who bought a pendant in a shop. The customer comes back to the shop to show the pendant to his friend, but it is broken. The shopkeeper offers to change it to a new one."}
{"answers": "#Person1# wants a product changed from #Person2#, and #Person2# agrees.", "prediction_text": "The dialogue is about a customer who bought a pendant in a shop. The customer comes back to the shop to show the pendant to his friend, but it is broken. The shopkeeper offers to change it to a new one."}
{"answers": "#Person1# is showing the new clothes to #Person2#, but #Person2# is busy watching the games. They quarrel and get angry.", "prediction_text": "This is a dialogue between two people who are having a conversation about a game. The first person is trying to talk about their new clothes, but the second person is trying to watch the game. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person"}
{"answers": "#Person1# and #Person2# have a serious quarrel over whether shopping for clothes or watching a sports game is more important.", "prediction_text": "This is a dialogue between two people who are having a conversation about a game. The first person is trying to talk about their new clothes, but the second person is trying to watch the game. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person"}
{"answers": "#Person1# asks #Person2#'s opinion on new clothes, but #Person2# only focuses on the game and feels annoyed. They are both angry and argue.", "prediction_text": "This is a dialogue between two people who are having a conversation about a game. The first person is trying to talk about their new clothes, but the second person is trying to watch the game. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person is trying to insult the second person's team, but the second person is trying to insult the first person's clothes. The first person"}
{"answers": "#Person1# gives suggestions on job choices to #Person2#. #Person2# likes interactive media.", "prediction_text": "The speaker is looking for a job. The other person is looking for a job. They are talking about jobs."}
{"answers": "#Person1# and #Person2# are looking at jobs. #Person2# is interested in the media industry.", "prediction_text": "The speaker is looking for a job. The other person is looking for a job. They are talking about jobs."}
{"answers": "#Person1# offers some job choices to #Person2#, but #Person2# doesn't like them.", "prediction_text": "The speaker is looking for a job. The other person suggests that he or she could work in the media. The speaker is not interested in that. The other person suggests working with computers. The speaker is interested in that."}
{"answers": "#Person2# feels bored with the meeting. #Person1# gives #Person2# some suggestions on how to entertain but finally asks #Person2# to keep quiet.", "prediction_text": "A man and a woman are at a charity event. The man is bored and the woman is trying to find something to do."}
{"answers": "#Person1#, a reporter, takes #Person2# to a forum. #Person2# is bored and tries to find a pastime.", "prediction_text": "A man and a woman are at a charity event. The man is bored and the woman is trying to find something to do."}
{"answers": "#Person2# tells #Person1# that #Person2# is bored and explains the reasons, so #Person1# suggests #Person2# read some old newspapers and be quiet.", "prediction_text": "A man and a woman are at a charity event. The man is bored and the woman is trying to find something to do."}
{"answers": "Sarah is considering moving. #Person1# gives her advice on buying a house.", "prediction_text": "The speaker is talking to a friend about a house that is for sale. The friend is interested in buying the house but is worried about the location. The speaker tells her that the house is in a good location and that the neighbors are friendly."}
{"answers": "Sarah wants to move. #Person1# suggests buying a cheaper house and offers information on possible options.", "prediction_text": "The speaker is talking to a friend about a house that is for sale. The friend is interested in buying the house but is worried about the location. The speaker tells her that the house is in a good location and that the neighbors are friendly."}
{"answers": "Sarah tells #Person1# that she can't afford a new house. #Person1# says #Person1#'s sister-in-law bought a cheap house and suggests Sarah should buy a house far from the city center.", "prediction_text": "The speaker is talking to a friend about a house that is for sale. The friend is interested in buying the house but is worried about the location. The speaker tells her that the house is in a good location and that the neighbors are friendly."}
{"answers": "#Person1# introduces Mark Leach to this week's People You Meet. Mark Leach describes his job responsibilities and gives advice to people coming to Britain.", "prediction_text": "This dialogue is about a tourist information officer in London."}
{"answers": "#Person1# hosts the week's People You Meet, featuring Mark Leach. Mark introduces his work as an information officer in London.", "prediction_text": "This dialogue is about a tourist information officer in London."}
{"answers": "Mark Leach introduces his information service and his work to the audience. He also offers suggestions for people coming to Britain.", "prediction_text": "This dialogue is about a tourist information officer in London."}
{"answers": "Lin Fang and Lucy are talking about their favourite subjects.", "prediction_text": "The dialogue is about two students, Lin Fang and Lucy. They talk about their favorite subjects."}
{"answers": "Lin Fang and Lucy are talking about how they like different subjects.", "prediction_text": "The dialogue is about two students, Lin Fang and Lucy. They talk about their favorite subjects."}
{"answers": "Lucy likes English and P.E. best, but Lin Fang's favorite is Chinese and Science.", "prediction_text": "The dialogue is about two students, Lin Fang and Lucy. They talk about their favorite subjects."}
{"answers": "James comes to help Mrs. Thomas to do some housework on the weekend. James is saving up to buy a bike.", "prediction_text": "The dialogue is about a man who helps his elderly neighbor with her chores. The man is very kind and helpful. He is also a good student and is saving up to buy a bike."}
{"answers": "James helped Mrs. Thomas with housework since she has broken her legs, and he wanted to save money for a bike.", "prediction_text": "The dialogue is about a man who helps his elderly neighbor with her chores. The man is very kind and helpful. He is also a good student and is saving up to buy a bike."}
{"answers": "James helps Mrs. Thomas to do chores because James wanted to save money and Mrs. Thomas is incapable.", "prediction_text": "The dialogue is about a man who helps his elderly neighbor with her chores. The man is very kind and helpful. He is also a good student and is saving up to buy a bike."}
{"answers": "#Person1# and #Person2# are talking about the low temperature at night, although spring has come.", "prediction_text": "I'm not sure if this is a good idea or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should"}
{"answers": "#Person1# and #Person2# agree that it still felt very cold in spring.", "prediction_text": "I'm not sure if this is a good idea or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should"}
{"answers": "#Person1# and #Person2# talk about the weather and how to keep warm.", "prediction_text": "I'm not sure if this is a good idea or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should be doing this or not. I'm not sure if I should"}
{"answers": "#Person1# and Mike are discussing what kind of emotion should be expressed by Mike in this play. They have different understandings.", "prediction_text": "Dialogue between two actors who are working on a scene."}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "Dialogue between two actors who are working on a scene."}
{"answers": "#Person1# wants Mike to act more angry, but Mike thinks he should act both angry and sad.", "prediction_text": "Dialogue between two actors who are working on a scene."}
{"answers": "#Person1# takes a taxi to the Friendship Hotel for something important.", "prediction_text": "Dialogue: #Person1#: Taxi!"}
{"answers": "#Person2# drives #Person1# to the Friendship Hotel as fast as #Person2# can be.", "prediction_text": "Dialogue: #Person1#: Taxi!"}
{"answers": "The taxi driver takes #Person1# to the Friendship Hotel quickly.", "prediction_text": "Dialogue: #Person1#: Taxi!"}
{"answers": "#Person1# doesn't have any change for the bus because #Person1# bought some chips. #Person2# suggests #Person1# get a transfer from the bus driver.", "prediction_text": "Dialogue: #Person1#: I'm broke, and I have to catch another bus."}
{"answers": "#Person1#'s broke, so #Person2# suggests #Person1# get a free bus transfer.", "prediction_text": "Dialogue: #Person1#: I'm broke, and I have to catch another bus."}
{"answers": "#Person1#'s broke, so #Person2# suggests #Person1# get a transfer from the bus driver to go home.", "prediction_text": "Dialogue: #Person1#: I'm broke, and I have to catch another bus."}
{"answers": "#Person2# tells #Person1# information about their company and its surroundings.", "prediction_text": "Dialogue: #Person1#: How long does it take to get to downtown from here?"}
{"answers": "#Person1# asks #Person2# about the company's surroundings, location, and staff restaurant.", "prediction_text": "Dialogue: #Person1#: How long does it take to get to downtown from here?"}
{"answers": "#Person2# introduces the surroundings and information about their company to #Person1#.", "prediction_text": "Dialogue: #Person1#: How long does it take to get to downtown from here?"}
{"answers": "#Person1# and #Person2# are gossiping about Lulu and Vic's break-up.", "prediction_text": "Lulu and Vic broke up and now she's asked for a transfer."}
{"answers": "#Person1# and #Person2# are talking about Lulu and Vic's breakup.", "prediction_text": "Lulu and Vic broke up and now she's asked for a transfer."}
{"answers": "#Person1# and #Person2# talk about Lulu and Vic's breakup.", "prediction_text": "Lulu and Vic broke up and now she's asked for a transfer."}
{"answers": "Dave Thomson calls Jim but Jim went out. Sally will tell Jim Dave called.", "prediction_text": "This is a short dialogue between two people. The first person is a receptionist in a shop. The second person is a customer who wants to speak to Jim, who is the manager of the shop. The customer asks the receptionist if Jim is in, and the receptionist says that he went out about an hour ago and he's not back yet. The customer asks if she knows when he might be back, and the receptionist says that he shouldn't be long. The customer asks if she can tell him that he's been called, and the receptionist says that she will. The customer thanks the receptionist and says goodbye."}
{"answers": "Dave calls Jim but Jim is out. Sally tells Dave Jim's whereabouts.", "prediction_text": "This is a short dialogue between two people. The first person is a receptionist in a shop. The second person is a customer who wants to speak to Jim, who is the manager of the shop. The customer asks the receptionist if Jim is in, and the receptionist says that he went out about an hour ago and he's not back yet. The customer asks if she knows when he might be back, and the receptionist says that he shouldn't be long. The customer asks if she can tell him that he's been called, and the receptionist says that she will. The customer thanks the receptionist and says goodbye."}
{"answers": "Dave Thomson calls to speak to Jim but Jim is out.", "prediction_text": "This is a short dialogue between two people. The first person is a receptionist in a shop. The second person is a customer who wants to speak to Jim, the manager. The first person tells the second person that Jim is not in the shop at the moment. The second person asks the first person if he knows when Jim will be back. The first person says that he shouldn't be long, but he might have stopped off at the pub on the way back. The second person asks the first person to tell Jim that he has called and to try again later. The first person says goodbye to the second person and the second person says goodbye to the first person."}
{"answers": "#Person2# shows #Person1# the way to City Hall patiently.", "prediction_text": "The dialogue is about a person who asks for directions to City Hall. The person who gives the directions repeats the directions several times, but the person who asks for directions doesn't understand. The person who gives the directions repeats the directions again, and the person who asks for directions understands."}
{"answers": "#Person1# asks #Person2# the way to City Hall and #Person2# answers with patience.", "prediction_text": "The dialogue is about a person who asks for directions to City Hall. The person who gives the directions repeats the directions several times, but the person who asks for directions doesn't understand. The person who gives the directions repeats the directions again, and the person who asks for directions understands."}
{"answers": "#Person2# patiently shows #Person1# the way to City Hall.", "prediction_text": "The dialogue is about a person who asks for directions to City Hall. The person who gives the directions repeats the directions several times, but the person who asks for directions doesn't understand. The person who gives the directions repeats the directions again, and the person who asks for directions understands."}
{"answers": "#Person1# lost #Person1#'s passport. #Person2# suggests #Person1# fill in the lost property report and inform the embassy of the situation so they can issue a new passport in case it cannot be found.", "prediction_text": "A dialogue between a customer and a store employee."}
{"answers": "#Person1# asks #Person2# for help to find #Person1#'s passport. #Person2# checks the shoe department but finds nothing. Thus, #Person2# lets #Person1# fill in a report and contact the embassy.", "prediction_text": "A dialogue between a customer and a store employee."}
{"answers": "#Person1# lost the passport. #Person2# suggests #Person1# fill in the lost property report and contact the embassy.", "prediction_text": "A dialogue between a customer and a store employee."}
{"answers": "Leah informs Nathaniel that Ms. Collins will not be able to call him back until next week, so he needs to book an appointment with someone else from the International Student Office. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "The International Student Office is calling to book an appointment with someone else today."}
{"answers": "Leah informs Nathaniel that Ms. Collins won't be available for their appointment and explains the reason. Then Nathaniel decides to speak with someone else.", "prediction_text": "The International Student Office is calling to book an appointment with someone else today."}
{"answers": "The International Student Office calls Nathaniel to inform him that he has to book another appointment with someone else as Ms. Collins is not available. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "The International Student Office is calling to book an appointment with someone else today."}
{"answers": "#Person1# and #Person2# feel scared when heard Sarah insists on marrying Dick.", "prediction_text": "Dialogue: #Person1#: When she told me that she would marry no man but Dick, my heart was almost in my mouth."}
{"answers": "#Person1# and #Person2# are shocked about Sarah's love for Dick.", "prediction_text": "Dialogue: #Person1#: When she told me that she would marry no man but Dick, my heart was almost in my mouth."}
{"answers": "#Person1# and #Person2# are surprised to know Sarah wants to marry Dick.", "prediction_text": "When Sarah told #Person1# that she would marry no man but Dick, #Person1# was scared. #Person2# was scared too, but he did not tell #Person1# his feeling."}
{"answers": "#Person1# and #Person2# are at a nice party. #Person2# has a crush on Janice but is afraid of talking to her. #Person1# wishes #Person2# good luck.", "prediction_text": "A guy is at a party and is trying to get a girl to talk to him. He is nervous and doesn't know what to say. He is also afraid that he will say something stupid and scare her off. The girl is also nervous and doesn't know what to say. She is also afraid that he will say something stupid and scare her off. The guy and the girl are both afraid of rejection and don't know how to deal with it. The guy and the girl are both afraid of rejection and don't know how to deal with it. The guy and the girl are both afraid of rejection and don't know how to deal with it. The guy and the girl are both afraid of rejection and don't know how to deal with"}
{"answers": "#Person1# and #Person2# talk about girls at a party and #Person2# tells #Person1# that #Person2# likes Janice but he feels nervous talking to girls. #Person1# encourages #Person2#.", "prediction_text": "A guy is at a party and is trying to get a girl to talk to him. He is nervous and doesn't know what to say. He is also afraid that he will say something stupid and scare her off. The girl is also nervous and doesn't know what to say. She is also afraid that he will say something stupid and scare her off. The guy and the girl are both afraid of rejection and don't know how to deal with it. The guy and the girl are both afraid of rejection and don't know how to deal with it. The guy and the girl are both afraid of rejection and don't know how to deal with it. The guy and the girl are both afraid of rejection and don't know how to deal with"}
{"answers": "#Person1# and #Person2# are at an exciting party. #Person2# has a crush on Janice and #Person1# encourages him to have a try to talk to her.", "prediction_text": "A guy is at a party and is trying to get a girl to talk to him. He is nervous and doesn't know what to say. He is also afraid that he will say something stupid and scare her off. The girl is also nervous and doesn't know what to say. She is also afraid that he will say something stupid and scare her off. The guy and the girl are both afraid of rejection and don't know how to deal with it. The guy and the girl are both afraid of rejection and don't know how to deal with it. The guy and the girl are both afraid of rejection and don't know how to deal with it. The guy and the girl are both afraid of rejection and don't know how to deal with"}
{"answers": "Jack tells #Person1# that business communication is his favorite last year and #Person1# will check it.", "prediction_text": "The dialogue is about a conversation between two people. The first person asks the second person how his classes went. The second person says that they were not too bad. The first person asks if the second person liked his poli-sci class. The second person says that he liked it, but it is not his favorite. The first person asks what his favorite class was. The second person says that he took a business communication class last year and it was terrific. The first person says that he never took that class and that he will check it out."}
{"answers": "#Person1# and Jack talk about Jack's classes last year.", "prediction_text": "The dialogue is about a conversation between two people. The first person asks the second person how his classes went. The second person says that they were not too bad. The first person asks if the second person liked his poli-sci class. The second person says that he liked it, but it is not his favorite. The first person asks what his favorite class was. The second person says that he took a business communication class last year and it was terrific. The first person says that he never took that class and that he will check it out."}
{"answers": "#Person1# and Jack are talking about Jack's favorite classes.", "prediction_text": "The dialogue is about a conversation between two people. The first person asks the second person how his classes went. The second person says that they were not too bad. The first person asks if the second person liked his poli-sci class. The second person says that he liked it, but it is not his favorite. The first person asks what his favorite class was. The second person says that he took a business communication class last year and it was terrific. The first person says that he never took that class and that he will check it out."}
{"answers": "#Person1# and #Person2# are talking about the weather in different seasons in Beijing. #Person2# reminds #Person1# to put on more clothes in the evening and not to catch a cold.", "prediction_text": "Dialogue: #Person1#: Well, what a nice day!"}
{"answers": "#Person1# and #Person2# are talking about the weather in Beijing. #Person2# tells #Person1# it's cold in the evening around this time.", "prediction_text": "Dialogue: #Person1#: Well, what a nice day!"}
{"answers": "#Person1# and #Person2# are talking about the heavy rain last night and the weather in Beijing.", "prediction_text": "Dialogue: #Person1#: Well, what a nice day!"}
{"answers": "#Person1# invites #Person2# to watch a movie together that night. #Person2# wants to see nothing but a war movie.", "prediction_text": "Dialogue: #Person1#: Would you like to go to the movies tonight?"}
{"answers": "#Person1# and #Person2# talk about what kind of movies to watch and they decided to watch a war movie.", "prediction_text": "Dialogue: #Person1#: Would you like to go to the movies tonight?"}
{"answers": "#Person1# invites #Person2# to movie and #Person2# wants to see a war movie.", "prediction_text": "Dialogue: #Person1#: Would you like to go to the movies tonight?"}
{"answers": "Adam is showing #Person1# around his school. #Person1# envies Adam and hoped #Person1# could enter this school one day.", "prediction_text": "The dialogue is about a conversation between a foreigner and a Chinese student. The foreigner wants to know about the school. The Chinese student shows him around the school and tells him about the buildings and facilities. The foreigner is very interested in the school and hopes to enter it one day. The Chinese student believes that the foreigner can make his dream come true."}
{"answers": "Adam shows #Person1# around his school and introduces the library, the new classroom building, and the swimming pool.", "prediction_text": "The dialogue is about a conversation between a foreigner and a Chinese student. The foreigner wants to know about the school. The Chinese student shows him around the school and tells him about the buildings and facilities. The foreigner is very interested in the school and hopes to enter it one day. The Chinese student believes that the foreigner can make his dream come true."}
{"answers": "Adam shows #Person1# around the school and introduces buildings and facilities in the school to #Person1#.", "prediction_text": "The dialogue is about a conversation between a foreigner and a Chinese student. The foreigner wants to know about the school. The Chinese student shows him around the school and tells him about the buildings and facilities. The foreigner is very interested in the school and hopes to enter it one day. The Chinese student believes that the foreigner can make his dream come true."}
{"answers": "#Person1# and #Person2# feel happy because #Person1# is pregnant.", "prediction_text": "The dialogue is about a couple who are going to have a baby. The man is happy because he is going to be a father. The woman is happy because she is going to have a baby."}
{"answers": "#Person1# and #Person2# are happy about #Person1#'s pregnancy.", "prediction_text": "The dialogue is about a couple who are going to have a baby. The man is happy because he is going to be a father. The woman is happy because she is going to have a baby."}
{"answers": "#Person1# is pregnant. She and #Person2# feel happy.", "prediction_text": "The dialogue is about a couple who are going to have a baby. The man is happy because he is going to be a father. The woman is happy because she is going to have a baby."}
{"answers": "#Person1# and #Person2# think John's fallen for a girl.", "prediction_text": "John dates her seven times a week."}
{"answers": "#Person1# and #Person2# talk about John's dating life.", "prediction_text": "John dates her seven times a week."}
{"answers": "#Person1# and #Person2# are talking about a couple.", "prediction_text": "John dates her seven times a week."}
{"answers": "#Person1# is giving #Person2# some advice for upgrading #Person2#'s system, such as adding a painting program and a faster processor.", "prediction_text": "The dialogue is about a person who is considering upgrading his computer system. The dialogue is about the hardware and software that he might need to upgrade."}
{"answers": "#Person1# tells #Person2# how to upgrade #Person2#'s system for better software and hardware.", "prediction_text": "The dialogue is about a person who is considering upgrading his computer system. The dialogue is about the hardware and software that he might need to upgrade."}
{"answers": "#Person1# teaches #Person2# how to upgrade software and hardware in #Person2#'s system.", "prediction_text": "The dialogue is about a person who is considering upgrading his computer system. The dialogue is about the hardware and software that he might need to upgrade."}
{"answers": "#Person1# is driving #Person2# to an inn. They talk about their careers, ages, and where they was born.", "prediction_text": "This dialogue is a conversation between a young woman and a man who is older than she is. The woman is from Mexico and is visiting China. The man is from the United States and is curious about the woman's age. The woman is also curious about the man's age. The dialogue is a good example of how to use the present perfect tense in English."}
{"answers": "#Person1# drives #Person2# to an inn and they have a talk. #Person2# is 26 and had a business trip to China. #Person1# is 40 years old American.", "prediction_text": "This dialogue is a conversation between a young woman and a man who is older than she is. The woman is from Mexico and is visiting China. The man is from the United States and is curious about the woman's age. The woman is also curious about the man's age. The dialogue is a good example of how to use the present perfect tense in English."}
{"answers": "#Person1# drives #Person2# from the airport to an inn and they have a casual talk about themselves.", "prediction_text": "This dialogue is a conversation between a young woman and a man who is older than she is. The woman is from Mexico and is visiting China. The man is from the United States and is curious about the woman's age. The woman is also curious about the man's age. The dialogue is a good example of how to use the present perfect tense in English."}
{"answers": "#Person1# wants to lose weight. #Person2# suggests #Person1# take an exercise class to exercise more.", "prediction_text": "This dialogue is about a person who is overweight and wants to lose weight. The person is told by a friend that he should go on a diet. The person says that he has tried diets before but they have not worked. The friend suggests that he should exercise more. The person says that he should do that."}
{"answers": "#Person2# offers #Person1# some suggestions to lose weight.", "prediction_text": "This dialogue is about a person who is overweight and wants to lose weight. The person is told by a friend that he should go on a diet. The person says that he has tried diets before but they have not worked. The friend suggests that he should exercise more. The person says that he should do that."}
{"answers": "#Person2# gives #Person1# some suggestions on how to lose weight.", "prediction_text": "This dialogue is about a person who is overweight and wants to lose weight. The person is told by a friend that he should go on a diet. The person says that he has tried diets before but they have not worked. The friend suggests that he should exercise more. The person says that he should do that."}
{"answers": "James reserves a dining room for eight at a restaurant. #Person1# will ask the waitress to show him the way.", "prediction_text": "Dialogue: #Person1#: Good evening. Welcome to our restaurant."}
{"answers": "#Person1# confirms Jame's reservation at the restaurant and asks a waitress to show him the way.", "prediction_text": "Dialogue: #Person1#: Good evening. Welcome to our restaurant."}
{"answers": "James has reserved a dining room and #Person1#'ll asks the waitress to show him the way.", "prediction_text": "Dialogue: #Person1#: Good evening. Welcome to our restaurant."}
{"answers": "#Person1# is visiting a large plant and #Person2# introduces its basic information.", "prediction_text": "The dialogue is about a plant. The speaker asks questions about the plant and the answers are given."}
{"answers": "#Person2# introduces the area of the plant, its history, size, and function to #Person1#.", "prediction_text": "The dialogue is about a plant. The speaker asks questions about the plant and the answers are given."}
{"answers": "#Person1# and #Person2# visit a plant. #Person2# introduces its occupy, history, and employee number.", "prediction_text": "The dialogue is about a plant. The speaker asks questions about the plant and the answers are given."}
{"answers": "#Person1# is asking Rebecca about her working experience. She worked for York Herald as an intern and then as a junior local news reporter. After that, she began to work for London Weekly.", "prediction_text": "This dialogue is a conversation between a journalist and a former colleague. The journalist is asking the former colleague about her career path. The former colleague explains that she started her career as a junior reporter for a local newspaper, and then moved on to a national newspaper. The dialogue highlights the importance of experience and networking in the field of journalism."}
{"answers": "Rebecca tells #Person1# about her working experiences. Rebecca first worked for the York Herald after her graduation, and then for the London Weekly.", "prediction_text": "This dialogue is a conversation between a journalist and a former colleague. The journalist is asking the former colleague about her career path. The former colleague explains that she started her career as a junior reporter for a local newspaper, and then moved on to a national newspaper. The dialogue highlights the importance of experience and networking in the field of journalism."}
{"answers": "#Person1# interviews Rebecca and asks her about her previous working experiences.", "prediction_text": "This dialogue is a conversation between a journalist and a former colleague. The journalist is asking the former colleague about her career path. The former colleague explains that she started her career as a junior reporter for a local newspaper, and then moved on to a national newspaper. The dialogue highlights the importance of experience and networking in the field of journalism."}
{"answers": "#Person1# and #Person2# will do a group report, so they plan to buy something to make posters. They make a shopping list before they go and find items according to signs on shelves.", "prediction_text": "The dialogue is about a group of students who are going to buy some things for their group report. They are going to make some posters for the report. They are going to make a shopping list first. They are going to buy some markers, some color pencils, a pair of scissors, some highlighter, rulers, thumbtacks, whiteout and paper clips. They are going to go to the stationer to buy the things. They are going to go to shelf number 3 to get their markers, color pencils and highlighters and some brushes. They are going to go to the back to get their thumbtacks, whiteout and paper clips. They are going to get themselves a pair of nice glasses."}
{"answers": "#Person1# and #Person2# make a shopping list and #Person1# helps #Person2# to find goods to make a poster for their group report tomorrow.", "prediction_text": "The dialogue is about a group of students who are going to buy some things for their group report. They are going to make some posters for the report. They are going to make a shopping list first. They are going to buy some markers, some color pencils, a pair of scissors, some highlighter, rulers, thumbtacks, whiteout and paper clips. They are going to go to the stationer to buy the things. They are going to go to shelf number 3 to get their markers, color pencils and highlighters and some brushes. They are going to go to the back to get their thumbtacks, whiteout and paper clips. They are going to get themselves a pair of nice glasses."}
{"answers": "#Person1# and #Person2# are preparing for the group report tomorrow and making a shopping list of things used to make the posters.", "prediction_text": "The dialogue is about a group of students who are going to buy some things for their group report. They are going to make some posters for the report. They are going to make a shopping list first. They are going to buy some markers, some color pencils, a pair of scissors, some highlighter, rulers, thumbtacks, whiteout and paper clips. They are going to go to the stationer to buy the things. They are going to go to shelf number 3 to get their markers, color pencils and highlighters and some brushes. They are going to go to the back to get their thumbtacks, whiteout and paper clips. They are going to get themselves a pair of nice glasses."}
{"answers": "Mary is tired because of finding jobs in the market. #Person1# suggests Mary apply for jobs on the Internet and says she could place her information on the Job Wanted channel on a recruitment web. Mary will try at once.", "prediction_text": "Mary is tired because she goes to the personnel market every day. She has never tried to apply for a job on the Internet. She doesn't know how to apply for a job on the Internet. She doesn't know which employer is hiring. She doesn't know if it is safe to job hunt on the Internet. She will give it a try at once."}
{"answers": "#Person1# advises Mary to try applying for a job online and teaches her how to do it. Mary will try it.", "prediction_text": "Mary is tired because she goes to the personnel market every day. She has never tried to apply for a job on the Internet. She doesn't know how to apply for a job on the Internet. She doesn't know which employer is hiring. She doesn't know if it is safe to job hunt on the Internet. She will give it a try at once."}
{"answers": "Mary goes to the personnel market every day and is tired. #Person1# suggests she go job hunting online. Mary will try it.", "prediction_text": "Mary is tired because she goes to the personnel market every day. She has never tried to apply for a job on the Internet. She doesn't know how to apply for a job on the Internet. She doesn't know which employer is hiring. She doesn't know if it is safe to job hunt on the Internet. She will give it a try at once."}
{"answers": "#Person2# is making a shopping budget to save the rest of the money. #Person1# thinks it's a good idea.", "prediction_text": "The dialogue is about a person who is making a budget to save money. The person is trying to figure out how much money they can spend and how much they need to save. The dialogue is a good example of how to use the present simple tense in English."}
{"answers": "#Person2# tells #Person1# #Person2# plan to save money and #Person1# thinks it is good.", "prediction_text": "The dialogue is about a person who is making a budget to save money. The person is trying to figure out how much money they can spend and how much they need to save. The dialogue is a good example of how to use the present simple tense in English."}
{"answers": "#Person2# introduces the shopping budget which is used to save money to #Person1#. #Person1# thinks it's good.", "prediction_text": "The dialogue is about a person who is making a budget to save money. The person is trying to figure out how much money they can spend and how much they need to save. The person is also trying to figure out what they need to pay their bills with. The dialogue is a good example of how to make a budget and how to save money."}
{"answers": "Jane is going to the hospital to see Susan. Henry tells her to catch the number 13.", "prediction_text": "The dialogue is between two people, Henry and Jane. Henry asks Jane where she is going. Jane replies that she is going to the hospital to see Susan. Henry says that he saw Susan yesterday and she was a little better. Jane asks if she has to catch a number 7 bus to get to the hospital. Henry says that she doesn't need to catch a number 7 bus. A number 13 bus will also take her to the hospital. Jane asks if number 13 buses run more frequently. Henry says that they do. Jane says that she will get a number 13. Henry says that number 13 buses leave from the center of town and she will have to walk two miles to catch one."}
{"answers": "Jane will go to the hospital to visit Susan. Henry tells her she can take the number 13.", "prediction_text": "The dialogue is between two people, Henry and Jane. Henry asks Jane where she is going. Jane replies that she is going to the hospital to see Susan. Henry says that he saw Susan yesterday and she was a little better. Jane asks if she has to catch a number 7 bus to get to the hospital. Henry says that she doesn't need to catch a number 7 bus. A number 13 bus will also take her to the hospital. Jane asks if number 13 buses run more frequently. Henry says that they do. Jane says that she will get a number 13. Henry says that number 13 buses leave from the center of town and she will have to walk two miles to catch one."}
{"answers": "Jane is going to visit Suman in the hospital, Henry suggests she catch a number 13 to get there.", "prediction_text": "The dialogue is between two people, Henry and Jane. Henry asks Jane where she is going. Jane replies that she is going to the hospital to see Susan. Henry says that he saw Susan yesterday and she was a little better. Jane asks if she has to catch a number 7 bus to get to the hospital. Henry says that she doesn't need to catch a number 7 bus. A number 13 bus will also take her to the hospital. Jane asks if number 13 buses run more frequently. Henry says that they do. Jane says that she will get a number 13. Henry says that number 13 buses leave from the center of town and she will have to walk two miles to catch one."}
{"answers": "#Person1# wants to talk to #Person2# about the sales projections for next year. They decide to meet on Tuesday at 2:30 the next week.", "prediction_text": "The speaker is trying to set up a meeting with the other person. The other person is busy, but the speaker is able to find a time that works for both of them."}
{"answers": "#Person1# and #Person2# negotiate on a time to talk about the sales projections for next year. They decide to meet next Tuesday.", "prediction_text": "The speaker is trying to set up a meeting with the other person. The other person is busy, but the speaker is able to find a time that works for both of them."}
{"answers": "#Person1# and #Person2# are scheduling to talk about the sales projections for next year and they decide to meet next Tuesday.", "prediction_text": "The speaker is trying to set up a meeting with the other person. The other person is busy, but the speaker is able to find a time that works for both of them."}
{"answers": "#Person1# is going to New York for the first time. #Person2# suggests #Person1# use a personal tour guide service to make #Person1#'s trip plan.", "prediction_text": "A dialogue between two people. The first person is going to New York for the first time and doesn't have a tour guide. The second person suggests a service called 'A friend in New York' which is a personal tour guide service. The first person asks for more information about the service and the second person gives him the information."}
{"answers": "#Person2# recommends a personal tour guide service to #Person1#.", "prediction_text": "A dialogue between two people about a tour guide service."}
{"answers": "#Person2# introduces #Person1# a website for personal tour guide service.", "prediction_text": "A dialogue between two people about a tour guide service."}
{"answers": "#Person1# is interviewing #Person2#. They discuss department #Person2# wants to work in, salary, and fringe benefits.", "prediction_text": "Dialogue: #Person1#: What do you know about our company?"}
{"answers": "#Person1# asks #Person2# how well #Person2# knows about the company, #Person2#'s ideal department, and current salary. #Person2# also asks #Person1# about the company's benefits and insurance.", "prediction_text": "Dialogue: #Person1#: What do you know about our company?"}
{"answers": "#Person1# interviews #Person2# about the understanding of this company, personal preference, and salary expectation. #Person2# asks about the fringe benefits.", "prediction_text": "Dialogue: #Person1#: What do you know about our company?"}
{"answers": "#Person1# comes to sign an agreement but it isn't ready until evening. So #Person1# is going through the draft.", "prediction_text": "The dialogue is about a business deal. The first person is a businessman who is here to sign the agreement. The second person is the person who is responsible for the agreement. The first person asks the second person to speed up the agreement and let them have it today. The second person says that he will try his best. He then gives the first person a draft of the agreement. The first person says that it contains basically all they have agreed upon. The second person asks about the terms concerning packing. The first person says that he doesn't think so. The second person says that if the first person totally agrees, he will type the agreement this evening and have it duplicated for signatures. The first person says that is fine."}
{"answers": "#Person1# is checking the draft of an agreement and #Person2# will finish the agreement this evening.", "prediction_text": "The dialogue is about a business deal. The first person is a businessman who is here to sign the agreement. The second person is the person who is responsible for the agreement. The first person asks the second person to speed up the agreement and let them have it today. The second person says that he will try his best. He then gives the first person a draft of the agreement. The first person says that it contains basically all they have agreed upon. The second person asks about the terms concerning packing. The first person says that he doesn't think so. The second person says that if the first person totally agrees, he will type the agreement this evening and have it duplicated for signatures. The first person says that is fine."}
{"answers": "#Person2# gives #Person1# an agreement draft and #Person1# thinks it good. #Person2# will get the agreement ready this evening.", "prediction_text": "The dialogue is about a business deal. The first person is a businessman who is here to sign the agreement. The second person is the person who is responsible for the agreement. The first person asks the second person to speed up the agreement and let them have it today. The second person says that he will try his best. He then gives the first person a draft of the agreement. The first person says that it contains basically all they have agreed upon. The second person asks about the terms concerning packing. The first person says that he doesn't think so. The second person says that if the first person totally agrees, he will type the agreement this evening and have it duplicated for signatures. The first person says that is fine."}
{"answers": "#Person1# rent a car from ABC Rent-a-car Company this morning and met an accident. #Person2# will call an ambulance and police for #Person1#.", "prediction_text": "The dialogue is about a car accident. The man is injured and the woman calls the police and an ambulance."}
{"answers": "#Person1# tells ABC Rent-a-car Company that #Person1# had a car accident. #Person2# will call an ambulance and the police.", "prediction_text": "The dialogue is about a car accident. The man is injured and the woman calls the police and an ambulance."}
{"answers": "#Person1# calls ABC Rent-a-car Company to report #Person1#'s car accident. #Person2# will call an ambulance and the police.", "prediction_text": "The dialogue is about a car accident. The man is injured and the woman calls the police and an ambulance."}
{"answers": "#Person1# is lost on the way to the school clinic. #Person2# shows #Person1# the correct direction.", "prediction_text": "The dialogue is between a person who is lost and a person who is giving directions. The dialogue is between a person who is lost and a person who is giving directions."}
{"answers": "#Person1# lost #Person1#'s way to the school clinic, #Person2# guides #Person1#.", "prediction_text": "The dialogue is between a person who is lost and a person who is giving directions. The dialogue is between a person who is lost and a person who is giving directions."}
{"answers": "#Person1# is lost, #Person2# shows the direction to the school clinic.", "prediction_text": "The dialogue is between a person who is lost and a person who is giving directions. The dialogue is between a person who is lost and a person who is giving directions."}
{"answers": "#Person2# wants to change her room because the baggage elevator is too noisy. #Person1# is sorry and tells her #Person1# will manage it when there's a spare room.", "prediction_text": "Dialogue: #Person1#: Good morning. What can I do for you?"}
{"answers": "#Person2# wants to change a room because it's too noisy, but #Person1# tells #Person2# there are no rooms left so #Person2# has to wait till tomorrow.", "prediction_text": "Dialogue: #Person1#: Good morning. What can I do for you?"}
{"answers": "#Person2# cannot stand the noise near her room and #Person1# promises to change her room tomorrow as there is no room available today.", "prediction_text": "Dialogue: #Person1#: Good morning. What can I do for you?"}
{"answers": "#Person1# is driving #Person2# to the Beijing hotel. #Person2# will attend a banquet at six o'clock in the International Hotel.", "prediction_text": "The dialogue is about a businessman who is going to meet a foreigner. The businessman is very kind to the foreigner and he helps the foreigner to get into the car. The foreigner is very grateful to the businessman and he thanks him for his kindness. The businessman also tells the foreigner that he will take him to the hotel and he will also arrange some sightseeing for him. The foreigner is very happy and he thanks the businessman for his kindness."}
{"answers": "#Person1# warmly welcomes #Person2# and drives #Person2# to the Beijing Hotel. Mr. Wu has arranged a banquet for #Person2# in the evening.", "prediction_text": "The dialogue is about a businessman who is going to meet a foreigner. The businessman is very kind to the foreigner and he helps the foreigner to get into the car. The foreigner is very grateful to the businessman and he thanks him for his kindness. The businessman also tells the foreigner that he will take him to the hotel and he will also arrange some sightseeing for him. The foreigner is very happy and he thanks the businessman for his kindness."}
{"answers": "#Person1# has arranged everything for #Person2# after #Person2# arrives in Beijing and invites #Person2# to a banquet.", "prediction_text": "The dialogue is about a businessman who is going to meet a foreigner. The businessman is very kind to the foreigner and he helps the foreigner to get into the car. The foreigner is very grateful to the businessman and he thanks him for his kindness. The businessman also tells the foreigner that he will take him to the hotel and he will also arrange some sightseeing for him. The foreigner is very happy and he thanks the businessman for his kindness."}
{"answers": "#Person1# gets lost on the way to Liu Ichang. #Person2# tells #Person1# to take Bus No. 713 because it's far.", "prediction_text": "Dialogue: #Person1#: Excuse me, I'm lost. Can you show me where I am in this map?"}
{"answers": "#Person2# helps #Person1# to find out the location of Liu Ichang and tells #Person1# to take Bus No. 173.", "prediction_text": "Dialogue: #Person1#: Excuse me, I'm lost. Can you show me where I am in this map?"}
{"answers": "#Person1# gets lost and #Person2# shows the way to Liu Ichang to #Person1#.", "prediction_text": "Dialogue: #Person1#: Excuse me, I'm lost. Can you show me where I am in this map?"}
{"answers": "#Person2# finds that the computer has stopped running. #Person1# suggests #Person2# ring up the repairmen.", "prediction_text": "Dialogue: #Person1#: What's the matter with this computer?"}
{"answers": "#Person1# advises #Person2# to call the repairmen to fix #Person2#'s computer.", "prediction_text": "Dialogue: #Person1#: What's the matter with this computer?"}
{"answers": "#Person2#'s computer breaks down and #Person1# suggests ringing up the repairman.", "prediction_text": "Dialogue: #Person1#: What's the matter with this computer?"}
{"answers": "#Person2# wants to buy a gift for his mother's birthday. After knowing his mother's preference, #Person1# recommends a gold watch.", "prediction_text": "The dialogue is about a man who is looking for a gift for his mother. He asks the salesperson for help, but the salesperson is not very helpful. The man finally finds a gift that he thinks his mother will like, but he is not sure if it is the right choice."}
{"answers": "#Person1# helps #Person2# pick a gift for #Person2#'s mother, and #Person2# decides to buy a watch.", "prediction_text": "The dialogue is about a man who is looking for a gift for his mother. He asks the salesperson for help, but the salesperson is not very helpful. The man finally finds a gift that he thinks his mother will like, but he is not sure if it is the right choice."}
{"answers": "#Person2# is looking for a gift for his mother in a shop and #Person1# gives him some helpful suggestions.", "prediction_text": "The dialogue is about a man who is looking for a gift for his mother. He asks the salesperson for help, but the salesperson is not very helpful. The man finally finds a gift that he thinks his mother will like, but the salesperson is not sure if it is the right choice."}
{"answers": "Ms. Ross and Mr. Fisher are having a coffee chat face to face. They talk about their career, child, life, and then get down to business. Ms. Ross will provide service for a presentation on Saturday 24 October in the morning held by Mr. Fisher. They are discussing the preparations as time, food, furniture, guest numbers. A problem is Mr. Fisher sent out 100 invitations but the room's capacity is only about 50.", "prediction_text": "Dialogue: #Person1#: Ah, good morning, Ms. Ross, do come in."}
{"answers": "Ms. Ross helps Mr. Fisher make arrangements for a presentation. Mr. Fisher decides to serve a pre-lunch snack buffet and designs the room setting. Mr. Fisher isn't sure about how many people to come and Ms. Ross is worried that there might be too many people because the room is small.", "prediction_text": "Dialogue: #Person1#: Ah, good morning, Ms. Ross, do come in."}
{"answers": "Ms. Ross and Mr. Fisher start the conversation with greetings. Then they discuss the preparation for a presentation. Ms. Ross confirms the details of the presentation, including schedule, food service. Ms. Ross is concerned that Mr. Fisher's sent too many invitations than the room capacity.", "prediction_text": "Dialogue: #Person1#: Ah, good morning, Ms. Ross, do come in."}
{"answers": "#Person2# tells #Person1#, in Russia, things happen very fast except for bank business, while in Canada people are calmer.", "prediction_text": "Dialogue: #Person1#: What are the main differences between this country and your country?"}
{"answers": "#Person1# and #Person2# are talking about the differences between Russia and Canada. #Person2# tells #Person1# that almost everything in Russia happens fast while it's the opposite in Canada.", "prediction_text": "Dialogue: #Person1#: What are the main differences between this country and your country?"}
{"answers": "#Person1# and #Person2# are sharing their opinions towards how different is Russia from Canada in lifestyle, especially the speed in life.", "prediction_text": "Dialogue: #Person1#: What are the main differences between this country and your country?"}
{"answers": "#Person2# thought to travel to the Caribbean Sea. #Person1# went there last year and thinks the weather can sometimes be really bad.", "prediction_text": "The speaker is going on a vacation to the Caribbean Sea. The other person is thinking about going there too. The speaker says that the weather can sometimes be bad. The other person says that he has been reading weather reports on the internet. The speaker says that the weather can be bad. The other person says that he will just have to hope for the best."}
{"answers": "#Person2# wanted to travel to the Caribbean Sea and but is worried about the storms.", "prediction_text": "The speaker is going on a vacation to the Caribbean Sea. The other person is thinking about going there too. The speaker says that the weather can sometimes be bad. The other person says that he has been reading weather reports on the internet. The speaker says that the weather can be bad. The other person says that he will just have to hope for the best."}
{"answers": "#Person2# hasn't decided to go to the Caribbean Sea on vacation because #Person2# is concerned about the storms there.", "prediction_text": "The speaker is going on a vacation to the Caribbean Sea. The other person is thinking about going there too. The speaker says that the weather can sometimes be bad. The other person says that he has been reading weather reports on the internet. The speaker says that the weather can be bad. The other person says that he will just have to hope for the best."}
{"answers": "#Person1# and #Person1#'s mother are preparing the fruits they are going to take to the picnic.", "prediction_text": "The dialogue is about a picnic. The first person wants to take oranges and bananas. The second person suggests taking grapes and apples instead. The first person agrees to take grapes."}
{"answers": "#Person1# and #Person2# prepare the fruits for the picnic.", "prediction_text": "The dialogue is about a picnic. The first person wants to take oranges and bananas. The second person suggests taking grapes and apples instead. The first person agrees to take grapes."}
{"answers": "#Person1# and #Person2# are preparing fruits for the picnic.", "prediction_text": "The dialogue is about a picnic. The first person wants to take oranges and bananas. The second person suggests taking grapes and apples instead. The first person agrees to take grapes."}
{"answers": "#Person1# wants to rent an economy car from #Person2# for $19 a day or $129 a week.", "prediction_text": "The dialogue is about renting a car. The first person asks how much it costs to rent an economy car. The second person tells him that it is $19 a day or$129 a week, unlimited mileage. The first person asks if he could have one for tomorrow morning. The second person asks for his driver's license. The first person gives it to him. The second person tells him to complete the form."}
{"answers": "#Person1# rents a car from #Person2#", "prediction_text": "The dialogue is about renting a car. The first person asks how much it costs to rent an economy car. The second person tells him that it is $19 a day or$129 a week, unlimited mileage. The first person asks if he could have one for tomorrow morning. The second person asks for his driver's license. The first person gives it to him. The second person tells him to complete the form."}
{"answers": "#Person1# rents an economy car in #Person2#'s shop.", "prediction_text": "The dialogue is about renting a car. The first person asks how much it costs to rent an economy car. The second person tells him that it is $19 a day or$129 a week, unlimited mileage. The first person asks if he could have one for tomorrow morning. The second person asks for his driver's license. The first person gives it to him. The second person tells him to complete the form."}
{"answers": "#Person1# hates those who keep smiling at #Person1# and #Person2# is fed up with those who keep a straight face in front of #Person2#.", "prediction_text": "The dialogue is about the importance of smiling. The speaker says that smiling is a good way to make people feel comfortable. However, the speaker also says that smiling is not always a good thing. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason"}
{"answers": "#Person2# is fed up with people with a straight face, and #Person1# hates people keeping smiling to #Person1#.", "prediction_text": "The dialogue is about the importance of smiling. The speaker says that smiling is a good way to make people feel comfortable. However, the speaker also says that smiling is not always a good thing. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason"}
{"answers": "#Person1# and #Person2# hold different opinions towards people with different facial expressions.", "prediction_text": "The dialogue is about the importance of smiling. The speaker says that smiling is a good way to make people feel comfortable. However, the speaker also says that smiling is not always a good thing. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason. The speaker says that smiling can be annoying if it is done for no reason"}
{"answers": "#Person1# and #Person2# are talking about the good performance of their business for last year. #Person2# thinks the success is partly because of Wallace's contribution, and partly because of their new marketing strategy.", "prediction_text": "The dialogue is about a company's sales review. The company has been making money again after a two-year slump. The company's performance has improved since Wallace became president. The company's success is partly because of Wallace's contribution, and partly because of their new marketing strategy."}
{"answers": "#Person1# and #Person2# talk about the success of their business and attribute the success to Wallace's contribution and the new marketing strategy. They hope good luck can last.", "prediction_text": "The dialogue is about a company's sales review. The company has been making money again after a two-year slump. The company's performance has improved since Wallace became president. The company's success is partly because of Wallace's contribution, and partly because of their new marketing strategy."}
{"answers": "#Person1# and #Person2# are pleasant to see their business performance has improved last year. They think the credits should be given to Wallace and the new marketing strategy.", "prediction_text": "The dialogue is about a company's sales review. The company has been making money again after a two-year slump. The company's performance has improved since Wallace became president. The company's success is partly because of Wallace's contribution, and partly because of their new marketing strategy."}
{"answers": "#Person2# left #Person2#'s backpack and wallet in a taxi and failed to contact the driver because #Person2# didn't have the number. #Person1# lends 50 dollars to #Person1# and will drive #Person2# home.", "prediction_text": "A man lost his wallet and asks for money from a stranger. The stranger offers to help him."}
{"answers": "#Person2# borrows some money from #Person1# because #Person2#'s backpack and wallet were lost and cannot take it back. #Person1# will drive #Person2# home.", "prediction_text": "A man lost his wallet and asks for money from a stranger. The stranger offers to help him."}
{"answers": "#Person2# lost #Person2#'s backpack, wallet, and money in a taxi. #Person1# lends #Person2# some money and will take #Person2# home.", "prediction_text": "A man lost his wallet and asks for money from a stranger. The stranger offers to help him."}
{"answers": "Steven and Lin just had a great meal. Then they talk about the different tipping cultures between America and China.", "prediction_text": "Dialogue: #Person1#: Thank you. Steven. That was the most magnificent meal I've had abroad. You'll have to let me reciprocate the next time you're in Beijing. #Person2#: Don't worry about it, Lin. That's no big deal. You know, Americans appreciate China's rich culinary culture. Just excuse me for a second while I check the number here and figure out how much to give the waiter. Hmm, by the way, what do you do about tipping in China? #Person1#: We don't. #Person2#: No tipping? Now that's what I call a civilized system. #Person1#: At hotels and some restaurants they add a service charge to the bill, but other"}
{"answers": "Steven buys Lin a magnificent dinner in America and they then talk about the tipping culture in China and America.", "prediction_text": "Dialogue: #Person1#: Thank you. Steven. That was the most magnificent meal I've had abroad. You'll have to let me reciprocate the next time you're in Beijing. #Person2#: Don't worry about it, Lin. That's no big deal. You know, Americans appreciate China's rich culinary culture. Just excuse me for a second while I check the number here and figure out how much to give the waiter. Hmm, by the way, what do you do about tipping in China? #Person1#: We don't. #Person2#: No tipping? Now that's what I call a civilized system. #Person1#: At hotels and some restaurants they add a service charge to the bill, but other"}
{"answers": "Steven treats Lin to a nice meal. Then they talk about the tipping cultures in their countries.", "prediction_text": "Dialogue: #Person1#: Thank you. Steven. That was the most magnificent meal I've had abroad. You'll have to let me reciprocate the next time you're in Beijing. #Person2#: Don't worry about it, Lin. That's no big deal. You know, Americans appreciate China's rich culinary culture. Just excuse me for a second while I check the number here and figure out how much to give the waiter. Hmm, by the way, what do you do about tipping in China? #Person1#: We don't. #Person2#: No tipping? Now that's what I call a civilized system. #Person1#: At hotels and some restaurants they add a service charge to the bill, but other"}
{"answers": "Bill is happy because he made a move to know his roommate today.", "prediction_text": "A dialogue between two people. The first person asks the second person how he can be so happy today. The second person says that he has made a move today. The first person asks what the matter is. The second person says that he has met his roommate. The first person says that he has not heard of him. The second person says that he is thin and tall, with brown hair, a holt nose, green eyes and wearing glasses. The first person says that he has seen him a couple of times, but does not know his name."}
{"answers": "#Person1# and Bill talk about Bill's roommate Brain Locker.", "prediction_text": "A dialogue between two people. The first person asks the second person how he can be so happy today. The second person says that he has made a move today. The first person asks what the matter is. The second person says that he has met his roommate. The first person says that he has not heard of him. The second person says that he is thin and tall, with brown hair, a holt nose, green eyes and wearing glasses. The first person says that he has seen him a couple of times, but does not know his name."}
{"answers": "Bill tells #Person1# that he has made a move to read of his roommate.", "prediction_text": "A dialogue between two people. The first person asks the second person how he can be so happy today. The second person says that he has made a move today. The first person asks what the matter is. The second person says that he has met his roommate. The first person says that he has not heard of him. The second person says that he is thin and tall, with brown hair, a holt nose, green eyes and wearing glasses. The first person says that he has seen him a couple of times, but does not know his name."}
{"answers": "#Person2# checks Tom Wilson's information and Tom pays his hotel and meal bill.", "prediction_text": "The dialogue is about a hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist who asks the guest for his name and room number. Then the guest is asked if he has used any hotel services this morning. The guest says no. The receptionist then tells the guest that he has stayed for four nights at 90 US dollars each and that he has had meals at the hotel. The guest asks if he can pay by credit card. The receptionist says of course and asks the guest to sign his name."}
{"answers": "Tom Wilson pays for his bill for hotel and food by credit card.", "prediction_text": "The dialogue is about a hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist. The dialogue is about the hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist. The dialogue is about the hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist. The dialogue is about the hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist. The dialogue is about the hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist. The dialogue is about the"}
{"answers": "Tom Wilson pays the bill for his room and meal cost by credit card.", "prediction_text": "The dialogue is about a hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist. The dialogue is about the hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist. The dialogue is about the hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist. The dialogue is about the hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist. The dialogue is about the hotel bill. The first person is a guest who wants to pay his bill. The second person is the receptionist. The dialogue is about the"}
{"answers": "Susan calls Carol to ask about the party time. But Carol is taking a shower so #Person1# answers for her.", "prediction_text": "Dialogue: #Person1#: Carol telephone."}
{"answers": "Carol is taking a shower when Carol calls her, so #Person1# answers the telephone and tells her the party time.", "prediction_text": "Dialogue: #Person1#: Carol telephone."}
{"answers": "Susan calls to ask Carol about the party time. #Person1# answers the phone and tells her.", "prediction_text": "Dialogue: #Person1#: Carol telephone."}
{"answers": "#Person1# thinks that she knows #Person2# somewhere, but #Person2# denies it.", "prediction_text": "A woman is trying to remember where she has met a man before."}
{"answers": "#Person1# thinks she has met #Person2# somewhere, but #Person2# thinks it's a mistake.", "prediction_text": "A woman is trying to remember where she has met a man before."}
{"answers": "#Person1# keeps asking where #Person2#'s from because she thinks she knows #Person2# but #Person2# denies it.", "prediction_text": "A woman is trying to remember where she has met a man before."}
{"answers": "#Person1# is crazy for Trump and voted for him. #Person2# doesn't agree with #Person1# on Trump and will vote for Biden.", "prediction_text": "The dialogue is about the 2020 US Presidential Election. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but"}
{"answers": "#Person1# is a crazy fan of Trump and wants him to be re-elected. #Person2# will vote for Biden.", "prediction_text": "The dialogue is about the 2020 US Presidential Election. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but"}
{"answers": "#Person1# is crazy for Trump and voted for him but #Person2# will vote for Biden.", "prediction_text": "The dialogue is about the 2020 US Presidential Election. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but the second person is sure that he will be re-elected. The first person is not sure about Trump's re-election, but"}
{"answers": "#Person1# doesn't know how to use the ATM. #Person2# teaches #Person1# step by step.", "prediction_text": "This dialogue is a conversation between two people. The first person, #Person1#, needs to use the ATM, but they don't know how. The second person, #Person2#, helps them figure it out. The first person asks what they have to do, and the second person explains the steps. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to"}
{"answers": "#Person1# doesn't know how to use an ATM. #Person2# teaches #Person1#.", "prediction_text": "This dialogue is a conversation between two people. The first person, #Person1#, needs to use the ATM, but they don't know how. The second person, #Person2#, helps them figure it out. The first person asks what they have to do, and the second person explains the steps. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to do after that, and the second person explains the final step. The first person thanks the second person for their help."}
{"answers": "#Person2# is teaching #Person1# how to use an ATM step by step because #Person1# doesn't know how to use.", "prediction_text": "This dialogue is a conversation between two people. The first person, #Person1#, needs to use the ATM, but they don't know how. The second person, #Person2#, helps them figure it out. The first person asks what they have to do, and the second person explains the steps. The first person asks what they have to do next, and the second person explains the next step. The first person asks what they have to do after that, and the second person explains the final step. The first person thanks the second person for their help."}
{"answers": "Susan wants to find the memo. John has no idea where it is.", "prediction_text": "This dialogue is about a person who is looking for a memo. The other person does not know where it is."}
{"answers": "Susan Miller is asking John if he knows where the memo is. John has no idea.", "prediction_text": "This dialogue is about a person who is looking for a memo. The other person does not know where it is."}
{"answers": "Susan asks John where a memo is but John doesn't know.", "prediction_text": "This dialogue is about a person who is looking for a memo. The other person does not know where it is."}
{"answers": "#Person1# invites Lily to picnic this weekend. Lily accepts.", "prediction_text": "Lily and her friend are going to have a picnic."}
{"answers": "#Person1# is inviting Lily to take part in their picnic this weekend, and she accepts.", "prediction_text": "Lily and her friend are going to have a picnic."}
{"answers": "#Person1# invites Lily to take part in their weekend picnic and Lily accepts.", "prediction_text": "Lily and her friend are going to have a picnic."}
{"answers": "#Person1# asks #Person2# about the table manners in China. #Person2# says there are many hazy rules that are different from Western. And #Person2# tells #Person1# stabbing chopsticks into a bowl resembles sacrifices for the death and is very inauspicious.", "prediction_text": "The dialogue is about table manners in China."}
{"answers": "#Person1# and #Person2# are discussing the differences between China and Western feasts. There are so many rules on the Chinese table, and they both feel hazy about its etiquette.", "prediction_text": "The dialogue is about table manners in China."}
{"answers": "#Person1# and #Person2# talk about the difference in table etiquette in China. They both feel hazy about Chinese table etiquette and wrong use of chopsticks can lead to people's enrage.", "prediction_text": "The dialogue is about table manners in China."}
{"answers": "Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies.", "prediction_text": "The dialogue is about two people who are talking about their favorite movies. The first person is Mary, and the second person is Frank. They are talking about their favorite movies, and they are both very interested in movies. They are also very different in their tastes. Mary likes art films, and Frank likes thrillers. They are also very different in their hobbies. Mary likes to watch movies, and Frank likes to watch movies. They are also very different in their hobbies. Mary likes to watch movies, and Frank likes to watch movies. They are also very different in their hobbies. Mary likes to watch movies, and Frank likes to watch movies. They are also very different in their hobbies. Mary likes to watch movies, and Frank likes to watch"}
{"answers": "Frank and Mary both like watching movies in their spare time. Mary usually rents movies at Movie Salon, and Frank is interested in signing up for its membership.", "prediction_text": "The dialogue is about two people who are talking about their favorite movies. The first person is Mary, and the second person is Frank. They are talking about their favorite movies, and they are both very interested in movies. They are also very different in their tastes. Mary likes art films, and Frank likes thrillers. They are also very different in their hobbies. Mary likes to watch movies, and Frank likes to watch movies. They are also very different in their hobbies. Mary likes to watch movies, and Frank likes to watch movies. They are also very different in their hobbies. Mary likes to watch movies, and Frank likes to watch movies. They are also very different in their hobbies. Mary likes to watch movies, and Frank likes to watch"}
{"answers": "Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies", "prediction_text": "The dialogue is about two people who are talking about their favorite movies. The first person is Mary, and the second person is Frank. They are talking about their favorite movies, and they are both very interested in movies. They are also very different in their tastes. Mary likes art films, and Frank likes thrillers. They are also very different in their hobbies. Mary likes to watch movies, and Frank likes to watch movies. They are also very different in their hobbies. Mary likes to watch movies, and Frank likes to watch movies. They are also very different in their hobbies. Mary likes to watch movies, and Frank likes to watch movies. They are also very different in their hobbies. Mary likes to watch movies, and Frank likes to watch"}
{"answers": "#Person2# wanted to join a small political party and thinks the smaller group can influence the larger one. #Person1# and #Person2# agree that most people don't fully understand politics.", "prediction_text": "The dialogue is about the green party. The first person is a member of the green party and the second person is not. The first person thinks that the green party has no chance of winning an election. The second person thinks that smaller political parties can influence large political parties."}
{"answers": "#Person2# believes smaller political and pressure groups can influence large parties. #Person1# and #Person2# both agree that most people often don't understand politics fully.", "prediction_text": "The dialogue is about the green party. The first person is a member of the green party and the second person is not. The first person thinks that the green party has no chance of winning an election. The second person thinks that smaller political parties can influence large political parties."}
{"answers": "#Person2# thought about joining a small party and thinks smaller political and pressure groups can influence larger ones. #Person1# and #Person2# agree most people don't understand political issues fully.", "prediction_text": "The dialogue is about the green party. The first person is a member of the party and the second person is thinking about joining. The first person says that the green party has no chance of winning an election. The second person says that smaller political parties can influence large political parties. The first person says that most people are not very politically aware. The second person says that the media often reports on political events."}
{"answers": "#Person1# apologizes for mistakes in goods. #Person1# will be responsible for Mr. Wilson's loss, and take measures to avoid such mistakes.", "prediction_text": "The dialogue is about a business deal between a Chinese company and a foreign company. The Chinese company has made a mistake in the goods they have exported to the foreign company. The foreign company is very angry about the mistake and wants to hold the Chinese company responsible for the loss they have sustained. The Chinese company admits that it is their fault and promises to exchange all the goods that fall short of the sample. The foreign company is satisfied with the Chinese company's apology and promises to improve the package of the goods in the future."}
{"answers": "#Person1# feels sorry for Mr. Wilson's loss caused by #Person1# and assures that such mistakes will not happen again.", "prediction_text": "The dialogue is about a business deal between a Chinese company and a foreign company. The Chinese company has made a mistake in the goods they have exported to the foreign company. The foreign company is very angry about the mistake and wants to hold the Chinese company responsible for the loss they have sustained. The Chinese company admits that it is their fault and promises to exchange all the goods that fall short of the sample. The foreign company is satisfied with the Chinese company's apology and promises to improve the package of the goods in the future."}
{"answers": "#Person1# apologizes for the loss caused by them to Mr. Wilson and assures that it will never happen again.", "prediction_text": "The dialogue is about a business deal between a Chinese company and a foreign company. The Chinese company has made a mistake in the goods they have exported to the foreign company. The foreign company is very angry about the mistake and wants to hold the Chinese company responsible for the loss they have sustained. The Chinese company admits that it is their fault and promises to exchange all the goods that fall short of the sample. The foreign company is satisfied with the Chinese company's apology and promises to improve the package of the goods in the future."}
{"answers": "#Person1# asks #Person2# who saw the robbery some questions. #Person2# agrees to come to the station for more questioning.", "prediction_text": "The speaker is asking the other person if they saw a robbery. The other person says that they did. The speaker asks if they saw everything, and the other person says that they were in the bank at the time. The speaker asks what they saw, and the other person says that they saw the guy come in with a gun. The speaker asks if they got a good look at his face, and the other person says that he was wearing a mask. The speaker asks if anyone else was with him, and the other person says that he came in alone. The speaker asks if the other person can come to the station for more questioning, and the other person says that that will be fine."}
{"answers": "#Person1# asks #Person2# who saw a robbery some questions and #Person2# is willing to go to the station for more questioning.", "prediction_text": "The speaker is asking the other person if they saw a robbery. The other person says that they did. The speaker asks if they saw everything, and the other person says that they were in the bank at the time. The speaker asks what they saw, and the other person says that they saw the guy come in with a gun. The speaker asks if they got a good look at his face, and the other person says that he was wearing a mask. The speaker asks if anyone else was with him, and the other person says that he came in alone. The speaker asks if the other person can come to the station for more questioning, and the other person says that that will be fine."}
{"answers": "#Person2# tells #Person1# #Person2# witnessed the robbery and agrees to take more questions in the station.", "prediction_text": "The speaker is asking the other person if they saw a robbery. The other person says that they did. The speaker asks if they saw everything, and the other person says that they were in the bank at the time. The speaker asks what they saw, and the other person says that they saw the guy come in with a gun. The speaker asks if they got a good look at his face, and the other person says that he was wearing a mask. The speaker asks if anyone else was with him, and the other person says that he came in alone. The speaker asks if the other person can come to the station for more questioning, and the other person says that that will be fine."}
{"answers": "#Person1# and #Person2#'s parents are out on a date and will go out for dinner regularly.", "prediction_text": "A father and son have a conversation about the father's date with his wife."}
{"answers": "The parents of #Person1# and #Person2# are out on a date.", "prediction_text": "A father and son have a conversation about the father's date with his wife."}
{"answers": "#Person1# and #Person2#'s parent are out on a date to revive their old tradition.", "prediction_text": "A father and son have a conversation about the father's date with his wife."}
{"answers": "#Person1# has decided to go on a diet in the new year. Carol reasonably doubts it.", "prediction_text": "This is a dialogue between two people. The first person is going to lose weight and the second person is going to be supportive."}
{"answers": "#Person1#'s decided to go on a diet for New Year's resolution. Carol doesn't believe #Person1# will stick to it.", "prediction_text": "This is a dialogue between two people. The first person is going to lose weight and the second person is going to be supportive."}
{"answers": "#Person1# has decided to go on a diet in the new year. Carol doesn't believe #Person1# will stick to it", "prediction_text": "This is a dialogue between two friends. The first friend has decided to go on a diet and the second friend is not very supportive."}
{"answers": "Karen Huang tried to register for Comp Lit 287 but failed because it's full. So Karen comes to the department office. #Person1# finds Karen is a Comp Lit major and the university has saved extra places for them. #Person1# gives Karen a special code and tells Karan how to use it to get into the class.", "prediction_text": "The dialogue is between a student and a department secretary. The student is trying to get into a class that is full. The secretary gives the student a special code that will allow her to register for the class."}
{"answers": "Karen Huang couldn't register for Comp Lit 287 so Karen comes to talk to #Person1# for a solution. #Person1# says it is full and will put Karen on the waiting list. But after #Person1# knows Karen majors in comparative literature, #Person1# gives her a special code to register for the class, as they've saved extra places for them.", "prediction_text": "The dialogue is between a student and a department secretary. The student is trying to get into a class that is full. The secretary gives the student a special code that will allow her to register for the class."}
{"answers": "Karen Huang wants to register for a class. #Person1# says it's full and will put Karen on the waiting list. But then #Person1# gives Karen a special code to register the class after #Person1# knows Karen is a Comparative Literature major.", "prediction_text": "The dialogue is between a student and a department secretary. The student is trying to get into a class that is full. The secretary gives the student a special code that will allow her to register for the class."}
{"answers": "#Person2# voluntarily shares an umbrella with #Person1# who doesn't bring the umbrella when it's rainy.", "prediction_text": "The dialogue is about a man and a woman who are walking in the rain. The man is in a hurry and he forgets his umbrella. The woman offers to share her umbrella with him. They walk together to the hotel."}
{"answers": "#Person1# forgot to take an umbrella while it's raining. #Person2# shares an umbrella with #Person1#.", "prediction_text": "The dialogue is about a man and a woman who are walking in the rain. The man is in a hurry and he forgets his umbrella. The woman offers to share her umbrella with him. They walk together to the hotel."}
{"answers": "#Person2# shares an umbrella with #Person1# when it's rainy.", "prediction_text": "The dialogue is about a man and a woman who are walking in the rain. The man is in a hurry and he forgets his umbrella. The woman offers to share her umbrella with him. They walk together to the hotel."}
{"answers": "Jack gives Daisy a ride in his new car. Daisy praises it.", "prediction_text": "Jack is a car salesman. He is driving his new car. He is talking to Daisy, a customer. Daisy is impressed with the car."}
{"answers": "Jack bought a new car and invites Daisy to hop in and take a ride. Daisy thinks it's a good feeling.", "prediction_text": "Jack is a car salesman. He is driving his new car. He is talking to Daisy, a customer. Daisy is impressed with the car."}
{"answers": "Jack takes Daisy for a ride to experience his new car. Daisy thinks he makes a perfect choice.", "prediction_text": "Jack is a car salesman. He is driving his new car. He is talking to Daisy, a customer. Daisy is impressed with the car."}
{"answers": "#Person1# asks #Person2# the exact time when the fire broke out. #Person2# says it was about 10 o'clock.", "prediction_text": "The speaker is asking the other person to tell him the exact time when the fire broke out. The other person says that he doesn't remember the exact time, but he remembers that it was about 10 o'clock."}
{"answers": "#Person1# asks the exact time when the big fire broke out. #Person2# remembers that it was about 10 o'clock.", "prediction_text": "The speaker is asking the other person to tell him the exact time when the fire broke out. The other person says that he doesn't remember the exact time, but he remembers that it was about 10 o'clock."}
{"answers": "#Person1# asks #Person2# the exact time when the fire broke out. #Person2# says it was about 10 o'clock.", "prediction_text": "The speaker is asking the other person to tell him the exact time when the fire broke out. The other person says that he doesn't remember the exact time, but he remembers that it was about 10 o'clock."}
{"answers": "#Person2# doesn't have enough cash to pay 905 yuan and asks to cut the service charge. #Person1# refuses. So #Person2# pays by her husband's credit card because it can be accepted.", "prediction_text": "The dialogue is about a customer who wants to pay 900 yuan for a pair of shoes. The shop assistant says that the price is 905 yuan, and the customer asks why there is a five yuan service charge. The shop assistant says that it is a store's rule. The customer says that her husband has a credit card, and the shop assistant says that they accept credit cards. The customer says that she will pay with the credit card, and the shop assistant gives her a receipt. The customer asks if the shoes can be wrapped, and the shop assistant says that it will be great to have her again. The customer says that she will come again."}
{"answers": "#Person1# requires 905 yuan in total, but #Person2# only has 900 in cash and asks for a release. #Person1# refuses, so #Person2# pays by her husband's credit card because it can be accepted.", "prediction_text": "The dialogue is about a customer who wants to pay 900 yuan for a pair of shoes. The shop assistant says that the price is 905 yuan, and the customer asks why there is a five yuan service charge. The shop assistant says that it is a store's rule. The customer says that her husband has a credit card, and the shop assistant says that they accept credit cards. The customer says that she will pay with the credit card, and the shop assistant gives her a receipt. The customer asks if the shoes can be wrapped, and the shop assistant says that it will be great to have her again. The customer says that she will come again."}
{"answers": "#Person2# is asked to pay 905 yuan but she just has 900 in cash and asks for a release from #Person1#. #Person1# refuses. So #Person2# pays by her husband's credit card because it can be accepted.", "prediction_text": "The dialogue is about a customer who wants to pay 900 yuan for a pair of shoes. The shop assistant says that the price is 905 yuan, and the customer asks why there is a five yuan service charge. The shop assistant says that it is a store's rule. The customer says that her husband has a credit card, and the shop assistant says that they accept credit cards. The customer says that she will pay with the credit card, and the shop assistant gives her a receipt. The customer asks if the shoes can be wrapped, and the shop assistant says that it will be great to have her again. The customer says that she will come again."}
{"answers": "#Person1# describes the contents of the regular car wash package. #Person2# will take that.", "prediction_text": "The dialogue is about a car wash. The customer wants to get his car washed. The customer asks about the regular car wash package. The customer asks about the interior. The customer asks about the windows. The customer asks about the trash. The customer asks about the vacuum cleaner. The customer asks about the shampoo. The customer asks about the shine. The customer asks about the exterior. The customer asks about the body. The customer asks about the dirt. The customer asks about the vacuum cleaner. The customer asks about the trash. The customer asks about the vacuum cleaner. The customer asks about the vacuum cleaner. The customer asks about the vacuum cleaner. The customer asks about the vacuum cleaner. The customer asks about the vacuum cleaner. The customer asks about"}
{"answers": "#Person1# introduces the content of regular car wash package and #Person2# accepts.", "prediction_text": "The dialogue is about a car wash. The customer wants to get his car washed. The customer asks about the regular car wash package. The customer asks about the interior. The customer asks about the windows. The customer asks about the trash. The customer asks about the vacuum cleaner. The customer asks about the shampoo. The customer asks about the shine. The customer asks about the exterior. The customer asks about the body. The customer asks about the dirt. The customer asks about the vacuum cleaner. The customer asks about the trash. The customer asks about the vacuum cleaner. The customer asks about the vacuum cleaner. The customer asks about the vacuum cleaner. The customer asks about the vacuum cleaner. The customer asks about the vacuum cleaner. The customer asks about"}
{"answers": "#Person1# introduces the services included in regular car wash package and #Person2# will take it.", "prediction_text": "The dialogue is about a car wash. The customer wants to get his car washed. The customer asks about the regular car wash package. The customer asks about the interior. The customer asks about the windows. The customer asks about the trash. The customer asks about the vacuum cleaner. The customer asks about the shampoo. The customer asks about the shine. The customer asks about the exterior. The customer asks about the body. The customer asks about the dirt. The customer asks about the vacuum cleaner. The customer asks about the trash. The customer asks about the vacuum cleaner. The customer asks about the vacuum cleaner. The customer asks about the vacuum cleaner. The customer asks about the vacuum cleaner. The customer asks about the vacuum cleaner. The customer asks about"}
{"answers": "Harry and his wife may go abroad during the holiday, but Harry's wife worries too much, so they may not go anywhere.", "prediction_text": "Dialogue: #Person1#: Where are you going to spend your holidays this year, Harry?"}
{"answers": "Harry and his wife may go abroad during the holidays. However, they might not go anywhere because his wife always worries too much.", "prediction_text": "Dialogue: #Person1#: Where are you going to spend your holidays this year, Harry?"}
{"answers": "Harry is not sure about the holiday plan of going abroad to spend the holiday, because his wife worries about things after they leave.", "prediction_text": "Dialogue: #Person1#: Where are you going to spend your holidays this year, Harry?"}
{"answers": "#Person1# is a new member who wants to use the machines. Johnson teaches #Person1# to get a card to keep track of the progress and record the suitable weight for each machine. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "The dialogue is about a new member of a gym. The narrator is a trainer who helps the new member to get started. The narrator explains how to use the machines and how to keep track of the weight used. The narrator also explains the importance of not pushing oneself too hard."}
{"answers": "Johnson is teaching #Person1# how to use the training machines. He introduces a training card that allows #Person1# to keep track of the weight used on each machine. #Person1# is exhausted when there are still 7 machines left. Johnson suggests #Person1# stop to train and go through the rest of the machines next time.", "prediction_text": "The dialogue is about a new member of a gym. The narrator is a trainer who helps the new member to get started. The narrator explains how to use the machines and how to keep track of the weight used. The narrator also explains the importance of not pushing oneself too hard."}
{"answers": "Johnson shows #Person1# the correct way to use weights machine and tells #Person1# using a card to keep track of exercise intensity. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "The dialogue is about a new member of a gym. The narrator is a trainer who helps the new member to get started. The narrator explains how to use the machines and how to keep track of the weight used. The narrator also explains the importance of not pushing oneself too hard."}
{"answers": "#Person1# and #Person2# are both unemployed. #Person2# suggests applying for the electrician program and #Person1# agrees.", "prediction_text": "The dialogue is about two people who are out of a job and are looking for a new one. They are talking about the job they want to apply for and how they can do it."}
{"answers": "Both #Person1# and #Person2# lost their jobs. They would like to apply for the electrician program.", "prediction_text": "The dialogue is about two people who are out of a job and are looking for a new one. They are talking about the job they want to apply for and how they can do it."}
{"answers": "#Person1# and #Person2# plan to apply for the electrician program after they were laid off.", "prediction_text": "The dialogue is about two people who are out of a job and are looking for a new one. They are talking about the job they want to apply for and how they can do it."}
{"answers": "#Person1# asks something about #Person2#'s care with puppies and reminds #Person2# of the vet appointment.", "prediction_text": "Dialogue: #Person1#: Have you given the puppies food yet?"}
{"answers": "#Person1# and #Person2# are talking about taking care of their puppies and the time of vet appointment.", "prediction_text": "Dialogue: #Person1#: Have you given the puppies food yet?"}
{"answers": "#Person1# inquires about #Person2#'s care with the puppies and reminds #Person2# of the vet appointment.", "prediction_text": "Dialogue: #Person1#: Have you given the puppies food yet?"}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay the rent. Ayden is short on cash but still lends $20 to #Person1#. They decide to have dinner at #Person1#'s place.", "prediction_text": "This is a dialogue between two people, #Person1# and #Person2#. #Person1# is asking #Person2# for a favor, and #Person2# is trying to help #Person1# out. #Person1# is in a tight spot, and #Person2# is trying to help him out. #Person2# is also in a tight spot, and he is trying to help #Person1# out. #Person1# is grateful for #Person2#'s help, and #Person2# is happy to help #Person1#. #Person1# and #Person2# agree to meet at #Person1#'s place at 6 pm."}
{"answers": "#Person1# borrows $ 20 from Ayden because #Person1# owes the landlady $ 200. Then #Person1# invites Ayden to have dinner at #Person1#'s place.", "prediction_text": "This is a dialogue between two people, #Person1# and #Person2#. #Person1# is asking #Person2# for a favor, and #Person2# is trying to help #Person1# out. #Person1# is in a tight spot, and #Person2# is trying to help him out. #Person2# is also in a tight spot, and he is trying to help #Person1# out. #Person1# is grateful for #Person2#'s help, and #Person2# is happy to help #Person1#. #Person1# and #Person2# agree to meet at #Person1#'s place at 6 pm."}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay for the house rent but Ayden lends $20 because of the lack of money. They decide to have dinner at #Person1#'s place.", "prediction_text": "This is a dialogue between two people, #Person1# and #Person2#. #Person1# is asking #Person2# for a favor, and #Person2# is trying to help #Person1# out. #Person1# is in a tight spot, and #Person2# is trying to help him out. #Person2# is also in a tight spot, and he is trying to help #Person1# out. #Person1# is grateful for #Person2#'s help, and #Person2# is happy to help #Person1#. #Person1# and #Person2# agree to meet at #Person1#'s place at 6 pm."}
{"answers": "#Person2# had been in the charity organization for 2 months. Social activities had a direct impact on #Person2#'s way of thinking.", "prediction_text": "The speaker is a person who has worked in a charity organization for 2 months. He has learned a lot from that experience, including love, sympathy, equality, and so on. He thinks that those social activities have direct impact on his way of thinking. He is not easy to be edgy."}
{"answers": "#Person2# had been in the charity organization for 2 months and the social activities influenced #Person2#'s way of thinking.", "prediction_text": "The speaker is a person who has worked in a charity organization for 2 months. He has learned a lot from that experience, including love, sympathy, equality, and so on. He thinks that those social activities have direct impact on his way of thinking. He is not easy to be edgy."}
{"answers": "#Person2# had been in the charity organization for 2 months. Social activities influenced #Person2#'s way of thinking.", "prediction_text": "The speaker is a person who has worked in a charity organization for 2 months. He has learned a lot from that experience, including love, sympathy, equality, and so on. He thinks that those social activities have direct impact on his way of thinking. He is not easy to be edgy."}
{"answers": "#Person1#'s organization will make a decision in about one week and contact #Person2# via phone or e-mail.", "prediction_text": "The dialogue between #Person1# and #Person2# is about how to get in touch with #Person1#."}
{"answers": "#Person1# can inform #Person2# by phone or e-mail when the organization makes a decision in about one week.", "prediction_text": "The dialogue between #Person1# and #Person2# is about how to get in touch with #Person1#."}
{"answers": "#Person1# can tell #Person2# their organization's decision by phone or e-mail in about one week.", "prediction_text": "The dialogue between #Person1# and #Person2# is about how to get in touch with #Person1#."}
{"answers": "#Person2# recommends Moutan and Roast Beijing Duck. #Person1# will take that in the hotel.", "prediction_text": "Dialogue: #Person1#: Would you please recommend some Chinese wine?"}
{"answers": "#Person2# recommends #Person1# to have Roast Beijing Duck along with Moutan in the hotel. #Person1# will have a try.", "prediction_text": "Dialogue: #Person1#: Would you please recommend some Chinese wine?"}
{"answers": "#Person2# recommends Moutan and Roast Beijing Duck. #Person1# will have a try in the hotel.", "prediction_text": "Dialogue: #Person1#: Would you please recommend some Chinese wine?"}
{"answers": "Angela calls Dan to make sure he's coming to the wedding with him next week and also asks him to invite Megan. They'll drive to the wedding together.", "prediction_text": "This dialogue is about a friend of the speaker's who is getting married. The speaker wants to invite the friend's friend to the wedding. The speaker also wants to remind the friend to buy a present for the couple."}
{"answers": "Angela calls to remind Dan that they are going to the wedding of her friend next week and asks him to invite Megan. The three of them will go together.", "prediction_text": "This dialogue is about a friend of the speaker's who is getting married. The speaker wants to invite Megan to the wedding, and he asks his friend to tell her for him."}
{"answers": "Angela reminds Dan to come to the wedding next week and asks him to invite Megan as well. They agree to go there together.", "prediction_text": "This dialogue is about a friend of the speaker's who is getting married. The speaker wants to invite Megan to the wedding, and he asks his friend to tell her for him."}
{"answers": "#Person2# has traditional Greek yogurt, which #Person1# thinks rather plain. #Person1# has an Italian tiramisu, which #Person2# thinks delicious. #Person1# goes and gets both of them a fried banana.", "prediction_text": "The dialogue is about two people who are having a dessert at a restaurant. The first person is trying a traditional Greek yogurt, while the second person is trying an Italian tiramisu. The second person is trying to convince the first person to try the yogurt. The first person is trying to convince the second person to try the tiramisu. The second person is trying to convince the first person to try the fried bananas."}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. They both decide to have fried bananas and #Person1# will bring bananas back.", "prediction_text": "The dialogue is about two people who are having a dessert at a restaurant. The first person is trying a traditional Greek yogurt, while the second person is trying an Italian tiramisu. The second person is trying to convince the first person to try the yogurt. The first person is trying to convince the second person to try the tiramisu. The second person is trying to convince the first person to try the fried bananas."}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. Then they want to have fried bananas and #Person1# goes out to buy some.", "prediction_text": "The dialogue is about two people who are having a dessert at a restaurant. The first person is trying a traditional Greek yogurt, while the second person is trying an Italian tiramisu. The second person is trying to convince the first person to try the yogurt. The first person is trying to convince the second person to try the tiramisu. The second person is trying to convince the first person to try the fried bananas."}
{"answers": "Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week.", "prediction_text": "The dialogue is about a job interview. The first person is the interviewer and the second person is the interviewee. The interviewer asks the interviewee about his education, age, and job history. The interviewee answers all the questions."}
{"answers": "Mr. Smith tells #Person1# that he graduated from Yale University with a B. A. in Economics, and he has worked in a bank for the last five years with $500 a week.", "prediction_text": "The dialogue is about a job interview. The first person is the interviewer and the second person is the interviewee. The interviewer asks the interviewee about his education, age, and job history. The interviewee answers all the questions."}
{"answers": "Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week.", "prediction_text": "The dialogue is about a job interview. The first person is the interviewer and the second person is the interviewee. The interviewer asks the interviewee about his education, age, and job history. The interviewee answers all the questions."}
{"answers": "#Person2# recommends Digital Barbie for #Person1#'s niece. #Person1# pays $32.42 for it by cash.", "prediction_text": "The dialogue is about a customer who wants to buy a Barbie doll for her niece. The salesperson suggests a digital Barbie doll, and the customer agrees to buy it. The customer also asks for a total amount, and the salesperson tells him the total amount with tax. The customer decides to pay cash, and the salesperson thanks him for shopping at the store."}
{"answers": "#Person2# recommends #Person1# to buy a $32. 42 Digital Barbie for #Person1#'s niece. #Person1# pays it by cash.", "prediction_text": "The dialogue is about a customer who wants to buy a Barbie doll for her niece. The salesperson suggests a digital Barbie doll, and the customer agrees to buy it. The customer also asks for a total amount, and the salesperson tells him the total amount with tax. The customer decides to pay cash, and the salesperson thanks him for shopping at the store."}
{"answers": "#Person1# paid $32.42 by cash to buy a Digital Barbie as a niece's gift after listening to #Person2#'s suggestion.", "prediction_text": "The dialogue is about a customer who wants to buy a Barbie doll for her niece. The salesperson suggests a digital Barbie doll, and the customer agrees to buy it. The customer also asks for a total amount, and the salesperson tells him the total amount with tax. The customer decides to pay cash, and the salesperson thanks him for shopping at the store."}
{"answers": "#Person2# wants a pair of Jordan shoes in size 41.", "prediction_text": "The dialogue is about a customer who wants to buy a pair of sport shoes. The shop assistant helps him to find the right size and then sells him the shoes. The customer is not satisfied with the price and asks the shop assistant to make it cheaper. The shop assistant says that the price is already on sale."}
{"answers": "#Person2# wants to buy a pair of Jordan shoes in size 41.", "prediction_text": "The dialogue is about a customer who wants to buy a pair of sport shoes. The shop assistant helps the customer to find the right size and then sells the shoes to him. The customer is not satisfied with the price and asks the shop assistant to make it cheaper. The shop assistant says that the price is already on sale."}
{"answers": "#Person2# wants to buy a pair of Jordan shoes in size 41.", "prediction_text": "The dialogue is about a customer who wants to buy a pair of sport shoes. The shop assistant helps the customer to find the right size and then sells the shoes to him. The customer is not satisfied with the price and asks the shop assistant to make it cheaper. The shop assistant says that the price is already on sale."}
{"answers": "#Person1# wants to go to the science museum but loses the way. #Person2# helps #Person1# buy the ticket and gives #Person1# directions.", "prediction_text": "A man is lost in a strange city and asks a stranger for directions."}
{"answers": "#Person1# is lost on the way to the science museum. #Person2# helps #Person1# to buy the tickets for the train and gives #Person1# directions.", "prediction_text": "A man is lost in a strange city and asks a stranger for directions."}
{"answers": "#Person2# helps #Person1# operate the ticket machine to buy a train ticket and tells #Person1# who loses the way how to go to the science museum.", "prediction_text": "A man is lost in a city and asks a stranger for directions."}
{"answers": "#Person1# asks Simon about his retirement and finds out Simon is on a trial scheme called phased retirement. He can work with his former company but manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "This dialogue is about a retired person who is working part-time for his old company. He is on a scheme called phased retirement, which means he can work for the company for a few months at a time. He can decide when to work and how long to work. He can also decide what sort of work he wants to do. The company gets flexibility, too."}
{"answers": "Simon tells #Person1# that he is on a scheme called phased retirement during which he had a six-month break from work, and after that he could apply for projects of the company he used to work for but manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "This dialogue is about a retired person who is working part-time for his old company. He is on a scheme called phased retirement, which means he can work for the company for a few months at a time. He can decide when to work and how long to work. He can also decide what sort of work he wants to do. The company gets flexibility, too."}
{"answers": "Simon tells #Person1# that he joined in a phased retirement scheme which offers retired people work positions from the former company, and he can manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "This dialogue is about a retired person who is working part-time for his old company. He is on a scheme called phased retirement, which means he can work for the company for a few months at a time. He can decide when to work and how long to work. He can also decide what sort of work he wants to do. The company gets flexibility, too."}
{"answers": "Rocky doesn't want to dance because no one at the party fits his type, but #Person1# likes different kinds of women from him. Finally, Rocky decides to go home to a TV dinner and his dog, Rusty", "prediction_text": "A man and a woman are talking about their ideas of the perfect woman."}
{"answers": "Rocky wants a woman who's affectionate and fulfills his every need. #Person1# likes different kinds of women from him. Rocky doesn't mind and plans to go home and spend the night with a TV dinner and his dog, Rusty.", "prediction_text": "A man and a woman are talking about their ideas of the perfect woman."}
{"answers": "Rocky has particular requirements and cannot find a suitable woman in the party, so he doesn't want to dance. #Person1# likes different kinds of women from him. He sticks with his views and decides to go home to a TV dinner and his dog, Rusty.", "prediction_text": "A man and a woman are talking about their ideas of the perfect woman."}
{"answers": "#Person1# and #Person2# is talking about the heavy storm last night. #Person2# thinks the weather is terrible. #Person1# is positive towards that.", "prediction_text": "The dialogue is about the weather. The speaker is complaining about the weather. The other speaker is not complaining."}
{"answers": "#Person1# and #Person2# are talking about the heavy storm last night, and #Person1#'s positive. #Person2# thinks the weather is terrible. #Person1# thinks they should not complain.", "prediction_text": "The dialogue is about the weather. The speaker is complaining about the weather. The other speaker is not complaining."}
{"answers": "#Person2# complains the terrible storm last night and wishes for nice weather. #Person1# thinks they should not complain.", "prediction_text": "The dialogue is about the weather. The speaker is complaining about the weather. The other speaker is not complaining."}
{"answers": "#Person1# wants to watch TV together with #Person2#, but #Person2# finds it boring. #Person2# suggests doing music together.", "prediction_text": "The dialogue is about a couple who have different views on TV. The man thinks that watching TV is boring and that he should go out to enjoy himself. The woman thinks that watching TV is not good for her and that she should do something else. The man suggests that they do something together, but the woman says that she doesn't want to watch TV."}
{"answers": "#Person1# asks #Person2# to watch TV with #Person1# at home. However, #Person2# finds it boring and suggests doing music together.", "prediction_text": "The dialogue is about a couple who have different views on TV. The man thinks that watching TV is boring and that he should go out to enjoy himself. The woman thinks that watching TV is not good for her and that she should do something else. The man suggests that they do something together, but the woman says that she doesn't want to watch TV."}
{"answers": "#Person1# wants #Person2# to watch TV with #Person1#. #Person2# thinks it is boring and suggests doing their music together.", "prediction_text": "The dialogue is about a couple who have different views on TV. The man thinks that watching TV is boring and that he should go out to enjoy himself. The woman thinks that watching TV is not good for her and that she should do something else. The man suggests that they do something together, but the woman says that she doesn't want to watch TV."}
{"answers": "Ben feels a bit nervous about his new school life. #Person1# cheers him up and tells him about the schedule of the new school.", "prediction_text": "Dialogue: #Person1#: Hello, Ben. You're getting ready for tomorrow's lessons, aren't you?"}
{"answers": "Ben is nervous about his new school life. #Person1# is giving him some brief information about the school.", "prediction_text": "Dialogue: #Person1#: Hello, Ben. You're getting ready for tomorrow's lessons, aren't you?"}
{"answers": "Ben is nervous about the upcoming new school life. #Person1# comforts him and tells him the school daily routine.", "prediction_text": "Dialogue: #Person1#: Hello, Ben. You're getting ready for tomorrow's lessons, aren't you?"}
{"answers": "Adam's knee is getting better but #Person1# still suggests that he should not do any running today. Adam decides to watch the films of Michigan after practice and will be back tomorrow for full practice.", "prediction_text": "Adam is a basketball player. He has a knee injury. He is going to play in a game on Saturday. He is going to the doctor today. He is going to take it easy today. He is going to practice throwing. He is not going to do any running. He is going to see the game films. He is going to rest his knee. He is going to be well in three days. He is going to play in a game on Saturday. He is going to the doctor today. He is going to take it easy today. He is going to practice throwing. He is not going to do any running. He is going to see the game films. He is going to rest his knee. He is going to be well"}
{"answers": "Adam's knee is getting better but #Person1# suggests that he should not do any running today. Adam decides to see films of Michigan after practice and says he'll be back tomorrow for full practice.", "prediction_text": "Adam is a basketball player. He has a knee injury. He is going to play in a game on Saturday. He is going to the doctor today. He is going to take it easy today. He is going to practice throwing. He is not going to do any running. He is going to see the game films. He is going to rest his knee. He is going to be well in three days. He is going to play in a game on Saturday. He is going to the doctor today. He is going to take it easy today. He is going to practice throwing. He is not going to do any running. He is going to see the game films. He is going to rest his knee. He is going to be well"}
{"answers": "Adam's knee is getting better but #Person1# suggests that he should not do any running today. #Person1# says he can watch some films of Michigan after practice. Adam decides to go back tomorrow for full practice.", "prediction_text": "Adam is a basketball player. He has a knee injury. He is going to play in a game on Saturday. He is going to the doctor today. He is going to take it easy today. He is going to practice throwing. He is not going to do any running. He is going to see the game films. He is going to rest his knee. He is going to be well in three days. He is going to play in a game on Saturday. He is going to the doctor today. He is going to take it easy today. He is going to practice throwing. He is not going to do any running. He is going to see the game films. He is going to rest his knee. He is going to be well"}
{"answers": "#Person1# wants #Person2# to help to print up a copy. #Person2# accepts and edits it for #Person1#.", "prediction_text": "The speaker asks the other person to do a favor for him. The other person agrees to do it. The speaker asks the other person to edit the document. The other person agrees to do it."}
{"answers": "#Person1# asks for #Person2#'s help to print up a copy. #Person2# accepts and edits it for #Person1#.", "prediction_text": "The speaker asks the other person to do a favor for him. The other person agrees to do it. The speaker asks the other person to edit the document. The other person agrees to do it."}
{"answers": "#Person1# asks for #Person2#'s help to print up a copy. #Person2# accepts and edits it for #Person1#.", "prediction_text": "The speaker asks the other person to do a favor for him. The other person agrees to do it. The speaker asks the other person to edit the document. The other person agrees to do it."}
{"answers": "#Person2# lets #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "This is a dialogue between two people. The first person wants to put up a curtain, but the second person offers to help. The first person says that he wants to hold the ladder, and the second person agrees to put up the curtain. The first person thanks the second person for helping."}
{"answers": "#Person2# suggests that #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "This is a dialogue between two people. The first person wants to put up a curtain, but the second person offers to help. The first person says that he wants to hold the ladder, and the second person agrees to put up the curtain. The first person thanks the second person for helping."}
{"answers": "#Person2# lets #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "This is a dialogue between two people. The first person wants to put up a curtain, but the second person offers to help. The first person says that he wants to hold the ladder, and the second person agrees to put up the curtain. The first person thanks the second person for helping."}
{"answers": "Jack is available for a camping trip the weekend after next.", "prediction_text": "Jack and his friend are planning a camping trip, and they want to know which weekend is best for everyone. Jack asks his friend to check his calendar, and his friend says that the weekend after next looks pretty good. Otherwise, he's all booked up."}
{"answers": "Jack will be available for a weekend camping trip the weekend after next.", "prediction_text": "Jack and his friend are planning a camping trip, and they want to know which weekend is best for everyone. Jack asks his friend to check his calendar, and his friend says that the weekend after next looks pretty good. Otherwise, he's all booked up."}
{"answers": "The weekend after next is suitable for Jack to have a camping trip.", "prediction_text": "Jack and his friend are planning a camping trip, and they want to know which weekend is best for everyone. Jack asks his friend to check his calendar, and his friend says that the weekend after next looks pretty good. Otherwise, he's all booked up."}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells #Person2# she is pregnant. #Person2# feels happy. They go to the doctor. #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "A man is accused of being the father of a child he did not conceive."}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells him that she's pregnant. They go to see the doctor. However, #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "A man is accused of being the father of a child he did not conceive."}
{"answers": "#Person1# apologises to #Person2# after the quarrel and tells #Person2# she's pregnant. #Person2# feels so happy and they decide to see the doctor. After the pregnancy test about the date, #Person2# finds that it is not his baby, because he was away the first week of February at a training seminar.", "prediction_text": "A man is accused of being the father of a child he did not conceive."}
{"answers": "#Person2# is worried about #Person2#'s daughter. #Person1# suggests #Person2# should encourage #Person2#'s daughter to make her own decision.", "prediction_text": "The dialogue is about a parent who is worried about her daughter's decision about college. The parent wants to give her some tips, but the person is told that she should let her daughter make her own decision."}
{"answers": "#Person1# advises #Person2# to let #Person2#'s daughter make her own decisions instead of worrying about her.", "prediction_text": "The dialogue is about a mother who is worried about her daughter's decision about college. The mother wants to give her some tips, but the father says that she should make her own decision."}
{"answers": "#Person1# suggests #Person2# letting the child make own decision instead of worrying about her.", "prediction_text": "The dialogue is about a mother who is worried about her daughter's decision about college. The mother wants to give her some tips, but the father says that she should make her own decision."}
{"answers": "#Person1# may lose #Person1#'s job because of a mistake that may cause a huge loss and a dishonest supervisor. #Person2# suggests #Person1# live with #Person2# and stop buying all the junk to save money if #Person1# loses the job. #Person1# agrees.", "prediction_text": "#Person1#: I don't know what I'm going to do! It's going to be impossible to make ends meet if I lose my job!"}
{"answers": "#Person1# makes a big mistake which may cause a huge loss and #Person1# may lose this job because of the loss and #Person1#'s dishonest supervisor. #Person2# suggests that #Person1# move in with #Person2# until #Person1# find another job and stop buying all the junk, if #Person1# loses the job.", "prediction_text": "#Person1#: I don't know what I'm going to do! It's going to be impossible to make ends meet if I lose my job!"}
{"answers": "#Person1# might lose the job because of a working mistake and is worried about financial problems after losing a job. #Person2# suggests #Person1# save money until #Person1# find another job, if #Person1# loses the job.", "prediction_text": "#Person1#: I don't know what I'm going to do! It's going to be impossible to make ends meet if I lose my job!"}
{"answers": "#Person2#'s friend is visiting #Person2#. #Person2# invites him to give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "The dialogue between two people. The first person is happy because his friend just dropped in from the United Kingdom. The second person is happy because he decided to put his friend up while he's here. They will have fun and the second person will invite his friend to give a speech at his lab. The first person is interested in the topic of the speech. The second person says that the topic is about multimodal text generation. The first person is interested in the topic and wants to go to the speech. The second person leaves a place for the first person."}
{"answers": "#Person2#'s friend is a Ph.D. student at the University of Edinburgh. He is visiting #Person2# and will give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "The dialogue between two people. The first person is happy because his friend just dropped in from the United Kingdom. The second person is happy because he decided to put his friend up while he's here. They will have fun and the second person will invite his friend to give a speech at his lab. The first person is interested in the topic of the speech. The second person says that the topic is about multimodal text generation. The first person is interested in the topic and wants to go to the speech. The second person leaves a place for the first person."}
{"answers": "#Person2#'s friend is visiting #Person2# and will give a speech about multimodal text generation. #Person1# thinks it interesting and will attend it.", "prediction_text": "The dialogue between two people. The first person is happy because his friend just dropped in from the United Kingdom. The second person is happy because he decided to put his friend up while he's here. They will have fun and the second person will invite his friend to give a speech at his lab. The first person is interested in the topic of the speech. The second person says that the topic is about multimodal text generation. The first person is interested in the topic and wants to go to the speech. The second person leaves a place for the first person."}
{"answers": "#Person1# suggests going to John's house, but #Person2# feels sick and decides to go to sleep.", "prediction_text": "The speaker is asking the other person if they want to go over to John's house tonight. The other person says that they are not feeling well and should probably stay at home and rest. The speaker offers to make them some soup, but the other person says that they are not hungry and might just go to sleep."}
{"answers": "#Person2# prefers to stay at home and rest rather than go over to John's house tonight because #Person2# gets sick.", "prediction_text": "The speaker is asking the other person if they want to go over to John's house tonight. The other person says that they are not feeling well and should probably stay at home and rest. The speaker offers to make them some soup, but the other person says that they are not hungry and might just go to sleep."}
{"answers": "#Person2# doesn't want to go to John's house tonight because of getting sick. #Person2# decides to go to sleep.", "prediction_text": "The speaker is asking the other person if they want to go over to John's house tonight. The other person says that they are not feeling well and should probably stay at home and rest. The speaker offers to make them some soup, but the other person says that they are not hungry and might just go to sleep."}
{"answers": "Mr. Faber calls #Person1# to book a double room for 3 nights at York Hotel.", "prediction_text": "A man calls a hotel to book a room for three nights."}
{"answers": "Mr. Faber books a double room for 3 nights from July 20th at York Hotel.", "prediction_text": "A man calls a hotel to book a room for three nights."}
{"answers": "Mr. Faber books a double room for three nights from July 20th at York Hotel.", "prediction_text": "A man calls a hotel to book a room for three nights."}
{"answers": "#Person1# wants a cheap single room. #Person2# recommends calling John Godfrey and see him on Saturday.", "prediction_text": "The dialogue is between a man and a woman who are looking for a flat to rent. The man is looking for a single room, but the woman suggests that he may have to share a flat with other people. The woman also suggests that the man should look at West Derby, which is closer to his office. She also says that the area has good schools if the man has children. The woman then suggests that the man should call a friend of hers, John Godfrey, who works in another office. The man asks for the friend's telephone number, and the woman gives it to him. She also suggests that the man should call the friend on Saturday."}
{"answers": "#Person1# is looking for a cheap single room. #Person2# recommends #Person1# to contact John Godfrey on Saturday.", "prediction_text": "The dialogue is between a man and a woman who are looking for a flat to rent. The man is looking for a single room, but the woman suggests that he may have to share a flat with other people. The woman also suggests that the man should look at West Derby, which is closer to his office. She also says that the area has good schools if the man has children. The woman then suggests that the man should call a friend of hers, John Godfrey, who works in another office. The man asks for the friend's telephone number, and the woman gives it to him. She also suggests that the man should call the friend on Saturday."}
{"answers": "#Person1# is looking for a cheap single room. #Person2# gives #Person1# John Godfrey's phone number that could help to find a room and suggests that they meet on Saturday.", "prediction_text": "The dialogue is between a man and a woman who are looking for a flat to rent. The man is looking for a single room, but the woman suggests that he may have to share a flat with other people. The woman also suggests that the man should look at West Derby, which is closer to his office. She also says that the area has good schools if the man has children. The woman then suggests that the man should call a friend of hers, John Godfrey, who works in another office. The man asks for the friend's telephone number, and the woman gives it to him. She also suggests that the man should call the friend on Saturday."}
{"answers": "#Person1# and #Person2# blame each other for the series of bad experiences during the journey.", "prediction_text": "Dialogue: #Person1#: This is all your fault. This wouldn't have happened if you hadn't tried to give money to that policeman. #Person2#: Well, if you'd listen to me when I suggested asking someone for directions, we wouldn't have got lost in the first place, would we? #Person1#: Oh, go on, blame it all on me. You were the one who was driving when we got stopped by the police. If you hadn't been speeding, we wouldn't have been stopped at all. #Person2#: That's not fair. We were doing fine until you suddenly decided you knew a quicker way. #Person1#: Oh, come on, I only suggest it because we were running so"}
{"answers": "#Person1# and #Person2# are blaming each other for lots of bad experience during the journey.", "prediction_text": "Dialogue: #Person1#: This is all your fault. This wouldn't have happened if you hadn't tried to give money to that policeman. #Person2#: Well, if you'd listen to me when I suggested asking someone for directions, we wouldn't have got lost in the first place, would we? #Person1#: Oh, go on, blame it all on me. You were the one who was driving when we got stopped by the police. If you hadn't been speeding, we wouldn't have been stopped at all. #Person2#: That's not fair. We were doing fine until you suddenly decided you knew a quicker way. #Person1#: Oh, come on, I only suggest it because we were running so"}
{"answers": "#Person1# and #Person2# quarrel and blame each other because of bad experiences during the journey.", "prediction_text": "Dialogue: #Person1#: This is all your fault. This wouldn't have happened if you hadn't tried to give money to that policeman. #Person2#: Well, if you'd listen to me when I suggested asking someone for directions, we wouldn't have got lost in the first place, would we? #Person1#: Oh, go on, blame it all on me. You were the one who was driving when we got stopped by the police. If you hadn't been speeding, we wouldn't have been stopped at all. #Person2#: That's not fair. We were doing fine until you suddenly decided you knew a quicker way. #Person1#: Oh, come on, I only suggest it because we were running so"}
{"answers": "Darlene calls to check the delayed order. Dan tells her the situation. Darlene will talk to Steve.", "prediction_text": "The speaker is calling to check on an order of computers that was supposed to be delivered on the tenth of September. However, the factory is short of hands and the delivery has been delayed for two days. The speaker asks for the phone number of the person who is in charge of the order. The person gives the number and the speaker asks if it is double 6 or double 3. The person says it is double 6. The speaker says that the delay shouldn't make that much difference and thanks the person for understanding."}
{"answers": "Darlene calls Dan to check on the delay of the order. Dan explains to her the reason for the delay. Darlene decides to talk to Steven.", "prediction_text": "The speaker is calling to check on an order of computers that was supposed to be delivered on the tenth of September. However, the factory is short of hands and the delivery has been delayed for two days. The speaker asks for the phone number of the person who is in charge of the order. The person gives the number and the speaker asks if it is double 6 or double 3. The person says it is double 6. The speaker says that the delay shouldn't make that much difference and thanks the person for understanding."}
{"answers": "Darlen calls Dan to check the delayed order of computers. Dan explains to her the reason for the delay. Darlene decides to talk to Steven.", "prediction_text": "The speaker is calling to check on an order of computers that was supposed to be delivered on the tenth of September. However, the factory is short of hands and the delivery has been delayed for two days. The speaker asks for the phone number of the person who is in charge of the order. The person gives the number and the speaker asks if it is double 6 or double 3. The person says it is double 6. The speaker says that the delay shouldn't make that much difference and thanks the person for understanding."}
{"answers": "#Person2# tells #Person1# the founders and founding times of the Washington Post, the New York Times, and the Los Angeles Times.", "prediction_text": "The dialogue is about the history of newspapers. The first question is about the Washington Post, which was founded in 1877. The second question is about the New York Times, which was founded in 1851. The third question is about the Los Angeles Times, which was founded in 1881."}
{"answers": "#Person1# and #Person2# are talking about the founding time and founder of the New York Times, the Washington Post, and the Los Angeles Times were founded.", "prediction_text": "The dialogue is about the history of newspapers. The first newspaper was founded in 1777. The New York Times is the oldest newspaper in the United States. The Los Angeles Times was founded in 1881."}
{"answers": "#Person2# tells #Person1# the founder and the founding time of the New York Times, the Washington Post, and the Los Angeles Times.", "prediction_text": "The dialogue is about the history of newspapers. The first newspaper was founded in 1777. The New York Times is the oldest newspaper in the United States. The Los Angeles Times was founded in 1881."}
{"answers": "#Person1# finds that #Person2# e-mail exceeds capacity and suggests #Person2# compress the email.", "prediction_text": "The dialogue between two people."}
{"answers": "#Person2#'s attachment exceeds the e-mail capacity, #Person1# suggests compressing it.", "prediction_text": "The dialogue between two people."}
{"answers": "#Person2# can't send out an email. #Person1# suggests #Person2#'s attachment be compressed.", "prediction_text": "The dialogue between two people."}
{"answers": "#Person2# tells #Person1# #Person2# enjoyed the weekend organized by #Person1#.", "prediction_text": "The speaker is asking the other person how they enjoyed their weekend. The other person replies that they enjoyed it very much. The speaker then says that they hope the other person will come and stay again next time they are in Changsha."}
{"answers": "#Person1# and #Person2# has enjoyed a nice weekend together.", "prediction_text": "The speaker is asking the other person how they enjoyed their weekend. The other person replies that they enjoyed it very much. The speaker then says that they hope the other person will come and stay again next time they are in Changsha."}
{"answers": "#Person1# invited #Person2# to spend the weekend and #Person2# has enjoyed it.", "prediction_text": "The speaker is asking the other person how they enjoyed their weekend. The other person replies that they enjoyed it very much. The speaker then says that they hope the other person will come and stay again next time they are in Changsha."}
{"answers": "#Person2# offers #Person1# suggestions about what to eat. #Person1# will go to Panda Express.", "prediction_text": "The dialogue is about a person who doesn't know what he wants to eat. He asks his friend for some suggestions. His friend suggests some places to get food from. The person decides to get his food from Panda Express."}
{"answers": "#Person1# wants to eat something and #Person2# gives some advice. #Person1# finally goes to eat Chinese food.", "prediction_text": "The dialogue is about a person who doesn't know what he wants to eat. He asks his friend for some suggestions. His friend suggests some places to get food from. The person decides to get his food from Panda Express."}
{"answers": "#Person1# is hungry. #Person2# says the Chinese food from Panda Express is good.", "prediction_text": "The dialogue is about a person who doesn't know what he wants to eat. He asks his friend for some suggestions. His friend suggests some places to get food from. The person decides to get his food from Panda Express."}
{"answers": "Mary from Hans furniture tells Tom they decide not to employ him but Tom asks the store to reconsider him.", "prediction_text": "Dialogue: #Person1#: Hi, Tom! This is Marry with Hans furniture store. #Person2#: Yes, Marry? #Person1#: I just wanted to let you to know that we had decided to offer the sales position to someone else. #Person2#: I see. #Person1#: We considered you very carefully but the other candidate has more experience. #Person2#: Maybe so. But I'm fast learner and I'm willing to work overtime to learn the business. #Person1#: I'm sure you are. #Person2#: I must to ask you to reconsider your decision and give me a chance."}
{"answers": "Marry calls Tom to inform him that they decide to offer the sales position to someone else. Tom wants them to reconsider.", "prediction_text": "Dialogue: #Person1#: Hi, Tom! This is Marry with Hans furniture store. #Person2#: Yes, Marry? #Person1#: I just wanted to let you to know that we had decided to offer the sales position to someone else. #Person2#: I see. #Person1#: We considered you very carefully but the other candidate has more experience. #Person2#: Maybe so. But I'm fast learner and I'm willing to work overtime to learn the business. #Person1#: I'm sure you are. #Person2#: I must to ask you to reconsider your decision and give me a chance."}
{"answers": "Tom wants Marry to reconsider the decision of offering the sales position to someone else instead of him.", "prediction_text": "Dialogue: #Person1#: Hi, Tom! This is Marry with Hans furniture store. #Person2#: Yes, Marry? #Person1#: I just wanted to let you to know that we had decided to offer the sales position to someone else. #Person2#: I see. #Person1#: We considered you very carefully but the other candidate has more experience. #Person2#: Maybe so. But I'm fast learner and I'm willing to work overtime to learn the business. #Person1#: I'm sure you are. #Person2#: I must to ask you to reconsider your decision and give me a chance."}
{"answers": "#Person1# is angry about the crank calls.", "prediction_text": "A man is being harassed by a woman who keeps calling him. He tells her to stop calling him or he will call the police."}
{"answers": "#Person1# gets a crank call and is angry about it.", "prediction_text": "A man is being harassed by a woman who keeps calling him. He tells her to stop calling him or he will call the police."}
{"answers": "#Person1# receives a phone call but no one speaks.", "prediction_text": "A man is being harassed by a woman who keeps calling him. He tells her to stop calling him or he will call the police."}
{"answers": "#Person2# is being interviewed for a music teacher. #Person2# says #Person2# has degrees in music and specializes in classic music. Then #Person2# shares some research about classic music that can make people relaxed. #Person1# is very satisfied with #Person2#. #Person2# gives some suggestions on how to start listening to classical music.", "prediction_text": "Dialogue: #Person1#: Good morning, thanks for coming. Shall we begin? Why do you want to be a music teacher?"}
{"answers": "#Person1# interviews #Person2# for a music teacher position. #Person1# is very satisfied with #Person2#'s educational background in music and #Person2#'s understanding of classical music. After the interview, #Person2# suggests #Person1# can develop interests in classic music by listening to different classic music online.", "prediction_text": "Dialogue: #Person1#: Good morning, thanks for coming. Shall we begin? Why do you want to be a music teacher?"}
{"answers": "#Person2# is being interviewed for a music teacher. #Person1# is impressed by #Person2#'s background and knowledge of classical music. And #Person1# will try classical music.", "prediction_text": "Dialogue: #Person1#: Good morning, thanks for coming. Shall we begin? Why do you want to be a music teacher?"}
{"answers": "#Person2# likes his neibourhood girl who is popular. Although #Person1# analyses the disadvantages, #Person2# still decides to date with her.", "prediction_text": "A dialogue between two people who are in love with the same girl."}
{"answers": "#Person2# tells #Person1# that he falls in love with his neighbour and #Person1# thinks he has some competition and wishes him good luck.", "prediction_text": "A dialogue between two people who are in love with the same girl."}
{"answers": "#Person2# tells #Person1# he falls in love with the girl living under him and she's very popular. #Person2#'s going to call her now and invite her to dinner tonight.", "prediction_text": "A dialogue between two people who are in love with the same girl."}
{"answers": "#Person1# and #Person2# talk about Mirella's dressing style in the office which is different from others. The management decided to give Mirella formal clothes directly to warn her.", "prediction_text": "The speaker is complaining about the way Mirella dresses at work. She is wearing jeans and sweatshirts, which is not acceptable in the office. The speaker thinks that Mirella is trying to make herself at home in the office. The speaker thinks that Mirella is influenced by the way they do things on the west coast. The speaker thinks that the working atmosphere is a lot more relaxed and casual in California. The speaker thinks that it comes from the information technology industry. The speaker thinks that there isn't such a large division between home and office, so people want to work in the clothes they feel more relaxed in. The speaker thinks that it is strange that Mirella is wearing jeans and sweatshirts. The speaker thinks"}
{"answers": "#Person1# thinks it's strange of Mirella wearing casually in the office. #Person2# thinks she was influenced by the casual atmosphere in California and tells #Person1# management decided to give her a new white dress to warn her.", "prediction_text": "The speaker is complaining about the way Mirella dresses at work. She is wearing jeans and sweatshirts, which is not acceptable in the office. The speaker thinks that Mirella is trying to make herself at home in the office. The speaker thinks that Mirella is influenced by the way they do things on the west coast. The speaker thinks that the working atmosphere is a lot more relaxed and casual in California. The speaker thinks that there isn't such a large division between home and office, so people want to work in the clothes they feel more relaxed in. The speaker thinks that Mirella was really influenced by the way they do things on the west coast. The speaker thinks that the working atmosphere is a lot more relaxed and"}
{"answers": "#Person1# and #Person2# talks about Mirella dressed casually after she came back from Silicon Valley. Managements put white shirts on her desk to warn her.", "prediction_text": "The speaker is complaining about the way Mirella dresses at work. She is wearing jeans and sweatshirts, which is not acceptable in the office. The speaker thinks that Mirella is trying to make herself at home in the office. The speaker thinks that Mirella is influenced by the way they do things on the west coast. The speaker thinks that the working atmosphere is a lot more relaxed and casual in California. The speaker thinks that there isn't such a large division between home and office, so people want to work in the clothes they feel more relaxed in. The speaker thinks that Mirella was really influenced by the way they do things on the west coast. The speaker thinks that the working atmosphere is a lot more relaxed and"}
{"answers": "#Person1# congratulates #Person2# on #Person2#'s new venture and expresses #Person1#'s willingness to help.", "prediction_text": "Dialogue: #Person1#: You're going to set up your own law office, aren't you?"}
{"answers": "#Person1# is willing to offer #Person2# help in setting up a law office.", "prediction_text": "Dialogue: #Person1#: You're going to set up your own law office, aren't you?"}
{"answers": "#Person2# is going to set up #Person2#'s own law office. #Person1# wishes #Person2# good luck.", "prediction_text": "Dialogue: #Person1#: You're going to set up your own law office, aren't you?"}
{"answers": "Peter tells Kate he played games and listened to music the whole last night.", "prediction_text": "Peter and Kate are talking about their night out."}
{"answers": "Peter tells Kate he stayed up and he's tired now.", "prediction_text": "Peter and Kate are talking about their night out."}
{"answers": "Peter tells Kate he stayed up. He's tired now and needs a rest.", "prediction_text": "Peter and Kate are talking about their night out."}
{"answers": "#Person2# explains alternatives of sea transportation but #Person1# addresses the importance of boat in transoceanic cargo transportation.", "prediction_text": "Nowadays, ships and boats are no longer so important in transportation as they used to be. With the extension of railways and highways, and the improvement of safety and capacity of airplanes, ships and boats have been giving place to trains, planes and automobiles. I read in the newspapers that the passenger liners from Shanghai to Ningpo and Dalian have been out of business ; and the liners up and down the Yangtze River have been reduced by 70 %. The pace of life of people is now getting faster and faster, so the speed of ships and boats seems to be so slow. I think it may be the reason why people don't like to travel by boat. But the most of the transoceanic cargo transportation is still carried by boat"}
{"answers": "#Person1# and #Person2# are talking about the development of transportation and the importance of boats.", "prediction_text": "Nowadays, ships and boats are no longer so important in transportation as they used to be. With the extension of railways and highways, and the improvement of safety and capacity of airplanes, ships and boats have been giving place to trains, planes and automobiles. I read in the newspapers that the passenger liners from Shanghai to Ningpo and Dalian have been out of business ; and the liners up and down the Yangtze River have been reduced by 70 %. The pace of life of people is now getting faster and faster, so the speed of ships and boats seems to be so slow. I think it may be the reason why people don't like to travel by boat. But the most of the transoceanic cargo transportation is still carried by boat"}
{"answers": "#Person2# tells #Person1# that ships and boats have been giving places to other transportations because people's life is getting faster, but #Person1# thinks they are still important.", "prediction_text": "Nowadays, ships and boats are no longer so important in transportation as they used to be. With the extension of railways and highways, and the improvement of safety and capacity of airplanes, ships and boats have been giving place to trains, planes and automobiles. I read in the newspapers that the passenger liners from Shanghai to Ningpo and Dalian have been out of business ; and the liners up and down the Yangtze River have been reduced by 70 %. The pace of life of people is now getting faster and faster, so the speed of ships and boats seems to be so slow. I think it may be the reason why people don't like to travel by boat. But the most of the transoceanic cargo transportation is still carried by boat"}
{"answers": "Fanny had a nightmare because of the pressure from school applications. Her mother and Andy both comfort her.", "prediction_text": "The dialogue is about a student who had a bad dream. He was chased by ghosts in the graveyard. His mother woke him up. He was embarrassed. He was afraid to fall back asleep. He was worrying about getting into the University of Michigan."}
{"answers": "Fanny had a nightmare last night and her mother consoled her. Andy listens to her experience and thinks she has a good mother.", "prediction_text": "The dialogue is about a student who had a bad dream. He was chased by ghosts in the graveyard. His mother woke him up. He was embarrassed. He was afraid to fall back asleep. He was worrying about getting into the University of Michigan."}
{"answers": "Fanny had a bad dream last night. She is worried about getting into the University of Michigan. Andy comforts her.", "prediction_text": "The dialogue is about a student who had a bad dream. He was chased by ghosts in the graveyard. His mother woke him up. He was embarrassed. He was afraid to fall back asleep. He was worrying about getting into the University of Michigan."}
{"answers": "#Person1# and Ernie plan to start a band and they decide to play hip hop music.", "prediction_text": "The speaker is excited about starting a band with a friend. They discuss what kind of music they should play. The speaker suggests Vanilla Ice, but the other person doesn't know his songs."}
{"answers": "#Person1# and Ernie are preparing to start the band at school.", "prediction_text": "The speaker is excited about starting a band with a friend. They discuss what kind of music they should play. The speaker suggests Vanilla Ice, but the other person doesn't know his songs."}
{"answers": "#Person1# and Ernie start their own band on campus. #Person1# suggests they play Vanilla Ice songs.", "prediction_text": "The speaker is excited about starting a band with a friend. They discuss what kind of music they should play. The speaker suggests Vanilla Ice, but the friend doesn't know his other songs."}
{"answers": "#Person1# and #Person2# are discussing where to have fun, and they decide to go to the theater tonight.", "prediction_text": "The speaker is asking the listener if he or she is enjoying his or her trip to New Orleans. The listener says that he or she is enjoying the trip. The speaker asks if the listener would like to do something tonight. The listener says that he or she would love to. The speaker asks if the listener has been to a jazz club yet. The listener says that he or she has already been to several clubs. The speaker asks if the listener would like to go on a riverboat tour. The listener says that he or she has already gone twice this week. The speaker asks what the listener would like to do. The listener says that he or she would like to go to the theater. The speaker says that he or she knows of a"}
{"answers": "#Person1# and #Person2# are talking about what to do tonight and they finally decide to go to watch a show.", "prediction_text": "The speaker is asking the listener if he or she is enjoying his or her trip to New Orleans. The listener says that he or she is enjoying the trip. The speaker asks if the listener would like to do something tonight. The listener says that he or she would love to. The speaker asks if the listener has been to a jazz club yet. The listener says that he or she has already been to several clubs. The speaker asks if the listener would like to go on a riverboat tour. The listener says that he or she has already gone twice this week. The speaker asks what the listener would like to do. The listener says that he or she would like to go to the theater. The speaker says that he or she knows of a"}
{"answers": "#Person2# hasn't been to the theater for a long time, so #Person1# and #Person2# decide to make a reservation for a show at the Sanger Theater.", "prediction_text": "The speaker is asking the listener if he or she is enjoying his or her trip to New Orleans. The listener says that he or she is enjoying the trip. The speaker asks if the listener would like to do something tonight. The listener says that he or she would love to. The speaker asks if the listener has been to a jazz club yet. The listener says that he or she has already been to several clubs. The speaker asks if the listener would like to go on a riverboat tour. The listener says that he or she has already gone twice this week. The speaker asks what the listener would like to do. The listener says that he or she would like to go to the theater. The speaker says that he or she knows of a"}
{"answers": "#Person1# buys some nice clothes by credit card with #Person2#'s assistance.", "prediction_text": "The speaker is in a store and is trying to buy something. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to"}
{"answers": "#Person1# pays #Person2# by credit card for some clothes.", "prediction_text": "The speaker is in a store and is trying to buy something. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to"}
{"answers": "#Person1# purchases some clothes by credit card with #Person2#'s assistance.", "prediction_text": "The speaker is in a store and is trying to buy something. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to buy something from a store. The speaker is trying to"}
{"answers": "Mr. Blake explains the training manuals cannot be sent today because they are still being copied.", "prediction_text": "The speaker is trying to arrange a meeting with Mr. Foster. He is trying to get the speaker to agree to a meeting time that is not convenient for the speaker. The speaker is trying to get the speaker to agree to a meeting time that is not convenient for the speaker."}
{"answers": "#Person1# is transferring the message between Mr. Blake and Mr. Foster about the training manuals.", "prediction_text": "The speaker is trying to arrange a meeting with Mr. Foster. He is trying to get the speaker to agree to a meeting time that is not convenient for the speaker. The speaker is trying to get the speaker to agree to a meeting time that is not convenient for the speaker."}
{"answers": "Mr. Foster wants the training manuals to be sent this afternoon but Mr. Blake explains that they haven't been printed out yet.", "prediction_text": "The speaker is trying to arrange a meeting with Mr. Foster. He is trying to get the speaker to agree to a meeting time that is not convenient for the speaker. The speaker is trying to get the speaker to agree to a meeting time that is not convenient for the speaker."}
{"answers": "#Person2# tells David about #Person2#'s planned a long trip for #Person2#'s vacation. David thinks it's nice.", "prediction_text": "The dialogue is about a couple who are going on a vacation. They are going to visit their brother and his family in Salt Lake City. They will be staying in hotels and enjoying local food. They will also do a lot of sightseeing."}
{"answers": "David and #Person2# are talking about #Person2#'s plan for the vacation. David thinks it sounds good.", "prediction_text": "The dialogue is about a couple who are going on a vacation. They are going to visit their brother and his family in Salt Lake City. They will be staying in hotels and enjoying local food. They will also do a lot of sightseeing."}
