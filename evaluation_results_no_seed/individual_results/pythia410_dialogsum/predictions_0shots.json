{"answers": "<PERSON><PERSON> <PERSON> helps #Person1# to write a memo to inform every employee that they have to change the communication method and should not use Instant Messaging anymore.", "prediction_text": "I have a memo that I need to send to all employees."}
{"answers": "In order to prevent employees from wasting time on Instant Message programs, #Person1# decides to terminate the use of those programs and asks <PERSON><PERSON> to send out a memo to all employees by the afternoon.", "prediction_text": "I have a memo that I need to send to all employees."}
{"answers": "<PERSON><PERSON> takes a dictation for #Person1# about prohibiting the use of Instant Message programs in the office. They argue about its reasonability but #Person1# still insists.", "prediction_text": "I have a memo that I need to send to all employees."}
{"answers": "#Person2# arrives late because of traffic jam. #Person1# persuades #Person2# to use public transportations to keep healthy and to protect the environment.", "prediction_text": "Yes, it's not good for me or for the environment."}
{"answers": "#Person2# decides to follow #Person1#'s suggestions on quitting driving to work and will try to use public transportations.", "prediction_text": "#Person1#: I'm going to quit driving to work."}
{"answers": "#Person2# complains to #Person1# about the traffic jam, #Person1# suggests quitting driving and taking public transportation instead.", "prediction_text": "#Person1#: I'm going to quit driving to work."}
{"answers": "#Person1# tells Kate that Masha and Hero get divorced. Kate is surprised because she thought they are perfect couple.", "prediction_text": "? Who get custody?"}
{"answers": "#Person1# tells Kate that Masha and <PERSON> are getting a peaceful divorce. Kate feels surprised and asks about their kids.", "prediction_text": "? Who get custody?"}
{"answers": "#Person1# and Kate talk about the divorce between Masha and Hero. Kate feels surprised because she thought they are well matched", "prediction_text": "#Person1#: Masha and Hero are getting divorced."}
{"answers": "#Person1# and Brian are at the birthday party of Brian. Brian thinks #Person1# looks great and is popular.", "prediction_text": "1#: Thanks, this is a fine party. We should have a drink together to celebrate your birthday"}
{"answers": "#Person1# attends Brian's birthday party. Brian thinks #Person1# looks great and charming.", "prediction_text": "1#: Thanks, this is a fine party. We should have a drink together to celebrate your birthday"}
{"answers": "#Person1# has a dance with Brian at Brian's birthday party. Brian thinks #Person1# looks great and is popular.", "prediction_text": "1#: Thanks, this is a fine party. We should have a drink together to celebrate your birthday"}
{"answers": "#Person1# is surprised at the Olympic Stadium'volume, capacity and interior setting to #Person1#.", "prediction_text": "Dialogue: #Person1#: This Olympic park is so big!"}
{"answers": "#Person2# shows #Person1# around the constructing Olympic stadium and introduces the stadium.", "prediction_text": "Dialogue: #Person1#: This Olympic park is so big!"}
{"answers": "#Person2# introduces the Olympic Stadium's finishing time, capacity and interior setting to #Person1#.", "prediction_text": "Dialogue: #Person1#: This Olympic park is so big!"}
{"answers": "#Person1# wants to create a company and is going to write a business plan. #Person2# gives #Person1# suggestions on how to summarise business ideas, describe the service, differ from competitors and attract investment in a good business plan. #Person1# decides to stick to the old job.", "prediction_text": "#Person1#: I am done! I have created my own business!"}
{"answers": "#Person1# abandons the idea of creating a company after #Person2# explains what a business plan includes. #Person2# specifies that a well-written business plan includes an executive summary, goods and service description, strategy and implementation, and financial analysis.", "prediction_text": "#Person1#: I am done! I have created my own business!"}
{"answers": "#Person1# wants to start #Person1#'s own business, but #Person2# warns #Person1# of the hassle. #Person2# tells #Person1# what is needed in a business plan and #Person1# decides to stick to the old job for now.", "prediction_text": "#Person1#: I am done! I have created my own business!"}
{"answers": "#Person2# feels itchy. #Person1# doubts it is chicken pox and asks #Person2# to get away. #Person2# doesn't believe it.", "prediction_text": "1#: What's wrong with you? Why are you scratching so much?"}
{"answers": "#Person1# suspects that #Person2# has chicken pox and wants to keep away from #Person2#. #Person2# thinks #Person1# is exaggerating.", "prediction_text": "1#: What's wrong with you? Why are you scratching so much?"}
{"answers": "#Person1# thinks #Person2# has chicken pox and warns #Person2# about the possible hazards but #Person2# thinks it will be fine.", "prediction_text": "I'm a little bit nervous."}
{"answers": "#Person2# is checking out and asks #Person1# for the bill. #Person1# gives #Person2# a wrong bill at first then corrects it.", "prediction_text": "#Person1#: I'm in Room 309. I'm checking out today. Can I have my bill now?"}
{"answers": "#Person1# helps #Person2# correct a mischarged bill on laundry service and helps #Person2# check out.", "prediction_text": "#Person1#: I'm in Room 309. I'm checking out today. Can I have my bill now?"}
{"answers": "#Person2# finds #Person2# being mischarged. #Person1# corrects the bill and #Person2# pays for it.", "prediction_text": "#Person1#: I'm in Room 309. I'm checking out today. Can I have my bill now?"}
{"answers": "#Person1# is begging Steven to persuade his wife not to divorce him. Steven agrees.", "prediction_text": "I swear."}
{"answers": "Steve will try to persuade #Person1#'s wife not to divorce #Person1# as #Person1# swears to remain faithful forever.", "prediction_text": "I swear."}
{"answers": "#Person1# begs Steven's to persuade #Person1#'s wife not to divorce him, and Steven agrees.", "prediction_text": "I swear."}
{"answers": "#Person1# and #Person2# are talking about Abraham Lincoln. They think he was a noble man.", "prediction_text": "A man is born, and he is called \"The Man\"."}
{"answers": "#Person2# admires Abraham Lincoln for his perseverance, courage and humility.", "prediction_text": "A man is born, and he is called \"The Man\"."}
{"answers": "#Person1# and #Person2# talk about Abraham Lincoln and his glorious history. They both admire him.", "prediction_text": "A man is born, and he is called \"The Man\"."}
{"answers": "#Person2# plans to have a trip in Hebei but #Person1# says there are sandstorms in there.", "prediction_text": "Summary: #Person1#: I am going to Hebei."}
{"answers": "#Person1# tells #Person2# Hebei was experiencing severe sandstorms and sandstorms threaten people's health.", "prediction_text": "Summary: #Person1#: I am going to Hebei."}
{"answers": "#Person2# wants to travel to Hebei but #Person1# informs #Person2# of terrible sandstorms there.", "prediction_text": "You are quite right."}
{"answers": "#Person2# comes to the birthday party of Francis and sends him a remote car model.", "prediction_text": "#Person1#: Thank you, I really like it."}
{"answers": "#Person2# gives Francis a car model that both of them love as a birthday gift.", "prediction_text": "#Person1#: Thank you, I really like it."}
{"answers": "#Person2# gives Francis a nice car model as a birthday gift and Francis appreciates it.", "prediction_text": "#Person1#: Thank you, I really like it."}
{"answers": "Tony got caught cheating and feels ashamed. Steven tells him to learn from it and study hard.", "prediction_text": "#Person1#: I'm sorry. I'm sorry."}
{"answers": "Tony tells Steven that he failed a class because of cheating and he feels awful.", "prediction_text": "#Person1#: I'm sorry. I'm sorry."}
{"answers": "Tony tells Steven that he cheated on the exam and realized his mistake.", "prediction_text": "#Person1#: I'm sorry. I'm sorry."}
{"answers": "#Person1# is catching a train. Tom asks #Person1# not to hurry.", "prediction_text": "by my watch."}
{"answers": "#Person1# is in a hurry to catch a train. Tom tells #Person1# there is plenty of time.", "prediction_text": "off now."}
{"answers": "#Person1# is rushing to catch a train but Tom thinks it isn't necessary.", "prediction_text": "off now."}
{"answers": "#Person1# wants to adjust #Person1#'s life and #Person2# suggests #Person1# be positive and stay healthy.", "prediction_text": "I'm a young man."}
{"answers": "#Person1# has a bad lifestyle. #Person2# kindly advises #Person1# to be positive.", "prediction_text": "I'm a young man."}
{"answers": "#Person2# hopes #Person1# will become healthy and positive.", "prediction_text": "shadow of death in front of me."}
{"answers": "#Person1# tells #Person2# that Ruojia is married and will have a party tonight. #Person2#'s surprised to know that. They will bring their gifts to bless her.", "prediction_text": "#Person1#: I'm going to the party."}
{"answers": "#Person2# is surprised to know from #Person1# that Ruojia is married. Then #Person2# finds Ruojia has sent an email about it. They will go to Ruojia's party and give their presents to her.", "prediction_text": "#Person1#: I'm going to the party."}
{"answers": "#Person2# is surprised that Ruojia's married. #Person1# and #Person2# will go to her party and give their presents to wish her a happy marriage.", "prediction_text": "#Person1#: I'm going to the party."}
{"answers": "#Person2# at first thinks #Person1#'s behaviour cruel but finally joins #Person1#.", "prediction_text": "#Person1#: I'm not sure what you're doing."}
{"answers": "#Person1# plans on playing a trick to others. #Person2# thinks it's cruel at first but then joins.", "prediction_text": "#Person1#: I'm not sure what you're doing."}
{"answers": "#Person1# is about to make a prank. #Person2# thinks it's cruel at first but then joins.", "prediction_text": "#Person1#: I'm not sure what you're doing."}
{"answers": "Mike is describing his sister to #Person1#.", "prediction_text": "-going."}
{"answers": "Mike describes to #Person1# his sister's characters and personality.", "prediction_text": "as clever as me."}
{"answers": "#Person1# asks Mike about his sister.", "prediction_text": "as clever as me."}
{"answers": "#Person1# feels sick and #Person2# gives #Person1# a check-up.", "prediction_text": "#Person1#: I have a headache."}
{"answers": "#Person2# finds that #Person1# has a fever and says #Person1# should've called in sick earlier.", "prediction_text": "#Person1#: I have a headache."}
{"answers": "#Person2# checks #Person1#'s physical condition and finds #Person1# has a fever.", "prediction_text": "#Person1#: I have a headache."}
{"answers": "#Person1# helps #Person2# to choose a new phone.", "prediction_text": "1#: Ok, would you like a phone with camera and MP3 player?"}
{"answers": "#Person2# wants to buy a new mobile phone from #Person1#.", "prediction_text": "1#: Ok, would you like a phone with camera and MP3 player?"}
{"answers": "#Person2# wants to buy a new mobile phone from #Person1#.", "prediction_text": "1#: Ok, would you like a phone with camera and MP3 player?"}
{"answers": "Frank got a new job and is telling Judy not only the heavy schedule but also the benefits of this job.", "prediction_text": "#Person2#: Now you know why I wanted to work for the Post Office!"}
{"answers": "Frank explains to Judy the reason why he took a job at the Post office is that the government offers excellent health insurance benefits for its employees.", "prediction_text": "#Person2#: Now you know why I wanted to work for the Post Office!"}
{"answers": "Frank describes his new job's schedule and insurance at the Post Office to Judy.", "prediction_text": "#Person2#: Now you know why I wanted to work for the Post Office!"}
{"answers": "#Person2# is describing the educational background and previous working experience to #Person1# in a job interview.", "prediction_text": "#Person1#: I'm a computer programmer, I have a good command of computer skills."}
{"answers": "#Person2# is being interviewed by #Person1#. #Person2# is equipped with a bunch of valuable office skills.", "prediction_text": "#Person1#: I'm a computer programmer, I have a good command of computer skills."}
{"answers": "#Person1# asks about #Person2#'s special skills, training, previous working experience and other qualification for the job.", "prediction_text": "#Person1#: I'm a computer programmer, I have a good command of computer skills."}
{"answers": "#Person1# is not satisfied with the steak and #Person2# will change it.", "prediction_text": "'m afraid it isn't. It's a bit too well done and rather tough. Would you mind changing?"}
{"answers": "#Person2# will change a steak for #Person1# as that one doesn't satisfy #Person1#.", "prediction_text": "'m afraid it isn't. It's a bit too well done and rather tough. Would you mind changing?"}
{"answers": "#Person1# is not satisfied with #Person1#'s steak and asks to change one, and #Person2# agrees.", "prediction_text": "#Person1#: I'm sorry, I'm not very good at this."}
{"answers": "#Person1# congratulates Tom for achieving the Nobel Prize.", "prediction_text": "Person2#: Thanks for the compliment."}
{"answers": "#Person1# tells Tom that his novel has won the Nobel Prize.", "prediction_text": "Person2#: Thanks for the compliment."}
{"answers": "#Person2# tells Tom he won the Nobel Prize.", "prediction_text": "Person2#: Thanks for the compliment."}
{"answers": "#Person1# is interviewing #Person2# about #Person2#'s ability and previous experience.", "prediction_text": "Dialogue: #Person1#: What makes you think you are able to do the job?"}
{"answers": "#Person1# asks #Person2# about #Person2#'s capacities and past working experience during an interview.", "prediction_text": "Dialogue: #Person1#: What makes you think you are able to do the job?"}
{"answers": "#Person1# asks #Person2# about #Person2#'s qualification for the job.", "prediction_text": "Dialogue: #Person1#: What makes you think you are able to do the job?"}
{"answers": "#Person1# and #Person2# are talking about some personal facts of drinking. #Person2# drinks a lot, while #Person1# cannot drink much for health reasons. They decide to have a drink together tomorrow night.", "prediction_text": "#Person1#: I'm a big drinker."}
{"answers": "#Person2#, a heavy drinker, invites #Person1#, a light drinker to go out the next day. #Person2# chooses a place that has a special on pitchers.", "prediction_text": "#Person1#: I'm a big drinker."}
{"answers": "#Person1# and #Person2# talk about their drinking capacity and their drinking habits. They decide to have a drink together tomorrow night.", "prediction_text": "#Person1#: I'm a big drinker."}
{"answers": "May is helping her mother to do some preparation for the picnic.", "prediction_text": "#Person2#: No, thanks Mom. I'd like some toast and chicken wings."}
{"answers": "May's mother asks May for help in preparing for a picnic. May gives her a hand.", "prediction_text": "#Person2#: No, thanks Mom. I'd like some toast and chicken wings."}
{"answers": "Mom asks May to help to prepare for the picnic and May agrees.", "prediction_text": "and crackers for me."}
{"answers": "Muriel Douglas and James meet each other and talk about what they have done during the holiday.", "prediction_text": "I'm Muriel Douglas, and I'm a single mother of two. I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two"}
{"answers": "James and Muriel are talking while waiting for Susan, Muriel's associate. They talk about how they spent the holiday with their families.", "prediction_text": "I'm Muriel Douglas, and I'm a single mother of two. I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two"}
{"answers": "Muriel Douglas and James send greetings and talk about their holiday before the meeting. Douglas stayed in L.A., while James went to Tahoe.", "prediction_text": "I'm Muriel Douglas, and I'm a single mother of two. I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two, and I'm a single mother of two"}
{"answers": "#Person1# wants to withdraw money from an ATM, but the ATM automatically transfers 10000 USD to the World Wildlife Foundation after confirming the withdrawal option. #Person1# gets mad and somehow locked in.", "prediction_text": "#Person1#: I'm sorry, I'm not allowed to withdraw money from the bank."}
{"answers": "#Person1# run out of money because of a girl, and is withdrawing money from an ATM. But the ATM seems to go wrong and transfers #Person1#'s money to the World Wildlife Foundation, driving #Person1# crazy.", "prediction_text": "#Person1#: I'm sorry, I'm not allowed to withdraw money from the bank."}
{"answers": "#Person1# is withdrawing money from an ATM. But the ATM wrongly transfers #Person1#'s money to the World Wildlife Foundation. It drives #Person1# crazy.", "prediction_text": "#Person1#: I'm sorry, I'm not allowed to withdraw money from the bank."}
{"answers": "#Person2# tells #Person1# #Person2#'s communication strategy.", "prediction_text": "Dialogue: #Person1#: Are you a social person?"}
{"answers": "Since #Person2# is very social, #Person1# asks for the communication strategy.", "prediction_text": "Dialogue: #Person1#: Are you a social person?"}
{"answers": "#Person2# shares #Person2#'s communication strategy with #Person1#.", "prediction_text": "Dialogue: #Person1#: Are you a social person?"}
{"answers": "Mr. Polly is tired and wants a break from work. #Person1# cannot buy a bottle of soft drink for him.", "prediction_text": "#Person1#: I'm sorry, Mr. Polly."}
{"answers": "Mr. Polly wants to get a break from work and he asks #Person1# to buy a drink for him, but #Person1# refuses.", "prediction_text": "#Person1#: I'm sorry, Mr. Polly."}
{"answers": "Mr. Polly asks #Person1#'s help to buy a drink, but #Person1# refuses.", "prediction_text": "#Person1#: I'm sorry, Mr. Polly."}
{"answers": "Francis and Monica are discussing when to work on the financial report.", "prediction_text": "1#: See you."}
{"answers": "Francis and Monica manage to find time to work on a report together.", "prediction_text": "1#: See you."}
{"answers": "Francis and Monica negotiate on the time to work on the report.", "prediction_text": "1#: See you."}
{"answers": "#Person1# joins #Person2#'s interview workshop. They discuss the tips to improve their interview performance.", "prediction_text": "#Person1#: I think that we should dress neatly and appropriately."}
{"answers": "#Person1# takes an interview workshop. #Person2# offer #Person1# some useful tips on getting ready for an interview.", "prediction_text": "#Person1#: I think that we should dress neatly and appropriately."}
{"answers": "In the workshop, #Person2# offer #Person1# some suggestions on how to perform well in interviews.", "prediction_text": "#Person1#: I think that we should dress neatly and appropriately."}
{"answers": "#Person1# and Mike are discussing what kind of emotion should be expressed by Mike in this play. They have different understandings.", "prediction_text": "something else."}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "#Person1#: I'm not so sure about that."}
{"answers": "#Person1# wants Mike to act more angry, but Mike thinks he should act both angry and sad.", "prediction_text": "#Person1#: I'm not so sure about that."}
{"answers": "#Person1# greets Mrs. Todd and then they say goodbye to each other.", "prediction_text": ", do you need anything from the store?"}
{"answers": "#Person1# comes over to say hello to Mrs. Todd, and will go shopping afterwards.", "prediction_text": ", do you need anything from the store?"}
{"answers": "#Person1# visits Mrs. Todd, and Mrs. Todd appreciates that.", "prediction_text": "and say hello."}
{"answers": "Bill is tired. Bill and #Person1# talk about Bill's brother.", "prediction_text": "#Person1#: I'm going to be at the library at five o'clock sharp."}
{"answers": "#Person1# has a chat with Bill, and learns updates about Bill and his brother. #Person1# asks for time because #Person1# will go meet a friend.", "prediction_text": "#Person1#: I'm going to be at the library at five o'clock sharp."}
{"answers": "#Person1# suggests Bill take it easy and asks him about his brother. #Person1# also synchronizes the time with Bill.", "prediction_text": "#Person1#: I'm going to be at the library at five o'clock sharp."}
{"answers": "Simon and Cleo have different opinions towards the demonstration to help stop the spread of nuclear weapons. Cleo thinks it is useless, while Simon considers that Cleo should go to the demonstration.", "prediction_text": "#Person1#: I am not going to the demonstration."}
{"answers": "Cleo has no intention to attend the demonstration to help stop the spread of nuclear weapons, because Cleo hates police standing by with tear gas. Simon tries to change Cleo's mind but it doesn't work.", "prediction_text": "#Person1#: I am not going to the demonstration."}
{"answers": "Simon and Cleo argue the effectiveness of demonstrations. Simon thinks they are helpful, but Cleo disagrees and refuses to go to the demonstration of helping stop the spread of nuclear weapons with Simon.", "prediction_text": "#Person1#: I am not going to the demonstration."}
{"answers": "#Person2# let in someone and #Person1# thinks it was #Person2#'s fault.", "prediction_text": "Dialogue: #Person1#: It's partly your own fault. You should never let in anyone like that unless you're expecting him."}
{"answers": "#Person1# blames #Person2# for letting someone in without much discretion.", "prediction_text": "Dialogue: #Person1#: It's partly your own fault. You should never let in anyone like that unless you're expecting him."}
{"answers": "#Person1# advises #Person2# not to let anyone in casually.", "prediction_text": "Dialogue: #Person1#: It's partly your own fault. You should never let in anyone like that unless you're expecting him."}
{"answers": "Mark wants to borrow Maggie's class notes. Maggie suggests Mark copy them in the library and invites him to be study partners.", "prediction_text": "#Person1#: I'm going to the library."}
{"answers": "Mark asks Maggie for her history notes because Mark has been too tired in class. They become study partners at the end.", "prediction_text": "#Person1#: I'm going to the library."}
{"answers": "Mark asks Maggie for her notes because Mark's notes aren't good. They decide to be study partners and study together.", "prediction_text": "#Person1#: I'm going to the library."}
{"answers": "#Person2# wants to take a course of Professor Turner and Professor agrees if #Person2# achieves the allowance of Professor Burman.", "prediction_text": "#Person2#: I should say not. I really want to learn something."}
{"answers": "#Person2#, a junior student, wants to enroll in Professor Turner's course for seniors very much. Professor Turner will ask for another professor's opinion.", "prediction_text": "#Person2#: I should say not. I really want to learn something."}
{"answers": "#Person2# wants to enroll in Professor Turner's course, and Professor Turner agrees to consider his application.", "prediction_text": "#Person2#: I should say not. I really want to learn something."}
{"answers": "#Person1# wants to change the broken pendant in #Person2#'s shop.", "prediction_text": "bought the pendant in your shop, just before."}
{"answers": "#Person1# goes back to #Person2#'s shop to replace a broken pendant.", "prediction_text": "bought the pendant in your shop, just before."}
{"answers": "#Person1# wants a product changed from #Person2#, and #Person2# agrees.", "prediction_text": "bought the pendant in your shop, just before."}
{"answers": "#Person1# is showing the new clothes to #Person2#, but #Person2# is busy watching the games. They quarrel and get angry.", "prediction_text": "#Person1#: I'm not sure what to do with this one."}
{"answers": "#Person1# and #Person2# have a serious quarrel over whether shopping for clothes or watching a sports game is more important.", "prediction_text": "#Person1#: I'm not sure what to do with this one."}
{"answers": "#Person1# asks #Person2#'s opinion on new clothes, but #Person2# only focuses on the game and feels annoyed. They are both angry and argue.", "prediction_text": "#Person1#: I'm not sure what to do with this one."}
{"answers": "#Person1# gives suggestions on job choices to #Person2#. #Person2# likes interactive media.", "prediction_text": "Dialogue: #Person1#: There are so many jobs to choose from. What do you want to do?"}
{"answers": "#Person1# and #Person2# are looking at jobs. #Person2# is interested in the media industry.", "prediction_text": "Dialogue: #Person1#: There are so many jobs to choose from. What do you want to do?"}
{"answers": "#Person1# offers some job choices to #Person2#, but #Person2# doesn't like them.", "prediction_text": "#: There are so many jobs to choose from. What do you want to do?"}
{"answers": "#Person2# feels bored with the meeting. #Person1# gives #Person2# some suggestions on how to entertain but finally asks #Person2# to keep quiet.", "prediction_text": "#Person1#: I'm sure you'll find a way to entertain yourself."}
{"answers": "#Person1#, a reporter, takes #Person2# to a forum. #Person2# is bored and tries to find a pastime.", "prediction_text": "#Person1#: I'm sure you'll find a way to entertain yourself."}
{"answers": "#Person2# tells #Person1# that #Person2# is bored and explains the reasons, so #Person1# suggests #Person2# read some old newspapers and be quiet.", "prediction_text": "#Person1#: I'm sure you'll find a way to entertain yourself."}
{"answers": "Sarah is considering moving. #Person1# gives her advice on buying a house.", "prediction_text": "money or time."}
{"answers": "Sarah wants to move. #Person1# suggests buying a cheaper house and offers information on possible options.", "prediction_text": "money or time."}
{"answers": "Sarah tells #Person1# that she can't afford a new house. #Person1# says #Person1#'s sister-in-law bought a cheap house and suggests Sarah should buy a house far from the city center.", "prediction_text": "money or time."}
{"answers": "#Person1# introduces Mark Leach to this week's People You Meet. Mark Leach describes his job responsibilities and gives advice to people coming to Britain.", "prediction_text": "#Person1#: I think that's a good idea."}
{"answers": "#Person1# hosts the week's People You Meet, featuring Mark Leach. Mark introduces his work as an information officer in London.", "prediction_text": "#Person1#: I think that's a good idea."}
{"answers": "Mark Leach introduces his information service and his work to the audience. He also offers suggestions for people coming to Britain.", "prediction_text": "#Person1#: I think that's a good idea."}
{"answers": "Lin Fang and Lucy are talking about their favourite subjects.", "prediction_text": "Math is my worst. I don't like it. I always get the answers wrong."}
{"answers": "Lin Fang and Lucy are talking about how they like different subjects.", "prediction_text": "Math is my worst. I don't like it. I always get the answers wrong."}
{"answers": "Lucy likes English and P.E. best, but Lin Fang's favorite is Chinese and Science.", "prediction_text": "like science at all."}
{"answers": "James comes to help Mrs. Thomas to do some housework on the weekend. James is saving up to buy a bike.", "prediction_text": "#Person1#: I'm so happy you can come on Saturdays."}
{"answers": "James helped Mrs. Thomas with housework since she has broken her legs, and he wanted to save money for a bike.", "prediction_text": "#Person1#: I'm so happy you can come on Saturdays."}
{"answers": "James helps Mrs. Thomas to do chores because James wanted to save money and Mrs. Thomas is incapable.", "prediction_text": "#Person1#: I'm so happy you can come on Saturdays."}
{"answers": "#Person1# and #Person2# are talking about the low temperature at night, although spring has come.", "prediction_text": "warm things up a little."}
{"answers": "#Person1# and #Person2# agree that it still felt very cold in spring.", "prediction_text": "warm things up a little."}
{"answers": "#Person1# and #Person2# talk about the weather and how to keep warm.", "prediction_text": "warm things up a little."}
{"answers": "#Person1# and Mike are discussing what kind of emotion should be expressed by Mike in this play. They have different understandings.", "prediction_text": "#Person1#: I'm sorry, Mike. I'm sorry I'm not the best person to be with."}
{"answers": "#Person1# and Mike have a disagreement on how to act out a scene. #Person1# proposes that Mike can try to act in #Person1#'s way.", "prediction_text": "#Person1#: I'm sorry, Mike. I'm sorry I'm not the best person to be with."}
{"answers": "#Person1# wants Mike to act more angry, but Mike thinks he should act both angry and sad.", "prediction_text": "#Person1#: I'm sorry, Mike. I'm sorry I'm not the best person to be with."}
{"answers": "#Person1# takes a taxi to the Friendship Hotel for something important.", "prediction_text": "2#: Sure, I'll try my best. Here we are."}
{"answers": "#Person2# drives #Person1# to the Friendship Hotel as fast as #Person2# can be.", "prediction_text": "2#: Sure, I'll try my best. Here we are."}
{"answers": "The taxi driver takes #Person1# to the Friendship Hotel quickly.", "prediction_text": "yuan."}
{"answers": "#Person1# doesn't have any change for the bus because #Person1# bought some chips. #Person2# suggests #Person1# get a transfer from the bus driver.", "prediction_text": "#Person1#: I'm going to the bus."}
{"answers": "#Person1#'s broke, so #Person2# suggests #Person1# get a free bus transfer.", "prediction_text": "#Person1#: I'm going to the bus."}
{"answers": "#Person1#'s broke, so #Person2# suggests #Person1# get a transfer from the bus driver to go home.", "prediction_text": "#Person1#: I'm going to the bus."}
{"answers": "#Person2# tells #Person1# information about their company and its surroundings.", "prediction_text": "#Person1#: How long does it take to get to downtown from here?"}
{"answers": "#Person1# asks #Person2# about the company's surroundings, location, and staff restaurant.", "prediction_text": "#Person1#: How long does it take to get to downtown from here?"}
{"answers": "#Person2# introduces the surroundings and information about their company to #Person1#.", "prediction_text": "#Person1#: How long does it take to get to downtown from here?"}
{"answers": "#Person1# and #Person2# are gossiping about Lulu and Vic's break-up.", "prediction_text": "Vic broke up and now she ' s asked for a transfer."}
{"answers": "#Person1# and #Person2# are talking about Lulu and Vic's breakup.", "prediction_text": "Dialogue: #Person1#: Did you hear about Lulu?"}
{"answers": "#Person1# and #Person2# talk about Lulu and Vic's breakup.", "prediction_text": "Dialogue: #Person1#: Did you hear about Lulu?"}
{"answers": "Dave Thomson calls Jim but Jim went out. Sally will tell Jim Dave called.", "prediction_text": "The two men are in a car, and the car is driving away."}
{"answers": "Dave calls Jim but Jim is out. Sally tells Dave Jim's whereabouts.", "prediction_text": "The two men are in a car, and the car is driving away."}
{"answers": "Dave Thomson calls to speak to Jim but Jim is out.", "prediction_text": "Person1#: All right. Goodbye, Dave."}
{"answers": "#Person2# shows #Person1# the way to City Hall patiently.", "prediction_text": "#Person1#: I'm sorry. I didn't follow you. Could you please repeat that?"}
{"answers": "#Person1# asks #Person2# the way to City Hall and #Person2# answers with patience.", "prediction_text": "#Person1#: I'm sorry. I didn't follow you. Could you please repeat that?"}
{"answers": "#Person2# patiently shows #Person1# the way to City Hall.", "prediction_text": "#Person1#: I'm sorry. I didn't follow you. Could you please repeat that?"}
{"answers": "#Person1# lost #Person1#'s passport. #Person2# suggests #Person1# fill in the lost property report and inform the embassy of the situation so they can issue a new passport in case it cannot be found.", "prediction_text": "#Person1#: I am afraid not. I can't find my passport."}
{"answers": "#Person1# asks #Person2# for help to find #Person1#'s passport. #Person2# checks the shoe department but finds nothing. Thus, #Person2# lets #Person1# fill in a report and contact the embassy.", "prediction_text": "#Person1#: I am afraid not. I can't find my passport."}
{"answers": "#Person1# lost the passport. #Person2# suggests #Person1# fill in the lost property report and contact the embassy.", "prediction_text": "#Person1#: I am afraid not. I can't find my passport."}
{"answers": "Leah informs Nathaniel that Ms. Collins will not be able to call him back until next week, so he needs to book an appointment with someone else from the International Student Office. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "be expecting you at 11:30. See you then."}
{"answers": "Leah informs Nathaniel that Ms. Collins won't be available for their appointment and explains the reason. Then Nathaniel decides to speak with someone else.", "prediction_text": "#Person1#: I'm sorry to hear that."}
{"answers": "The International Student Office calls Nathaniel to inform him that he has to book another appointment with someone else as Ms. Collins is not available. Nathaniel decides to speak with Ms. Fonda.", "prediction_text": "#Person1#: I'm sorry to hear that."}
{"answers": "#Person1# and #Person2# feel scared when heard Sarah insists on marrying Dick.", "prediction_text": "Dialogue: #Person1#: When she told me that she would marry no man but Dick, my heart was almost in my mouth."}
{"answers": "#Person1# and #Person2# are shocked about Sarah's love for Dick.", "prediction_text": "Dialogue: #Person1#: When she told me that she would marry no man but Dick, my heart was almost in my mouth."}
{"answers": "#Person1# and #Person2# are surprised to know Sarah wants to marry Dick.", "prediction_text": "Dialogue: #Person1#: When she told me that she would marry no man but Dick, my heart was almost in my mouth."}
{"answers": "#Person1# and #Person2# are at a nice party. #Person2# has a crush on Janice but is afraid of talking to her. #Person1# wishes #Person2# good luck.", "prediction_text": "#Person1#: I'm with you on that. Check out that one over there. I think I'm in love!"}
{"answers": "#Person1# and #Person2# talk about girls at a party and #Person2# tells #Person1# that #Person2# likes Janice but he feels nervous talking to girls. #Person1# encourages #Person2#.", "prediction_text": "#Person1#: I'm with you on that. Check out that one over there. I think I'm in love!"}
{"answers": "#Person1# and #Person2# are at an exciting party. #Person2# has a crush on Janice and #Person1# encourages him to have a try to talk to her.", "prediction_text": "#Person1#: I'm with you on that. Check out that one over there. I think I'm in love!"}
{"answers": "Jack tells #Person1# that business communication is his favorite last year and #Person1# will check it.", "prediction_text": "Dialogue: #Person1#: Hey Jack. How were your classes this semester?"}
{"answers": "#Person1# and Jack talk about Jack's classes last year.", "prediction_text": "Dialogue: #Person1#: Hey Jack. How were your classes this semester?"}
{"answers": "#Person1# and Jack are talking about Jack's favorite classes.", "prediction_text": "Dialogue: #Person1#: Hey Jack. How were your classes this semester?"}
{"answers": "#Person1# and #Person2# are talking about the weather in different seasons in Beijing. #Person2# reminds #Person1# to put on more clothes in the evening and not to catch a cold.", "prediction_text": "#Person1#: I'm going to go to the market."}
{"answers": "#Person1# and #Person2# are talking about the weather in Beijing. #Person2# tells #Person1# it's cold in the evening around this time.", "prediction_text": "#Person1#: I'm going to go to the market."}
{"answers": "#Person1# and #Person2# are talking about the heavy rain last night and the weather in Beijing.", "prediction_text": "#Person1#: I'm going to go to the market."}
{"answers": "#Person1# invites #Person2# to watch a movie together that night. #Person2# wants to see nothing but a war movie.", "prediction_text": "for a detective film. It also makes me nervous."}
{"answers": "#Person1# and #Person2# talk about what kind of movies to watch and they decided to watch a war movie.", "prediction_text": "for a detective film. It also makes me nervous."}
{"answers": "#Person1# invites #Person2# to movie and #Person2# wants to see a war movie.", "prediction_text": "a war movie."}
{"answers": "Adam is showing #Person1# around his school. #Person1# envies Adam and hoped #Person1# could enter this school one day.", "prediction_text": "#Person1#: I'm going to the library."}
{"answers": "Adam shows #Person1# around his school and introduces the library, the new classroom building, and the swimming pool.", "prediction_text": "#Person1#: I'm going to the library."}
{"answers": "Adam shows #Person1# around the school and introduces buildings and facilities in the school to #Person1#.", "prediction_text": "#Person1#: I'm going to the library."}
{"answers": "#Person1# and #Person2# feel happy because #Person1# is pregnant.", "prediction_text": "#Person1#: I'm going to be a father."}
{"answers": "#Person1# and #Person2# are happy about #Person1#'s pregnancy.", "prediction_text": "#Person1#: I'm going to be a father."}
{"answers": "#Person1# is pregnant. She and #Person2# feel happy.", "prediction_text": "#Person1#: I'm going to be a father."}
{"answers": "#Person1# and #Person2# think John's fallen for a girl.", "prediction_text": "#Person2#: Yeah. They suit each other. A perfect match between a man and a girl."}
{"answers": "#Person1# and #Person2# talk about John's dating life.", "prediction_text": "Dialogue: #Person1#: John dates her seven times a week."}
{"answers": "#Person1# and #Person2# are talking about a couple.", "prediction_text": "Dialogue: #Person1#: John dates her seven times a week."}
{"answers": "#Person1# is giving #Person2# some advice for upgrading #Person2#'s system, such as adding a painting program and a faster processor.", "prediction_text": "#Person1#: I'm not sure what I would need."}
{"answers": "#Person1# tells #Person2# how to upgrade #Person2#'s system for better software and hardware.", "prediction_text": "#Person1#: I'm not sure what I would need."}
{"answers": "#Person1# teaches #Person2# how to upgrade software and hardware in #Person2#'s system.", "prediction_text": "can we do that?"}
{"answers": "#Person1# is driving #Person2# to an inn. They talk about their careers, ages, and where they was born.", "prediction_text": "The two men are in a car, and the driver is driving."}
{"answers": "#Person1# drives #Person2# to an inn and they have a talk. #Person2# is 26 and had a business trip to China. #Person1# is 40 years old American.", "prediction_text": "The two men are in a car, and the driver is driving."}
{"answers": "#Person1# drives #Person2# from the airport to an inn and they have a casual talk about themselves.", "prediction_text": "The two men are in a car, and the driver is driving."}
{"answers": "#Person1# wants to lose weight. #Person2# suggests #Person1# take an exercise class to exercise more.", "prediction_text": "#Person1#: I'm not sure what to do."}
{"answers": "#Person2# offers #Person1# some suggestions to lose weight.", "prediction_text": "#Person1#: I'm not sure what to do."}
{"answers": "#Person2# gives #Person1# some suggestions on how to lose weight.", "prediction_text": "#Person1#: I'm not sure what to do."}
{"answers": "James reserves a dining room for eight at a restaurant. #Person1# will ask the waitress to show him the way.", "prediction_text": "Summary:"}
{"answers": "#Person1# confirms Jame's reservation at the restaurant and asks a waitress to show him the way.", "prediction_text": "your name, please?"}
{"answers": "James has reserved a dining room and #Person1#'ll asks the waitress to show him the way.", "prediction_text": "your name, please?"}
{"answers": "#Person1# is visiting a large plant and #Person2# introduces its basic information.", "prediction_text": "#Person1#: We're going to start with the assembly line."}
{"answers": "#Person2# introduces the area of the plant, its history, size, and function to #Person1#.", "prediction_text": "#Person1#: We're going to start with the assembly line."}
{"answers": "#Person1# and #Person2# visit a plant. #Person2# introduces its occupy, history, and employee number.", "prediction_text": "the plant work with everything from the raw material to the finished product?"}
{"answers": "#Person1# is asking Rebecca about her working experience. She worked for York Herald as an intern and then as a junior local news reporter. After that, she began to work for London Weekly.", "prediction_text": "I was a reporter for the London Weekly, and I was very happy there. I was very happy in London, and I was very happy in the world."}
{"answers": "Rebecca tells #Person1# about her working experiences. Rebecca first worked for the York Herald after her graduation, and then for the London Weekly.", "prediction_text": "I was a reporter for the London Weekly, and I was very happy there. I was very happy in London, and I was very happy in the world."}
{"answers": "#Person1# interviews Rebecca and asks her about her previous working experiences.", "prediction_text": "I was a reporter for the London Weekly, and I was very happy there. I was very happy in London, and I was very happy in the world."}
{"answers": "#Person1# and #Person2# will do a group report, so they plan to buy something to make posters. They make a shopping list before they go and find items according to signs on shelves.", "prediction_text": "#Person1#: I have to go to the store first. I need to buy some markers, color pencils and highlighters."}
{"answers": "#Person1# and #Person2# make a shopping list and #Person1# helps #Person2# to find goods to make a poster for their group report tomorrow.", "prediction_text": "#Person1#: I have to go to the store first. I need to buy some markers, color pencils and highlighters."}
{"answers": "#Person1# and #Person2# are preparing for the group report tomorrow and making a shopping list of things used to make the posters.", "prediction_text": "#Person1#: I have to go to the store first. I need to buy some markers, color pencils and highlighters."}
{"answers": "Mary is tired because of finding jobs in the market. #Person1# suggests Mary apply for jobs on the Internet and says she could place her information on the Job Wanted channel on a recruitment web. Mary will try at once.", "prediction_text": "see my information?"}
{"answers": "#Person1# advises Mary to try applying for a job online and teaches her how to do it. Mary will try it.", "prediction_text": "#Person1#: I am a person who is looking for a job."}
{"answers": "Mary goes to the personnel market every day and is tired. #Person1# suggests she go job hunting online. Mary will try it.", "prediction_text": "#Person1#: I am a person who is looking for a job."}
{"answers": "#Person2# is making a shopping budget to save the rest of the money. #Person1# thinks it's a good idea.", "prediction_text": "spend $ 300 a month."}
{"answers": "#Person2# tells #Person1# #Person2# plan to save money and #Person1# thinks it is good.", "prediction_text": "spend $ 300 a month."}
{"answers": "#Person2# introduces the shopping budget which is used to save money to #Person1#. #Person1# thinks it's good.", "prediction_text": "to save me a lot of money, I hope."}
{"answers": "Jane is going to the hospital to see Susan. Henry tells her to catch the number 13.", "prediction_text": "#Person1#: I'm going to the hospital to see Susan."}
{"answers": "Jane will go to the hospital to visit Susan. Henry tells her she can take the number 13.", "prediction_text": "#Person1#: I'm going to the hospital to see Susan."}
{"answers": "Jane is going to visit Suman in the hospital, Henry suggests she catch a number 13 to get there.", "prediction_text": "#Person1#: I'm going to the hospital to see Susan."}
{"answers": "#Person1# wants to talk to #Person2# about the sales projections for next year. They decide to meet on Tuesday at 2:30 the next week.", "prediction_text": "#Person1#: I'm sorry, I'm not very good at this. I'm not sure how to do it."}
{"answers": "#Person1# and #Person2# negotiate on a time to talk about the sales projections for next year. They decide to meet next Tuesday.", "prediction_text": "#Person1#: I'm sorry, I'm not very good at this. I'm not sure how to do it."}
{"answers": "#Person1# and #Person2# are scheduling to talk about the sales projections for next year and they decide to meet next Tuesday.", "prediction_text": "#Person1#: I'm sorry, I'm not very good at this. I'm not sure how to do it."}
{"answers": "#Person1# is going to New York for the first time. #Person2# suggests #Person1# use a personal tour guide service to make #Person1#'s trip plan.", "prediction_text": ". What does it do?"}
{"answers": "#Person2# recommends a personal tour guide service to #Person1#.", "prediction_text": "Dialogue: #Person1#: I'm going to New York for the first time, but I don't have a tour guide. Can you give me any suggestions?"}
{"answers": "#Person2# introduces #Person1# a website for personal tour guide service.", "prediction_text": "Dialogue: #Person1#: I'm going to New York for the first time, but I don't have a tour guide. Can you give me any suggestions?"}
{"answers": "#Person1# is interviewing #Person2#. They discuss department #Person2# wants to work in, salary, and fringe benefits.", "prediction_text": "#Person1#: I'm a clerk in the sales department."}
{"answers": "#Person1# asks #Person2# how well #Person2# knows about the company, #Person2#'s ideal department, and current salary. #Person2# also asks #Person1# about the company's benefits and insurance.", "prediction_text": "#Person1#: I'm a clerk in the sales department."}
{"answers": "#Person1# interviews #Person2# about the understanding of this company, personal preference, and salary expectation. #Person2# asks about the fringe benefits.", "prediction_text": "#Person1#: I'm a clerk in the sales department."}
{"answers": "#Person1# comes to sign an agreement but it isn't ready until evening. So #Person1# is going through the draft.", "prediction_text": "prepared. It will be ready by tomorrow."}
{"answers": "#Person1# is checking the draft of an agreement and #Person2# will finish the agreement this evening.", "prediction_text": "prepared. It will be ready by tomorrow."}
{"answers": "#Person2# gives #Person1# an agreement draft and #Person1# thinks it good. #Person2# will get the agreement ready this evening.", "prediction_text": "prepared. It will be ready by tomorrow."}
{"answers": "#Person1# rent a car from ABC Rent-a-car Company this morning and met an accident. #Person2# will call an ambulance and police for #Person1#.", "prediction_text": "#Person1#: I'm afraid we have a car accident near the border."}
{"answers": "#Person1# tells ABC Rent-a-car Company that #Person1# had a car accident. #Person2# will call an ambulance and the police.", "prediction_text": "#Person1#: I'm afraid we have a car accident near the border."}
{"answers": "#Person1# calls ABC Rent-a-car Company to report #Person1#'s car accident. #Person2# will call an ambulance and the police.", "prediction_text": "#Person1#: I'm afraid we have a car accident near the border."}
{"answers": "#Person1# is lost on the way to the school clinic. #Person2# shows #Person1# the correct direction.", "prediction_text": "#Person1#: Straight ahead to the traffic lights, left and then right."}
{"answers": "#Person1# lost #Person1#'s way to the school clinic, #Person2# guides #Person1#.", "prediction_text": "Dialogue: #Person1#: Excuse me, could you tell me how to get to the school clinic? I've lost my way."}
{"answers": "#Person1# is lost, #Person2# shows the direction to the school clinic.", "prediction_text": "Dialogue: #Person1#: Excuse me, could you tell me how to get to the school clinic? I've lost my way."}
{"answers": "#Person2# wants to change her room because the baggage elevator is too noisy. #Person1# is sorry and tells her #Person1# will manage it when there's a spare room.", "prediction_text": "#Person1#: Good morning. What can I do for you?"}
{"answers": "#Person2# wants to change a room because it's too noisy, but #Person1# tells #Person2# there are no rooms left so #Person2# has to wait till tomorrow.", "prediction_text": "#Person1#: Good morning. What can I do for you?"}
{"answers": "#Person2# cannot stand the noise near her room and #Person1# promises to change her room tomorrow as there is no room available today.", "prediction_text": "#Person1#: Good morning. What can I do for you?"}
{"answers": "#Person1# is driving #Person2# to the Beijing hotel. #Person2# will attend a banquet at six o'clock in the International Hotel.", "prediction_text": "#Person1#: I'm sorry, but I have to go."}
{"answers": "#Person1# warmly welcomes #Person2# and drives #Person2# to the Beijing Hotel. Mr. Wu has arranged a banquet for #Person2# in the evening.", "prediction_text": "#Person1#: I'm sorry, but I have to go."}
{"answers": "#Person1# has arranged everything for #Person2# after #Person2# arrives in Beijing and invites #Person2# to a banquet.", "prediction_text": "#Person1#: I'm sorry, but I have to go."}
{"answers": "#Person1# gets lost on the way to Liu Ichang. #Person2# tells #Person1# to take Bus No. 713 because it's far.", "prediction_text": "#Person1#: I'm sorry, I'm not very good at this."}
{"answers": "#Person2# helps #Person1# to find out the location of Liu Ichang and tells #Person1# to take Bus No. 173.", "prediction_text": "#Person1#: I'm sorry, I'm not very good at this."}
{"answers": "#Person1# gets lost and #Person2# shows the way to Liu Ichang to #Person1#.", "prediction_text": "#Person1#: I'm sorry, I'm not very good at this."}
{"answers": "#Person2# finds that the computer has stopped running. #Person1# suggests #Person2# ring up the repairmen.", "prediction_text": "to settle the problem."}
{"answers": "#Person1# advises #Person2# to call the repairmen to fix #Person2#'s computer.", "prediction_text": "solve the problem, either. Can you help me?"}
{"answers": "#Person2#'s computer breaks down and #Person1# suggests ringing up the repairman.", "prediction_text": "solve the problem, either. Can you help me?"}
{"answers": "#Person2# wants to buy a gift for his mother's birthday. After knowing his mother's preference, #Person1# recommends a gold watch.", "prediction_text": "#Person1#: I'm sure she will love it."}
{"answers": "#Person1# helps #Person2# pick a gift for #Person2#'s mother, and #Person2# decides to buy a watch.", "prediction_text": "#Person1#: I'm sure she will love it."}
{"answers": "#Person2# is looking for a gift for his mother in a shop and #Person1# gives him some helpful suggestions.", "prediction_text": "Dialogue: #Person1#: May I help you, sir?"}
{"answers": "Ms. Ross and Mr. Fisher are having a coffee chat face to face. They talk about their career, child, life, and then get down to business. Ms. Ross will provide service for a presentation on Saturday 24 October in the morning held by Mr. Fisher. They are discussing the preparations as time, food, furniture, guest numbers. A problem is Mr. Fisher sent out 100 invitations but the room's capacity is only about 50.", "prediction_text": "* * *"}
{"answers": "Ms. Ross helps Mr. Fisher make arrangements for a presentation. Mr. Fisher decides to serve a pre-lunch snack buffet and designs the room setting. Mr. Fisher isn't sure about how many people to come and Ms. Ross is worried that there might be too many people because the room is small.", "prediction_text": "* * *"}
{"answers": "Ms. Ross and Mr. Fisher start the conversation with greetings. Then they discuss the preparation for a presentation. Ms. Ross confirms the details of the presentation, including schedule, food service. Ms. Ross is concerned that Mr. Fisher's sent too many invitations than the room capacity.", "prediction_text": "* * *"}
{"answers": "#Person2# tells #Person1#, in Russia, things happen very fast except for bank business, while in Canada people are calmer.", "prediction_text": "I think that the main difference between the two countries is that the Russian people are more relaxed and the Americans are more stressed."}
{"answers": "#Person1# and #Person2# are talking about the differences between Russia and Canada. #Person2# tells #Person1# that almost everything in Russia happens fast while it's the opposite in Canada.", "prediction_text": "I think that the main difference between the two countries is that the Russian people are more relaxed and the Americans are more stressed."}
{"answers": "#Person1# and #Person2# are sharing their opinions towards how different is Russia from Canada in lifestyle, especially the speed in life.", "prediction_text": "I think that the main difference between the two countries is that the Russian people are more relaxed and the Americans are more stressed."}
{"answers": "#Person2# thought to travel to the Caribbean Sea. #Person1# went there last year and thinks the weather can sometimes be really bad.", "prediction_text": "the Caribbean Sea. It's a beautiful part of the world."}
{"answers": "#Person2# wanted to travel to the Caribbean Sea and but is worried about the storms.", "prediction_text": "#Person1#: I'm going to the Caribbean."}
{"answers": "#Person2# hasn't decided to go to the Caribbean Sea on vacation because #Person2# is concerned about the storms there.", "prediction_text": "#Person1#: I'm going to the Caribbean."}
{"answers": "#Person1# and #Person1#'s mother are preparing the fruits they are going to take to the picnic.", "prediction_text": "ue: #Person1#: Mum, have we got any fruit to take on the picnic? I thought there were some oranges and bananas."}
{"answers": "#Person1# and #Person2# prepare the fruits for the picnic.", "prediction_text": "ue: #Person1#: Mum, have we got any fruit to take on the picnic? I thought there were some oranges and bananas."}
{"answers": "#Person1# and #Person2# are preparing fruits for the picnic.", "prediction_text": "That'll be enough."}
{"answers": "#Person1# wants to rent an economy car from #Person2# for $19 a day or $129 a week.", "prediction_text": "#Person1#: I have a car."}
{"answers": "#Person1# rents a car from #Person2#", "prediction_text": "#Person1#: I have a car."}
{"answers": "#Person1# rents an economy car in #Person2#'s shop.", "prediction_text": "#Person1#: I have a car."}
{"answers": "#Person1# hates those who keep smiling at #Person1# and #Person2# is fed up with those who keep a straight face in front of #Person2#.", "prediction_text": ". For instance, if you are in an elevator and you smile, you will make other takers uncomfortable."}
{"answers": "#Person2# is fed up with people with a straight face, and #Person1# hates people keeping smiling to #Person1#.", "prediction_text": ". For instance, if you are in an elevator and you smile, you will make other takers uncomfortable."}
{"answers": "#Person1# and #Person2# hold different opinions towards people with different facial expressions.", "prediction_text": ". For instance, if you are in an elevator and you smile, you will make other takers uncomfortable."}
{"answers": "#Person1# and #Person2# are talking about the good performance of their business for last year. #Person2# thinks the success is partly because of Wallace's contribution, and partly because of their new marketing strategy.", "prediction_text": "#Person1#: We are now in the top 10% of the market. We are now in the top 10% of the industry. We are now in the top 10% of the country. We are now in the top 10% of the world."}
{"answers": "#Person1# and #Person2# talk about the success of their business and attribute the success to Wallace's contribution and the new marketing strategy. They hope good luck can last.", "prediction_text": "top sellers in the field."}
{"answers": "#Person1# and #Person2# are pleasant to see their business performance has improved last year. They think the credits should be given to Wallace and the new marketing strategy.", "prediction_text": "top sellers in the field."}
{"answers": "#Person2# left #Person2#'s backpack and wallet in a taxi and failed to contact the driver because #Person2# didn't have the number. #Person1# lends 50 dollars to #Person1# and will drive #Person2# home.", "prediction_text": "#Person1#: I'm going to take a bus home."}
{"answers": "#Person2# borrows some money from #Person1# because #Person2#'s backpack and wallet were lost and cannot take it back. #Person1# will drive #Person2# home.", "prediction_text": "#Person1#: I'm going to take a bus home."}
{"answers": "#Person2# lost #Person2#'s backpack, wallet, and money in a taxi. #Person1# lends #Person2# some money and will take #Person2# home.", "prediction_text": "#Person1#: I'm going to take a bus home."}
{"answers": "Steven and Lin just had a great meal. Then they talk about the different tipping cultures between America and China.", "prediction_text": "it says on the meter. I generally tip hotel porters 10 yuan per bag, but in first-class hotels they're instructed not to accept gratuities."}
{"answers": "Steven buys Lin a magnificent dinner in America and they then talk about the tipping culture in China and America.", "prediction_text": "it says on the meter. I generally tip hotel porters 10 yuan per bag, but in first-class hotels they're instructed not to accept gratuities."}
{"answers": "Steven treats Lin to a nice meal. Then they talk about the tipping cultures in their countries.", "prediction_text": "it says on the meter. I generally tip hotel porters 10 yuan per bag, but in first-class hotels they're instructed not to accept gratuities."}
{"answers": "Bill is happy because he made a move to know his roommate today.", "prediction_text": "#Person1#: I'm not sure. I think he's a friend."}
{"answers": "#Person1# and Bill talk about Bill's roommate Brain Locker.", "prediction_text": "#Person1#: I'm not sure. I think he's a friend."}
{"answers": "Bill tells #Person1# that he has made a move to read of his roommate.", "prediction_text": "#Person1#: I'm not sure. I think he's a friend."}
{"answers": "#Person2# checks Tom Wilson's information and Tom pays his hotel and meal bill.", "prediction_text": "here."}
{"answers": "Tom Wilson pays for his bill for hotel and food by credit card.", "prediction_text": "meals that you had at the hotel. That makes a total of 660 US dollars."}
{"answers": "Tom Wilson pays the bill for his room and meal cost by credit card.", "prediction_text": "meals that you had at the hotel. That makes a total of 660 US dollars."}
{"answers": "Susan calls Carol to ask about the party time. But Carol is taking a shower so #Person1# answers for her.", "prediction_text": "Carol is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the"}
{"answers": "Carol is taking a shower when Carol calls her, so #Person1# answers the telephone and tells her the party time.", "prediction_text": "Carol is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the shower. She is in the"}
{"answers": "Susan calls to ask Carol about the party time. #Person1# answers the phone and tells her.", "prediction_text": "#: She said it is tomorrow night at 8:30 at Jills House, she said you could bring a dessert if you want. Sure no problem have a good evening. Bye."}
{"answers": "#Person1# thinks that she knows #Person2# somewhere, but #Person2# denies it.", "prediction_text": "The two of them are in the middle of a conversation when a man walks up to them."}
{"answers": "#Person1# thinks she has met #Person2# somewhere, but #Person2# thinks it's a mistake.", "prediction_text": "The two of them are in the middle of a conversation when a man walks up to them."}
{"answers": "#Person1# keeps asking where #Person2#'s from because she thinks she knows #Person2# but #Person2# denies it.", "prediction_text": "The two of them are in the middle of a conversation when a man walks up to them."}
{"answers": "#Person1# is crazy for Trump and voted for him. #Person2# doesn't agree with #Person1# on Trump and will vote for Biden.", "prediction_text": "#Person1#: I am proud to say that I am our President."}
{"answers": "#Person1# is a crazy fan of Trump and wants him to be re-elected. #Person2# will vote for Biden.", "prediction_text": "#Person1#: I am proud to say that I am our President."}
{"answers": "#Person1# is crazy for Trump and voted for him but #Person2# will vote for Biden.", "prediction_text": "#Person1#: I am proud to say that I am our President."}
{"answers": "#Person1# doesn't know how to use the ATM. #Person2# teaches #Person1# step by step.", "prediction_text": "#Person2#: You need to type your PIN in."}
{"answers": "#Person1# doesn't know how to use an ATM. #Person2# teaches #Person1#.", "prediction_text": "#Person1#: I need to use the ATM."}
{"answers": "#Person2# is teaching #Person1# how to use an ATM step by step because #Person1# doesn't know how to use.", "prediction_text": "#Person1#: I need to use the ATM."}
{"answers": "Susan wants to find the memo. John has no idea where it is.", "prediction_text": "#Person2#: Hi, Susan. What's up?"}
{"answers": "Susan Miller is asking John if he knows where the memo is. John has no idea.", "prediction_text": "#Person2#: Hi, Susan. What's up?"}
{"answers": "Susan asks John where a memo is but John doesn't know.", "prediction_text": "#Person1#: I'm Susan Miller."}
{"answers": "#Person1# invites Lily to picnic this weekend. Lily accepts.", "prediction_text": ": #Person1#: I think we can go to the river, go around and have supper."}
{"answers": "#Person1# is inviting Lily to take part in their picnic this weekend, and she accepts.", "prediction_text": ": #Person1#: I think we can go to the river, go around and have supper."}
{"answers": "#Person1# invites Lily to take part in their weekend picnic and Lily accepts.", "prediction_text": ": #Person1#: I think we can go to the river, go around and have supper."}
{"answers": "#Person1# asks #Person2# about the table manners in China. #Person2# says there are many hazy rules that are different from Western. And #Person2# tells #Person1# stabbing chopsticks into a bowl resembles sacrifices for the death and is very inauspicious.", "prediction_text": "#Person1#: I'm not sure if I should be offended or not."}
{"answers": "#Person1# and #Person2# are discussing the differences between China and Western feasts. There are so many rules on the Chinese table, and they both feel hazy about its etiquette.", "prediction_text": "#Person1#: I'm not sure if I should be offended or not."}
{"answers": "#Person1# and #Person2# talk about the difference in table etiquette in China. They both feel hazy about Chinese table etiquette and wrong use of chopsticks can lead to people's enrage.", "prediction_text": "#Person1#: I'm not sure if I should be offended or not."}
{"answers": "Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies.", "prediction_text": "#Person1#: Really? Maybe I shall also sign up for its membership."}
{"answers": "Frank and Mary both like watching movies in their spare time. Mary usually rents movies at Movie Salon, and Frank is interested in signing up for its membership.", "prediction_text": "#Person1#: I'm a very good reader."}
{"answers": "Mary and Frank both like watching movies in their spare time. Mary usually rents movies at Movie Salon and Frank also wants to get a membership to rent movies", "prediction_text": "#Person1#: I'm a very good reader."}
{"answers": "#Person2# wanted to join a small political party and thinks the smaller group can influence the larger one. #Person1# and #Person2# agree that most people don't fully understand politics.", "prediction_text": "groups can raise awareness"}
{"answers": "#Person2# believes smaller political and pressure groups can influence large parties. #Person1# and #Person2# both agree that most people often don't understand politics fully.", "prediction_text": "groups can raise awareness"}
{"answers": "#Person2# thought about joining a small party and thinks smaller political and pressure groups can influence larger ones. #Person1# and #Person2# agree most people don't understand political issues fully.", "prediction_text": "#Person1#: I'm not sure if I'm a member of the green party."}
{"answers": "#Person1# apologizes for mistakes in goods. #Person1# will be responsible for Mr. Wilson's loss, and take measures to avoid such mistakes.", "prediction_text": "our sample."}
{"answers": "#Person1# feels sorry for Mr. Wilson's loss caused by #Person1# and assures that such mistakes will not happen again.", "prediction_text": "our sample."}
{"answers": "#Person1# apologizes for the loss caused by them to Mr. Wilson and assures that it will never happen again.", "prediction_text": "our sample."}
{"answers": "#Person1# asks #Person2# who saw the robbery some questions. #Person2# agrees to come to the station for more questioning.", "prediction_text": "#Person1#: I'm sure you'll find out everything you need to know."}
{"answers": "#Person1# asks #Person2# who saw a robbery some questions and #Person2# is willing to go to the station for more questioning.", "prediction_text": "#Person1#: I'm sure you'll find out everything you need to know."}
{"answers": "#Person2# tells #Person1# #Person2# witnessed the robbery and agrees to take more questions in the station.", "prediction_text": "#Person1#: I'm sure you'll find out everything you need to know."}
{"answers": "#Person1# and #Person2#'s parents are out on a date and will go out for dinner regularly.", "prediction_text": ". Do you want to order some pizza?"}
{"answers": "The parents of #Person1# and #Person2# are out on a date.", "prediction_text": "the old tradition."}
{"answers": "#Person1# and #Person2#'s parent are out on a date to revive their old tradition.", "prediction_text": "the old tradition."}
{"answers": "#Person1# has decided to go on a diet in the new year. Carol reasonably doubts it.", "prediction_text": "I'm going to be a new man in one year's time."}
{"answers": "#Person1#'s decided to go on a diet for New Year's resolution. Carol doesn't believe #Person1# will stick to it.", "prediction_text": "I'm going to be a new man in one year's time."}
{"answers": "#Person1# has decided to go on a diet in the new year. Carol doesn't believe #Person1# will stick to it", "prediction_text": "Dialogue: #Person1#: So, did I tell you about my New Year's resolution? I've decided to go on a diet."}
{"answers": "Karen Huang tried to register for Comp Lit 287 but failed because it's full. So Karen comes to the department office. #Person1# finds Karen is a Comp Lit major and the university has saved extra places for them. #Person1# gives Karen a special code and tells Karan how to use it to get into the class.", "prediction_text": "#Person1#: I'm sorry, but that class is already full. And also, students are supposed to register through the touch-tone registration system."}
{"answers": "Karen Huang couldn't register for Comp Lit 287 so Karen comes to talk to #Person1# for a solution. #Person1# says it is full and will put Karen on the waiting list. But after #Person1# knows Karen majors in comparative literature, #Person1# gives her a special code to register for the class, as they've saved extra places for them.", "prediction_text": "#Person1#: I'm sorry, but that class is already full. And also, students are supposed to register through the touch-tone registration system."}
{"answers": "Karen Huang wants to register for a class. #Person1# says it's full and will put Karen on the waiting list. But then #Person1# gives Karen a special code to register the class after #Person1# knows Karen is a Comparative Literature major.", "prediction_text": "#Person1#: I'm sorry, but that class is already full. And also, students are supposed to register through the touch-tone registration system."}
{"answers": "#Person2# voluntarily shares an umbrella with #Person1# who doesn't bring the umbrella when it's rainy.", "prediction_text": "2#: I have one. We could share it. Which way are you going?"}
{"answers": "#Person1# forgot to take an umbrella while it's raining. #Person2# shares an umbrella with #Person1#.", "prediction_text": "2#: I have one. We could share it. Which way are you going?"}
{"answers": "#Person2# shares an umbrella with #Person1# when it's rainy.", "prediction_text": "2#: I have one. We could share it. Which way are you going?"}
{"answers": "Jack gives Daisy a ride in his new car. Daisy praises it.", "prediction_text": "#Person1#: I'm a little bit of a sucker."}
{"answers": "Jack bought a new car and invites Daisy to hop in and take a ride. Daisy thinks it's a good feeling.", "prediction_text": "#Person1#: I'm a little bit of a sucker."}
{"answers": "Jack takes Daisy for a ride to experience his new car. Daisy thinks he makes a perfect choice.", "prediction_text": "#Person1#: I'm a little bit of a sucker."}
{"answers": "#Person1# asks #Person2# the exact time when the fire broke out. #Person2# says it was about 10 o'clock.", "prediction_text": "Dialogue: #Person1#: Please tell us the exact time when the big fire broke out."}
{"answers": "#Person1# asks the exact time when the big fire broke out. #Person2# remembers that it was about 10 o'clock.", "prediction_text": "Dialogue: #Person1#: Please tell us the exact time when the big fire broke out."}
{"answers": "#Person1# asks #Person2# the exact time when the fire broke out. #Person2# says it was about 10 o'clock.", "prediction_text": "Dialogue: #Person1#: Please tell us the exact time when the big fire broke out."}
{"answers": "#Person2# doesn't have enough cash to pay 905 yuan and asks to cut the service charge. #Person1# refuses. So #Person2# pays by her husband's credit card because it can be accepted.", "prediction_text": "#Person1#: I'm sorry, but we can't do that. It's store's rule."}
{"answers": "#Person1# requires 905 yuan in total, but #Person2# only has 900 in cash and asks for a release. #Person1# refuses, so #Person2# pays by her husband's credit card because it can be accepted.", "prediction_text": "#Person1#: I'm sorry, but we can't do that. It's store's rule."}
{"answers": "#Person2# is asked to pay 905 yuan but she just has 900 in cash and asks for a release from #Person1#. #Person1# refuses. So #Person2# pays by her husband's credit card because it can be accepted.", "prediction_text": "#Person1#: I'm sorry, but we can't do that. It's store's rule."}
{"answers": "#Person1# describes the contents of the regular car wash package. #Person2# will take that.", "prediction_text": "OK."}
{"answers": "#Person1# introduces the content of regular car wash package and #Person2# accepts.", "prediction_text": "OK."}
{"answers": "#Person1# introduces the services included in regular car wash package and #Person2# will take it.", "prediction_text": "OK."}
{"answers": "Harry and his wife may go abroad during the holiday, but Harry's wife worries too much, so they may not go anywhere.", "prediction_text": "#Person1#: I'm sure you will enjoy yourselves."}
{"answers": "Harry and his wife may go abroad during the holidays. However, they might not go anywhere because his wife always worries too much.", "prediction_text": "Dialogue: #Person1#: Where are you going to spend your holidays this year, Harry?"}
{"answers": "Harry is not sure about the holiday plan of going abroad to spend the holiday, because his wife worries about things after they leave.", "prediction_text": "Dialogue: #Person1#: Where are you going to spend your holidays this year, Harry?"}
{"answers": "#Person1# is a new member who wants to use the machines. Johnson teaches #Person1# to get a card to keep track of the progress and record the suitable weight for each machine. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "#Person1#: I'm sorry. I know there are still seven machines. But I'm exhausted."}
{"answers": "Johnson is teaching #Person1# how to use the training machines. He introduces a training card that allows #Person1# to keep track of the weight used on each machine. #Person1# is exhausted when there are still 7 machines left. Johnson suggests #Person1# stop to train and go through the rest of the machines next time.", "prediction_text": "#Person1#: I'm sorry. I know there are still seven machines. But I'm exhausted."}
{"answers": "Johnson shows #Person1# the correct way to use weights machine and tells #Person1# using a card to keep track of exercise intensity. #Person1# stops training as Johnson suggests and will go through the rest of the machines next time.", "prediction_text": "#Person1#: I'm sorry. I know there are still seven machines. But I'm exhausted."}
{"answers": "#Person1# and #Person2# are both unemployed. #Person2# suggests applying for the electrician program and #Person1# agrees.", "prediction_text": "Dialogue: #Person1#: Hi! How are things going with you?"}
{"answers": "Both #Person1# and #Person2# lost their jobs. They would like to apply for the electrician program.", "prediction_text": "Dialogue: #Person1#: Hi! How are things going with you?"}
{"answers": "#Person1# and #Person2# plan to apply for the electrician program after they were laid off.", "prediction_text": "Dialogue: #Person1#: Hi! How are things going with you?"}
{"answers": "#Person1# asks something about #Person2#'s care with puppies and reminds #Person2# of the vet appointment.", "prediction_text": "Dialogue: #Person1#: Have you given the puppies food yet?"}
{"answers": "#Person1# and #Person2# are talking about taking care of their puppies and the time of vet appointment.", "prediction_text": "Dialogue: #Person1#: Have you given the puppies food yet?"}
{"answers": "#Person1# inquires about #Person2#'s care with the puppies and reminds #Person2# of the vet appointment.", "prediction_text": "Dialogue: #Person1#: Have you given the puppies food yet?"}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay the rent. Ayden is short on cash but still lends $20 to #Person1#. They decide to have dinner at #Person1#'s place.", "prediction_text": "#Person1#: Hey Ayden, I was wondering if you could do me a favor."}
{"answers": "#Person1# borrows $ 20 from Ayden because #Person1# owes the landlady $ 200. Then #Person1# invites Ayden to have dinner at #Person1#'s place.", "prediction_text": "#Person1#: Hey Ayden, I was wondering if you could do me a favor."}
{"answers": "#Person1# wants to borrow $200 from Ayden to pay for the house rent but Ayden lends $20 because of the lack of money. They decide to have dinner at #Person1#'s place.", "prediction_text": "#Person1#: Hey Ayden, I was wondering if you could do me a favor."}
{"answers": "#Person2# had been in the charity organization for 2 months. Social activities had a direct impact on #Person2#'s way of thinking.", "prediction_text": "Dialogue: #Person1#: Did you work in a charity organization?"}
{"answers": "#Person2# had been in the charity organization for 2 months and the social activities influenced #Person2#'s way of thinking.", "prediction_text": "Dialogue: #Person1#: Did you work in a charity organization?"}
{"answers": "#Person2# had been in the charity organization for 2 months. Social activities influenced #Person2#'s way of thinking.", "prediction_text": "#Person1: I learned to look at people and things from an overall perspective. In that case, I am not easy to be edgy."}
{"answers": "#Person1#'s organization will make a decision in about one week and contact #Person2# via phone or e-mail.", "prediction_text": "Could you please let me know when your organization can make a decision?"}
{"answers": "#Person1# can inform #Person2# by phone or e-mail when the organization makes a decision in about one week.", "prediction_text": "Could you please let me know when your organization can make a decision?"}
{"answers": "#Person1# can tell #Person2# their organization's decision by phone or e-mail in about one week.", "prediction_text": "Could you please let me know when your organization can make a decision?"}
{"answers": "#Person2# recommends Moutan and Roast Beijing Duck. #Person1# will take that in the hotel.", "prediction_text": "Dialogue: #Person1#: Would you please recommend some Chinese wine?"}
{"answers": "#Person2# recommends #Person1# to have Roast Beijing Duck along with Moutan in the hotel. #Person1# will have a try.", "prediction_text": "Dialogue: #Person1#: Would you please recommend some Chinese wine?"}
{"answers": "#Person2# recommends Moutan and Roast Beijing Duck. #Person1# will have a try in the hotel.", "prediction_text": "Dialogue: #Person1#: Would you please recommend some Chinese wine?"}
{"answers": "Angela calls Dan to make sure he's coming to the wedding with him next week and also asks him to invite Megan. They'll drive to the wedding together.", "prediction_text": "#Person1#: I'm going to the hospital."}
{"answers": "Angela calls to remind Dan that they are going to the wedding of her friend next week and asks him to invite Megan. The three of them will go together.", "prediction_text": "already bought them a present."}
{"answers": "Angela reminds Dan to come to the wedding next week and asks him to invite Megan as well. They agree to go there together.", "prediction_text": "already bought them a present."}
{"answers": "#Person2# has traditional Greek yogurt, which #Person1# thinks rather plain. #Person1# has an Italian tiramisu, which #Person2# thinks delicious. #Person1# goes and gets both of them a fried banana.", "prediction_text": "#Person1#: I'm going to try my first tiramisu."}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. They both decide to have fried bananas and #Person1# will bring bananas back.", "prediction_text": "#Person1#: I'm going to try my first tiramisu."}
{"answers": "#Person2# tries Greek yogurt which #Person1# thinks it's rather plain. #Person1# has an Italian tiramisu which #Person2# thinks delicious. Then they want to have fried bananas and #Person1# goes out to buy some.", "prediction_text": "#Person1#: I'm going to try my first tiramisu."}
{"answers": "Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week.", "prediction_text": "Dialogue: #Person1#: Could you tell me what university you went to, Mr. Smith?"}
{"answers": "Mr. Smith tells #Person1# that he graduated from Yale University with a B. A. in Economics, and he has worked in a bank for the last five years with $500 a week.", "prediction_text": "Dialogue: #Person1#: Could you tell me what university you went to, Mr. Smith?"}
{"answers": "Mr. Smith tells #Person1# that he is 27 and gets a B.A. in Economics at Yale University. During the last five years, he has worked in a bank for $500 a week.", "prediction_text": "Dialogue: #Person1#: Could you tell me what university you went to, Mr. Smith?"}
{"answers": "#Person2# recommends Digital Barbie for #Person1#'s niece. #Person1# pays $32.42 for it by cash.", "prediction_text": "A:"}
{"answers": "#Person2# recommends #Person1# to buy a $32. 42 Digital Barbie for #Person1#'s niece. #Person1# pays it by cash.", "prediction_text": "A:"}
{"answers": "#Person1# paid $32.42 by cash to buy a Digital Barbie as a niece's gift after listening to #Person2#'s suggestion.", "prediction_text": "A:"}
{"answers": "#Person2# wants a pair of Jordan shoes in size 41.", "prediction_text": "Dialogue: #Person1#: Can I help you?"}
{"answers": "#Person2# wants to buy a pair of Jordan shoes in size 41.", "prediction_text": "Dialogue: #Person1#: Can I help you?"}
{"answers": "#Person2# wants to buy a pair of Jordan shoes in size 41.", "prediction_text": "Dialogue: #Person1#: Can I help you?"}
{"answers": "#Person1# wants to go to the science museum but loses the way. #Person2# helps #Person1# buy the ticket and gives #Person1# directions.", "prediction_text": "#Person1#: Okay."}
{"answers": "#Person1# is lost on the way to the science museum. #Person2# helps #Person1# to buy the tickets for the train and gives #Person1# directions.", "prediction_text": "#Person1#: Okay."}
{"answers": "#Person2# helps #Person1# operate the ticket machine to buy a train ticket and tells #Person1# who loses the way how to go to the science museum.", "prediction_text": "number 4."}
{"answers": "#Person1# asks Simon about his retirement and finds out Simon is on a trial scheme called phased retirement. He can work with his former company but manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "#Person1#: I'm not sure what to say."}
{"answers": "Simon tells #Person1# that he is on a scheme called phased retirement during which he had a six-month break from work, and after that he could apply for projects of the company he used to work for but manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "#Person1#: I'm not sure what to say."}
{"answers": "Simon tells #Person1# that he joined in a phased retirement scheme which offers retired people work positions from the former company, and he can manage his own time. #Person1# thinks it's good for Simon.", "prediction_text": "#Person1#: I'm not sure what to say."}
{"answers": "Rocky doesn't want to dance because no one at the party fits his type, but #Person1# likes different kinds of women from him. Finally, Rocky decides to go home to a TV dinner and his dog, Rusty", "prediction_text": "#Person1#: Hey Rocky! You've been sitting around all night. Get out and dance with someone like that woman over there."}
{"answers": "Rocky wants a woman who's affectionate and fulfills his every need. #Person1# likes different kinds of women from him. Rocky doesn't mind and plans to go home and spend the night with a TV dinner and his dog, Rusty.", "prediction_text": "#Person1#: Hey Rocky! You've been sitting around all night. Get out and dance with someone like that woman over there."}
{"answers": "Rocky has particular requirements and cannot find a suitable woman in the party, so he doesn't want to dance. #Person1# likes different kinds of women from him. He sticks with his views and decides to go home to a TV dinner and his dog, Rusty.", "prediction_text": "#Person1#: Hey Rocky! You've been sitting around all night. Get out and dance with someone like that woman over there."}
{"answers": "#Person1# and #Person2# is talking about the heavy storm last night. #Person2# thinks the weather is terrible. #Person1# is positive towards that.", "prediction_text": "Dialogue: #Person1#: It was a heavy storm last night, wasn't it?"}
{"answers": "#Person1# and #Person2# are talking about the heavy storm last night, and #Person1#'s positive. #Person2# thinks the weather is terrible. #Person1# thinks they should not complain.", "prediction_text": "are beginning to blossom."}
{"answers": "#Person2# complains the terrible storm last night and wishes for nice weather. #Person1# thinks they should not complain.", "prediction_text": "are beginning to blossom."}
{"answers": "#Person1# wants to watch TV together with #Person2#, but #Person2# finds it boring. #Person2# suggests doing music together.", "prediction_text": "I can't see why you need to go out to enjoy yourself. Why can't you have a nice time, watching TV with me? And when you are at home, you're either reading a book or something, you never actually sit and enjoy a good program with me."}
{"answers": "#Person1# asks #Person2# to watch TV with #Person1# at home. However, #Person2# finds it boring and suggests doing music together.", "prediction_text": "I can't see why you need to go out to enjoy yourself. Why can't you have a nice time, watching TV with me? And when you are at home, you're either reading a book or something, you never actually sit and enjoy a good program with me."}
{"answers": "#Person1# wants #Person2# to watch TV with #Person1#. #Person2# thinks it is boring and suggests doing their music together.", "prediction_text": "want to do."}
{"answers": "Ben feels a bit nervous about his new school life. #Person1# cheers him up and tells him about the schedule of the new school.", "prediction_text": "#Person1#: I'm a bit nervous. I have no idea what'll happen in class and how I'll get along with my classmates."}
{"answers": "Ben is nervous about his new school life. #Person1# is giving him some brief information about the school.", "prediction_text": "#Person1#: I'm a bit nervous. I have no idea what'll happen in class and how I'll get along with my classmates."}
{"answers": "Ben is nervous about the upcoming new school life. #Person1# comforts him and tells him the school daily routine.", "prediction_text": "#Person1#: I'm a bit nervous. I have no idea what'll happen in class and how I'll get along with my classmates."}
{"answers": "Adam's knee is getting better but #Person1# still suggests that he should not do any running today. Adam decides to watch the films of Michigan after practice and will be back tomorrow for full practice.", "prediction_text": "#Person1#: I'm going to be out for a while. I'm going to be out for a while."}
{"answers": "Adam's knee is getting better but #Person1# suggests that he should not do any running today. Adam decides to see films of Michigan after practice and says he'll be back tomorrow for full practice.", "prediction_text": "#Person1#: I'm going to be out for a while. I'm going to be out for a while."}
{"answers": "Adam's knee is getting better but #Person1# suggests that he should not do any running today. #Person1# says he can watch some films of Michigan after practice. Adam decides to go back tomorrow for full practice.", "prediction_text": "#Person1#: I'm going to be out for a while. I'm going to be out for a while."}
{"answers": "#Person1# wants #Person2# to help to print up a copy. #Person2# accepts and edits it for #Person1#.", "prediction_text": "Dialogue: #Person1#: I wonder if you could do me a favor."}
{"answers": "#Person1# asks for #Person2#'s help to print up a copy. #Person2# accepts and edits it for #Person1#.", "prediction_text": "#Person1#: I'm sorry, but I can't help you."}
{"answers": "#Person1# asks for #Person2#'s help to print up a copy. #Person2# accepts and edits it for #Person1#.", "prediction_text": "#Person1#: I'm sorry, but I can't help you."}
{"answers": "#Person2# lets #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "and I'll put it up."}
{"answers": "#Person2# suggests that #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "and I'll put it up."}
{"answers": "#Person2# lets #Person1# hold the ladder and #Person2# will put the curtain up.", "prediction_text": "#Person1#: I'm going to put the curtain up."}
{"answers": "Jack is available for a camping trip the weekend after next.", "prediction_text": "#Person1#: I'm going to the weekend."}
{"answers": "Jack will be available for a weekend camping trip the weekend after next.", "prediction_text": "#Person1#: I'm going to the weekend."}
{"answers": "The weekend after next is suitable for Jack to have a camping trip.", "prediction_text": "#Person1#: I'm going to the weekend."}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells #Person2# she is pregnant. #Person2# feels happy. They go to the doctor. #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "#Person1#: I'm sorry, I'm not sure. I'm not sure I want to know."}
{"answers": "#Person1# calls #Person2# to tell him that she was wrong to get upset over nothing and tells him that she's pregnant. They go to see the doctor. However, #Person2# finds out the baby isn't his because he was away the first week of February at a training seminar.", "prediction_text": "#Person1#: I'm sorry, I'm not sure. I'm not sure I want to know."}
{"answers": "#Person1# apologises to #Person2# after the quarrel and tells #Person2# she's pregnant. #Person2# feels so happy and they decide to see the doctor. After the pregnancy test about the date, #Person2# finds that it is not his baby, because he was away the first week of February at a training seminar.", "prediction_text": "#Person1#: I'm sorry, I'm not sure. I'm not sure I want to know."}
{"answers": "#Person2# is worried about #Person2#'s daughter. #Person1# suggests #Person2# should encourage #Person2#'s daughter to make her own decision.", "prediction_text": "Dialogue: #Person1#: Is there anything wrong?"}
{"answers": "#Person1# advises #Person2# to let #Person2#'s daughter make her own decisions instead of worrying about her.", "prediction_text": "Dialogue: #Person1#: Is there anything wrong?"}
{"answers": "#Person1# suggests #Person2# letting the child make own decision instead of worrying about her.", "prediction_text": "Dialogue: #Person1#: Is there anything wrong?"}
{"answers": "#Person1# may lose #Person1#'s job because of a mistake that may cause a huge loss and a dishonest supervisor. #Person2# suggests #Person1# live with #Person2# and stop buying all the junk to save money if #Person1# loses the job. #Person1# agrees.", "prediction_text": "I'm going to be a little greedy. I'll be a little greedy. I'll be a little greedy."}
{"answers": "#Person1# makes a big mistake which may cause a huge loss and #Person1# may lose this job because of the loss and #Person1#'s dishonest supervisor. #Person2# suggests that #Person1# move in with #Person2# until #Person1# find another job and stop buying all the junk, if #Person1# loses the job.", "prediction_text": "I'm going to be a little greedy. I'll be a little greedy. I'll be a little greedy."}
{"answers": "#Person1# might lose the job because of a working mistake and is worried about financial problems after losing a job. #Person2# suggests #Person1# save money until #Person1# find another job, if #Person1# loses the job.", "prediction_text": "I'm going to be a little greedy. I'll be a little greedy. I'll be a little greedy."}
{"answers": "#Person2#'s friend is visiting #Person2#. #Person2# invites him to give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "Dialogue: #Person1#: You look like so happy. Anything good happens?"}
{"answers": "#Person2#'s friend is a Ph.D. student at the University of Edinburgh. He is visiting #Person2# and will give a speech about multimodal text generation. #Person1# will attend it.", "prediction_text": "Dialogue: #Person1#: You look like so happy. Anything good happens?"}
{"answers": "#Person2#'s friend is visiting #Person2# and will give a speech about multimodal text generation. #Person1# thinks it interesting and will attend it.", "prediction_text": "Dialogue: #Person1#: You look like so happy. Anything good happens?"}
{"answers": "#Person1# suggests going to John's house, but #Person2# feels sick and decides to go to sleep.", "prediction_text": "drink some tea and stay warm. Would you like me to make you some soup?"}
{"answers": "#Person2# prefers to stay at home and rest rather than go over to John's house tonight because #Person2# gets sick.", "prediction_text": "drink some tea and stay warm. Would you like me to make you some soup?"}
{"answers": "#Person2# doesn't want to go to John's house tonight because of getting sick. #Person2# decides to go to sleep.", "prediction_text": "drink some tea and stay warm. Would you like me to make you some soup?"}
{"answers": "Mr. Faber calls #Person1# to book a double room for 3 nights at York Hotel.", "prediction_text": "The following are the results of the following queries:"}
{"answers": "Mr. Faber books a double room for 3 nights from July 20th at York Hotel.", "prediction_text": "twenty-third."}
{"answers": "Mr. Faber books a double room for three nights from July 20th at York Hotel.", "prediction_text": "twenty-third."}
{"answers": "#Person1# wants a cheap single room. #Person2# recommends calling John Godfrey and see him on Saturday.", "prediction_text": "I'm here for a whole week until Sunday tenth."}
{"answers": "#Person1# is looking for a cheap single room. #Person2# recommends #Person1# to contact John Godfrey on Saturday.", "prediction_text": "I'm here for a whole week until Sunday tenth."}
{"answers": "#Person1# is looking for a cheap single room. #Person2# gives #Person1# John Godfrey's phone number that could help to find a room and suggests that they meet on Saturday.", "prediction_text": "I'm here for a whole week until Sunday tenth."}
{"answers": "#Person1# and #Person2# blame each other for the series of bad experiences during the journey.", "prediction_text": "If you hadn't been speeding, we wouldn't have been stopped at all."}
{"answers": "#Person1# and #Person2# are blaming each other for lots of bad experience during the journey.", "prediction_text": "If you hadn't been speeding, we wouldn't have been stopped at all."}
{"answers": "#Person1# and #Person2# quarrel and blame each other because of bad experiences during the journey.", "prediction_text": "If you hadn't been speeding, we wouldn't have been stopped at all."}
{"answers": "Darlene calls to check the delayed order. Dan tells her the situation. Darlene will talk to Steve.", "prediction_text": "#Person1#: I'm calling you to tell you that the factory is short of hands at the moment."}
{"answers": "Darlene calls Dan to check on the delay of the order. Dan explains to her the reason for the delay. Darlene decides to talk to Steven.", "prediction_text": "#Person1#: I'm calling you to tell you that the factory is short of hands at the moment."}
{"answers": "Darlen calls Dan to check the delayed order of computers. Dan explains to her the reason for the delay. Darlene decides to talk to Steven.", "prediction_text": "#Person1#: I'm calling you to tell you that the factory is short of hands at the moment."}
{"answers": "#Person2# tells #Person1# the founders and founding times of the Washington Post, the New York Times, and the Los Angeles Times.", "prediction_text": "in September eighteen fifty-one by Henry Raymond and George Jones."}
{"answers": "#Person1# and #Person2# are talking about the founding time and founder of the New York Times, the Washington Post, and the Los Angeles Times were founded.", "prediction_text": "The Washington Post is a newspaper that was founded in December eighteen seventy-seven by Stilson Hutchens."}
{"answers": "#Person2# tells #Person1# the founder and the founding time of the New York Times, the Washington Post, and the Los Angeles Times.", "prediction_text": "The Washington Post is a newspaper that was founded in December eighteen seventy-seven by Stilson Hutchens."}
{"answers": "#Person1# finds that #Person2# e-mail exceeds capacity and suggests #Person2# compress the email.", "prediction_text": "#Person1#: You can send a compressed one."}
{"answers": "#Person2#'s attachment exceeds the e-mail capacity, #Person1# suggests compressing it.", "prediction_text": "#Person1#: You can send a compressed one."}
{"answers": "#Person2# can't send out an email. #Person1# suggests #Person2#'s attachment be compressed.", "prediction_text": "#Person1#: I have a virus."}
{"answers": "#Person2# tells #Person1# #Person2# enjoyed the weekend organized by #Person1#.", "prediction_text": "marvelous. It really was very kind of you to invite me. I hope it hasn't been too much trouble."}
{"answers": "#Person1# and #Person2# has enjoyed a nice weekend together.", "prediction_text": "marvelous. It really was very kind of you to invite me. I hope it hasn't been too much trouble."}
{"answers": "#Person1# invited #Person2# to spend the weekend and #Person2# has enjoyed it.", "prediction_text": "marvelous. It really was very kind of you to invite me. I hope it hasn't been too much trouble."}
{"answers": "#Person2# offers #Person1# suggestions about what to eat. #Person1# will go to Panda Express.", "prediction_text": "#Person1#: I want to get a bite to eat."}
{"answers": "#Person1# wants to eat something and #Person2# gives some advice. #Person1# finally goes to eat Chinese food.", "prediction_text": "#Person1#: I want to get a bite to eat."}
{"answers": "#Person1# is hungry. #Person2# says the Chinese food from Panda Express is good.", "prediction_text": "#Person1#: I want to get a bite to eat."}
{"answers": "Mary from Hans furniture tells Tom they decide not to employ him but Tom asks the store to reconsider him.", "prediction_text": "1#: I'm sure you are."}
{"answers": "Marry calls Tom to inform him that they decide to offer the sales position to someone else. Tom wants them to reconsider.", "prediction_text": "#Person1#: I'm sure you are."}
{"answers": "Tom wants Marry to reconsider the decision of offering the sales position to someone else instead of him.", "prediction_text": "#Person1#: I'm sure you are."}
{"answers": "#Person1# is angry about the crank calls.", "prediction_text": "Person2#: ..."}
{"answers": "#Person1# gets a crank call and is angry about it.", "prediction_text": "Person2#: ..."}
{"answers": "#Person1# receives a phone call but no one speaks.", "prediction_text": "Dialogue: #Person1#: Hello? Hello?"}
{"answers": "#Person2# is being interviewed for a music teacher. #Person2# says #Person2# has degrees in music and specializes in classic music. Then #Person2# shares some research about classic music that can make people relaxed. #Person1# is very satisfied with #Person2#. #Person2# gives some suggestions on how to start listening to classical music.", "prediction_text": "#Person1#: I'm very happy with the job. I'm very satisfied with the job. I'm very satisfied with the job."}
{"answers": "#Person1# interviews #Person2# for a music teacher position. #Person1# is very satisfied with #Person2#'s educational background in music and #Person2#'s understanding of classical music. After the interview, #Person2# suggests #Person1# can develop interests in classic music by listening to different classic music online.", "prediction_text": "#Person1#: I'm very happy with the job. I'm very satisfied with the job. I'm very satisfied with the job."}
{"answers": "#Person2# is being interviewed for a music teacher. #Person1# is impressed by #Person2#'s background and knowledge of classical music. And #Person1# will try classical music.", "prediction_text": "#Person1#: I'm very happy with the job. I'm very satisfied with the job. I'm very satisfied with the job."}
{"answers": "#Person2# likes his neibourhood girl who is popular. Although #Person1# analyses the disadvantages, #Person2# still decides to date with her.", "prediction_text": "The two men are sitting in a restaurant, talking about their plans for the day."}
{"answers": "#Person2# tells #Person1# that he falls in love with his neighbour and #Person1# thinks he has some competition and wishes him good luck.", "prediction_text": "The two men are sitting in a restaurant, talking about their plans for the day."}
{"answers": "#Person2# tells #Person1# he falls in love with the girl living under him and she's very popular. #Person2#'s going to call her now and invite her to dinner tonight.", "prediction_text": "The two men are sitting in a restaurant, talking about their plans for the day."}
{"answers": "#Person1# and #Person2# talk about Mirella's dressing style in the office which is different from others. The management decided to give Mirella formal clothes directly to warn her.", "prediction_text": "of dressing down is not quite acceptable."}
{"answers": "#Person1# thinks it's strange of Mirella wearing casually in the office. #Person2# thinks she was influenced by the casual atmosphere in California and tells #Person1# management decided to give her a new white dress to warn her.", "prediction_text": "Mirella is a very nice person. She's a good person. She's a good manager. She's a good employee. She's a good friend. She's a good person."}
{"answers": "#Person1# and #Person2# talks about Mirella dressed casually after she came back from Silicon Valley. Managements put white shirts on her desk to warn her.", "prediction_text": "Mirella is a very nice person. She's a good person. She's a good manager. She's a good employee. She's a good friend. She's a good person."}
{"answers": "#Person1# congratulates #Person2# on #Person2#'s new venture and expresses #Person1#'s willingness to help.", "prediction_text": "Dialogue: #Person1#: You're going to set up your own law office, aren't you?"}
{"answers": "#Person1# is willing to offer #Person2# help in setting up a law office.", "prediction_text": "Dialogue: #Person1#: You're going to set up your own law office, aren't you?"}
{"answers": "#Person2# is going to set up #Person2#'s own law office. #Person1# wishes #Person2# good luck.", "prediction_text": "#Person1#: I'm going to set up my own law office."}
{"answers": "Peter tells Kate he played games and listened to music the whole last night.", "prediction_text": "#Person1#: I'm tired. I need a rest."}
{"answers": "Peter tells Kate he stayed up and he's tired now.", "prediction_text": "#Person1#: I'm tired. I need a rest."}
{"answers": "Peter tells Kate he stayed up. He's tired now and needs a rest.", "prediction_text": "#Person1#: I'm tired. I need a rest."}
{"answers": "#Person2# explains alternatives of sea transportation but #Person1# addresses the importance of boat in transoceanic cargo transportation.", "prediction_text": "70 %."}
{"answers": "#Person1# and #Person2# are talking about the development of transportation and the importance of boats.", "prediction_text": "70 %."}
{"answers": "#Person2# tells #Person1# that ships and boats have been giving places to other transportations because people's life is getting faster, but #Person1# thinks they are still important.", "prediction_text": "70 %."}
{"answers": "Fanny had a nightmare because of the pressure from school applications. Her mother and Andy both comfort her.", "prediction_text": "#Person1#: I am a very good student."}
{"answers": "Fanny had a nightmare last night and her mother consoled her. Andy listens to her experience and thinks she has a good mother.", "prediction_text": "#Person1#: I am a very good student."}
{"answers": "Fanny had a bad dream last night. She is worried about getting into the University of Michigan. Andy comforts her.", "prediction_text": "#Person1#: I am a very good student."}
{"answers": "#Person1# and Ernie plan to start a band and they decide to play hip hop music.", "prediction_text": "campus."}
{"answers": "#Person1# and Ernie are preparing to start the band at school.", "prediction_text": "campus."}
{"answers": "#Person1# and Ernie start their own band on campus. #Person1# suggests they play Vanilla Ice songs.", "prediction_text": "know his most famous song, ' Ice Ice Baby, ' but I don't know his other songs."}
{"answers": "#Person1# and #Person2# are discussing where to have fun, and they decide to go to the theater tonight.", "prediction_text": "#Person1#: I'm going to New Orleans."}
{"answers": "#Person1# and #Person2# are talking about what to do tonight and they finally decide to go to watch a show.", "prediction_text": "#Person1#: I'm going to New Orleans."}
{"answers": "#Person2# hasn't been to the theater for a long time, so #Person1# and #Person2# decide to make a reservation for a show at the Sanger Theater.", "prediction_text": "#Person1#: I'm going to New Orleans."}
{"answers": "#Person1# buys some nice clothes by credit card with #Person2#'s assistance.", "prediction_text": "#Person1#: I'm sorry, but I can't accept this."}
{"answers": "#Person1# pays #Person2# by credit card for some clothes.", "prediction_text": "#Person1#: I'm sorry, but I can't accept this."}
{"answers": "#Person1# purchases some clothes by credit card with #Person2#'s assistance.", "prediction_text": "#Person1#: I'm sorry, but I can't accept this."}
{"answers": "Mr. Blake explains the training manuals cannot be sent today because they are still being copied.", "prediction_text": "Mr. Blake and Mr. Foster are on the phone."}
{"answers": "#Person1# is transferring the message between Mr. Blake and Mr. Foster about the training manuals.", "prediction_text": "Dialogue: #Person1#: Mr. Blake? Mr. Foster's on the phone. He'd like to know if you can send over those training manuals?"}
{"answers": "Mr. Foster wants the training manuals to be sent this afternoon but Mr. Blake explains that they haven't been printed out yet.", "prediction_text": "Dialogue: #Person1#: Mr. Blake? Mr. Foster's on the phone. He'd like to know if you can send over those training manuals?"}
{"answers": "#Person2# tells David about #Person2#'s planned a long trip for #Person2#'s vacation. David thinks it's nice.", "prediction_text": "#Person1#: I'm going to be a little bit of a tourist."}
{"answers": "David and #Person2# are talking about #Person2#'s plan for the vacation. David thinks it sounds good.", "prediction_text": "#Person1#: I'm going to be a little bit of a tourist."}
