# Zero-Shot Evaluation Pipeline

This directory contains a comprehensive evaluation pipeline for running zero-shot evaluations on multiple language models across different datasets.

## Overview

The pipeline evaluates the following models:
- `meta-llama/Llama-3.2-1B`
- `meta-llama/Llama-3.2-3B` 
- `EleutherAI/pythia-410m-deduped` (pythia410)
- `facebook/opt-350m` (opt350)
- `bigscience/bloomz-560m` (bloomz 560)

On these datasets:
- **DialogSum**: Dialogue summarization task, evaluated with ROUGE-LSUM metric (500 samples from test set)
  - Additional metrics: ROUGE-1, ROUGE-2, ROUGE-L
- **QED**: Question answering task, evaluated with F1 metric (500 samples from validation set)
  - Additional metrics: Precision, Recall, Exact Match (EM)
  - Note: QED dataset is loaded from local Python script at `qed/qed.py`

## Files

- `zero_shot_evaluation.py`: Main evaluation script
- `test_evaluation.py`: Test script to validate the pipeline
- `requirements_evaluation.txt`: Required Python packages
- `EVALUATION_README.md`: This documentation

## Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements_evaluation.txt
   ```

2. **Test the pipeline:**
   ```bash
   python test_evaluation.py
   ```

3. **Run full evaluation:**
   ```bash
   python zero_shot_evaluation.py
   ```

## Usage

### Basic Usage

Run evaluation for all models on all datasets:
```bash
python zero_shot_evaluation.py
```

### Advanced Usage

Evaluate specific models:
```bash
python zero_shot_evaluation.py --models pythia410 opt350
```

Evaluate specific datasets:
```bash
python zero_shot_evaluation.py --datasets dialogsum
```

Custom wandb project name:
```bash
python zero_shot_evaluation.py --project_name my-evaluation-project
```

### Command Line Arguments

- `--wandb_api_key`: Wandb API key (default: provided key)
- `--models`: List of models to evaluate (default: all)
- `--datasets`: List of datasets to evaluate (default: all)
- `--project_name`: Wandb project name (default: "zero-shot-evaluation")

Available models: `meta-llama/Llama-3.2-1B`, `meta-llama/Llama-3.2-3B`, `pythia410`, `opt350`, `bloomz560`

Available datasets: `dialogsum`, `qed`

## Output

The evaluation produces several outputs:

1. **CSV Files:**
   - `evaluation_results.csv`: Detailed results with all metrics
   - `evaluation_summary.csv`: Summary with key metrics only

2. **Individual Results:**
   - `individual_results/`: Directory containing detailed results for each model-dataset combination
   - JSON files with predictions and metrics

3. **Wandb Logging:**
   - Metrics logged to wandb with the provided API key
   - Summary table with all results
   - Individual result details

## Metrics

### DialogSum (Dialogue Summarization)
- **Primary Metric**: ROUGE-LSUM
- **Additional Metrics**: ROUGE-1, ROUGE-2, ROUGE-L
- **Dataset Source**: Hugging Face (`knkarthick/dialogsum`)

### QED (Question Answering)
- **Primary Metric**: F1 Score
- **Additional Metrics**: Precision, Recall, Exact Match (EM)
- **Dataset Source**: Local Python script (`qed/qed.py`)

## Sample Limits

- **DialogSum**: 500 samples from test set
- **QED**: 500 samples from validation set

This ensures reasonable evaluation time while maintaining statistical significance.

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory:**
   - Reduce batch size in the evaluation script
   - Use smaller models first

2. **Missing Dependencies:**
   - Run `pip install -r requirements_evaluation.txt`
   - Check `test_evaluation.py` output for specific missing packages

3. **Wandb Issues:**
   - Verify API key is correct
   - Check internet connection
   - Evaluation will continue without wandb if it fails

4. **Dataset Loading Issues:**
   - Ensure internet connection for downloading datasets
   - Check Hugging Face Hub access

### Performance Tips

1. **GPU Usage:**
   - The pipeline automatically detects and uses available GPUs
   - Ensure CUDA is properly installed for GPU acceleration

2. **Memory Management:**
   - Models are loaded with bfloat16 precision to save memory
   - Batch size is set to 4 by default, reduce if needed

3. **Parallel Processing:**
   - Evaluations run sequentially to avoid memory issues
   - Each model-dataset combination is independent

## Expected Runtime

Approximate evaluation times (with GPU):
- Small models (350M-560M): 5-10 minutes per dataset
- Medium models (1B-3B): 15-30 minutes per dataset

Total expected runtime: 2-4 hours for all models and datasets.

## Results Interpretation

### DialogSum Results
- ROUGE-LSUM scores typically range from 0.1-0.4
- Higher scores indicate better summarization quality
- Compare against baseline models in the literature

### QED Results  
- F1 scores typically range from 0.2-0.6
- Higher scores indicate better question answering performance
- Exact Match is typically lower than F1

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Run `test_evaluation.py` to diagnose problems
3. Check individual result files for detailed error messages
